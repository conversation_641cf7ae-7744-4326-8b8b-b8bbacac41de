<?php
/**
 * 域名生成器 - 基于价格列表生成可销售域名
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置JSON响应头
header('Content-Type: application/json');

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) return;
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

loadEnv(ROOT_PATH . '/.env');

// 引入价格API
require_once ROOT_PATH . '/public/admin/ajax/domain-price-api.php';

/**
 * 生成域名建议
 */
function generateDomainSuggestions($keyword, $tlds = [], $limit = 50) {
    $suggestions = [];
    
    // 如果没有指定TLD，使用热门TLD
    if (empty($tlds)) {
        $tlds = ['com', 'net', 'org', 'io', 'co', 'app', 'dev', 'tech', 'online', 'site'];
    }
    
    // 基本域名
    foreach ($tlds as $tld) {
        $suggestions[] = [
            'domain' => $keyword . '.' . $tld,
            'type' => 'exact',
            'tld' => $tld
        ];
    }
    
    // 添加前缀
    $prefixes = ['get', 'my', 'the', 'best', 'top', 'pro', 'super', 'mega', 'ultra', 'smart'];
    foreach ($prefixes as $prefix) {
        foreach (array_slice($tlds, 0, 3) as $tld) {
            $suggestions[] = [
                'domain' => $prefix . $keyword . '.' . $tld,
                'type' => 'prefix',
                'tld' => $tld
            ];
        }
    }
    
    // 添加后缀
    $suffixes = ['hub', 'zone', 'spot', 'place', 'world', 'space', 'lab', 'studio', 'works', 'solutions'];
    foreach ($suffixes as $suffix) {
        foreach (array_slice($tlds, 0, 3) as $tld) {
            $suggestions[] = [
                'domain' => $keyword . $suffix . '.' . $tld,
                'type' => 'suffix',
                'tld' => $tld
            ];
        }
    }
    
    // 添加数字
    $numbers = ['1', '2', '24', '365', '2024', '2025'];
    foreach ($numbers as $number) {
        foreach (array_slice($tlds, 0, 2) as $tld) {
            $suggestions[] = [
                'domain' => $keyword . $number . '.' . $tld,
                'type' => 'number',
                'tld' => $tld
            ];
        }
    }
    
    // 限制结果数量
    return array_slice($suggestions, 0, $limit);
}

/**
 * 获取TLD价格信息
 */
function getTldPrice($tld) {
    $prices = getCompleteDomainPrices();
    return $prices[$tld] ?? null;
}

/**
 * 按分类获取推荐TLD
 */
function getRecommendedTlds($category = null, $priceRange = null) {
    $prices = getCompleteDomainPrices();
    $recommended = [];
    
    foreach ($prices as $tld => $price) {
        // 分类过滤
        if ($category && $price['category'] !== $category) {
            continue;
        }
        
        // 价格过滤
        if ($priceRange) {
            $regPrice = $price['registration'];
            switch ($priceRange) {
                case 'low':
                    if ($regPrice > 15) continue 2;
                    break;
                case 'medium':
                    if ($regPrice < 15 || $regPrice > 50) continue 2;
                    break;
                case 'high':
                    if ($regPrice < 50) continue 2;
                    break;
            }
        }
        
        $recommended[] = [
            'tld' => $tld,
            'price' => $price,
            'popularity' => calculatePopularity($tld, $price)
        ];
    }
    
    // 按受欢迎程度排序
    usort($recommended, function($a, $b) {
        return $b['popularity'] - $a['popularity'];
    });
    
    return $recommended;
}

/**
 * 计算TLD受欢迎程度
 */
function calculatePopularity($tld, $price) {
    $score = 0;
    
    // 基于价格的评分（价格越低越受欢迎）
    $regPrice = $price['registration'];
    if ($regPrice < 10) $score += 50;
    elseif ($regPrice < 20) $score += 30;
    elseif ($regPrice < 50) $score += 10;
    
    // 基于TLD类型的评分
    $popularTlds = ['com', 'net', 'org', 'io', 'co', 'app', 'dev'];
    if (in_array($tld, $popularTlds)) {
        $score += 30;
    }
    
    // 基于分类的评分
    $popularCategories = ['generic', 'tech', 'business'];
    if (in_array($price['category'], $popularCategories)) {
        $score += 20;
    }
    
    // 基于长度的评分（短的更受欢迎）
    if (strlen($tld) <= 3) $score += 20;
    elseif (strlen($tld) <= 5) $score += 10;
    
    return $score;
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'generate-suggestions':
            // 生成域名建议
            $keyword = $_GET['keyword'] ?? '';
            $category = $_GET['category'] ?? '';
            $priceRange = $_GET['price_range'] ?? '';
            $limit = (int)($_GET['limit'] ?? 50);
            
            if (empty($keyword)) {
                throw new Exception('请提供关键词');
            }
            
            // 获取推荐的TLD
            $recommendedTlds = getRecommendedTlds($category, $priceRange);
            $tlds = array_slice(array_column($recommendedTlds, 'tld'), 0, 20);
            
            // 生成域名建议
            $suggestions = generateDomainSuggestions($keyword, $tlds, $limit);
            
            // 添加价格信息
            foreach ($suggestions as &$suggestion) {
                $priceInfo = getTldPrice($suggestion['tld']);
                $suggestion['price'] = $priceInfo;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '域名建议生成成功',
                'keyword' => $keyword,
                'total' => count($suggestions),
                'suggestions' => $suggestions
            ]);
            break;
            
        case 'get-recommended-tlds':
            // 获取推荐TLD
            $category = $_GET['category'] ?? '';
            $priceRange = $_GET['price_range'] ?? '';
            $limit = (int)($_GET['limit'] ?? 20);
            
            $recommended = getRecommendedTlds($category, $priceRange);
            $recommended = array_slice($recommended, 0, $limit);
            
            echo json_encode([
                'success' => true,
                'message' => '推荐TLD获取成功',
                'total' => count($recommended),
                'tlds' => $recommended
            ]);
            break;
            
        case 'get-tld-info':
            // 获取TLD详细信息
            $tld = $_GET['tld'] ?? '';
            
            if (empty($tld)) {
                throw new Exception('请提供TLD');
            }
            
            $priceInfo = getTldPrice($tld);
            if (!$priceInfo) {
                throw new Exception('TLD不存在');
            }
            
            $popularity = calculatePopularity($tld, $priceInfo);
            
            echo json_encode([
                'success' => true,
                'message' => 'TLD信息获取成功',
                'tld' => $tld,
                'price' => $priceInfo,
                'popularity' => $popularity
            ]);
            break;
            
        case 'bulk-generate':
            // 批量生成域名
            $keywords = $_POST['keywords'] ?? [];
            $tlds = $_POST['tlds'] ?? [];
            
            if (empty($keywords)) {
                throw new Exception('请提供关键词列表');
            }
            
            $allSuggestions = [];
            foreach ($keywords as $keyword) {
                $suggestions = generateDomainSuggestions($keyword, $tlds, 10);
                foreach ($suggestions as &$suggestion) {
                    $priceInfo = getTldPrice($suggestion['tld']);
                    $suggestion['price'] = $priceInfo;
                }
                $allSuggestions[$keyword] = $suggestions;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '批量域名生成成功',
                'keywords' => count($keywords),
                'suggestions' => $allSuggestions
            ]);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
