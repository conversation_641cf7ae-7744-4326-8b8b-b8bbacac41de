<?php
$db = getDatabase();

// 获取数据库信息
$dbInfo = [];
$tableStats = [];
$connectionStatus = true;

try {
    // 获取数据库版本
    $version = $db->query("SELECT VERSION() as version")->fetchColumn();
    $dbInfo['version'] = $version;
    
    // 获取数据库大小
    $dbName = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
    $sizeQuery = $db->prepare("SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = ?");
    $sizeQuery->execute([$dbName]);
    $dbInfo['size'] = $sizeQuery->fetchColumn() ?? 0;
    
    // 获取表统计信息
    $tablesQuery = $db->prepare("SELECT 
        table_name,
        table_rows,
        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
        engine,
        table_collation,
        create_time,
        update_time
        FROM information_schema.tables 
        WHERE table_schema = ? 
        ORDER BY table_name");
    $tablesQuery->execute([$dbName]);
    $tableStats = $tablesQuery->fetchAll();
    
    // 获取连接信息
    $processQuery = $db->query("SHOW PROCESSLIST");
    $processes = $processQuery->fetchAll();
    $dbInfo['connections'] = count($processes);
    
} catch (Exception $e) {
    $connectionStatus = false;
    $error = $e->getMessage();
}

// 检查必需的表
$requiredTables = [
    'users' => '用户表',
    'domains' => '域名表',
    'orders' => '订单表',
    'system_settings' => '系统设置表',
    'logs' => '日志表'
];

$tableStatus = [];
foreach ($requiredTables as $table => $description) {
    try {
        $result = $db->query("SHOW TABLES LIKE '$table'")->fetch();
        $tableStatus[$table] = [
            'exists' => !empty($result),
            'description' => $description
        ];
        
        if ($tableStatus[$table]['exists']) {
            $countResult = $db->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            $tableStatus[$table]['count'] = $countResult;
        }
    } catch (Exception $e) {
        $tableStatus[$table] = [
            'exists' => false,
            'description' => $description,
            'error' => $e->getMessage()
        ];
    }
}
?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">数据库状态</h1>
        <p class="text-muted">查看数据库连接状态和表信息</p>
    </div>
    <div class="d-flex gap-2">
        <a href="../database-manager.php" class="btn btn-outline-primary" target="_blank">
            <i class="fas fa-database me-2"></i>
            数据库管理器
        </a>
        <button type="button" class="btn btn-outline-success" onclick="refreshStatus()">
            <i class="fas fa-sync-alt me-2"></i>
            刷新状态
        </button>
    </div>
</div>

<!-- 连接状态 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="status-icon mb-3">
                    <?php if ($connectionStatus): ?>
                    <i class="fas fa-check-circle fa-3x text-success"></i>
                    <?php else: ?>
                    <i class="fas fa-times-circle fa-3x text-danger"></i>
                    <?php endif; ?>
                </div>
                <h5>数据库连接</h5>
                <span class="badge bg-<?= $connectionStatus ? 'success' : 'danger' ?>">
                    <?= $connectionStatus ? '正常' : '异常' ?>
                </span>
            </div>
        </div>
    </div>
    
    <?php if ($connectionStatus): ?>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-server"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">数据库版本</h6>
                        <h6 class="mb-0"><?= htmlspecialchars(explode('-', $dbInfo['version'])[0]) ?></h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">数据库大小</h6>
                        <h6 class="mb-0"><?= number_format($dbInfo['size'], 2) ?> MB</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-link"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">活跃连接</h6>
                        <h6 class="mb-0"><?= $dbInfo['connections'] ?></h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php if (!$connectionStatus): ?>
<!-- 连接错误信息 -->
<div class="alert alert-danger">
    <h5 class="alert-heading">
        <i class="fas fa-exclamation-triangle me-2"></i>
        数据库连接失败
    </h5>
    <p class="mb-2">无法连接到数据库，请检查以下配置：</p>
    <ul class="mb-3">
        <li>数据库服务器是否运行</li>
        <li>数据库连接参数是否正确</li>
        <li>用户权限是否足够</li>
        <li>网络连接是否正常</li>
    </ul>
    <hr>
    <p class="mb-0">
        <strong>错误信息：</strong> <?= htmlspecialchars($error ?? '未知错误') ?>
    </p>
</div>
<?php else: ?>

<!-- 表状态检查 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            必需表状态检查
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <?php foreach ($tableStatus as $table => $status): ?>
            <div class="col-md-6">
                <div class="d-flex align-items-center p-3 border rounded">
                    <div class="me-3">
                        <?php if ($status['exists']): ?>
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                        <?php else: ?>
                        <i class="fas fa-times-circle fa-2x text-danger"></i>
                        <?php endif; ?>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?= htmlspecialchars($table) ?></h6>
                        <p class="text-muted mb-1"><?= htmlspecialchars($status['description']) ?></p>
                        <?php if ($status['exists']): ?>
                        <small class="text-success">
                            <i class="fas fa-database me-1"></i>
                            <?= number_format($status['count'] ?? 0) ?> 条记录
                        </small>
                        <?php else: ?>
                        <small class="text-danger">表不存在</small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<!-- 详细表信息 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            数据库表详情
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>表名</th>
                        <th>记录数</th>
                        <th>大小</th>
                        <th>引擎</th>
                        <th>字符集</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($tableStats)): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="fas fa-table fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无表信息</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($tableStats as $table): ?>
                    <tr>
                        <td>
                            <span class="fw-medium"><?= htmlspecialchars($table['table_name']) ?></span>
                        </td>
                        <td>
                            <span class="badge bg-primary"><?= number_format($table['table_rows'] ?? 0) ?></span>
                        </td>
                        <td>
                            <span class="text-muted"><?= number_format($table['size_mb'] ?? 0, 2) ?> MB</span>
                        </td>
                        <td>
                            <span class="badge bg-info"><?= htmlspecialchars($table['engine'] ?? 'N/A') ?></span>
                        </td>
                        <td>
                            <span class="text-muted small"><?= htmlspecialchars($table['table_collation'] ?? 'N/A') ?></span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= $table['create_time'] ? date('Y-m-d H:i', strtotime($table['create_time'])) : 'N/A' ?>
                            </span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= $table['update_time'] ? date('Y-m-d H:i', strtotime($table['update_time'])) : 'N/A' ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 数据库操作工具 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-tools me-2"></i>
            数据库工具
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary" onclick="optimizeTables()">
                        <i class="fas fa-wrench me-2"></i>
                        优化表
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-warning" onclick="repairTables()">
                        <i class="fas fa-tools me-2"></i>
                        修复表
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-info" onclick="analyzePerformance()">
                        <i class="fas fa-chart-line me-2"></i>
                        性能分析
                    </button>
                </div>
            </div>
        </div>
        
        <hr>
        
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>注意：</strong> 数据库操作可能影响系统性能，建议在低峰期进行。
        </div>
    </div>
</div>

<?php endif; ?>

<script>
function refreshStatus() {
    window.location.reload();
}

function optimizeTables() {
    if (confirm('确定要优化所有表吗？这可能需要一些时间。')) {
        alert('表优化功能开发中...');
    }
}

function repairTables() {
    if (confirm('确定要修复表吗？请确保已备份数据。')) {
        alert('表修复功能开发中...');
    }
}

function analyzePerformance() {
    alert('性能分析功能开发中...');
}
</script>
