<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - NameSilo域名销售系统</title>
    <meta name="description" content="登录您的域名管理账户">
    <meta name="keywords" content="域名登录,NameSilo,域名管理">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #3730a3;
            --success-color: #059669;
            --danger-color: #dc2626;
            --warning-color: #d97706;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
            margin: 1rem;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .auth-header .icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .auth-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .auth-header p {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .input-group {
            margin-bottom: 1rem;
        }
        
        .input-group-text {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            color: #6b7280;
        }
        
        .form-control {
            border: 1px solid #e5e7eb;
            padding: 0.75rem;
            font-size: 0.9rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 0.75rem;
            font-weight: 500;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .auth-link {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .auth-link:hover {
            color: var(--primary-dark);
        }
        
        .alert {
            border-radius: 10px;
            font-size: 0.9rem;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .shake {
            animation: shake 0.5s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <i class="fas fa-user-circle icon"></i>
            <h1>用户登录</h1>
            <p>NameSilo域名销售系统</p>
        </div>
        
        <!-- 消息显示区域 -->
        <div id="messageContainer"></div>
        
        <form id="loginForm">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-envelope"></i>
                </span>
                <input type="email" id="email" name="email" class="form-control"
                       placeholder="请输入您的邮箱地址" required>
            </div>
            
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                </span>
                <input type="password" id="password" name="password" class="form-control"
                       placeholder="请输入您的密码" required>
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">记住我</label>
                </div>
                <a href="forgot-password.php" class="auth-link">忘记密码？</a>
            </div>
            
            <button type="submit" id="loginBtn" class="btn btn-primary w-100">
                <i class="fas fa-sign-in-alt me-2"></i>
                <span class="btn-text">登录</span>
            </button>
        </form>
        
        <div class="text-center mt-3">
            <p class="text-muted">还没有账户？ <a href="register.php" class="auth-link">立即注册</a></p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简单的表单验证
        function validateForm(formData) {
            const email = formData.get('email');
            const password = formData.get('password');
            
            if (!email || !email.trim()) {
                return { valid: false, message: '请输入邮箱地址' };
            }
            
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                return { valid: false, message: '请输入有效的邮箱地址' };
            }
            
            if (!password || password.length < 6) {
                return { valid: false, message: '密码长度至少为6个字符' };
            }
            
            return { valid: true };
        }
        
        // 显示消息
        function showMessage(message, type = 'danger') {
            const container = document.getElementById('messageContainer');
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 'alert-danger';
            const icon = type === 'success' ? 'fa-check-circle' : 
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle';
            
            container.innerHTML = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${icon} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const formData = new FormData(this);
            
            // 表单验证
            const validation = validateForm(formData);
            if (!validation.valid) {
                showMessage(validation.message);
                loginBtn.classList.add('shake');
                setTimeout(() => loginBtn.classList.remove('shake'), 500);
                return;
            }
            
            try {
                // 显示加载状态
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登录中...';
                
                console.log('发送登录请求到:', 'api/auth.php?action=login');
                console.log('表单数据:', Object.fromEntries(formData));

                const response = await fetch('api/auth.php?action=login', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                console.log('响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('服务器错误响应:', errorText);
                    throw new Error(`服务器错误 ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('API响应结果:', result);
                
                if (result.success) {
                    showMessage(result.message || '登录成功！', 'success');
                    loginBtn.style.background = 'var(--success-color)';
                    loginBtn.innerHTML = '<i class="fas fa-check me-2"></i>登录成功';
                    
                    // 跳转到用户中心
                    setTimeout(() => {
                        window.location.href = result.redirect || 'dashboard.php';
                    }, 1500);
                } else {
                    showMessage(result.message || '登录失败，请检查您的邮箱和密码');
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>登录';
                    loginBtn.classList.add('shake');
                    setTimeout(() => loginBtn.classList.remove('shake'), 500);
                }
            } catch (error) {
                console.error('登录错误:', error);
                console.error('错误详情:', error.message);
                console.error('错误堆栈:', error.stack);
                showMessage('网络连接失败：' + error.message);
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>登录';
                loginBtn.classList.add('shake');
                setTimeout(() => loginBtn.classList.remove('shake'), 500);
            }
        });
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动聚焦到邮箱输入框
            const emailField = document.getElementById('email');
            if (emailField) {
                emailField.focus();
            }
        });
    </script>
</body>
</html>
