<?php
/**
 * 域名价格专用API - 通过价格获取可销售域名列表
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置JSON响应头
header('Content-Type: application/json');

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) return;
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

loadEnv(ROOT_PATH . '/.env');

// 数据库连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    return $pdo;
}

/**
 * 获取完整的域名价格列表（包含所有可销售的TLD）
 */
function getCompleteDomainPrices() {
    return [
        // 通用顶级域名 (gTLD)
        'com' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'generic'],
        'net' => ['registration' => 10.99, 'renewal' => 10.99, 'transfer' => 10.99, 'category' => 'generic'],
        'org' => ['registration' => 10.99, 'renewal' => 10.99, 'transfer' => 10.99, 'category' => 'generic'],
        'info' => ['registration' => 2.99, 'renewal' => 15.99, 'transfer' => 15.99, 'category' => 'generic'],
        'biz' => ['registration' => 15.99, 'renewal' => 15.99, 'transfer' => 15.99, 'category' => 'generic'],
        'name' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'generic'],
        'mobi' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'generic'],
        
        // 国家顶级域名 (ccTLD)
        'us' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'ca' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'uk' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'de' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'fr' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'it' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'es' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'nl' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'be' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'ch' => ['registration' => 12.99, 'renewal' => 12.99, 'transfer' => 12.99, 'category' => 'country'],
        'at' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'au' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'jp' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'country'],
        'cn' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'in' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'br' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'mx' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'country'],
        'ru' => ['registration' => 5.99, 'renewal' => 5.99, 'transfer' => 5.99, 'category' => 'country'],
        
        // 新顶级域名 (New gTLD)
        'co' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'new'],
        'io' => ['registration' => 59.99, 'renewal' => 59.99, 'transfer' => 59.99, 'category' => 'new'],
        'me' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'new'],
        'tv' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'new'],
        'cc' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'new'],
        'ws' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'new'],
        
        // 技术相关
        'app' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'tech'],
        'dev' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'tech'],
        'tech' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'tech'],
        'online' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'tech'],
        'site' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'tech'],
        'website' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'tech'],
        
        // 商业相关
        'store' => ['registration' => 59.99, 'renewal' => 59.99, 'transfer' => 59.99, 'category' => 'business'],
        'shop' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'business'],
        'business' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'business'],
        'company' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'business'],
        'services' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'business'],
        'solutions' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'business'],
        'consulting' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'business'],
        'management' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'business'],
        'marketing' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'business'],
        
        // 创意相关
        'design' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'creative'],
        'studio' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'creative'],
        'photography' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'creative'],
        'gallery' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'creative'],
        'art' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'creative'],
        'music' => ['registration' => 149.99, 'renewal' => 149.99, 'transfer' => 149.99, 'category' => 'creative'],
        'video' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'creative'],
        'film' => ['registration' => 89.99, 'renewal' => 89.99, 'transfer' => 89.99, 'category' => 'creative'],
        'photo' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'creative'],
        
        // 媒体相关
        'blog' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'media'],
        'news' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'media'],
        'media' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'media'],
        
        // 社交相关
        'club' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'social'],
        'team' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'social'],
        'group' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'social'],
        'community' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'social'],
        'network' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'social'],
        'social' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'social'],
        
        // 教育相关
        'academy' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'education'],
        'education' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'education'],
        'university' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'education'],
        'college' => ['registration' => 69.99, 'renewal' => 69.99, 'transfer' => 69.99, 'category' => 'education'],
        'school' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'education'],
        'training' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'education'],
        
        // 健康相关
        'health' => ['registration' => 79.99, 'renewal' => 79.99, 'transfer' => 79.99, 'category' => 'health'],
        'fitness' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'health'],
        'care' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'health'],
        'clinic' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'health'],
        'dental' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'health'],
        
        // 法律相关
        'lawyer' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'legal'],
        'attorney' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'legal'],
        'legal' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'legal'],
        'law' => ['registration' => 99.99, 'renewal' => 99.99, 'transfer' => 99.99, 'category' => 'legal'],
        
        // 金融相关
        'finance' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'finance'],
        'financial' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'finance'],
        'money' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'finance'],
        'loan' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'finance'],
        'tax' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'finance'],
        
        // 餐饮相关
        'restaurant' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        'food' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'food'],
        'kitchen' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        'cafe' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'food'],
        'bar' => ['registration' => 79.99, 'renewal' => 79.99, 'transfer' => 79.99, 'category' => 'food'],
        'wine' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        'beer' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'food'],
        'pizza' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        
        // 旅游相关
        'travel' => ['registration' => 119.99, 'renewal' => 119.99, 'transfer' => 119.99, 'category' => 'travel'],
        'vacation' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'travel'],
        'holiday' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'travel'],
        'tours' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'travel'],
        'hotel' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'travel'],
        
        // 运动相关
        'sports' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'sports'],
        'football' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'sports'],
        'soccer' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'sports'],
        'golf' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'tennis' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'hockey' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'baseball' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'basketball' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'racing' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'sports'],
        'run' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'sports'],
        'yoga' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'sports'],
        'dance' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'sports'],
        
        // 时尚相关
        'fashion' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        'style' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        'beauty' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'fashion'],
        'clothing' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        'shoes' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'fashion'],
        'boutique' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        
        // 游戏相关
        'games' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'gaming'],
        'casino' => ['registration' => 149.99, 'renewal' => 149.99, 'transfer' => 149.99, 'category' => 'gaming'],
        'poker' => ['registration' => 59.99, 'renewal' => 59.99, 'transfer' => 59.99, 'category' => 'gaming'],
        'bet' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'gaming']
    ];
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get-all-prices':
            // 获取所有域名价格
            $prices = getCompleteDomainPrices();
            
            echo json_encode([
                'success' => true,
                'message' => '获取价格列表成功',
                'total' => count($prices),
                'data' => $prices
            ]);
            break;
            
        case 'get-prices-by-category':
            // 按分类获取价格
            $category = $_GET['category'] ?? '';
            $prices = getCompleteDomainPrices();
            
            if ($category) {
                $filtered = array_filter($prices, function($price) use ($category) {
                    return $price['category'] === $category;
                });
            } else {
                $filtered = $prices;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '获取分类价格成功',
                'category' => $category,
                'total' => count($filtered),
                'data' => $filtered
            ]);
            break;
            
        case 'sync-to-database':
            // 同步价格到数据库
            $db = getDatabase();
            $prices = getCompleteDomainPrices();
            
            // 检查表是否存在
            $stmt = $db->query("SHOW TABLES LIKE 'domain_prices'");
            if (!$stmt->fetch()) {
                throw new Exception('domain_prices表不存在，请先创建表');
            }

            $added = 0;
            $updated = 0;

            foreach ($prices as $tld => $price) {
                // 检查TLD是否已存在
                $stmt = $db->prepare("SELECT id FROM domain_prices WHERE tld = ?");
                $stmt->execute([$tld]);
                $exists = $stmt->fetch();

                if ($exists) {
                    // 更新现有TLD
                    $stmt = $db->prepare("
                        UPDATE domain_prices
                        SET registration_price = ?,
                            renewal_price = ?,
                            transfer_price = ?,
                            category = ?,
                            updated_at = NOW()
                        WHERE tld = ?
                    ");
                    $stmt->execute([
                        $price['registration'],
                        $price['renewal'],
                        $price['transfer'],
                        $price['category'],
                        $tld
                    ]);
                    $updated++;
                } else {
                    // 添加新TLD
                    $stmt = $db->prepare("
                        INSERT INTO domain_prices (
                            tld, registration_price, renewal_price, transfer_price,
                            category, enabled, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
                    ");
                    $stmt->execute([
                        $tld,
                        $price['registration'],
                        $price['renewal'],
                        $price['transfer'],
                        $price['category']
                    ]);
                    $added++;
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => '价格同步成功',
                'added' => $added,
                'updated' => $updated,
                'total' => count($prices)
            ]);
            break;
            
        case 'get-categories':
            // 获取所有分类
            $prices = getCompleteDomainPrices();
            $categories = [];
            
            foreach ($prices as $tld => $price) {
                $category = $price['category'];
                if (!isset($categories[$category])) {
                    $categories[$category] = 0;
                }
                $categories[$category]++;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '获取分类成功',
                'data' => $categories
            ]);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
