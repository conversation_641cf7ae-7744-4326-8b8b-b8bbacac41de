<?php
// 域名转移页面
if (!defined('ADMIN_PATH')) {
    die('Direct access not permitted');
}

// 获取域名ID
$domainId = $_GET['id'] ?? 0;

if (!$domainId) {
    header('Location: ?page=domains');
    exit;
}

// 获取数据库连接
$db = getDatabase();

// 获取域名信息
try {
    $stmt = $db->prepare("
        SELECT d.*, u.username, u.email as user_email
        FROM domains d
        LEFT JOIN users u ON d.user_id = u.id
        WHERE d.id = ?
    ");
    $stmt->execute([$domainId]);
    $domain = $stmt->fetch();

    if (!$domain) {
        header('Location: ?page=domains');
        exit;
    }
} catch (Exception $e) {
    $error = "获取域名信息失败：" . $e->getMessage();
}

// 获取转移价格
$transferPrice = $domain['transfer_price'] ?? 12.99;

// 获取所有用户列表用于转移
$users = $db->query("SELECT id, username, email FROM users ORDER BY username")->fetchAll();

// 处理转移提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'transfer_to_user':
                $newUserId = $_POST['new_user_id'] ?? null;
                $transferReason = trim($_POST['transfer_reason'] ?? '');

                if (empty($newUserId)) {
                    $error = "请选择转移目标用户";
                } else {
                    // 更新域名归属
                    $stmt = $db->prepare("UPDATE domains SET user_id = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$newUserId, $domainId]);

                    // 记录转移日志
                    $stmt = $db->prepare("
                        INSERT INTO domain_transfer_logs (domain_id, from_user_id, to_user_id, reason, created_at)
                        VALUES (?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$domainId, $domain['user_id'], $newUserId, $transferReason]);

                    $success = "域名转移成功！";

                    // 重新获取域名信息
                    $stmt = $db->prepare("
                        SELECT d.*, u.username, u.email as user_email
                        FROM domains d
                        LEFT JOIN users u ON d.user_id = u.id
                        WHERE d.id = ?
                    ");
                    $stmt->execute([$domainId]);
                    $domain = $stmt->fetch();
                }
                break;

            case 'initiate_external_transfer':
                $authCode = trim($_POST['auth_code'] ?? '');
                $newRegistrar = trim($_POST['new_registrar'] ?? '');
                $contactEmail = trim($_POST['contact_email'] ?? '');

                if (empty($authCode)) {
                    $error = "请输入域名转移码";
                } elseif (empty($newRegistrar)) {
                    $error = "请输入目标注册商";
                } elseif (empty($contactEmail)) {
                    $error = "请输入联系邮箱";
                } else {
                    // 创建转移记录
                    $stmt = $db->prepare("
                        INSERT INTO domain_transfers 
                        (domain_id, domain_name, auth_code, new_registrar, contact_email, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
                    ");
                    $stmt->execute([$domainId, $domain['domain_name'], $authCode, $newRegistrar, $contactEmail]);
                    
                    // 更新域名状态
                    $stmt = $db->prepare("UPDATE domains SET status = 'transferred', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$domainId]);
                    
                    // 记录日志
                    $logData = [
                        'domain_id' => $domainId,
                        'domain_name' => $domain['domain_name'],
                        'new_registrar' => $newRegistrar,
                        'contact_email' => $contactEmail
                    ];
                    
                    $stmt = $db->prepare("
                        INSERT INTO logs (user_id, action, resource_type, resource_id, request_data, created_at) 
                        VALUES (?, 'domain_transfer_initiated', 'domain', ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $_SESSION['user_id'] ?? null,
                        $domainId,
                        json_encode($logData)
                    ]);
                    
                    $success = "域名转移申请已提交！转移状态：待处理";
                }
                break;
                
            case 'cancel_transfer':
                // 取消转移
                $stmt = $db->prepare("UPDATE domain_transfers SET status = 'cancelled' WHERE domain_id = ? AND status = 'pending'");
                $stmt->execute([$domainId]);
                
                $stmt = $db->prepare("UPDATE domains SET status = 'registered', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$domainId]);
                
                $success = "域名转移已取消";
                break;
        }
        
        // 重新获取更新后的数据
        $stmt = $db->prepare("
            SELECT d.*, u.username, u.email as user_email 
            FROM domains d 
            LEFT JOIN users u ON d.user_id = u.id 
            WHERE d.id = ?
        ");
        $stmt->execute([$domainId]);
        $domain = $stmt->fetch();
        
    } catch (Exception $e) {
        $error = "操作失败：" . $e->getMessage();
    }
}

// 创建域名转移表（如果不存在）
try {
    $db->exec("
        CREATE TABLE IF NOT EXISTS domain_transfers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            domain_id INT NOT NULL,
            domain_name VARCHAR(255) NOT NULL,
            auth_code VARCHAR(255) NOT NULL,
            new_registrar VARCHAR(100) NOT NULL,
            contact_email VARCHAR(255) NOT NULL,
            status ENUM('pending', 'completed', 'cancelled', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_domain_id (domain_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // 创建域名转移日志表
    $db->exec("
        CREATE TABLE IF NOT EXISTS domain_transfer_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            domain_id INT NOT NULL,
            from_user_id INT NULL,
            to_user_id INT NOT NULL,
            reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_domain_id (domain_id),
            INDEX idx_from_user_id (from_user_id),
            INDEX idx_to_user_id (to_user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (Exception $e) {
    // 表已存在或创建失败
}

// 获取转移记录
try {
    $stmt = $db->prepare("SELECT * FROM domain_transfers WHERE domain_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$domainId]);
    $transferRecord = $stmt->fetch();
} catch (Exception $e) {
    $transferRecord = null;
}

ob_start();
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">域名转移</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="?page=dashboard">首页</a></li>
                    <li class="breadcrumb-item"><a href="?page=domains">域名管理</a></li>
                    <li class="breadcrumb-item active">转移 - <?= htmlspecialchars($domain['domain_name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="?page=domains&action=manage&id=<?= $domainId ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                返回管理
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- 转移表单 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        域名转移
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 域名信息 -->
                    <div class="alert alert-info">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">
                                    <i class="fas fa-globe me-2"></i>
                                    <?= htmlspecialchars($domain['domain_name']) ?>
                                </h6>
                                <small class="text-muted">
                                    当前注册商：<?= htmlspecialchars($domain['registrar'] ?? 'NameSilo') ?>
                                </small>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <?php
                                $statusClass = [
                                    'available' => 'success',
                                    'registered' => 'primary',
                                    'transferred' => 'warning',
                                    'expired' => 'danger',
                                    'pending' => 'info'
                                ][$domain['status']] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?= $statusClass ?>">
                                    <?= [
                                        'available' => '可用',
                                        'registered' => '已注册',
                                        'transferred' => '已转移',
                                        'expired' => '已过期',
                                        'pending' => '待处理'
                                    ][$domain['status']] ?? $domain['status'] ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- 转移选项卡 -->
                    <ul class="nav nav-tabs" id="transferTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="user-transfer-tab" data-bs-toggle="tab" data-bs-target="#user-transfer" type="button" role="tab">
                                <i class="fas fa-user-friends me-2"></i>
                                用户间转移
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="external-transfer-tab" data-bs-toggle="tab" data-bs-target="#external-transfer" type="button" role="tab">
                                <i class="fas fa-external-link-alt me-2"></i>
                                外部转移
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="transferTabsContent">
                        <!-- 用户间转移 -->
                        <div class="tab-pane fade show active" id="user-transfer" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="transfer_to_user">

                                <div class="mb-3">
                                    <label class="form-label">当前归属用户</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control"
                                               value="<?= $domain['username'] ? htmlspecialchars($domain['username']) . ' (' . htmlspecialchars($domain['user_email']) . ')' : '未分配' ?>"
                                               readonly>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">转移到用户 *</label>
                                    <select class="form-select" name="new_user_id" required>
                                        <option value="">请选择目标用户</option>
                                        <?php foreach ($users as $user): ?>
                                            <?php if ($user['id'] != $domain['user_id']): ?>
                                            <option value="<?= $user['id'] ?>">
                                                <?= htmlspecialchars($user['username']) ?> (<?= htmlspecialchars($user['email']) ?>)
                                            </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">转移原因</label>
                                    <textarea class="form-control" name="transfer_reason" rows="3" placeholder="请输入转移原因..."></textarea>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    确认转移
                                </button>
                            </form>
                        </div>

                        <!-- 外部转移 -->
                        <div class="tab-pane fade" id="external-transfer" role="tabpanel">
                            <?php if ($transferRecord && $transferRecord['status'] === 'pending'): ?>
                            <!-- 转移进行中 -->
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-clock me-2"></i>转移进行中</h6>
                                <p class="mb-2">域名转移申请已提交，正在等待处理...</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">目标注册商：</small>
                                        <div><?= htmlspecialchars($transferRecord['new_registrar']) ?></div>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">申请时间：</small>
                                        <div><?= date('Y-m-d H:i', strtotime($transferRecord['created_at'])) ?></div>
                                    </div>
                                </div>
                                <hr>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="cancel_transfer">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要取消域名转移吗？')">
                                        <i class="fas fa-times me-1"></i>
                                        取消转移
                                    </button>
                                </form>
                            </div>
                            <?php else: ?>
                            <!-- 外部转移表单 -->
                            <form method="POST">
                                <input type="hidden" name="action" value="initiate_external_transfer">

                                <div class="mb-4">
                                    <label class="form-label">域名转移码 (Auth Code) <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="auth_code" placeholder="请输入域名转移码" required>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        转移码可从当前注册商处获取，通常为8-16位字符
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">目标注册商 <span class="text-danger">*</span></label>
                                    <select class="form-select" name="new_registrar" required>
                                        <option value="">请选择目标注册商</option>
                                        <option value="GoDaddy">GoDaddy</option>
                                        <option value="Namecheap">Namecheap</option>
                                        <option value="Google Domains">Google Domains</option>
                                        <option value="Cloudflare">Cloudflare</option>
                                        <option value="Porkbun">Porkbun</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">联系邮箱 <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" name="contact_email" placeholder="请输入联系邮箱" required>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        转移确认邮件将发送到此邮箱
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>转移说明</h6>
                                    <ul class="mb-0">
                                        <li>域名转移通常需要5-7个工作日完成</li>
                                        <li>转移期间域名服务不会中断</li>
                                        <li>转移费用：$<?= number_format($transferPrice, 2) ?></li>
                                        <li>转移成功后域名将自动续费1年</li>
                                    </ul>
                                </div>

                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    发起转移申请
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="col-lg-4">
            <!-- 域名信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        域名信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">域名</small>
                        <div class="fw-medium"><?= htmlspecialchars($domain['domain_name']) ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">注册日期</small>
                        <div><?= $domain['registration_date'] ? date('Y-m-d', strtotime($domain['registration_date'])) : '未注册' ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">到期日期</small>
                        <div><?= $domain['expiry_date'] ? date('Y-m-d', strtotime($domain['expiry_date'])) : '无到期日期' ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">当前归属</small>
                        <div><?= $domain['username'] ? htmlspecialchars($domain['username']) : '未分配' ?></div>
                    </div>
                </div>
            </div>

            <!-- 转移历史 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        转移历史
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $stmt = $db->prepare("SELECT * FROM domain_transfers WHERE domain_id = ? ORDER BY created_at DESC LIMIT 5");
                        $stmt->execute([$domainId]);
                        $transfers = $stmt->fetchAll();
                    } catch (Exception $e) {
                        $transfers = [];
                    }
                    ?>

                    <?php if (!empty($transfers)): ?>
                        <?php foreach ($transfers as $transfer): ?>
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="fw-medium"><?= htmlspecialchars($transfer['new_registrar']) ?></div>
                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($transfer['created_at'])) ?></small>
                                </div>
                                <span class="badge bg-<?=
                                    $transfer['status'] === 'completed' ? 'success' :
                                    ($transfer['status'] === 'pending' ? 'warning' :
                                    ($transfer['status'] === 'cancelled' ? 'secondary' : 'danger')) ?>">
                                    <?= [
                                        'pending' => '待处理',
                                        'completed' => '已完成',
                                        'cancelled' => '已取消',
                                        'failed' => '失败'
                                    ][$transfer['status']] ?? $transfer['status'] ?>
                                </span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="fas fa-history fa-2x mb-2"></i>
                            <br>
                            暂无转移记录
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



