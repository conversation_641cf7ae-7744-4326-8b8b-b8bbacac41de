<?php
/**
 * 订单管理页面
 * Order Management
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '订单管理';

// 获取用户订单列表
function getUserOrders($userId, $status = '', $limit = 20, $offset = 0) {
    try {
        $db = getDatabase();
        
        $sql = "SELECT * FROM orders WHERE user_id = ?";
        $params = [$userId];
        
        if ($status) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        return [];
    }
}

// 获取订单统计
function getOrderStats($userId) {
    try {
        $db = getDatabase();
        
        $stmt = $db->prepare("SELECT 
                                COUNT(*) as total,
                                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                                SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_spent
                             FROM orders WHERE user_id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch();
        
    } catch (Exception $e) {
        return [
            'total' => 0,
            'completed' => 0,
            'pending' => 0,
            'failed' => 0,
            'cancelled' => 0,
            'total_spent' => 0
        ];
    }
}

$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

$orders = getUserOrders($userId, $status, $limit, $offset);
$stats = getOrderStats($userId);

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <h1 class="page-title">订单管理</h1>
    <p class="page-subtitle">查看和管理您的所有订单</p>
</div>

<!-- 订单统计 -->
<div class="row g-4 mb-4">
    <div class="col-md-2">
        <div class="stats-card">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="h5 mb-1"><?= $stats['total'] ?></h3>
            <p class="text-muted mb-0 small">总订单</p>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="stats-card">
            <div class="stats-icon bg-success bg-opacity-10 text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="h5 mb-1"><?= $stats['completed'] ?></h3>
            <p class="text-muted mb-0 small">已完成</p>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="stats-card">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="h5 mb-1"><?= $stats['pending'] ?></h3>
            <p class="text-muted mb-0 small">待处理</p>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="stats-card">
            <div class="stats-icon bg-danger bg-opacity-10 text-danger">
                <i class="fas fa-times-circle"></i>
            </div>
            <h3 class="h5 mb-1"><?= $stats['failed'] ?></h3>
            <p class="text-muted mb-0 small">失败</p>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="stats-card">
            <div class="stats-icon bg-secondary bg-opacity-10 text-secondary">
                <i class="fas fa-ban"></i>
            </div>
            <h3 class="h5 mb-1"><?= $stats['cancelled'] ?></h3>
            <p class="text-muted mb-0 small">已取消</p>
        </div>
    </div>
    
    <div class="col-md-2">
        <div class="stats-card">
            <div class="stats-icon bg-info bg-opacity-10 text-info">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <h3 class="h5 mb-1">¥<?= number_format($stats['total_spent'] ?? 0, 2) ?></h3>
            <p class="text-muted mb-0 small">总消费</p>
        </div>
    </div>
</div>

<!-- 订单筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-center">
            <div class="col-md-3">
                <label class="form-label">订单状态</label>
                <select class="form-select" id="statusFilter" onchange="filterOrders()">
                    <option value="">全部状态</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>待处理</option>
                    <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>已完成</option>
                    <option value="failed" <?= $status === 'failed' ? 'selected' : '' ?>>失败</option>
                    <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>已取消</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">时间范围</label>
                <select class="form-select" id="timeFilter">
                    <option value="">全部时间</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="year">今年</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">搜索订单</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="订单号、域名..." id="orderSearch">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button class="btn btn-outline-primary w-100" onclick="exportOrders()">
                        <i class="fas fa-download me-2"></i>
                        导出
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 订单列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            订单列表
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($orders)): ?>
            <div class="text-center py-5">
                <i class="fas fa-shopping-cart fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">暂无订单</h4>
                <p class="text-muted mb-4">您还没有任何订单记录</p>
                <a href="../index.php" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>
                    开始购买域名
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>订单信息</th>
                            <th>域名</th>
                            <th>金额</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong class="font-monospace">#<?= htmlspecialchars($order['order_number'] ?? $order['id']) ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?= htmlspecialchars($order['order_type'] ?? '域名注册') ?>
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($order['domain_name'] ?? '未知域名') ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?= htmlspecialchars($order['period'] ?? '1') ?>年
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold text-success">
                                    ¥<?= number_format($order['total_amount'] ?? 0, 2) ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $status = $order['status'] ?? 'unknown';
                                $statusConfig = [
                                    'pending' => ['class' => 'warning', 'text' => '待处理', 'icon' => 'clock'],
                                    'completed' => ['class' => 'success', 'text' => '已完成', 'icon' => 'check-circle'],
                                    'failed' => ['class' => 'danger', 'text' => '失败', 'icon' => 'times-circle'],
                                    'cancelled' => ['class' => 'secondary', 'text' => '已取消', 'icon' => 'ban'],
                                    'processing' => ['class' => 'info', 'text' => '处理中', 'icon' => 'spinner']
                                ];
                                $config = $statusConfig[$status] ?? ['class' => 'secondary', 'text' => '未知', 'icon' => 'question'];
                                ?>
                                <span class="badge bg-<?= $config['class'] ?>">
                                    <i class="fas fa-<?= $config['icon'] ?> me-1"></i>
                                    <?= $config['text'] ?>
                                </span>
                            </td>
                            <td>
                                <div>
                                    <?= date('Y-m-d', strtotime($order['created_at'])) ?>
                                    <br>
                                    <small class="text-muted">
                                        <?= date('H:i:s', strtotime($order['created_at'])) ?>
                                    </small>
                                </div>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        操作
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="viewOrder(<?= $order['id'] ?>)">
                                            <i class="fas fa-eye me-2"></i>查看详情
                                        </a></li>
                                        <?php if ($order['status'] === 'completed'): ?>
                                        <li><a class="dropdown-item" href="#" onclick="downloadInvoice(<?= $order['id'] ?>)">
                                            <i class="fas fa-download me-2"></i>下载发票
                                        </a></li>
                                        <?php endif; ?>
                                        <?php if ($order['status'] === 'pending'): ?>
                                        <li><a class="dropdown-item" href="#" onclick="payOrder(<?= $order['id'] ?>)">
                                            <i class="fas fa-credit-card me-2"></i>立即支付
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="cancelOrder(<?= $order['id'] ?>)">
                                            <i class="fas fa-times me-2"></i>取消订单
                                        </a></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $page - 1 ?><?= $status ? '&status=' . $status : '' ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php for ($i = max(1, $page - 2); $i <= min($page + 2, ceil($stats['total'] / $limit)); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?><?= $status ? '&status=' . $status : '' ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                    <li class="page-item <?= $page >= ceil($stats['total'] / $limit) ? 'disabled' : '' ?>">
                        <a class="page-link" href="?page=<?= $page + 1 ?><?= $status ? '&status=' . $status : '' ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</div>

<!-- 订单详情模态框 -->
<div class="modal fade" id="orderDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">订单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>
</div>

<script>
// 筛选订单
function filterOrders() {
    const status = document.getElementById('statusFilter').value;
    const url = new URL(window.location);
    
    if (status) {
        url.searchParams.set('status', status);
    } else {
        url.searchParams.delete('status');
    }
    
    window.location.href = url.toString();
}

// 查看订单详情
function viewOrder(orderId) {
    // 这里应该发送AJAX请求获取订单详情
    document.getElementById('orderDetailBody').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载订单详情...</p>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('orderDetailModal')).show();
    
    // 模拟加载订单详情
    setTimeout(() => {
        document.getElementById('orderDetailBody').innerHTML = `
            <div class="row g-3">
                <div class="col-md-6">
                    <strong>订单号:</strong> #${orderId}
                </div>
                <div class="col-md-6">
                    <strong>状态:</strong> <span class="badge bg-success">已完成</span>
                </div>
                <div class="col-md-6">
                    <strong>域名:</strong> namesilo-namesilo-sample.com
                </div>
                <div class="col-md-6">
                    <strong>注册年限:</strong> 1年
                </div>
                <div class="col-md-6">
                    <strong>金额:</strong> ¥89.00
                </div>
                <div class="col-md-6">
                    <strong>支付方式:</strong> 余额支付
                </div>
                <div class="col-12">
                    <strong>创建时间:</strong> 2024-01-15 14:30:25
                </div>
                <div class="col-12">
                    <strong>完成时间:</strong> 2024-01-15 14:31:02
                </div>
            </div>
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                订单详情功能正在完善中，更多信息即将提供...
            </div>
        `;
    }, 1000);
}

// 支付订单
function payOrder(orderId) {
    if (confirm('确定要支付这个订单吗？')) {
        // 跳转到支付页面
        window.location.href = `../payment.php?order_id=${orderId}`;
    }
}

// 取消订单
function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？此操作不可撤销！')) {
        // 发送取消请求
        fetch('../api/orders.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'cancel_order',
                order_id: orderId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('取消失败：' + data.message);
            }
        })
        .catch(error => {
            alert('网络错误');
        });
    }
}

// 下载发票
function downloadInvoice(orderId) {
    // 下载发票
    window.open(`../api/orders.php?action=download_invoice&order_id=${orderId}`, '_blank');
}

// 导出订单
function exportOrders() {
    const status = document.getElementById('statusFilter').value;
    const timeFilter = document.getElementById('timeFilter').value;
    
    let url = '../api/orders.php?action=export';
    if (status) url += `&status=${status}`;
    if (timeFilter) url += `&time=${timeFilter}`;
    
    window.open(url, '_blank');
}

// 搜索功能
document.getElementById('orderSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
