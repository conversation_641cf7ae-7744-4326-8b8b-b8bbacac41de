<?php
$db = getDatabase();

// 获取支付记录列表
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$gateway = $_GET['gateway'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page_num = max(1, intval($_GET['page_num'] ?? 1));
$limit = 20;
$offset = ($page_num - 1) * $limit;

$where = [];
$params = [];

if ($search) {
    $where[] = "(pr.transaction_id LIKE ? OR o.order_number LIKE ? OR u.username LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

if ($status) {
    $where[] = "pr.status = ?";
    $params[] = $status;
}

if ($gateway) {
    $where[] = "pr.payment_gateway = ?";
    $params[] = $gateway;
}

if ($date_from) {
    $where[] = "DATE(pr.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where[] = "DATE(pr.created_at) <= ?";
    $params[] = $date_to;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

try {
    // 获取总数
    $countSql = "SELECT COUNT(*) FROM payment_records pr 
                 LEFT JOIN orders o ON pr.order_id = o.id 
                 LEFT JOIN users u ON o.user_id = u.id 
                 $whereClause";
    $totalRecords = $db->prepare($countSql);
    $totalRecords->execute($params);
    $totalCount = $totalRecords->fetchColumn();
    $totalPages = ceil($totalCount / $limit);

    // 获取支付记录列表
    $sql = "SELECT pr.*, o.order_number, o.total_amount as order_amount, u.username, u.email
            FROM payment_records pr
            LEFT JOIN orders o ON pr.order_id = o.id
            LEFT JOIN users u ON o.user_id = u.id
            $whereClause
            ORDER BY pr.created_at DESC
            LIMIT ?, ?";
    $params[] = $offset;
    $params[] = $limit;
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $paymentRecords = $stmt->fetchAll();

    // 获取统计数据
    $stats = $db->query("SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_records
        FROM payment_records")->fetch();
} catch (Exception $e) {
    // 如果表不存在，使用默认值
    $paymentRecords = [];
    $totalCount = 0;
    $totalPages = 0;
    $stats = [
        'total' => 0,
        'completed' => 0,
        'pending' => 0,
        'failed' => 0,
        'total_amount' => 0,
        'today_records' => 0
    ];
}
?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">支付记录</h1>
        <p class="text-muted">查看所有支付交易记录</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-primary" onclick="exportRecords()">
            <i class="fas fa-download me-2"></i>
            导出记录
        </button>
        <button type="button" class="btn btn-outline-success" onclick="refreshRecords()">
            <i class="fas fa-sync-alt me-2"></i>
            刷新
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总记录数</h6>
                        <h4 class="mb-0"><?= number_format($stats['total']) ?></h4>
                        <small class="text-success">今日: <?= $stats['today_records'] ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success bg-opacity-10 text-success rounded-3 p-3 me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">成功支付</h6>
                        <h4 class="mb-0"><?= number_format($stats['completed']) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">待处理</h6>
                        <h4 class="mb-0"><?= number_format($stats['pending']) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总金额</h6>
                        <h4 class="mb-0">$<?= number_format($stats['total_amount'], 2) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="payment-records">
            <div class="col-md-3">
                <label class="form-label">搜索</label>
                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="交易号、订单号、用户...">
            </div>
            <div class="col-md-2">
                <label class="form-label">支付状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>待处理</option>
                    <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>已完成</option>
                    <option value="failed" <?= $status === 'failed' ? 'selected' : '' ?>>失败</option>
                    <option value="refunded" <?= $status === 'refunded' ? 'selected' : '' ?>>已退款</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">支付网关</label>
                <select class="form-select" name="gateway">
                    <option value="">全部网关</option>
                    <option value="paypal" <?= $gateway === 'paypal' ? 'selected' : '' ?>>PayPal</option>
                    <option value="stripe" <?= $gateway === 'stripe' ? 'selected' : '' ?>>Stripe</option>
                    <option value="alipay" <?= $gateway === 'alipay' ? 'selected' : '' ?>>支付宝</option>
                    <option value="wechat" <?= $gateway === 'wechat' ? 'selected' : '' ?>>微信支付</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">开始日期</label>
                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">结束日期</label>
                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 支付记录列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            支付记录 (共 <?= number_format($totalCount) ?> 条记录)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>交易ID</th>
                        <th>订单信息</th>
                        <th>用户</th>
                        <th>金额</th>
                        <th>支付网关</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($paymentRecords)): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无支付记录</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($paymentRecords as $record): ?>
                    <tr>
                        <td>
                            <span class="fw-medium font-monospace"><?= htmlspecialchars($record['transaction_id'] ?? 'N/A') ?></span>
                        </td>
                        <td>
                            <div>
                                <div class="fw-medium">#<?= htmlspecialchars($record['order_number'] ?? $record['order_id']) ?></div>
                                <div class="text-muted small">订单金额: $<?= number_format($record['order_amount'] ?? 0, 2) ?></div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-medium"><?= htmlspecialchars($record['username'] ?? 'N/A') ?></div>
                                    <div class="text-muted small"><?= htmlspecialchars($record['email'] ?? 'N/A') ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="fw-medium text-success">$<?= number_format($record['amount'] ?? 0, 2) ?></span>
                        </td>
                        <td>
                            <?php
                            $gatewayIcons = [
                                'paypal' => 'fab fa-paypal text-primary',
                                'stripe' => 'fab fa-stripe text-info',
                                'alipay' => 'fab fa-alipay text-success',
                                'wechat' => 'fab fa-weixin text-warning'
                            ];
                            $gatewayNames = [
                                'paypal' => 'PayPal',
                                'stripe' => 'Stripe',
                                'alipay' => '支付宝',
                                'wechat' => '微信支付'
                            ];
                            $gateway = $record['payment_gateway'] ?? 'unknown';
                            ?>
                            <div class="d-flex align-items-center">
                                <i class="<?= $gatewayIcons[$gateway] ?? 'fas fa-credit-card text-muted' ?> me-2"></i>
                                <span><?= $gatewayNames[$gateway] ?? $gateway ?></span>
                            </div>
                        </td>
                        <td>
                            <?php
                            $statusClass = [
                                'pending' => 'warning',
                                'completed' => 'success',
                                'failed' => 'danger',
                                'refunded' => 'info'
                            ][$record['status']] ?? 'secondary';
                            $statusText = [
                                'pending' => '待处理',
                                'completed' => '已完成',
                                'failed' => '失败',
                                'refunded' => '已退款'
                            ][$record['status']] ?? $record['status'];
                            ?>
                            <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= date('Y-m-d H:i', strtotime($record['created_at'])) ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="viewPaymentDetails(<?= $record['id'] ?>)">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($record['status'] === 'completed'): ?>
                                <button type="button" class="btn btn-outline-warning" onclick="refundPayment(<?= $record['id'] ?>)">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="card-footer">
        <nav aria-label="支付记录分页">
            <ul class="pagination justify-content-center mb-0">
                <?php if ($page_num > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=payment-records&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&gateway=<?= urlencode($gateway) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                    <a class="page-link" href="?page=payment-records&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&gateway=<?= urlencode($gateway) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page_num < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=payment-records&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&gateway=<?= urlencode($gateway) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<script>
function exportRecords() {
    alert('导出记录功能开发中...');
}

function refreshRecords() {
    window.location.reload();
}

function viewPaymentDetails(recordId) {
    alert('查看支付详情功能开发中...\n记录ID: ' + recordId);
}

function refundPayment(recordId) {
    if (confirm('确定要退款这笔交易吗？')) {
        alert('退款功能开发中...\n记录ID: ' + recordId);
    }
}
</script>
