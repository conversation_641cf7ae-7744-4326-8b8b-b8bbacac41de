<?php
/**
 * API响应工具类
 * API Response Utility Class
 */

class ApiResponse {
    
    /**
     * 发送成功响应
     */
    public static function success($data = null, $message = '操作成功', $code = 200) {
        self::sendResponse(true, $code, $message, $data);
    }
    
    /**
     * 发送错误响应
     */
    public static function error($message = '操作失败', $code = 400, $data = null) {
        self::sendResponse(false, $code, $message, $data);
    }
    
    /**
     * 发送分页响应
     */
    public static function paginated($data, $total, $page, $perPage, $message = '获取成功') {
        $totalPages = ceil($total / $perPage);
        
        $response = [
            'items' => $data,
            'pagination' => [
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'total' => (int)$total,
                'total_pages' => (int)$totalPages,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ]
        ];
        
        // 设置分页头
        header('X-Total-Count: ' . $total);
        header('X-Page-Count: ' . $totalPages);
        header('X-Current-Page: ' . $page);
        header('X-Per-Page: ' . $perPage);
        
        self::success($response, $message);
    }
    
    /**
     * 发送创建成功响应
     */
    public static function created($data = null, $message = '创建成功') {
        self::success($data, $message, 201);
    }
    
    /**
     * 发送更新成功响应
     */
    public static function updated($data = null, $message = '更新成功') {
        self::success($data, $message, 200);
    }
    
    /**
     * 发送删除成功响应
     */
    public static function deleted($message = '删除成功') {
        self::success(null, $message, 200);
    }
    
    /**
     * 发送未找到响应
     */
    public static function notFound($message = '资源不存在') {
        self::error($message, 404);
    }
    
    /**
     * 发送未授权响应
     */
    public static function unauthorized($message = '未授权访问') {
        self::error($message, 401);
    }
    
    /**
     * 发送权限不足响应
     */
    public static function forbidden($message = '权限不足') {
        self::error($message, 403);
    }
    
    /**
     * 发送验证失败响应
     */
    public static function validationError($errors, $message = '数据验证失败') {
        self::error($message, 422, ['validation_errors' => $errors]);
    }
    
    /**
     * 发送服务器错误响应
     */
    public static function serverError($message = '服务器内部错误') {
        self::error($message, 500);
    }
    
    /**
     * 发送请求过于频繁响应
     */
    public static function tooManyRequests($message = '请求过于频繁，请稍后重试') {
        self::error($message, 429);
    }
    
    /**
     * 发送维护模式响应
     */
    public static function maintenance($message = '系统维护中，请稍后访问') {
        self::error($message, 503);
    }
    
    /**
     * 发送响应
     */
    private static function sendResponse($success, $code, $message, $data = null) {
        // 设置HTTP状态码
        http_response_code($code);
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');
        header('Pragma: no-cache');
        
        // 构建响应数据
        $response = [
            'success' => $success,
            'code' => $code,
            'message' => $message,
            'timestamp' => date('c'),
            'request_id' => self::generateRequestId()
        ];
        
        // 添加数据
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        // 添加调试信息（仅在开发环境）
        if (self::isDebugMode()) {
            $response['debug'] = self::getDebugInfo();
        }
        
        // 输出JSON响应
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
        // 记录API日志
        self::logApiResponse($response);
        
        exit;
    }
    
    /**
     * 生成请求ID
     */
    private static function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 检查是否为调试模式
     */
    private static function isDebugMode() {
        return filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN);
    }
    
    /**
     * 获取调试信息
     */
    private static function getDebugInfo() {
        $debug = [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)),
            'included_files_count' => count(get_included_files())
        ];
        
        // 添加SQL查询信息（如果有）
        if (class_exists('Database') && method_exists('Database', 'getQueryLog')) {
            $debug['sql_queries'] = Database::getQueryLog();
        }
        
        return $debug;
    }
    
    /**
     * 记录API响应日志
     */
    private static function logApiResponse($response) {
        try {
            $logData = [
                'method' => $_SERVER['REQUEST_METHOD'],
                'uri' => $_SERVER['REQUEST_URI'],
                'ip' => self::getClientIp(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'response_code' => $response['code'],
                'success' => $response['success'],
                'message' => $response['message'],
                'timestamp' => $response['timestamp']
            ];
            
            // 添加用户信息（如果已登录）
            if (isset($_SESSION['user_id'])) {
                $logData['user_id'] = $_SESSION['user_id'];
            }
            
            // 记录到日志文件
            $logFile = self::getLogFile();
            $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
            
        } catch (Exception $e) {
            error_log('API日志记录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取客户端IP
     */
    private static function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 获取日志文件路径
     */
    private static function getLogFile() {
        $logDir = dirname(__DIR__, 2) . '/storage/logs';
        
        // 确保日志目录存在
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        return $logDir . '/api_' . date('Y-m-d') . '.log';
    }
    
    /**
     * 验证请求数据
     */
    public static function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = is_string($rule) ? explode('|', $rule) : $rule;
            
            foreach ($ruleList as $singleRule) {
                $error = self::validateField($field, $value, $singleRule);
                if ($error) {
                    $errors[$field][] = $error;
                }
            }
        }
        
        if (!empty($errors)) {
            self::validationError($errors);
        }
        
        return true;
    }
    
    /**
     * 验证单个字段
     */
    private static function validateField($field, $value, $rule) {
        if (strpos($rule, ':') !== false) {
            list($ruleName, $ruleValue) = explode(':', $rule, 2);
        } else {
            $ruleName = $rule;
            $ruleValue = null;
        }
        
        switch ($ruleName) {
            case 'required':
                if (empty($value)) {
                    return "{$field}字段是必填的";
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "{$field}必须是有效的邮箱地址";
                }
                break;
                
            case 'min':
                if (!empty($value) && strlen($value) < $ruleValue) {
                    return "{$field}最少需要{$ruleValue}个字符";
                }
                break;
                
            case 'max':
                if (!empty($value) && strlen($value) > $ruleValue) {
                    return "{$field}最多允许{$ruleValue}个字符";
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return "{$field}必须是数字";
                }
                break;
                
            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return "{$field}必须是有效的URL";
                }
                break;
        }
        
        return null;
    }
}
?>
