<?php
/**
 * 设置备份API
 * Settings Backup API
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
define('ADMIN_PATH', dirname(__DIR__));

// 引入通用函数
require_once ADMIN_PATH . '/includes/functions.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

try {
    $db = getDatabase();
    
    // 检查system_settings表是否存在
    $tablesCheck = $db->query("SHOW TABLES LIKE 'system_settings'")->fetchAll();
    
    if (empty($tablesCheck)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => '系统设置表不存在']);
        exit;
    }
    
    // 获取所有设置
    $stmt = $db->query("SELECT `key`, `value`, `type`, `group` FROM system_settings ORDER BY `group`, `key`");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 创建备份数据
    $backup = [
        'version' => '1.0',
        'timestamp' => date('Y-m-d H:i:s'),
        'settings' => $settings
    ];
    
    // 设置下载头
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="settings-backup-' . date('Y-m-d-H-i-s') . '.json"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // 输出备份数据
    echo json_encode($backup, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => '备份失败: ' . $e->getMessage()]);
}
?>
