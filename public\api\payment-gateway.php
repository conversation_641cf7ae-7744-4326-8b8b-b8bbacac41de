<?php
/**
 * 支付网关集成
 */

require_once '../includes/database.php';

class PaymentGateway {
    private $pdo;
    
    public function __construct() {
        $this->pdo = getDatabase();
    }
    
    /**
     * 处理支付宝支付
     */
    public function processAlipay($orderNumber, $amount, $subject) {
        // 这里应该集成真实的支付宝API
        // 暂时返回模拟支付URL
        
        $paymentId = 'ALIPAY_' . time() . rand(1000, 9999);
        
        // 记录支付记录
        $this->createPaymentRecord($orderNumber, 'alipay', $amount, $paymentId, 'pending');
        
        return [
            'success' => true,
            'payment_id' => $paymentId,
            'payment_url' => "https://openapi.alipay.com/gateway.do?payment_id=$paymentId",
            'qr_code' => $this->generateQRCode($paymentId),
            'expires_at' => date('Y-m-d H:i:s', time() + 900) // 15分钟过期
        ];
    }
    
    /**
     * 处理微信支付
     */
    public function processWechatPay($orderNumber, $amount, $subject) {
        $paymentId = 'WECHAT_' . time() . rand(1000, 9999);
        
        $this->createPaymentRecord($orderNumber, 'wechat', $amount, $paymentId, 'pending');
        
        return [
            'success' => true,
            'payment_id' => $paymentId,
            'qr_code' => $this->generateQRCode($paymentId),
            'code_url' => "weixin://wxpay/bizpayurl?pr=$paymentId",
            'expires_at' => date('Y-m-d H:i:s', time() + 900)
        ];
    }
    
    /**
     * 处理PayPal支付
     */
    public function processPayPal($orderNumber, $amount, $subject) {
        $paymentId = 'PAYPAL_' . time() . rand(1000, 9999);
        
        $this->createPaymentRecord($orderNumber, 'paypal', $amount, $paymentId, 'pending');
        
        return [
            'success' => true,
            'payment_id' => $paymentId,
            'payment_url' => "https://www.paypal.com/cgi-bin/webscr?payment_id=$paymentId",
            'expires_at' => date('Y-m-d H:i:s', time() + 1800) // 30分钟过期
        ];
    }
    
    /**
     * 处理银行卡支付
     */
    public function processBankCard($orderNumber, $amount, $cardInfo) {
        $paymentId = 'CARD_' . time() . rand(1000, 9999);
        
        // 验证卡号（简单验证）
        if (!$this->validateCardNumber($cardInfo['card_number'])) {
            return [
                'success' => false,
                'error' => '银行卡号格式不正确'
            ];
        }
        
        $this->createPaymentRecord($orderNumber, 'card', $amount, $paymentId, 'pending');
        
        // 模拟银行处理
        $success = rand(0, 10) > 1; // 90%成功率
        
        if ($success) {
            $this->updatePaymentStatus($paymentId, 'completed');
            return [
                'success' => true,
                'payment_id' => $paymentId,
                'transaction_id' => 'TXN_' . time(),
                'message' => '支付成功'
            ];
        } else {
            $this->updatePaymentStatus($paymentId, 'failed');
            return [
                'success' => false,
                'payment_id' => $paymentId,
                'error' => '银行卡支付失败，请检查卡片信息'
            ];
        }
    }
    
    /**
     * 创建支付记录
     */
    private function createPaymentRecord($orderNumber, $method, $amount, $paymentId, $status) {
        $stmt = $this->pdo->prepare("
            INSERT INTO payment_records (
                order_number, payment_method, amount, payment_id, status, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([$orderNumber, $method, $amount, $paymentId, $status]);
    }
    
    /**
     * 更新支付状态
     */
    private function updatePaymentStatus($paymentId, $status) {
        $stmt = $this->pdo->prepare("
            UPDATE payment_records 
            SET status = ?, updated_at = NOW() 
            WHERE payment_id = ?
        ");
        
        $stmt->execute([$status, $paymentId]);
        
        // 如果支付成功，更新订单状态
        if ($status === 'completed') {
            $stmt = $this->pdo->prepare("
                SELECT order_number FROM payment_records WHERE payment_id = ?
            ");
            $stmt->execute([$paymentId]);
            $orderNumber = $stmt->fetchColumn();
            
            if ($orderNumber) {
                $this->updateOrderPaymentStatus($orderNumber, 'paid', $paymentId);
            }
        }
    }
    
    /**
     * 更新订单支付状态
     */
    private function updateOrderPaymentStatus($orderNumber, $paymentStatus, $paymentId) {
        $stmt = $this->pdo->prepare("
            UPDATE orders 
            SET payment_status = ?, payment_id = ?, status = 'processing', updated_at = NOW()
            WHERE order_number = ?
        ");
        
        $stmt->execute([$paymentStatus, $paymentId, $orderNumber]);
    }
    
    /**
     * 验证银行卡号
     */
    private function validateCardNumber($cardNumber) {
        // 移除空格和连字符
        $cardNumber = preg_replace('/[\s\-]/', '', $cardNumber);
        
        // 检查是否为数字且长度合适
        if (!preg_match('/^\d{13,19}$/', $cardNumber)) {
            return false;
        }
        
        // Luhn算法验证
        $sum = 0;
        $length = strlen($cardNumber);
        
        for ($i = $length - 1; $i >= 0; $i--) {
            $digit = intval($cardNumber[$i]);
            
            if (($length - $i) % 2 === 0) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            
            $sum += $digit;
        }
        
        return $sum % 10 === 0;
    }
    
    /**
     * 生成二维码（模拟）
     */
    private function generateQRCode($paymentId) {
        // 这里应该调用真实的二维码生成服务
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    }
    
    /**
     * 处理支付回调
     */
    public function handleCallback($method, $data) {
        switch ($method) {
            case 'alipay':
                return $this->handleAlipayCallback($data);
            case 'wechat':
                return $this->handleWechatCallback($data);
            case 'paypal':
                return $this->handlePayPalCallback($data);
            default:
                return ['success' => false, 'error' => 'Unknown payment method'];
        }
    }
    
    /**
     * 处理支付宝回调
     */
    private function handleAlipayCallback($data) {
        // 验证签名等安全检查
        $paymentId = $data['out_trade_no'] ?? '';
        $status = $data['trade_status'] ?? '';
        
        if ($status === 'TRADE_SUCCESS') {
            $this->updatePaymentStatus($paymentId, 'completed');
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'Payment not successful'];
    }
    
    /**
     * 处理微信支付回调
     */
    private function handleWechatCallback($data) {
        $paymentId = $data['out_trade_no'] ?? '';
        $resultCode = $data['result_code'] ?? '';
        
        if ($resultCode === 'SUCCESS') {
            $this->updatePaymentStatus($paymentId, 'completed');
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'Payment not successful'];
    }
    
    /**
     * 处理PayPal回调
     */
    private function handlePayPalCallback($data) {
        $paymentId = $data['custom'] ?? '';
        $paymentStatus = $data['payment_status'] ?? '';
        
        if ($paymentStatus === 'Completed') {
            $this->updatePaymentStatus($paymentId, 'completed');
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'Payment not successful'];
    }
    
    /**
     * 获取支付状态
     */
    public function getPaymentStatus($paymentId) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM payment_records WHERE payment_id = ?
        ");
        $stmt->execute([$paymentId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * 模拟支付成功（用于测试）
     */
    public function simulatePaymentSuccess($paymentId) {
        $this->updatePaymentStatus($paymentId, 'completed');
        
        return [
            'success' => true,
            'message' => 'Payment simulated successfully'
        ];
    }
}

// API端点处理
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $gateway = new PaymentGateway();
    
    switch ($action) {
        case 'create_payment':
            $orderNumber = $_POST['order_number'] ?? '';
            $method = $_POST['method'] ?? '';
            $amount = floatval($_POST['amount'] ?? 0);
            $subject = $_POST['subject'] ?? '';
            
            switch ($method) {
                case 'alipay':
                    $result = $gateway->processAlipay($orderNumber, $amount, $subject);
                    break;
                case 'wechat':
                    $result = $gateway->processWechatPay($orderNumber, $amount, $subject);
                    break;
                case 'paypal':
                    $result = $gateway->processPayPal($orderNumber, $amount, $subject);
                    break;
                case 'card':
                    $cardInfo = [
                        'card_number' => $_POST['card_number'] ?? '',
                        'expiry_month' => $_POST['expiry_month'] ?? '',
                        'expiry_year' => $_POST['expiry_year'] ?? '',
                        'cvv' => $_POST['cvv'] ?? ''
                    ];
                    $result = $gateway->processBankCard($orderNumber, $amount, $cardInfo);
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Unsupported payment method'];
            }
            
            echo json_encode($result);
            break;
            
        case 'check_status':
            $paymentId = $_POST['payment_id'] ?? '';
            $result = $gateway->getPaymentStatus($paymentId);
            echo json_encode(['success' => true, 'data' => $result]);
            break;
            
        case 'simulate_success':
            $paymentId = $_POST['payment_id'] ?? '';
            $result = $gateway->simulatePaymentSuccess($paymentId);
            echo json_encode($result);
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
}
?>
