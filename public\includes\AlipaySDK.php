<?php
/**
 * 支付宝SDK类
 * 支持合作伙伴身份(MD5)和开放平台(RSA)两种接入方式
 */

class AlipaySDK {
    private $config;
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    /**
     * 创建支付订单
     */
    public function createPayment($orderData) {
        $signType = $this->config['sign_type'] ?? 'MD5';
        
        if ($signType === 'MD5') {
            return $this->createPartnerPayment($orderData);
        } else {
            return $this->createOpenPlatformPayment($orderData);
        }
    }
    
    /**
     * 合作伙伴身份支付 (MD5签名)
     */
    private function createPartnerPayment($orderData) {
        $params = [
            'service' => 'create_direct_pay_by_user',
            'partner' => $this->config['partner_id'],
            'payment_type' => '1',
            'notify_url' => $this->getNotifyUrl(),
            'return_url' => $this->config['return_url'],
            'seller_email' => $this->config['seller_email'] ?? '',
            'out_trade_no' => $orderData['payment_number'],
            'subject' => $orderData['description'],
            'total_fee' => $orderData['amount'],
            '_input_charset' => 'utf-8'
        ];
        
        // 生成MD5签名
        $params['sign'] = $this->generateMD5Sign($params);
        $params['sign_type'] = 'MD5';
        
        $paymentUrl = $this->config['gateway_url'] . '?' . http_build_query($params);
        
        return [
            'payment_url' => $paymentUrl,
            'expires_at' => date('Y-m-d H:i:s', time() + 900)
        ];
    }
    
    /**
     * 开放平台支付 (RSA签名)
     */
    private function createOpenPlatformPayment($orderData) {
        $params = [
            'app_id' => $this->config['app_id'],
            'method' => 'alipay.trade.page.pay',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'notify_url' => $this->getNotifyUrl(),
            'return_url' => $this->config['return_url'],
            'biz_content' => json_encode([
                'out_trade_no' => $orderData['payment_number'],
                'product_code' => 'FAST_INSTANT_TRADE_PAY',
                'total_amount' => $orderData['amount'],
                'subject' => $orderData['description'],
                'timeout_express' => '15m'
            ])
        ];
        
        // 生成RSA签名
        $params['sign'] = $this->generateRSASign($params);
        
        $paymentUrl = 'https://openapi.alipay.com/gateway.do?' . http_build_query($params);
        
        return [
            'payment_url' => $paymentUrl,
            'expires_at' => date('Y-m-d H:i:s', time() + 900)
        ];
    }
    
    /**
     * 生成MD5签名
     */
    private function generateMD5Sign($params) {
        ksort($params);
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($key != 'sign' && $key != 'sign_type' && $value != '') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&') . $this->config['md5_key'];
        return md5($signStr);
    }
    
    /**
     * 生成RSA签名
     */
    private function generateRSASign($params) {
        ksort($params);
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($key != 'sign' && $value != '') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');
        
        $privateKey = $this->config['rsa_private_key'];
        if (strpos($privateKey, '-----BEGIN') === false) {
            $privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" . chunk_split($privateKey, 64, "\n") . "-----END RSA PRIVATE KEY-----";
        }
        
        $privateKeyResource = openssl_pkey_get_private($privateKey);
        if (!$privateKeyResource) {
            throw new Exception('RSA私钥格式错误');
        }
        
        openssl_sign($signStr, $signature, $privateKeyResource, OPENSSL_ALGO_SHA256);
        return base64_encode($signature);
    }
    
    /**
     * 验证回调签名
     */
    public function verifyNotify($params) {
        $signType = $this->config['sign_type'] ?? 'MD5';
        
        if ($signType === 'MD5') {
            return $this->verifyMD5Sign($params);
        } else {
            return $this->verifyRSASign($params);
        }
    }
    
    /**
     * 验证MD5签名
     */
    private function verifyMD5Sign($params) {
        $sign = $params['sign'];
        unset($params['sign'], $params['sign_type']);
        
        $expectedSign = $this->generateMD5Sign($params);
        return $sign === $expectedSign;
    }
    
    /**
     * 验证RSA签名
     */
    private function verifyRSASign($params) {
        $sign = $params['sign'];
        unset($params['sign'], $params['sign_type']);
        
        ksort($params);
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value != '') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');
        
        $publicKey = $this->config['rsa_public_key'];
        if (strpos($publicKey, '-----BEGIN') === false) {
            $publicKey = "-----BEGIN PUBLIC KEY-----\n" . chunk_split($publicKey, 64, "\n") . "-----END PUBLIC KEY-----";
        }
        
        $publicKeyResource = openssl_pkey_get_public($publicKey);
        if (!$publicKeyResource) {
            throw new Exception('RSA公钥格式错误');
        }
        
        $result = openssl_verify($signStr, base64_decode($sign), $publicKeyResource, OPENSSL_ALGO_SHA256);
        return $result === 1;
    }
    
    /**
     * 获取通知URL
     */
    private function getNotifyUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . $this->config['notify_url'];
    }
    
    /**
     * 查询支付状态
     */
    public function queryPayment($outTradeNo) {
        $signType = $this->config['sign_type'] ?? 'MD5';
        
        if ($signType === 'MD5') {
            return $this->queryPartnerPayment($outTradeNo);
        } else {
            return $this->queryOpenPlatformPayment($outTradeNo);
        }
    }
    
    /**
     * 合作伙伴身份查询
     */
    private function queryPartnerPayment($outTradeNo) {
        $params = [
            'service' => 'single_trade_query',
            'partner' => $this->config['partner_id'],
            'out_trade_no' => $outTradeNo,
            '_input_charset' => 'utf-8'
        ];
        
        $params['sign'] = $this->generateMD5Sign($params);
        $params['sign_type'] = 'MD5';
        
        $url = $this->config['gateway_url'] . '?' . http_build_query($params);
        
        $response = file_get_contents($url);
        // 解析XML响应
        $xml = simplexml_load_string($response);
        
        return [
            'trade_status' => (string)$xml->trade_status,
            'trade_no' => (string)$xml->trade_no,
            'total_fee' => (string)$xml->total_fee
        ];
    }
    
    /**
     * 开放平台查询
     */
    private function queryOpenPlatformPayment($outTradeNo) {
        $params = [
            'app_id' => $this->config['app_id'],
            'method' => 'alipay.trade.query',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'biz_content' => json_encode([
                'out_trade_no' => $outTradeNo
            ])
        ];
        
        $params['sign'] = $this->generateRSASign($params);
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'https://openapi.alipay.com/gateway.do',
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        $result = json_decode($response, true);
        $responseKey = 'alipay_trade_query_response';
        
        if (isset($result[$responseKey])) {
            return $result[$responseKey];
        }
        
        throw new Exception('查询支付状态失败');
    }
}
?>
