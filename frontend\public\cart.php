<?php
/**
 * 域名购物车系统
 */

session_start();
require_once 'includes/database.php';
require_once 'api/domain-functions.php';

// 初始化购物车
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

$action = $_GET['action'] ?? $_POST['action'] ?? 'view';
$message = '';
$error = '';

// 处理购物车操作
switch ($action) {
    case 'add':
        $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
        $years = intval($_GET['years'] ?? $_POST['years'] ?? 1);

        if ($domain) {
            $domain = strtolower(trim($domain));

            // 验证域名格式
            if (preg_match('/^[a-z0-9\-]+\.[a-z]{2,}$/', $domain)) {
                // 获取TLD
                $parts = explode('.', $domain);
                $tld = '.' . implode('.', array_slice($parts, 1));

                // 获取价格信息
                $priceInfo = getDomainPrice($tld);

                if ($priceInfo['available_for_registration']) {
                    // 检查是否已在购物车中
                    $found = false;
                    foreach ($_SESSION['cart'] as &$item) {
                        if ($item['domain'] === $domain) {
                            $item['years'] = $years;
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        $_SESSION['cart'][] = [
                            'domain' => $domain,
                            'tld' => $tld,
                            'years' => $years,
                            'price_per_year' => $priceInfo['register'],
                            'total_price' => $priceInfo['register'] * $years,
                            'added_time' => time()
                        ];
                    }

                    $message = "域名 $domain 已添加到购物车";
                } else {
                    $error = "该TLD暂不支持注册";
                }
            } else {
                $error = "域名格式无效";
            }
        }
        break;

    case 'remove':
        $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
        if ($domain) {
            $_SESSION['cart'] = array_filter($_SESSION['cart'], function($item) use ($domain) {
                return $item['domain'] !== $domain;
            });
            $_SESSION['cart'] = array_values($_SESSION['cart']); // 重新索引
            $message = "域名 $domain 已从购物车移除";
        }
        break;

    case 'update':
        $domain = $_POST['domain'] ?? '';
        $years = intval($_POST['years'] ?? 1);

        if ($domain && $years >= 1 && $years <= 10) {
            foreach ($_SESSION['cart'] as &$item) {
                if ($item['domain'] === $domain) {
                    $item['years'] = $years;
                    $item['total_price'] = $item['price_per_year'] * $years;
                    break;
                }
            }
            $message = "域名 $domain 的注册年限已更新";
        }
        break;

    case 'clear':
        $_SESSION['cart'] = [];
        $message = "购物车已清空";
        break;
}

// 计算购物车总价
$cartTotal = 0;
$cartCount = count($_SESSION['cart']);

foreach ($_SESSION['cart'] as $item) {
    $cartTotal += $item['total_price'];
}

// 页面标题
$pageTitle = "购物车 ($cartCount)";
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - NameSilo域名销售系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .cart-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            background: white;
        }
        .domain-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        .price-display {
            font-size: 1.1em;
            font-weight: bold;
            color: #28a745;
        }
        .cart-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            position: sticky;
            top: 20px;
        }
        .btn-checkout {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-weight: bold;
            padding: 12px 30px;
            border-radius: 25px;
        }
        .btn-checkout:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .empty-cart {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-cart i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-globe"></i> NameSilo域名销售系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">首页</a>
                <a class="nav-link" href="domain-search.php">域名搜索</a>
                <a class="nav-link active" href="cart.php">
                    购物车 <span class="badge bg-warning text-dark"><?= $cartCount ?></span>
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 消息提示 -->
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-shopping-cart"></i> 购物车
                        </h4>
                        <?php if ($cartCount > 0): ?>
                        <a href="?action=clear" class="btn btn-outline-danger btn-sm"
                           onclick="return confirm('确定要清空购物车吗？')">
                            <i class="fas fa-trash"></i> 清空购物车
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if ($cartCount === 0): ?>
                        <div class="empty-cart">
                            <i class="fas fa-shopping-cart"></i>
                            <h3>购物车为空</h3>
                            <p>您还没有添加任何域名到购物车</p>
                            <a href="domain-search.php" class="btn btn-primary">
                                <i class="fas fa-search"></i> 搜索域名
                            </a>
                        </div>
                        <?php else: ?>
                        <?php foreach ($_SESSION['cart'] as $index => $item): ?>
                        <div class="cart-item">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="domain-name"><?= htmlspecialchars($item['domain']) ?></div>
                                    <small class="text-muted">TLD: <?= htmlspecialchars($item['tld']) ?></small>
                                </div>
                                <div class="col-md-3">
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="update">
                                        <input type="hidden" name="domain" value="<?= htmlspecialchars($item['domain']) ?>">
                                        <div class="input-group input-group-sm">
                                            <select name="years" class="form-select" onchange="this.form.submit()">
                                                <?php for ($y = 1; $y <= 10; $y++): ?>
                                                <option value="<?= $y ?>" <?= $item['years'] == $y ? 'selected' : '' ?>>
                                                    <?= $y ?> 年
                                                </option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-3">
                                    <div class="price-display">$<?= number_format($item['total_price'], 2) ?></div>
                                    <small class="text-muted">$<?= number_format($item['price_per_year'], 2) ?>/年</small>
                                </div>
                                <div class="col-md-2 text-end">
                                    <a href="?action=remove&domain=<?= urlencode($item['domain']) ?>"
                                       class="btn btn-outline-danger btn-sm"
                                       onclick="return confirm('确定要移除这个域名吗？')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if ($cartCount > 0): ?>
            <div class="col-lg-4">
                <div class="cart-summary">
                    <h5 class="mb-3">订单摘要</h5>

                    <div class="d-flex justify-content-between mb-2">
                        <span>域名数量:</span>
                        <span><?= $cartCount ?> 个</span>
                    </div>

                    <div class="d-flex justify-content-between mb-3">
                        <span>小计:</span>
                        <span class="price-display">$<?= number_format($cartTotal, 2) ?></span>
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between mb-3">
                        <strong>总计:</strong>
                        <strong class="price-display">$<?= number_format($cartTotal, 2) ?></strong>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="checkout.php" class="btn btn-checkout">
                            <i class="fas fa-credit-card"></i> 立即结算
                        </a>
                        <a href="domain-search.php" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> 继续添加域名
                        </a>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt"></i> 安全支付保障<br>
                            <i class="fas fa-clock"></i> 即时域名激活<br>
                            <i class="fas fa-headset"></i> 24/7 客户支持
                        </small>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

