<?php
/**
 * 支付API接口
 * Payment API Endpoints
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 定义项目根目录（如果未定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(__DIR__)));
}

// 引入配置文件
require_once dirname(__DIR__) . '/config.php';



// 记录日志
function logActivity($action, $resourceType = null, $resourceId = null, $data = null) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("INSERT INTO logs (user_id, action, resource_type, resource_id, ip_address, user_agent, request_data, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['user_id'] ?? null,
            $action,
            $resourceType,
            $resourceId,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $data ? json_encode($data) : null
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主要功能
    }
}

// 获取支付配置
function getPaymentConfig($method) {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT * FROM payment_configs WHERE payment_method = ? AND enabled = 1");
    $stmt->execute([$method]);
    $config = $stmt->fetch();
    
    if ($config) {
        $config['config'] = json_decode($config['config'], true);
        return $config;
    }
    
    return null;
}

// 生成支付单号
function generatePaymentNumber() {
    return 'PAY' . date('YmdHis') . rand(1000, 9999);
}

// 微信支付
function createWechatPayment($orderId, $amount, $description) {
    $config = getPaymentConfig('wechat');
    if (!$config) {
        throw new Exception('微信支付未配置或未启用');
    }
    
    $paymentNumber = generatePaymentNumber();
    
    // 这里应该调用微信支付API
    // 目前返回模拟数据
    $paymentData = [
        'type' => 'qrcode',
        'payment_number' => $paymentNumber,
        'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'payment_url' => 'weixin://wxpay/bizpayurl?pr=' . $paymentNumber,
        'expires_at' => date('Y-m-d H:i:s', time() + 900), // 15分钟过期
        'instructions' => '请使用微信扫描二维码完成支付'
    ];
    
    // 保存支付记录
    $db = getDatabase();
    if ($db !== null) {
        $stmt = $db->prepare("INSERT INTO payments (order_id, payment_method, payment_number, amount, status, created_at) VALUES (?, 'wechat', ?, ?, 'pending', NOW())");
        $stmt->execute([$orderId, $paymentNumber, $amount]);
    }
    
    return $paymentData;
}

// 支付宝支付
function createAlipayPayment($orderId, $amount, $description) {
    $config = getPaymentConfig('alipay');
    if (!$config) {
        throw new Exception('支付宝未配置或未启用');
    }

    $paymentNumber = generatePaymentNumber();

    // 加载支付宝SDK
    require_once __DIR__ . '/../includes/AlipaySDK.php';

    try {
        $alipay = new AlipaySDK($config['config']);
        $orderData = [
            'payment_number' => $paymentNumber,
            'amount' => $amount,
            'description' => $description
        ];

        $paymentData = $alipay->createPayment($orderData);

        // 保存支付记录
        $db = getDatabase();
        if ($db !== null) {
            $stmt = $db->prepare("INSERT INTO payments (order_id, payment_method, payment_number, amount, status, created_at) VALUES (?, 'alipay', ?, ?, 'pending', NOW())");
            $stmt->execute([$orderId, $paymentNumber, $amount]);
        }

        // 返回标准化的支付数据结构
        return [
            'type' => 'redirect',
            'payment_number' => $paymentNumber,
            'payment_url' => $paymentData['payment_url'],
            'expires_at' => $paymentData['expires_at'],
            'instructions' => '请在新窗口完成支付宝支付，支付完成后点击下方按钮确认'
        ];

    } catch (Exception $e) {
        throw new Exception('支付宝支付创建失败: ' . $e->getMessage());
    }
}

// 易支付
function createEpayPayment($orderId, $amount, $description) {
    $config = getPaymentConfig('epay');
    if (!$config) {
        throw new Exception('易支付未配置或未启用');
    }
    
    $paymentNumber = generatePaymentNumber();
    
    // 构建易支付参数
    $params = [
        'pid' => $config['config']['pid'],
        'type' => 'alipay', // 默认支付宝
        'out_trade_no' => $paymentNumber,
        'notify_url' => $_SERVER['HTTP_HOST'] . $config['config']['notify_url'],
        'return_url' => $_SERVER['HTTP_HOST'] . $config['config']['return_url'],
        'name' => $description,
        'money' => $amount,
        'sitename' => '域名销售系统'
    ];
    
    // 生成签名
    ksort($params);
    $signStr = '';
    foreach ($params as $key => $value) {
        if ($key != 'sign' && $value != '') {
            $signStr .= $key . '=' . $value . '&';
        }
    }
    $signStr = rtrim($signStr, '&') . $config['config']['key'];
    $params['sign'] = md5($signStr);
    $params['sign_type'] = 'MD5';
    
    $paymentUrl = $config['config']['api_url'] . 'submit.php?' . http_build_query($params);
    
    $paymentData = [
        'type' => 'redirect',
        'payment_number' => $paymentNumber,
        'payment_url' => $paymentUrl,
        'expires_at' => date('Y-m-d H:i:s', time() + 900), // 15分钟过期
        'instructions' => '请在新窗口完成易支付，支付完成后点击下方按钮确认'
    ];
    
    // 保存支付记录
    $db = getDatabase();
    $stmt = $db->prepare("INSERT INTO payments (order_id, payment_method, payment_number, amount, status, created_at) VALUES (?, 'epay', ?, ?, 'pending', NOW())");
    $stmt->execute([$orderId, $paymentNumber, $amount]);
    
    return $paymentData;
}

// 处理支付通知
function handlePaymentNotify($method, $data) {
    $db = getDatabase();
    
    switch ($method) {
        case 'wechat':
            // 处理微信支付通知
            $paymentNumber = $data['out_trade_no'] ?? '';
            $tradeNo = $data['transaction_id'] ?? '';
            $status = $data['result_code'] === 'SUCCESS' ? 'success' : 'failed';
            break;
            
        case 'alipay':
            // 处理支付宝通知
            $config = getPaymentConfig('alipay');
            if (!$config) {
                throw new Exception('支付宝配置不存在');
            }

            // 验证签名
            require_once __DIR__ . '/../includes/AlipaySDK.php';
            $alipay = new AlipaySDK($config['config']);

            if (!$alipay->verifyNotify($data)) {
                throw new Exception('支付宝通知签名验证失败');
            }

            $paymentNumber = $data['out_trade_no'] ?? '';
            $tradeNo = $data['trade_no'] ?? '';
            $status = in_array($data['trade_status'] ?? '', ['TRADE_SUCCESS', 'TRADE_FINISHED']) ? 'success' : 'failed';
            break;
            
        case 'epay':
            // 处理易支付通知
            $paymentNumber = $data['out_trade_no'] ?? '';
            $tradeNo = $data['trade_no'] ?? '';
            $status = $data['trade_status'] === '1' ? 'success' : 'failed';
            break;
            
        default:
            throw new Exception('不支持的支付方式');
    }
    
    if ($paymentNumber) {
        // 更新支付状态
        $stmt = $db->prepare("UPDATE payments SET status = ?, trade_no = ?, notify_data = ?, paid_at = ?, updated_at = NOW() WHERE payment_number = ?");
        $stmt->execute([
            $status,
            $tradeNo,
            json_encode($data),
            $status === 'success' ? date('Y-m-d H:i:s') : null,
            $paymentNumber
        ]);
        
        // 如果支付成功，更新订单状态
        if ($status === 'success') {
            $stmt = $db->prepare("UPDATE orders o JOIN payments p ON o.id = p.order_id SET o.status = 'completed', o.updated_at = NOW() WHERE p.payment_number = ?");
            $stmt->execute([$paymentNumber]);
        }
        
        // 记录日志
        logActivity('payment_notify', 'payment', null, [
            'method' => $method,
            'payment_number' => $paymentNumber,
            'status' => $status
        ]);
    }
    
    return $status === 'success';
}

// 处理API请求
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? $_GET['path'] ?? '';
$action = $_GET['action'] ?? '';

try {
    if ($action === 'get_methods' && $method === 'GET') {
        // 获取启用的支付方式
        $db = getDatabase();
        $stmt = $db->prepare("SELECT payment_method, name, enabled, sort_order FROM payment_configs ORDER BY sort_order ASC, id ASC");
        $stmt->execute();
        $methods = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => $methods
        ]);

    } elseif ($action === 'test-alipay' && $method === 'POST') {
        // 测试支付宝配置
        $signType = $_POST['sign_type'] ?? 'MD5';

        if ($signType === 'MD5') {
            $partnerId = $_POST['partner_id'] ?? '';
            $md5Key = $_POST['md5_key'] ?? '';

            if (!$partnerId || !$md5Key) {
                throw new Exception('合作伙伴身份和MD5密钥不能为空', 400);
            }

            // 测试MD5签名
            $testParams = [
                'service' => 'create_direct_pay_by_user',
                'partner' => $partnerId,
                'payment_type' => '1',
                'notify_url' => 'http://namesilo-namesilo-sample.com/notify',
                'return_url' => 'http://namesilo-namesilo-sample.com/return',
                'seller_email' => '<EMAIL>',
                'out_trade_no' => 'TEST' . time(),
                'subject' => '测试订单',
                'total_fee' => '0.01',
                '_input_charset' => 'utf-8'
            ];

            // 生成MD5签名
            ksort($testParams);
            $signStr = '';
            foreach ($testParams as $key => $value) {
                if ($key != 'sign' && $value != '') {
                    $signStr .= $key . '=' . $value . '&';
                }
            }
            $signStr = rtrim($signStr, '&') . $md5Key;
            $sign = md5($signStr);

            echo json_encode([
                'success' => true,
                'message' => 'MD5签名测试成功',
                'data' => [
                    'sign_type' => 'MD5',
                    'partner_id' => $partnerId,
                    'test_sign' => $sign
                ]
            ]);

        } else {
            $appId = $_POST['app_id'] ?? '';
            $privateKey = $_POST['private_key'] ?? '';
            $publicKey = $_POST['public_key'] ?? '';

            if (!$appId || !$privateKey || !$publicKey) {
                throw new Exception('应用ID、私钥和公钥不能为空', 400);
            }

            // 测试RSA签名
            try {
                $testData = 'app_id=' . $appId . '&method=alipay.trade.query&charset=utf-8&sign_type=RSA2&timestamp=' . date('Y-m-d H:i:s') . '&version=1.0';

                // 验证私钥格式
                if (strpos($privateKey, '-----BEGIN') === false) {
                    $privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" . chunk_split($privateKey, 64, "\n") . "-----END RSA PRIVATE KEY-----";
                }

                $privateKeyResource = openssl_pkey_get_private($privateKey);
                if (!$privateKeyResource) {
                    throw new Exception('私钥格式错误');
                }

                openssl_sign($testData, $signature, $privateKeyResource, OPENSSL_ALGO_SHA256);
                $sign = base64_encode($signature);

                echo json_encode([
                    'success' => true,
                    'message' => 'RSA签名测试成功',
                    'data' => [
                        'sign_type' => 'RSA2',
                        'app_id' => $appId,
                        'test_sign' => substr($sign, 0, 20) . '...'
                    ]
                ]);

            } catch (Exception $e) {
                throw new Exception('RSA签名测试失败: ' . $e->getMessage());
            }
        }

    } elseif ($path === '/create' && $method === 'POST') {
        // 创建支付
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        
        $orderId = (int)($input['order_id'] ?? 0);
        $paymentMethod = $input['payment_method'] ?? '';
        $amount = floatval($input['amount'] ?? 0);
        $description = $input['description'] ?? '域名注册费用';
        
        if (!$orderId || !$paymentMethod || $amount <= 0) {
            throw new Exception('参数错误', 400);
        }
        
        // 验证订单
        $db = getDatabase();
        if (!$db) {
            throw new Exception('数据库连接失败');
        }

        $stmt = $db->prepare("SELECT * FROM orders WHERE id = ? AND status = 'pending'");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch();

        if (!$order) {
            throw new Exception('订单不存在或状态不正确', 400);
        }
        
        // 创建支付
        switch ($paymentMethod) {
            case 'wechat':
                $paymentData = createWechatPayment($orderId, $amount, $description);
                break;
            case 'alipay':
                $paymentData = createAlipayPayment($orderId, $amount, $description);
                break;
            case 'epay':
                $paymentData = createEpayPayment($orderId, $amount, $description);
                break;
            default:
                throw new Exception('不支持的支付方式', 400);
        }
        
        // 记录日志
        logActivity('payment_created', 'payment', null, [
            'order_id' => $orderId,
            'payment_method' => $paymentMethod,
            'amount' => $amount
        ]);
        
        echo json_encode([
            'success' => true,
            'data' => $paymentData
        ]);
        
    } elseif (strpos($path, '/notify/') === 0 && $method === 'POST') {
        // 支付通知
        $notifyMethod = substr($path, 8); // 去掉 '/notify/'
        
        $notifyData = $_POST;
        if (empty($notifyData)) {
            $notifyData = json_decode(file_get_contents('php://input'), true);
        }
        
        $success = handlePaymentNotify($notifyMethod, $notifyData);
        
        // 返回通知响应
        switch ($notifyMethod) {
            case 'wechat':
                echo $success ? 'SUCCESS' : 'FAIL';
                break;
            case 'alipay':
                echo $success ? 'success' : 'fail';
                break;
            case 'epay':
                echo $success ? 'success' : 'fail';
                break;
        }
        
    } elseif ($path === '/status' && $method === 'GET') {
        // 查询支付状态
        $paymentNumber = $_GET['payment_number'] ?? '';
        
        if (!$paymentNumber) {
            throw new Exception('支付单号不能为空', 400);
        }
        
        $db = getDatabase();
        $stmt = $db->prepare("SELECT * FROM payments WHERE payment_number = ?");
        $stmt->execute([$paymentNumber]);
        $payment = $stmt->fetch();
        
        if (!$payment) {
            throw new Exception('支付记录不存在', 404);
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'payment_number' => $payment['payment_number'],
                'status' => $payment['status'],
                'amount' => $payment['amount'],
                'paid_at' => $payment['paid_at']
            ]
        ]);
        
    } else if ($method === 'GET' && empty($path)) {
        // 默认GET请求，返回支付API信息
        echo json_encode([
            'success' => true,
            'message' => '支付API工作正常',
            'data' => [
                'available_methods' => ['alipay', 'wechat', 'epay'],
                'endpoints' => [
                    'POST /create' => '创建支付',
                    'POST /notify/{method}' => '支付通知',
                    'GET /status/{payment_number}' => '查询支付状态'
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        throw new Exception('API路径不存在', 404);
    }
    
} catch (Exception $e) {
    $code = $e->getCode() ?: 500;
    http_response_code($code);
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => $code
    ]);
    
    // 记录错误日志
    logActivity('payment_api_error', 'api', null, [
        'path' => $path,
        'error' => $e->getMessage(),
        'code' => $code
    ]);
}
?>
