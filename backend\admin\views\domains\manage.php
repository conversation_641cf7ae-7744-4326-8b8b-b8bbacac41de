<?php
// 域名详细管理页面
if (!defined('ADMIN_PATH')) {
    die('Direct access not permitted');
}



// 引入NameSilo API客户端
require_once ROOT_PATH . '/public/api/NameSiloClient.php';

// 获取域名ID
$domainId = $_GET['id'] ?? 0;

if (!$domainId) {
    header('Location: ?page=domains');
    exit;
}

// 获取数据库连接
$db = getDatabase();

// 获取NameSilo客户端
function getNameSiloClient() {
    static $client = null;
    if ($client === null) {
        $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
        $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
        $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

        if (empty($apiKey)) {
            return null; // API未配置
        }

        $client = new NameSiloClient($apiKey, $apiUrl, $sandbox);
    }
    return $client;
}

// 获取域名详细信息
try {
    $stmt = $db->prepare("
        SELECT d.*, u.username, u.email as user_email
        FROM domains d
        LEFT JOIN users u ON d.user_id = u.id
        WHERE d.id = ?
    ");
    $stmt->execute([$domainId]);
    $domain = $stmt->fetch();

    if (!$domain) {
        header('Location: ?page=domains');
        exit;
    }
} catch (Exception $e) {
    $error = "获取域名信息失败：" . $e->getMessage();
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'update_basic':
                $description = $_POST['description'] ?? '';
                $category = $_POST['category'] ?? '';
                $keywords = $_POST['keywords'] ?? '';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $price = floatval($_POST['price'] ?? 0);

                $stmt = $db->prepare("
                    UPDATE domains
                    SET description = ?, category = ?, keywords = ?, featured = ?, price = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$description, $category, $keywords, $featured, $price, $domainId]);
                $success = "域名基本信息更新成功！";
                break;

            case 'update_status':
                $status = $_POST['status'] ?? '';
                $stmt = $db->prepare("UPDATE domains SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$status, $domainId]);
                $success = "域名状态更新成功！";
                break;

            case 'update_owner':
                $userId = $_POST['user_id'] ?? null;
                if (empty($userId)) $userId = null;

                $stmt = $db->prepare("UPDATE domains SET user_id = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$userId, $domainId]);
                $success = "域名归属更新成功！";
                break;

            case 'update_dates':
                $registrationDate = $_POST['registration_date'] ?? null;
                $expiryDate = $_POST['expiry_date'] ?? null;
                $autoRenew = isset($_POST['auto_renew']) ? 1 : 0;

                $stmt = $db->prepare("
                    UPDATE domains
                    SET registration_date = ?, expiry_date = ?, auto_renew = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$registrationDate, $expiryDate, $autoRenew, $domainId]);
                $success = "域名日期信息更新成功！";
                break;

            case 'update_nameservers':
                $nameservers = [];
                for ($i = 1; $i <= 4; $i++) {
                    $ns = trim($_POST["ns{$i}"] ?? '');
                    if (!empty($ns)) {
                        $nameservers[] = $ns;
                    }
                }

                $nameserversJson = !empty($nameservers) ? json_encode($nameservers) : null;
                $stmt = $db->prepare("UPDATE domains SET nameservers = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$nameserversJson, $domainId]);
                $success = "域名服务器更新成功！";
                break;

            case 'sync_from_namesilo':
                $client = getNameSiloClient();
                if (!$client) {
                    $error = "NameSilo API未配置，请先在系统设置中配置API密钥";
                    break;
                }

                try {
                    // 获取NameSilo域名列表
                    $nameSiloDomains = $client->listDomains();
                    $found = false;

                    foreach ($nameSiloDomains as $nsData) {
                        if ($nsData['domain'] === $domain['domain_name']) {
                            $found = true;

                            // 更新域名信息
                            $status = $nsData['status'] === 'Active' ? 'registered' : 'expired';
                            $expiryDate = null;
                            $registrationDate = null;
                            $autoRenew = $nsData['auto_renew'] ? 1 : 0;

                            if (!empty($nsData['expires'])) {
                                $timestamp = strtotime($nsData['expires']);
                                if ($timestamp !== false) {
                                    $expiryDate = date('Y-m-d', $timestamp);
                                }
                            }

                            if (!empty($nsData['created'])) {
                                $timestamp = strtotime($nsData['created']);
                                if ($timestamp !== false) {
                                    $registrationDate = date('Y-m-d', $timestamp);
                                }
                            }

                            $stmt = $db->prepare("
                                UPDATE domains
                                SET status = ?, expiry_date = ?, registration_date = ?, auto_renew = ?, updated_at = NOW()
                                WHERE id = ?
                            ");
                            $stmt->execute([$status, $expiryDate, $registrationDate, $autoRenew, $domainId]);

                            $success = "已从NameSilo同步域名信息！";
                            break;
                        }
                    }

                    if (!$found) {
                        $error = "在NameSilo账户中未找到此域名";
                    }

                } catch (Exception $e) {
                    $error = "同步失败：" . $e->getMessage();
                }
                break;

            case 'check_domain_status':
                $client = getNameSiloClient();
                if (!$client) {
                    $error = "NameSilo API未配置";
                    break;
                }

                try {
                    $result = $client->checkDomainAvailability($domain['domain_name']);
                    if ($result['available']) {
                        $error = "域名显示为可注册状态，可能已过期或未注册";
                    } else {
                        $success = "域名状态正常，已注册";
                    }
                } catch (Exception $e) {
                    $error = "查询失败：" . $e->getMessage();
                }
                break;
        }

        // 重新获取更新后的数据
        $stmt = $db->prepare("
            SELECT d.*, u.username, u.email as user_email
            FROM domains d
            LEFT JOIN users u ON d.user_id = u.id
            WHERE d.id = ?
        ");
        $stmt->execute([$domainId]);
        $domain = $stmt->fetch();

    } catch (Exception $e) {
        $error = "操作失败：" . $e->getMessage();
    }
}

// 获取所有用户列表
$users = $db->query("SELECT id, username, email FROM users ORDER BY username")->fetchAll();

// 解析域名服务器
$nameservers = [];
if (!empty($domain['nameservers'])) {
    $nameservers = json_decode($domain['nameservers'], true) ?: [];
}
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">域名管理</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="?page=dashboard">首页</a></li>
                    <li class="breadcrumb-item"><a href="?page=domains">域名管理</a></li>
                    <li class="breadcrumb-item active"><?= htmlspecialchars($domain['domain_name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="?page=domains" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                返回列表
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- 域名基本信息 -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>
                        域名基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_basic">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">域名</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                    <input type="text" class="form-control" value="<?= htmlspecialchars($domain['domain_name']) ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">TLD</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                    <input type="text" class="form-control" value="<?= htmlspecialchars($domain['tld']) ?>" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">分类</label>
                                <input type="text" class="form-control" name="category" value="<?= htmlspecialchars($domain['category'] ?? '') ?>" placeholder="输入域名分类">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">价格 (USD)</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" name="price" value="<?= $domain['price'] ?>" step="0.01" min="0">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" name="description" rows="3" placeholder="输入域名描述"><?= htmlspecialchars($domain['description'] ?? '') ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">关键词</label>
                            <input type="text" class="form-control" name="keywords" value="<?= htmlspecialchars($domain['keywords'] ?? '') ?>" placeholder="输入关键词，用逗号分隔">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="featured" id="featured" <?= $domain['featured'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="featured">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    推荐域名
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存基本信息
                        </button>
                    </form>
                </div>
            </div>

            <!-- 日期管理 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar me-2"></i>
                        日期管理
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_dates">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">注册日期</label>
                                <input type="date" class="form-control" name="registration_date" value="<?= $domain['registration_date'] ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">到期日期</label>
                                <input type="date" class="form-control" name="expiry_date" value="<?= $domain['expiry_date'] ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="auto_renew" id="auto_renew" <?= $domain['auto_renew'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="auto_renew">
                                    <i class="fas fa-sync me-1"></i>
                                    自动续费
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存日期信息
                        </button>
                    </form>
                </div>
            </div>

            <!-- 域名服务器设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>
                        域名服务器
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_nameservers">

                        <?php for ($i = 1; $i <= 4; $i++): ?>
                        <div class="mb-3">
                            <label class="form-label">域名服务器 <?= $i ?></label>
                            <input type="text" class="form-control" name="ns<?= $i ?>"
                                   value="<?= htmlspecialchars($nameservers[$i-1] ?? '') ?>"
                                   placeholder="ns<?= $i ?>.namesilo-namesilo-sample.com">
                        </div>
                        <?php endfor; ?>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存域名服务器
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="col-lg-4">
            <!-- 域名状态 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        域名状态
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_status">
                        
                        <div class="mb-3">
                            <label class="form-label">当前状态</label>
                            <select class="form-select" name="status">
                                <option value="available" <?= $domain['status'] === 'available' ? 'selected' : '' ?>>可售</option>
                                <option value="registered" <?= $domain['status'] === 'registered' ? 'selected' : '' ?>>已注册</option>
                                <option value="expired" <?= $domain['status'] === 'expired' ? 'selected' : '' ?>>已过期</option>
                                <option value="pending" <?= $domain['status'] === 'pending' ? 'selected' : '' ?>>待处理</option>
                                <option value="transferred" <?= $domain['status'] === 'transferred' ? 'selected' : '' ?>>已转移</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync me-1"></i>
                            更新状态
                        </button>
                    </form>
                </div>
            </div>

            <!-- 域名归属 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        域名归属
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_owner">
                        
                        <div class="mb-3">
                            <label class="form-label">当前归属人</label>
                            <select class="form-select" name="user_id">
                                <option value="">未分配</option>
                                <?php foreach ($users as $user): ?>
                                <option value="<?= $user['id'] ?>" <?= $domain['user_id'] == $user['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($user['username']) ?> (<?= htmlspecialchars($user['email']) ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-user-edit me-1"></i>
                            更新归属
                        </button>
                    </form>
                </div>
            </div>

            <!-- 时间信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        时间信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">注册时间</small>
                        <div><?= $domain['registration_date'] ? date('Y-m-d', strtotime($domain['registration_date'])) : '未注册' ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">到期时间</small>
                        <div><?= $domain['expiry_date'] ? date('Y-m-d', strtotime($domain['expiry_date'])) : '无到期日期' ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">创建时间</small>
                        <div><?= date('Y-m-d H:i', strtotime($domain['created_at'])) ?></div>
                    </div>
                    <div>
                        <small class="text-muted">更新时间</small>
                        <div><?= date('Y-m-d H:i', strtotime($domain['updated_at'])) ?></div>
                    </div>
                </div>
            </div>

            <!-- NameSilo集成 -->
            <?php $client = getNameSiloClient(); ?>
            <?php if ($client): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cloud me-2"></i>
                        NameSilo集成
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <form method="POST" style="margin: 0;">
                            <input type="hidden" name="action" value="sync_from_namesilo">
                            <button type="submit" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-sync me-2"></i>
                                从NameSilo同步信息
                            </button>
                        </form>

                        <form method="POST" style="margin: 0;">
                            <input type="hidden" name="action" value="check_domain_status">
                            <button type="submit" class="btn btn-outline-info btn-sm w-100">
                                <i class="fas fa-search me-2"></i>
                                检查域名状态
                            </button>
                        </form>

                        <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="renewDomainWithNameSilo()">
                            <i class="fas fa-credit-card me-2"></i>
                            通过NameSilo续费
                        </button>

                        <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="manageDNSWithNameSilo()">
                            <i class="fas fa-network-wired me-2"></i>
                            NameSilo DNS管理
                        </button>
                    </div>

                    <!-- 自动续费快速切换 -->
                    <div class="mt-3 p-2 bg-light rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="small">
                                <i class="fas fa-sync me-1"></i>
                                自动续费
                            </span>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="autoRenewCheckbox"
                                       <?= $domain['auto_renew'] ? 'checked' : '' ?>
                                       onchange="toggleAutoRenew()">
                            </div>
                        </div>
                    </div>

                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            这些功能需要域名在NameSilo账户中存在
                        </small>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cloud me-2 text-muted"></i>
                        NameSilo集成
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-3">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <p class="text-muted mb-2">NameSilo API未配置</p>
                        <a href="?page=settings" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog me-1"></i>
                            前往设置
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 快速操作 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="?page=domains&action=renew&id=<?= $domainId ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-sync me-2"></i>
                            续费域名
                        </a>
                        <a href="?page=domains&action=transfer&id=<?= $domainId ?>" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-exchange-alt me-2"></i>
                            转移域名
                        </a>
                        <a href="?page=domains&action=dns&id=<?= $domainId ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-network-wired me-2"></i>
                            DNS设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// NameSilo集成功能
function renewDomainWithNameSilo() {
    const domainName = '<?= htmlspecialchars($domain['domain_name']) ?>';

    if (confirm(`确定要通过NameSilo续费域名 ${domainName} 吗？\n\n注意：这将在NameSilo账户中产生费用。`)) {
        // 显示加载状态
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
        btn.disabled = true;

        // 发送续费请求
        fetch('ajax/namesilo-operations.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'renew_domain',
                domain: domainName,
                domain_id: <?= $domainId ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('域名续费成功！页面将刷新以显示最新信息。');
                window.location.reload();
            } else {
                alert('续费失败：' + data.error);
            }
        })
        .catch(error => {
            alert('操作失败：' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

function manageDNSWithNameSilo() {
    const domainName = '<?= htmlspecialchars($domain['domain_name']) ?>';

    // 打开NameSilo DNS管理页面
    const namesiloUrl = `https://www.namesilo.com/account_domain_manage_dns.php?domain=${encodeURIComponent(domainName)}`;
    window.open(namesiloUrl, '_blank');
}

// 自动续费设置
function toggleAutoRenew() {
    const checkbox = document.getElementById('autoRenewCheckbox');
    const domainName = '<?= htmlspecialchars($domain['domain_name']) ?>';

    fetch('ajax/namesilo-operations.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'toggle_auto_renew',
            domain: domainName,
            domain_id: <?= $domainId ?>,
            enabled: checkbox.checked
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '自动续费设置已更新');
        } else {
            // 恢复复选框状态
            checkbox.checked = !checkbox.checked;
            showAlert('error', '设置失败：' + data.error);
        }
    })
    .catch(error => {
        // 恢复复选框状态
        checkbox.checked = !checkbox.checked;
        showAlert('error', '操作失败：' + error.message);
    });
}

// 显示提示信息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show">
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 在页面顶部插入提示
    const container = document.querySelector('.container-fluid');
    const firstChild = container.firstElementChild;
    const alertDiv = document.createElement('div');
    alertDiv.innerHTML = alertHtml;
    container.insertBefore(alertDiv.firstElementChild, firstChild.nextSibling);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
