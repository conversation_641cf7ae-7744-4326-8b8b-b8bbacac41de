-- 创建支付记录表
-- Create payments table

CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL COMMENT '订单ID',
    payment_method VARCHAR(50) NOT NULL COMMENT '支付方式',
    payment_id VARCHAR(100) COMMENT '支付ID',
    transaction_id VARCHAR(100) COMMENT '交易ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '金额',
    currency VARCHAR(3) DEFAULT 'CNY' COMMENT '货币',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending' COMMENT '支付状态',
    gateway_response JSON COMMENT '网关响应',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    INDEX idx_order_id (order_id),
    INDEX idx_payment_method (payment_method),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';
