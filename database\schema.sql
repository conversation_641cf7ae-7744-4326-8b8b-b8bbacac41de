-- NameSilo域名管理系统 - 统一数据库架构
-- Domain Management System - Unified Database Schema
-- 版本: 2.0
-- 创建时间: 2025-07-22

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `www_bt_cn` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `www_bt_cn`;

-- ============================================================================
-- 用户管理表
-- ============================================================================

-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE COMMENT '用户名',
  `email` varchar(100) NOT NULL UNIQUE COMMENT '邮箱地址',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `role` enum('user','admin','moderator') DEFAULT 'user' COMMENT '用户角色',
  `status` enum('active','inactive','suspended','banned') DEFAULT 'active' COMMENT '账户状态',
  `first_name` varchar(50) DEFAULT NULL COMMENT '名',
  `last_name` varchar(50) DEFAULT NULL COMMENT '姓',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话号码',
  `address` text DEFAULT NULL COMMENT '地址',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `state` varchar(50) DEFAULT NULL COMMENT '省份/州',
  `zip_code` varchar(20) DEFAULT NULL COMMENT '邮政编码',
  `country` varchar(50) DEFAULT 'CN' COMMENT '国家',
  `email_verified` tinyint(1) DEFAULT 0 COMMENT '邮箱是否验证',
  `email_verification_token` varchar(255) DEFAULT NULL COMMENT '邮箱验证令牌',
  `password_reset_token` varchar(255) DEFAULT NULL COMMENT '密码重置令牌',
  `password_reset_expires` datetime DEFAULT NULL COMMENT '密码重置过期时间',
  `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ============================================================================
-- 域名管理表
-- ============================================================================

-- 域名分类表
CREATE TABLE `domain_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `slug` varchar(100) NOT NULL UNIQUE COMMENT '分类标识',
  `description` text DEFAULT NULL COMMENT '分类描述',
  `parent_id` int(11) DEFAULT NULL COMMENT '父分类ID',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  FOREIGN KEY (`parent_id`) REFERENCES `domain_categories`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名分类表';

-- 域名表
CREATE TABLE `domains` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain_name` varchar(255) NOT NULL UNIQUE COMMENT '域名',
  `tld` varchar(20) NOT NULL COMMENT '顶级域名',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `user_id` int(11) DEFAULT NULL COMMENT '所有者用户ID',
  `status` enum('available','registered','expired','pending','transferred','reserved') DEFAULT 'available' COMMENT '域名状态',
  `registrar` varchar(100) DEFAULT 'NameSilo' COMMENT '注册商',
  `registrar_order_id` varchar(100) DEFAULT NULL COMMENT '注册商订单ID',
  `registration_date` date DEFAULT NULL COMMENT '注册日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期',
  `auto_renew` tinyint(1) DEFAULT 0 COMMENT '自动续费',
  `privacy_protection` tinyint(1) DEFAULT 1 COMMENT '隐私保护',
  `nameservers` json DEFAULT NULL COMMENT 'DNS服务器',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `renewal_price` decimal(10,2) DEFAULT NULL COMMENT '续费价格',
  `transfer_price` decimal(10,2) DEFAULT NULL COMMENT '转移价格',
  `currency` varchar(3) DEFAULT 'CNY' COMMENT '货币',
  `featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐',
  `premium` tinyint(1) DEFAULT 0 COMMENT '是否高级域名',
  `description` text DEFAULT NULL COMMENT '描述',
  `keywords` text DEFAULT NULL COMMENT '关键词',
  `views` int(11) DEFAULT 0 COMMENT '浏览次数',
  `traffic_stats` json DEFAULT NULL COMMENT '流量统计',
  `seo_metrics` json DEFAULT NULL COMMENT 'SEO指标',
  `whois_data` json DEFAULT NULL COMMENT 'WHOIS数据',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `domain_name` (`domain_name`),
  KEY `idx_tld` (`tld`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_registrar` (`registrar`),
  KEY `idx_expiry_date` (`expiry_date`),
  KEY `idx_featured` (`featured`),
  KEY `idx_premium` (`premium`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`category_id`) REFERENCES `domain_categories`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名表';

-- DNS记录表
CREATE TABLE `dns_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain_id` int(11) NOT NULL COMMENT '域名ID',
  `type` enum('A','AAAA','CNAME','MX','TXT','NS','SRV','PTR') NOT NULL COMMENT '记录类型',
  `name` varchar(255) NOT NULL COMMENT '记录名称',
  `value` text NOT NULL COMMENT '记录值',
  `ttl` int(11) DEFAULT 3600 COMMENT 'TTL值',
  `priority` int(11) DEFAULT NULL COMMENT '优先级',
  `registrar_record_id` varchar(100) DEFAULT NULL COMMENT '注册商记录ID',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_domain_id` (`domain_id`),
  KEY `idx_type` (`type`),
  KEY `idx_name` (`name`),
  KEY `idx_is_active` (`is_active`),
  FOREIGN KEY (`domain_id`) REFERENCES `domains`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DNS记录表';

-- ============================================================================
-- 订单管理表
-- ============================================================================

-- 订单表
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) NOT NULL UNIQUE COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `status` enum('pending','processing','completed','cancelled','refunded') DEFAULT 'pending' COMMENT '订单状态',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `currency` varchar(3) DEFAULT 'CNY' COMMENT '货币',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `payment_status` enum('pending','paid','failed','refunded','partial') DEFAULT 'pending' COMMENT '支付状态',
  `payment_id` varchar(100) DEFAULT NULL COMMENT '支付ID',
  `payment_data` json DEFAULT NULL COMMENT '支付数据',
  `billing_address` json DEFAULT NULL COMMENT '账单地址',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 订单项表
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `domain_id` int(11) DEFAULT NULL COMMENT '域名ID',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `item_type` enum('registration','renewal','transfer','privacy') DEFAULT 'registration' COMMENT '项目类型',
  `years` int(11) DEFAULT 1 COMMENT '年数',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `quantity` int(11) DEFAULT 1 COMMENT '数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '总价',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_domain_id` (`domain_id`),
  KEY `idx_item_type` (`item_type`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`domain_id`) REFERENCES `domains`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';

-- 支付记录表
CREATE TABLE `payment_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `payment_method` varchar(50) NOT NULL COMMENT '支付方式',
  `payment_gateway` varchar(50) NOT NULL COMMENT '支付网关',
  `transaction_id` varchar(255) DEFAULT NULL COMMENT '交易ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(3) DEFAULT 'CNY' COMMENT '货币',
  `status` enum('pending','success','failed','cancelled','refunded') DEFAULT 'pending' COMMENT '支付状态',
  `gateway_response` json DEFAULT NULL COMMENT '网关响应',
  `processed_at` datetime DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';

-- ============================================================================
-- 系统管理表
-- ============================================================================

-- 系统设置表
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL UNIQUE COMMENT '设置键',
  `value` text DEFAULT NULL COMMENT '设置值',
  `type` enum('string','integer','boolean','json','text') DEFAULT 'string' COMMENT '值类型',
  `group` varchar(50) DEFAULT 'general' COMMENT '设置分组',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `idx_group` (`group`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 系统日志表
CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` enum('debug','info','notice','warning','error','critical','alert','emergency') DEFAULT 'info' COMMENT '日志级别',
  `message` text NOT NULL COMMENT '日志消息',
  `context` json DEFAULT NULL COMMENT '上下文数据',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `method` varchar(10) DEFAULT NULL COMMENT '请求方法',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 会话表
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL COMMENT '会话ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `payload` text NOT NULL COMMENT '会话数据',
  `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';

-- 购物车表
CREATE TABLE `cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `session_id` varchar(255) DEFAULT NULL COMMENT '会话ID',
  `domain_name` varchar(255) NOT NULL COMMENT '域名',
  `item_type` enum('registration','renewal','transfer','privacy') DEFAULT 'registration' COMMENT '项目类型',
  `years` int(11) DEFAULT 1 COMMENT '年数',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- 邮件模板表
CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL UNIQUE COMMENT '模板名称',
  `subject` varchar(255) NOT NULL COMMENT '邮件主题',
  `body` text NOT NULL COMMENT '邮件内容',
  `type` enum('html','text') DEFAULT 'html' COMMENT '内容类型',
  `variables` json DEFAULT NULL COMMENT '可用变量',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件模板表';

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
