<?php
/**
 * 域名注册API
 * Domain Registration API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once '../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDatabase();
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
            
        case 'POST':
            handlePostRequest($db, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误：' . $e->getMessage()]);
}

/**
 * 处理GET请求 - 检查域名可用性
 */
function handleGetRequest($db) {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'check':
            checkDomainAvailability($_GET['domain'] ?? '');
            break;
            
        case 'suggestions':
            getDomainSuggestions($_GET['keyword'] ?? '');
            break;
            
        case 'pricing':
            getDomainPricing();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 处理POST请求 - 注册域名
 */
function handlePostRequest($db, $input) {
    // 检查用户登录
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'register':
            registerDomain($db, $_SESSION['user_id'], $input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 检查域名可用性
 */
function checkDomainAvailability($domain) {
    if (empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名不能为空']);
        return;
    }
    
    // 验证域名格式
    if (!isValidDomain($domain)) {
        echo json_encode([
            'success' => false,
            'message' => '域名格式不正确'
        ]);
        return;
    }
    
    // 模拟检查域名可用性
    $available = checkDomainAvailabilityMock($domain);
    $price = getDomainPrice($domain);
    
    echo json_encode([
        'success' => true,
        'data' => [
            'domain' => $domain,
            'available' => $available,
            'price' => $price,
            'currency' => 'CNY',
            'period' => 1
        ]
    ]);
}

/**
 * 获取域名建议
 */
function getDomainSuggestions($keyword) {
    if (empty($keyword)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '关键词不能为空']);
        return;
    }
    
    $suggestions = generateDomainSuggestions($keyword);
    
    echo json_encode([
        'success' => true,
        'data' => $suggestions
    ]);
}

/**
 * 获取域名价格表
 */
function getDomainPricing() {
    $pricing = [
        '.com' => ['register' => 89, 'renew' => 89, 'transfer' => 89],
        '.net' => ['register' => 99, 'renew' => 99, 'transfer' => 99],
        '.org' => ['register' => 109, 'renew' => 109, 'transfer' => 109],
        '.cn' => ['register' => 29, 'renew' => 29, 'transfer' => 29],
        '.com.cn' => ['register' => 39, 'renew' => 39, 'transfer' => 39],
        '.net.cn' => ['register' => 39, 'renew' => 39, 'transfer' => 39],
        '.org.cn' => ['register' => 39, 'renew' => 39, 'transfer' => 39],
        '.info' => ['register' => 119, 'renew' => 119, 'transfer' => 119],
        '.biz' => ['register' => 129, 'renew' => 129, 'transfer' => 129],
        '.me' => ['register' => 199, 'renew' => 199, 'transfer' => 199]
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $pricing
    ]);
}

/**
 * 注册域名
 */
function registerDomain($db, $userId, $input) {
    $domain = $input['domain'] ?? '';
    $period = intval($input['period'] ?? 1);
    $nameservers = $input['nameservers'] ?? [];
    
    if (empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名不能为空']);
        return;
    }
    
    if ($period < 1 || $period > 10) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '注册年限必须在1-10年之间']);
        return;
    }
    
    // 验证域名格式
    if (!isValidDomain($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名格式不正确']);
        return;
    }
    
    // 检查域名是否可用
    if (!checkDomainAvailabilityMock($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名不可用']);
        return;
    }
    
    // 计算价格
    $price = getDomainPrice($domain) * $period;
    
    // 检查用户余额
    $stmt = $db->prepare("SELECT balance FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user || $user['balance'] < $price) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '余额不足，请先充值']);
        return;
    }
    
    try {
        $db->beginTransaction();
        
        // 创建订单
        $orderNumber = 'DOM' . date('YmdHis') . rand(1000, 9999);
        $stmt = $db->prepare("
            INSERT INTO orders (user_id, order_number, order_type, domain_name, period, total_amount, status, created_at) 
            VALUES (?, ?, 'domain_register', ?, ?, ?, 'completed', NOW())
        ");
        $stmt->execute([$userId, $orderNumber, $domain, $period, $price]);
        $orderId = $db->lastInsertId();
        
        // 扣除余额
        $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
        $stmt->execute([$price, $userId]);
        
        // 添加域名记录（这里应该调用域名注册商API）
        // 目前只是模拟添加到数据库
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => '域名注册成功',
            'data' => [
                'order_id' => $orderId,
                'order_number' => $orderNumber,
                'domain' => $domain,
                'period' => $period,
                'amount' => $price
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '注册失败：' . $e->getMessage()]);
    }
}

/**
 * 验证域名格式
 */
function isValidDomain($domain) {
    return preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/', $domain);
}

/**
 * 模拟检查域名可用性
 */
function checkDomainAvailabilityMock($domain) {
    // 模拟一些域名不可用
    $unavailableDomains = [
        'google.com',
        'facebook.com',
        'twitter.com',
        'github.com',
        'stackoverflow.com'
    ];
    
    return !in_array(strtolower($domain), $unavailableDomains);
}

/**
 * 获取域名价格
 */
function getDomainPrice($domain) {
    $extension = strtolower(substr($domain, strrpos($domain, '.')));
    
    $prices = [
        '.com' => 89,
        '.net' => 99,
        '.org' => 109,
        '.cn' => 29,
        '.com.cn' => 39,
        '.net.cn' => 39,
        '.org.cn' => 39,
        '.info' => 119,
        '.biz' => 129,
        '.me' => 199
    ];
    
    return $prices[$extension] ?? 149; // 默认价格
}

/**
 * 生成域名建议
 */
function generateDomainSuggestions($keyword) {
    $extensions = ['.com', '.net', '.org', '.cn', '.com.cn', '.info', '.biz'];
    $prefixes = ['', 'my', 'get', 'the', 'best', 'top', 'new'];
    $suffixes = ['', 'app', 'web', 'site', 'online', 'pro', 'tech'];
    
    $suggestions = [];
    
    // 基本扩展名建议
    foreach ($extensions as $ext) {
        $domain = $keyword . $ext;
        $suggestions[] = [
            'domain' => $domain,
            'available' => checkDomainAvailabilityMock($domain),
            'price' => getDomainPrice($domain),
            'type' => 'exact'
        ];
    }
    
    // 前缀建议
    foreach ($prefixes as $prefix) {
        if (empty($prefix)) continue;
        foreach (['.com', '.net', '.cn'] as $ext) {
            $domain = $prefix . $keyword . $ext;
            $suggestions[] = [
                'domain' => $domain,
                'available' => checkDomainAvailabilityMock($domain),
                'price' => getDomainPrice($domain),
                'type' => 'prefix'
            ];
        }
    }
    
    // 后缀建议
    foreach ($suffixes as $suffix) {
        if (empty($suffix)) continue;
        foreach (['.com', '.net', '.cn'] as $ext) {
            $domain = $keyword . $suffix . $ext;
            $suggestions[] = [
                'domain' => $domain,
                'available' => checkDomainAvailabilityMock($domain),
                'price' => getDomainPrice($domain),
                'type' => 'suffix'
            ];
        }
    }
    
    // 限制返回数量并排序
    $suggestions = array_slice($suggestions, 0, 20);
    
    // 按可用性和价格排序
    usort($suggestions, function($a, $b) {
        if ($a['available'] !== $b['available']) {
            return $b['available'] - $a['available']; // 可用的排在前面
        }
        return $a['price'] - $b['price']; // 价格低的排在前面
    });
    
    return $suggestions;
}
?>
