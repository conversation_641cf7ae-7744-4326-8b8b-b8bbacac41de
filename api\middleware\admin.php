<?php
/**
 * 管理员权限中间件
 * Admin Authorization Middleware
 */

class AdminMiddleware {
    
    /**
     * 处理管理员权限检查
     */
    public function handle() {
        // 检查会话
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // 检查用户是否已登录
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            $this->forbidden('未登录');
            return;
        }
        
        // 检查用户角色
        if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
            // 从数据库重新验证用户角色
            if (!$this->validateAdminRole($_SESSION['user_id'])) {
                $this->forbidden('权限不足，需要管理员权限');
                return;
            }
        }
        
        // 检查IP白名单（如果配置了）
        if (!$this->checkIpWhitelist()) {
            $this->forbidden('IP地址不在允许范围内');
            return;
        }
        
        // 记录管理员操作日志
        $this->logAdminAction();
    }
    
    /**
     * 验证管理员角色
     */
    private function validateAdminRole($userId) {
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT role, status 
                FROM users 
                WHERE id = ? AND status = 'active'
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && in_array($user['role'], ['admin', 'moderator'])) {
                $_SESSION['role'] = $user['role'];
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('验证管理员角色失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查IP白名单
     */
    private function checkIpWhitelist() {
        // 获取配置的IP白名单
        $whitelist = $this->getIpWhitelist();
        
        if (empty($whitelist)) {
            return true; // 没有配置白名单，允许所有IP
        }
        
        $clientIp = $this->getClientIp();
        
        foreach ($whitelist as $allowedIp) {
            if ($this->ipMatches($clientIp, trim($allowedIp))) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取IP白名单
     */
    private function getIpWhitelist() {
        // 从环境变量获取
        $envWhitelist = $_ENV['ADMIN_IP_WHITELIST'] ?? '';
        if (!empty($envWhitelist)) {
            return explode(',', $envWhitelist);
        }
        
        // 从数据库配置获取
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT value 
                FROM system_settings 
                WHERE `key` = 'admin_ip_whitelist'
            ");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result && !empty($result['value'])) {
                return explode(',', $result['value']);
            }
            
        } catch (Exception $e) {
            error_log('获取IP白名单失败: ' . $e->getMessage());
        }
        
        return [];
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 检查IP是否匹配
     */
    private function ipMatches($ip, $pattern) {
        // 精确匹配
        if ($ip === $pattern) {
            return true;
        }
        
        // CIDR匹配
        if (strpos($pattern, '/') !== false) {
            list($subnet, $mask) = explode('/', $pattern);
            $ipLong = ip2long($ip);
            $subnetLong = ip2long($subnet);
            $maskLong = -1 << (32 - (int)$mask);
            
            return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
        }
        
        // 通配符匹配
        if (strpos($pattern, '*') !== false) {
            $pattern = str_replace('*', '.*', $pattern);
            return preg_match('/^' . $pattern . '$/', $ip);
        }
        
        return false;
    }
    
    /**
     * 记录管理员操作日志
     */
    private function logAdminAction() {
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $userId = $_SESSION['user_id'];
            $action = $_SERVER['REQUEST_METHOD'] . ' ' . $_SERVER['REQUEST_URI'];
            $ip = $this->getClientIp();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // 创建管理员日志表（如果不存在）
            $db->exec("
                CREATE TABLE IF NOT EXISTS admin_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(500) NOT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT,
                    request_data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_id (user_id),
                    INDEX idx_created_at (created_at),
                    INDEX idx_ip_address (ip_address)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // 记录请求数据（敏感信息除外）
            $requestData = [];
            if (!empty($_POST)) {
                $requestData['post'] = $this->filterSensitiveData($_POST);
            }
            if (!empty($_GET)) {
                $requestData['get'] = $this->filterSensitiveData($_GET);
            }
            
            $stmt = $db->prepare("
                INSERT INTO admin_logs (user_id, action, ip_address, user_agent, request_data) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                $action,
                $ip,
                $userAgent,
                json_encode($requestData, JSON_UNESCAPED_UNICODE)
            ]);
            
        } catch (Exception $e) {
            error_log('记录管理员日志失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 过滤敏感数据
     */
    private function filterSensitiveData($data) {
        $sensitiveKeys = ['password', 'token', 'key', 'secret', 'api_key', 'private_key'];
        
        foreach ($sensitiveKeys as $key) {
            if (isset($data[$key])) {
                $data[$key] = '***';
            }
        }
        
        return $data;
    }
    
    /**
     * 返回权限不足错误
     */
    private function forbidden($message = '权限不足') {
        http_response_code(403);
        header('Content-Type: application/json; charset=utf-8');
        
        echo json_encode([
            'success' => false,
            'code' => 403,
            'message' => $message,
            'data' => null,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        
        exit;
    }
    
    /**
     * 检查特定权限
     */
    public static function hasPermission($permission) {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
            return false;
        }
        
        // 超级管理员拥有所有权限
        if ($_SESSION['role'] === 'admin') {
            return true;
        }
        
        // 这里可以实现更细粒度的权限控制
        // 例如基于权限表的检查
        
        return false;
    }
    
    /**
     * 获取管理员统计信息
     */
    public static function getAdminStats() {
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stats = [];
            
            // 用户统计
            $stmt = $db->query("SELECT COUNT(*) as total, status FROM users GROUP BY status");
            $userStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stats['users'] = $userStats;
            
            // 域名统计
            $stmt = $db->query("SELECT COUNT(*) as total, status FROM domains GROUP BY status");
            $domainStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stats['domains'] = $domainStats;
            
            // 订单统计
            $stmt = $db->query("SELECT COUNT(*) as total, status FROM orders GROUP BY status");
            $orderStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stats['orders'] = $orderStats;
            
            // 今日统计
            $stmt = $db->query("
                SELECT 
                    (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as new_users,
                    (SELECT COUNT(*) FROM orders WHERE DATE(created_at) = CURDATE()) as new_orders,
                    (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE DATE(created_at) = CURDATE() AND status = 'completed') as today_revenue
            ");
            $todayStats = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['today'] = $todayStats;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('获取管理员统计失败: ' . $e->getMessage());
            return [];
        }
    }
}
?>
