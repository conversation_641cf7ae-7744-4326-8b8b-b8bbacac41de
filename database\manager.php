<?php
/**
 * 数据库管理工具
 * Database Management Tool
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception('.env file not found');
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

// 数据库管理类
class DatabaseManager {
    private $pdo;
    private $config;
    
    public function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    private function loadConfig() {
        $this->config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'www_bt_cn',
            'username' => $_ENV['DB_USERNAME'] ?? 'www_bt_cn',
            'password' => $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci'
        ];
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']} COLLATE {$this->config['collation']}"
            ]);
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    public function showStatus() {
        echo "数据库连接状态\n";
        echo str_repeat('=', 50) . "\n";
        
        // 基本信息
        $info = $this->pdo->query("SELECT VERSION() as version")->fetch();
        echo "MySQL版本: " . $info['version'] . "\n";
        echo "数据库名: " . $this->config['database'] . "\n";
        echo "字符集: " . $this->config['charset'] . "\n";
        
        // 表信息
        $tables = $this->pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "表数量: " . count($tables) . "\n";
        
        // 数据库大小
        $size = $this->pdo->query("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = '{$this->config['database']}'
        ")->fetch();
        echo "数据库大小: " . ($size['size_mb'] ?? 0) . " MB\n";
        
        echo "\n";
    }
    
    public function showTables() {
        echo "数据表信息\n";
        echo str_repeat('=', 80) . "\n";
        printf("%-25s %-10s %-15s %-15s\n", "表名", "记录数", "数据大小(KB)", "索引大小(KB)");
        echo str_repeat('-', 80) . "\n";
        
        $query = "
            SELECT 
                table_name,
                table_rows,
                ROUND(data_length / 1024, 2) as data_size,
                ROUND(index_length / 1024, 2) as index_size
            FROM information_schema.tables 
            WHERE table_schema = '{$this->config['database']}'
            ORDER BY table_name
        ";
        
        $tables = $this->pdo->query($query)->fetchAll();
        
        foreach ($tables as $table) {
            printf("%-25s %-10s %-15s %-15s\n", 
                $table['table_name'],
                number_format($table['table_rows']),
                $table['data_size'],
                $table['index_size']
            );
        }
        
        echo "\n";
    }
    
    public function showTableStructure($tableName) {
        echo "表结构: {$tableName}\n";
        echo str_repeat('=', 100) . "\n";
        
        // 检查表是否存在
        $exists = $this->pdo->query("SHOW TABLES LIKE '{$tableName}'")->fetch();
        if (!$exists) {
            echo "表 '{$tableName}' 不存在\n";
            return;
        }
        
        // 显示列信息
        printf("%-20s %-15s %-10s %-10s %-10s %-30s\n", 
            "字段名", "类型", "允许NULL", "键", "默认值", "注释");
        echo str_repeat('-', 100) . "\n";
        
        $columns = $this->pdo->query("SHOW FULL COLUMNS FROM {$tableName}")->fetchAll();
        
        foreach ($columns as $column) {
            printf("%-20s %-15s %-10s %-10s %-10s %-30s\n",
                $column['Field'],
                $column['Type'],
                $column['Null'],
                $column['Key'],
                $column['Default'] ?? 'NULL',
                $column['Comment']
            );
        }
        
        // 显示索引信息
        echo "\n索引信息:\n";
        echo str_repeat('-', 60) . "\n";
        
        $indexes = $this->pdo->query("SHOW INDEX FROM {$tableName}")->fetchAll();
        $indexGroups = [];
        
        foreach ($indexes as $index) {
            $indexGroups[$index['Key_name']][] = $index;
        }
        
        foreach ($indexGroups as $keyName => $indexCols) {
            $columns = array_map(function($col) {
                return $col['Column_name'];
            }, $indexCols);
            
            $type = $indexCols[0]['Key_name'] === 'PRIMARY' ? 'PRIMARY' : 
                   ($indexCols[0]['Non_unique'] == 0 ? 'UNIQUE' : 'INDEX');
            
            echo "{$type}: {$keyName} (" . implode(', ', $columns) . ")\n";
        }
        
        echo "\n";
    }
    
    public function optimizeTables() {
        echo "优化数据表\n";
        echo str_repeat('=', 50) . "\n";
        
        $tables = $this->pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            echo "优化表: {$table}... ";
            
            try {
                $result = $this->pdo->query("OPTIMIZE TABLE {$table}")->fetch();
                echo $result['Msg_text'] . "\n";
            } catch (Exception $e) {
                echo "失败: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n";
    }
    
    public function checkTables() {
        echo "检查数据表\n";
        echo str_repeat('=', 50) . "\n";
        
        $tables = $this->pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            echo "检查表: {$table}... ";
            
            try {
                $result = $this->pdo->query("CHECK TABLE {$table}")->fetch();
                echo $result['Msg_text'] . "\n";
            } catch (Exception $e) {
                echo "失败: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n";
    }
    
    public function repairTables() {
        echo "修复数据表\n";
        echo str_repeat('=', 50) . "\n";
        
        $tables = $this->pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            echo "修复表: {$table}... ";
            
            try {
                $result = $this->pdo->query("REPAIR TABLE {$table}")->fetch();
                echo $result['Msg_text'] . "\n";
            } catch (Exception $e) {
                echo "失败: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n";
    }
    
    public function showProcessList() {
        echo "进程列表\n";
        echo str_repeat('=', 100) . "\n";
        printf("%-5s %-15s %-15s %-20s %-10s %-10s %-20s\n", 
            "ID", "用户", "主机", "数据库", "命令", "时间", "状态");
        echo str_repeat('-', 100) . "\n";
        
        $processes = $this->pdo->query("SHOW PROCESSLIST")->fetchAll();
        
        foreach ($processes as $process) {
            printf("%-5s %-15s %-15s %-20s %-10s %-10s %-20s\n",
                $process['Id'],
                $process['User'],
                $process['Host'],
                $process['db'] ?? '',
                $process['Command'],
                $process['Time'],
                substr($process['State'] ?? '', 0, 20)
            );
        }
        
        echo "\n";
    }
    
    public function executeQuery($sql) {
        echo "执行SQL查询\n";
        echo str_repeat('=', 50) . "\n";
        echo "SQL: {$sql}\n\n";
        
        try {
            $startTime = microtime(true);
            
            if (stripos($sql, 'SELECT') === 0) {
                $result = $this->pdo->query($sql)->fetchAll();
                $endTime = microtime(true);
                
                echo "查询结果 (" . count($result) . " 行, " . 
                     round(($endTime - $startTime) * 1000, 2) . " ms):\n";
                echo str_repeat('-', 50) . "\n";
                
                if (!empty($result)) {
                    // 显示列标题
                    $columns = array_keys($result[0]);
                    echo implode("\t", $columns) . "\n";
                    echo str_repeat('-', 50) . "\n";
                    
                    // 显示数据（限制显示前10行）
                    foreach (array_slice($result, 0, 10) as $row) {
                        echo implode("\t", array_map(function($v) {
                            return $v === null ? 'NULL' : (string)$v;
                        }, $row)) . "\n";
                    }
                    
                    if (count($result) > 10) {
                        echo "... 还有 " . (count($result) - 10) . " 行\n";
                    }
                }
            } else {
                $affectedRows = $this->pdo->exec($sql);
                $endTime = microtime(true);
                
                echo "执行成功，影响行数: {$affectedRows}\n";
                echo "执行时间: " . round(($endTime - $startTime) * 1000, 2) . " ms\n";
            }
        } catch (Exception $e) {
            echo "执行失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
}

// 显示使用说明
function showUsage() {
    echo "数据库管理工具\n";
    echo str_repeat('=', 50) . "\n";
    echo "用法: php manager.php [命令] [参数]\n\n";
    echo "命令:\n";
    echo "  status              显示数据库状态\n";
    echo "  tables              显示所有表信息\n";
    echo "  structure <table>   显示表结构\n";
    echo "  optimize            优化所有表\n";
    echo "  check               检查所有表\n";
    echo "  repair              修复所有表\n";
    echo "  processes           显示进程列表\n";
    echo "  query <sql>         执行SQL查询\n";
    echo "\n";
    echo "示例:\n";
    echo "  php manager.php status\n";
    echo "  php manager.php structure users\n";
    echo "  php manager.php query \"SELECT COUNT(*) FROM users\"\n";
}

// 主执行逻辑
try {
    // 加载环境变量
    loadEnv(ROOT_PATH . '/.env');
    
    $manager = new DatabaseManager();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'status':
            $manager->showStatus();
            break;
            
        case 'tables':
            $manager->showTables();
            break;
            
        case 'structure':
            if (!isset($argv[2])) {
                echo "错误: 请指定表名\n";
                showUsage();
                exit(1);
            }
            $manager->showTableStructure($argv[2]);
            break;
            
        case 'optimize':
            $manager->optimizeTables();
            break;
            
        case 'check':
            $manager->checkTables();
            break;
            
        case 'repair':
            $manager->repairTables();
            break;
            
        case 'processes':
            $manager->showProcessList();
            break;
            
        case 'query':
            if (!isset($argv[2])) {
                echo "错误: 请指定SQL查询\n";
                showUsage();
                exit(1);
            }
            $manager->executeQuery($argv[2]);
            break;
            
        case 'help':
        default:
            showUsage();
            break;
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
