-- 创建DNS记录表
-- Create DNS records table

CREATE TABLE IF NOT EXISTS dns_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    domain_id INT NOT NULL COMMENT '域名ID',
    domain_name VARCHAR(255) NOT NULL COMMENT '域名',
    user_id INT NOT NULL COMMENT '用户ID',
    type ENUM('A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'PTR', 'SRV') NOT NULL COMMENT '记录类型',
    name VARCHAR(255) NOT NULL COMMENT '记录名称',
    value TEXT NOT NULL COMMENT '记录值',
    ttl INT DEFAULT 3600 COMMENT 'TTL值',
    priority INT NULL COMMENT '优先级（MX、SRV记录使用）',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active' COMMENT '记录状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_domain_id (domain_id),
    INDEX idx_domain_name (domain_name),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DNS记录表';
