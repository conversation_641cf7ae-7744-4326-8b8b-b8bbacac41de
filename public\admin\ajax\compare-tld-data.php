<?php
/**
 * TLD数据对比API - 对比数据库和API中的TLD差异
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置JSON响应头
header('Content-Type: application/json');

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 引入统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database.php';
}

/**
 * 获取完整的域名价格列表（复制自domain-price-api.php避免输出冲突）
 */
function getCompleteDomainPrices() {
    return [
        // 通用顶级域名 (gTLD)
        'com' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'generic'],
        'net' => ['registration' => 10.99, 'renewal' => 10.99, 'transfer' => 10.99, 'category' => 'generic'],
        'org' => ['registration' => 10.99, 'renewal' => 10.99, 'transfer' => 10.99, 'category' => 'generic'],
        'info' => ['registration' => 2.99, 'renewal' => 15.99, 'transfer' => 15.99, 'category' => 'generic'],
        'biz' => ['registration' => 15.99, 'renewal' => 15.99, 'transfer' => 15.99, 'category' => 'generic'],
        'name' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'generic'],
        'mobi' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'generic'],

        // 国家顶级域名 (ccTLD)
        'us' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'ca' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'uk' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'de' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'fr' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'it' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'es' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'nl' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'be' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'ch' => ['registration' => 12.99, 'renewal' => 12.99, 'transfer' => 12.99, 'category' => 'country'],
        'at' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'au' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'jp' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'country'],
        'cn' => ['registration' => 8.99, 'renewal' => 8.99, 'transfer' => 8.99, 'category' => 'country'],
        'in' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'country'],
        'br' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'country'],
        'mx' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'country'],
        'ru' => ['registration' => 5.99, 'renewal' => 5.99, 'transfer' => 5.99, 'category' => 'country'],

        // 新顶级域名 (New gTLD)
        'co' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'new'],
        'io' => ['registration' => 59.99, 'renewal' => 59.99, 'transfer' => 59.99, 'category' => 'new'],
        'me' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'new'],
        'tv' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'new'],
        'cc' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'new'],
        'ws' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'new'],

        // 技术相关
        'app' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'tech'],
        'dev' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'tech'],
        'tech' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'tech'],
        'online' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'tech'],
        'site' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'tech'],
        'website' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'tech'],

        // 商业相关
        'store' => ['registration' => 59.99, 'renewal' => 59.99, 'transfer' => 59.99, 'category' => 'business'],
        'shop' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'business'],
        'business' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'business'],
        'company' => ['registration' => 9.99, 'renewal' => 9.99, 'transfer' => 9.99, 'category' => 'business'],
        'services' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'business'],
        'solutions' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'business'],
        'consulting' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'business'],
        'management' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'business'],
        'marketing' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'business'],

        // 创意相关
        'design' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'creative'],
        'studio' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'creative'],
        'photography' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'creative'],
        'gallery' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'creative'],
        'art' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'creative'],
        'music' => ['registration' => 149.99, 'renewal' => 149.99, 'transfer' => 149.99, 'category' => 'creative'],
        'video' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'creative'],
        'film' => ['registration' => 89.99, 'renewal' => 89.99, 'transfer' => 89.99, 'category' => 'creative'],
        'photo' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'creative'],

        // 媒体相关
        'blog' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'media'],
        'news' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'media'],
        'media' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'media'],

        // 社交相关
        'club' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'social'],
        'team' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'social'],
        'group' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'social'],
        'community' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'social'],
        'network' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'social'],
        'social' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'social'],

        // 教育相关
        'academy' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'education'],
        'education' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'education'],
        'university' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'education'],
        'college' => ['registration' => 69.99, 'renewal' => 69.99, 'transfer' => 69.99, 'category' => 'education'],
        'school' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'education'],
        'training' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'education'],

        // 健康相关
        'health' => ['registration' => 79.99, 'renewal' => 79.99, 'transfer' => 79.99, 'category' => 'health'],
        'fitness' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'health'],
        'care' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'health'],
        'clinic' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'health'],
        'dental' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'health'],

        // 法律相关
        'lawyer' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'legal'],
        'attorney' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'legal'],
        'legal' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'legal'],
        'law' => ['registration' => 99.99, 'renewal' => 99.99, 'transfer' => 99.99, 'category' => 'legal'],

        // 金融相关
        'finance' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'finance'],
        'financial' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'finance'],
        'money' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'finance'],
        'loan' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'finance'],
        'tax' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'finance'],

        // 餐饮相关
        'restaurant' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        'food' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'food'],
        'kitchen' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        'cafe' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'food'],
        'bar' => ['registration' => 79.99, 'renewal' => 79.99, 'transfer' => 79.99, 'category' => 'food'],
        'wine' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],
        'beer' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'food'],
        'pizza' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'food'],

        // 旅游相关
        'travel' => ['registration' => 119.99, 'renewal' => 119.99, 'transfer' => 119.99, 'category' => 'travel'],
        'vacation' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'travel'],
        'holiday' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'travel'],
        'tours' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'travel'],
        'hotel' => ['registration' => 39.99, 'renewal' => 39.99, 'transfer' => 39.99, 'category' => 'travel'],

        // 运动相关
        'sports' => ['registration' => 34.99, 'renewal' => 34.99, 'transfer' => 34.99, 'category' => 'sports'],
        'football' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'sports'],
        'soccer' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'sports'],
        'golf' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'tennis' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'hockey' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'baseball' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'basketball' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'sports'],
        'racing' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'sports'],
        'run' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'sports'],
        'yoga' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'sports'],
        'dance' => ['registration' => 24.99, 'renewal' => 24.99, 'transfer' => 24.99, 'category' => 'sports'],

        // 时尚相关
        'fashion' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        'style' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        'beauty' => ['registration' => 14.99, 'renewal' => 14.99, 'transfer' => 14.99, 'category' => 'fashion'],
        'clothing' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],
        'shoes' => ['registration' => 49.99, 'renewal' => 49.99, 'transfer' => 49.99, 'category' => 'fashion'],
        'boutique' => ['registration' => 29.99, 'renewal' => 29.99, 'transfer' => 29.99, 'category' => 'fashion'],

        // 游戏相关
        'games' => ['registration' => 22.99, 'renewal' => 22.99, 'transfer' => 22.99, 'category' => 'gaming'],
        'casino' => ['registration' => 149.99, 'renewal' => 149.99, 'transfer' => 149.99, 'category' => 'gaming'],
        'poker' => ['registration' => 59.99, 'renewal' => 59.99, 'transfer' => 59.99, 'category' => 'gaming'],
        'bet' => ['registration' => 19.99, 'renewal' => 19.99, 'transfer' => 19.99, 'category' => 'gaming']
    ];
}

// 数据库连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = getDatabase();
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    return $pdo;
}

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    switch ($action) {
        case 'compare-data':
            // 对比数据库和API中的TLD
            $db = getDatabase();
            
            // 获取数据库中的TLD
            $stmt = $db->query("SELECT tld, registration_price, category FROM domain_prices ORDER BY tld");
            $dbTlds = $stmt->fetchAll();
            $dbTldList = array_column($dbTlds, 'tld');
            
            // 获取API中的TLD
            $apiPrices = getCompleteDomainPrices();
            $apiTldList = array_keys($apiPrices);
            
            // 找出差异
            $onlyInDb = array_diff($dbTldList, $apiTldList);
            $onlyInApi = array_diff($apiTldList, $dbTldList);
            $common = array_intersect($dbTldList, $apiTldList);
            
            // 获取只在数据库中的TLD详细信息
            $onlyInDbDetails = [];
            foreach ($dbTlds as $tld) {
                if (in_array($tld['tld'], $onlyInDb)) {
                    $onlyInDbDetails[] = $tld;
                }
            }
            
            // 获取只在API中的TLD详细信息
            $onlyInApiDetails = [];
            foreach ($onlyInApi as $tld) {
                $onlyInApiDetails[] = [
                    'tld' => $tld,
                    'registration_price' => $apiPrices[$tld]['registration'],
                    'category' => $apiPrices[$tld]['category']
                ];
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'TLD数据对比完成',
                'summary' => [
                    'db_total' => count($dbTldList),
                    'api_total' => count($apiTldList),
                    'common' => count($common),
                    'only_in_db' => count($onlyInDb),
                    'only_in_api' => count($onlyInApi)
                ],
                'details' => [
                    'only_in_db' => $onlyInDbDetails,
                    'only_in_api' => $onlyInApiDetails,
                    'common_tlds' => $common
                ]
            ]);
            break;
            
        case 'sync-missing':
            // 同步缺失的TLD（只在API中有的）
            $db = getDatabase();
            
            // 获取数据库中的TLD
            $stmt = $db->query("SELECT tld FROM domain_prices");
            $dbTlds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // 获取API中的TLD
            $apiPrices = getCompleteDomainPrices();
            $apiTlds = array_keys($apiPrices);
            
            // 找出只在API中有的TLD
            $missingTlds = array_diff($apiTlds, $dbTlds);
            
            $added = 0;
            foreach ($missingTlds as $tld) {
                $price = $apiPrices[$tld];
                
                $stmt = $db->prepare("
                    INSERT INTO domain_prices (
                        tld, registration_price, renewal_price, transfer_price,
                        category, enabled, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
                ");
                $stmt->execute([
                    $tld,
                    $price['registration'],
                    $price['renewal'],
                    $price['transfer'],
                    $price['category']
                ]);
                $added++;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '缺失TLD同步完成',
                'added' => $added,
                'missing_tlds' => $missingTlds
            ]);
            break;
            
        case 'remove-extra':
            // 移除多余的TLD（只在数据库中有的）
            $db = getDatabase();
            
            // 获取数据库中的TLD
            $stmt = $db->query("SELECT tld FROM domain_prices");
            $dbTlds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // 获取API中的TLD
            $apiPrices = getCompleteDomainPrices();
            $apiTlds = array_keys($apiPrices);
            
            // 找出只在数据库中有的TLD
            $extraTlds = array_diff($dbTlds, $apiTlds);
            
            $removed = 0;
            foreach ($extraTlds as $tld) {
                $stmt = $db->prepare("DELETE FROM domain_prices WHERE tld = ?");
                $stmt->execute([$tld]);
                $removed++;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '多余TLD移除完成',
                'removed' => $removed,
                'extra_tlds' => $extraTlds
            ]);
            break;
            
        case 'full-sync':
            // 完全同步（清空数据库，重新导入API数据）
            $db = getDatabase();
            
            // 清空现有数据
            $stmt = $db->prepare("DELETE FROM domain_prices");
            $stmt->execute();
            $cleared = $stmt->rowCount();

            // 导入API数据
            $apiPrices = getCompleteDomainPrices();
            $added = 0;

            foreach ($apiPrices as $tld => $price) {
                $stmt = $db->prepare("
                    INSERT INTO domain_prices (
                        tld, registration_price, renewal_price, transfer_price,
                        category, enabled, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
                ");
                $stmt->execute([
                    $tld,
                    $price['registration'],
                    $price['renewal'],
                    $price['transfer'],
                    $price['category']
                ]);
                $added++;
            }
            
            echo json_encode([
                'success' => true,
                'message' => '完全同步完成',
                'cleared' => $cleared,
                'added' => $added,
                'total' => count($apiPrices)
            ]);
            break;
            
        case 'backup-extra':
            // 备份多余的TLD数据
            $db = getDatabase();
            
            // 获取数据库中的TLD
            $stmt = $db->query("SELECT * FROM domain_prices");
            $dbTlds = $stmt->fetchAll();
            
            // 获取API中的TLD
            $apiPrices = getCompleteDomainPrices();
            $apiTlds = array_keys($apiPrices);
            
            // 找出只在数据库中有的TLD
            $extraTlds = [];
            foreach ($dbTlds as $tld) {
                if (!in_array($tld['tld'], $apiTlds)) {
                    $extraTlds[] = $tld;
                }
            }
            
            // 保存备份文件
            $backupFile = ROOT_PATH . '/backup/extra_tlds_' . date('Y-m-d_H-i-s') . '.json';
            $backupDir = dirname($backupFile);
            
            if (!is_dir($backupDir)) {
                mkdir($backupDir, 0755, true);
            }
            
            file_put_contents($backupFile, json_encode($extraTlds, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            echo json_encode([
                'success' => true,
                'message' => '多余TLD备份完成',
                'backup_file' => $backupFile,
                'extra_count' => count($extraTlds),
                'extra_tlds' => array_column($extraTlds, 'tld')
            ]);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
