<?php
/**
 * 域名列表API
 * Domain List API
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 启动会话
session_start();

// 直接使用硬编码数据库连接
function getDatabase() {
    static $pdo = null;

    if ($pdo === null) {
        try {
            $dsn = "mysql:host=localhost;port=3306;dbname=www_bt_cn;charset=utf8mb4";
            $pdo = new PDO($dsn, 'www_bt_cn', 'YAfxfrB8nr6F84LP', [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }

    return $pdo;
}

// API响应函数
function sendResponse($success, $code, $message, $data = null) {
    http_response_code($code);

    $response = [
        'success' => $success,
        'code' => $code,
        'message' => $message,
        'timestamp' => date('c')
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = '操作成功', $code = 200) {
    sendResponse(true, $code, $message, $data);
}

function sendError($message = '操作失败', $code = 400, $data = null) {
    sendResponse(false, $code, $message, $data);
}

// 分页查询函数
function paginate($db, $sql, $params = [], $page = 1, $perPage = 20) {
    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as count_table";
    $stmt = $db->prepare($countSql);
    $stmt->execute($params);
    $totalResult = $stmt->fetch();
    $total = $totalResult['total'];

    // 计算偏移量
    $offset = ($page - 1) * $perPage;

    // 添加LIMIT子句
    $paginatedSql = $sql . " LIMIT {$offset}, {$perPage}";
    $stmt = $db->prepare($paginatedSql);
    $stmt->execute($params);
    $data = $stmt->fetchAll();

    return [
        'data' => $data,
        'total' => (int)$total,
        'page' => (int)$page,
        'per_page' => (int)$perPage,
        'total_pages' => ceil($total / $perPage)
    ];
}

try {
    // 获取当前用户ID（不需要验证，直接访问）
    $userId = $_SESSION['user_id'] ?? 1; // 默认使用用户ID 1
    
    // 获取请求参数
    $page = max(1, intval($_GET['page'] ?? 1));
    $perPage = min(100, max(10, intval($_GET['per_page'] ?? 20)));
    $search = trim($_GET['search'] ?? '');
    $status = $_GET['status'] ?? '';
    $sortBy = $_GET['sort_by'] ?? 'created_at';
    $sortOrder = strtoupper($_GET['sort_order'] ?? 'DESC');
    
    // 验证排序参数
    $allowedSortFields = ['domain_name', 'status', 'expiry_date', 'created_at', 'updated_at'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'created_at';
    }
    
    if (!in_array($sortOrder, ['ASC', 'DESC'])) {
        $sortOrder = 'DESC';
    }
    
    // 构建查询条件
    $whereConditions = ['user_id = :user_id'];
    $params = [':user_id' => $userId];
    
    // 搜索条件
    if (!empty($search)) {
        $whereConditions[] = 'domain_name LIKE :search';
        $params[':search'] = '%' . $search . '%';
    }
    
    // 状态筛选
    if (!empty($status)) {
        $whereConditions[] = 'status = :status';
        $params[':status'] = $status;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 构建查询SQL
    $sql = "
        SELECT 
            d.id,
            d.domain_name,
            d.tld,
            d.status,
            d.registrar,
            d.registration_date,
            d.expiry_date,
            d.auto_renew,
            d.privacy_protection,
            d.price,
            d.renewal_price,
            d.currency,
            d.created_at,
            d.updated_at,
            dc.name as category_name,
            CASE 
                WHEN d.expiry_date <= CURDATE() THEN 'expired'
                WHEN d.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'expiring_soon'
                ELSE 'normal'
            END as expiry_status,
            DATEDIFF(d.expiry_date, CURDATE()) as days_until_expiry
        FROM domains d
        LEFT JOIN domain_categories dc ON d.category_id = dc.id
        WHERE {$whereClause}
        ORDER BY d.{$sortBy} {$sortOrder}
    ";
    
    // 获取数据库连接
    $db = getDatabase();

    // 执行分页查询
    $result = paginate($db, $sql, $params, $page, $perPage);
    
    // 处理数据格式
    $domains = array_map(function($domain) {
        // 格式化日期
        if ($domain['registration_date']) {
            $domain['registration_date'] = date('Y-m-d', strtotime($domain['registration_date']));
        }
        if ($domain['expiry_date']) {
            $domain['expiry_date'] = date('Y-m-d', strtotime($domain['expiry_date']));
        }

        // 格式化时间戳
        $domain['created_at'] = date('Y-m-d H:i:s', strtotime($domain['created_at']));
        $domain['updated_at'] = date('Y-m-d H:i:s', strtotime($domain['updated_at']));

        // 转换布尔值
        $domain['auto_renew'] = (bool)$domain['auto_renew'];
        $domain['privacy_protection'] = (bool)$domain['privacy_protection'];

        // 格式化价格
        if ($domain['price']) {
            $domain['price'] = number_format($domain['price'], 2);
        }
        if ($domain['renewal_price']) {
            $domain['renewal_price'] = number_format($domain['renewal_price'], 2);
        }

        // 添加状态标签
        $domain['status_label'] = getStatusLabel($domain['status']);
        $domain['expiry_status_label'] = getExpiryStatusLabel($domain['expiry_status']);

        return $domain;
    }, $result['data']);

    // 获取统计信息
    $stats = getDomainStats($userId, $db);

    // 返回分页响应
    $response = [
        'items' => $domains,
        'pagination' => [
            'current_page' => $result['page'],
            'per_page' => $result['per_page'],
            'total' => $result['total'],
            'total_pages' => $result['total_pages'],
            'has_next' => $result['page'] < $result['total_pages'],
            'has_prev' => $result['page'] > 1
        ],
        'stats' => $stats
    ];

    // 设置分页头
    header('X-Total-Count: ' . $result['total']);
    header('X-Page-Count: ' . $result['total_pages']);
    header('X-Current-Page: ' . $result['page']);
    header('X-Per-Page: ' . $result['per_page']);

    sendSuccess($response, '域名列表获取成功');

} catch (Exception $e) {
    error_log('域名列表API错误: ' . $e->getMessage());
    sendError('获取域名列表失败: ' . $e->getMessage(), 500);
}

/**
 * 获取状态标签
 */
function getStatusLabel($status) {
    $labels = [
        'available' => '可用',
        'registered' => '已注册',
        'expired' => '已过期',
        'pending' => '处理中',
        'transferred' => '已转移',
        'reserved' => '保留'
    ];
    
    return $labels[$status] ?? $status;
}

/**
 * 获取过期状态标签
 */
function getExpiryStatusLabel($status) {
    $labels = [
        'normal' => '正常',
        'expiring_soon' => '即将过期',
        'expired' => '已过期'
    ];
    
    return $labels[$status] ?? $status;
}

/**
 * 获取域名统计信息
 */
function getDomainStats($userId, $db) {
    $stats = [];

    try {
        // 总域名数
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM domains WHERE user_id = ?");
        $stmt->execute([$userId]);
        $total = $stmt->fetch();
        $stats['total'] = (int)$total['count'];

        // 按状态统计
        $stmt = $db->prepare("
            SELECT status, COUNT(*) as count
            FROM domains
            WHERE user_id = ?
            GROUP BY status
        ");
        $stmt->execute([$userId]);
        $statusStats = $stmt->fetchAll();

        $stats['by_status'] = [];
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = (int)$stat['count'];
        }

        // 即将过期的域名数
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM domains
            WHERE user_id = ?
            AND expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ");
        $stmt->execute([$userId]);
        $expiring = $stmt->fetch();
        $stats['expiring_soon'] = (int)$expiring['count'];

        // 已过期的域名数
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM domains
            WHERE user_id = ?
            AND expiry_date < CURDATE()
        ");
        $stmt->execute([$userId]);
        $expired = $stmt->fetch();
        $stats['expired'] = (int)$expired['count'];

    } catch (Exception $e) {
        error_log('获取域名统计失败: ' . $e->getMessage());
        $stats = [
            'total' => 0,
            'by_status' => [],
            'expiring_soon' => 0,
            'expired' => 0
        ];
    }

    return $stats;
}
?>
