<?php
/**
 * CORS中间件
 * Cross-Origin Resource Sharing Middleware
 */

class CorsMiddleware {
    
    /**
     * 处理CORS
     */
    public function handle() {
        $this->setCorsHeaders();
        
        // 处理预检请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            $this->handlePreflightRequest();
        }
    }
    
    /**
     * 设置CORS头
     */
    private function setCorsHeaders() {
        $allowedOrigins = $this->getAllowedOrigins();
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        // 检查来源是否被允许
        if ($this->isOriginAllowed($origin, $allowedOrigins)) {
            header('Access-Control-Allow-Origin: ' . $origin);
        } else {
            // 如果没有配置特定来源，允许所有来源（开发环境）
            if (empty($allowedOrigins) || $this->isDevelopmentMode()) {
                header('Access-Control-Allow-Origin: *');
            }
        }
        
        // 设置其他CORS头
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-CSRF-Token, Accept, Origin, User-Agent');
        header('Access-Control-Expose-Headers: Content-Length, X-JSON');
        header('Access-Control-Max-Age: 86400'); // 24小时
        
        // 设置内容类型
        if (!headers_sent()) {
            header('Content-Type: application/json; charset=utf-8');
        }
    }
    
    /**
     * 获取允许的来源
     */
    private function getAllowedOrigins() {
        // 从环境变量获取
        $envOrigins = $_ENV['CORS_ALLOWED_ORIGINS'] ?? '';
        if (!empty($envOrigins)) {
            return explode(',', $envOrigins);
        }
        
        // 从数据库配置获取
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT value 
                FROM system_settings 
                WHERE `key` = 'cors_allowed_origins'
            ");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result && !empty($result['value'])) {
                return explode(',', $result['value']);
            }
            
        } catch (Exception $e) {
            error_log('获取CORS配置失败: ' . $e->getMessage());
        }
        
        // 默认允许的来源
        return [
            'http://localhost',
            'http://127.0.0.1',
            'http://localhost:3000',
            'http://localhost:8080',
            'http://127.0.0.1:777'
        ];
    }
    
    /**
     * 检查来源是否被允许
     */
    private function isOriginAllowed($origin, $allowedOrigins) {
        if (empty($origin)) {
            return false;
        }
        
        foreach ($allowedOrigins as $allowedOrigin) {
            $allowedOrigin = trim($allowedOrigin);
            
            // 精确匹配
            if ($origin === $allowedOrigin) {
                return true;
            }
            
            // 通配符匹配
            if (strpos($allowedOrigin, '*') !== false) {
                $pattern = str_replace('*', '.*', preg_quote($allowedOrigin, '/'));
                if (preg_match('/^' . $pattern . '$/', $origin)) {
                    return true;
                }
            }
            
            // 子域名匹配
            if (strpos($allowedOrigin, '.') === 0) {
                $domain = substr($allowedOrigin, 1);
                if (strpos($origin, $domain) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 处理预检请求
     */
    private function handlePreflightRequest() {
        // 验证请求的方法
        $requestMethod = $_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'] ?? '';
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'];
        
        if (!in_array($requestMethod, $allowedMethods)) {
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'code' => 405,
                'message' => '不支持的请求方法',
                'data' => null,
                'timestamp' => date('c')
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        
        // 验证请求的头部
        $requestHeaders = $_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'] ?? '';
        if (!empty($requestHeaders)) {
            $allowedHeaders = [
                'content-type',
                'authorization',
                'x-requested-with',
                'x-csrf-token',
                'accept',
                'origin',
                'user-agent'
            ];
            
            $requestedHeaders = array_map('trim', explode(',', strtolower($requestHeaders)));
            foreach ($requestedHeaders as $header) {
                if (!in_array($header, $allowedHeaders)) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'code' => 400,
                        'message' => '不支持的请求头: ' . $header,
                        'data' => null,
                        'timestamp' => date('c')
                    ], JSON_UNESCAPED_UNICODE);
                    exit;
                }
            }
        }
        
        // 返回成功响应
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'code' => 200,
            'message' => 'Preflight OK',
            'data' => null,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 检查是否为开发模式
     */
    private function isDevelopmentMode() {
        $env = $_ENV['APP_ENV'] ?? 'production';
        return in_array($env, ['development', 'dev', 'local']);
    }
    
    /**
     * 记录CORS日志
     */
    private function logCorsRequest() {
        if (!$this->shouldLogCors()) {
            return;
        }
        
        try {
            $logData = [
                'origin' => $_SERVER['HTTP_ORIGIN'] ?? '',
                'method' => $_SERVER['REQUEST_METHOD'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'ip' => $this->getClientIp(),
                'timestamp' => date('c')
            ];
            
            error_log('CORS请求: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
            
        } catch (Exception $e) {
            error_log('记录CORS日志失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 是否应该记录CORS日志
     */
    private function shouldLogCors() {
        return filter_var($_ENV['CORS_LOG_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN);
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 设置安全头
     */
    public static function setSecurityHeaders() {
        // 防止点击劫持
        header('X-Frame-Options: DENY');
        
        // 防止MIME类型嗅探
        header('X-Content-Type-Options: nosniff');
        
        // XSS保护
        header('X-XSS-Protection: 1; mode=block');
        
        // 引用者策略
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // 内容安全策略
        $csp = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'";
        header('Content-Security-Policy: ' . $csp);
        
        // HSTS（仅在HTTPS下）
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
    }
    
    /**
     * 验证请求来源
     */
    public static function validateOrigin() {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        
        // 如果没有来源信息，可能是直接访问
        if (empty($origin) && empty($referer)) {
            return true;
        }
        
        $instance = new self();
        $allowedOrigins = $instance->getAllowedOrigins();
        
        // 检查Origin头
        if (!empty($origin) && !$instance->isOriginAllowed($origin, $allowedOrigins)) {
            return false;
        }
        
        // 检查Referer头
        if (!empty($referer)) {
            $refererOrigin = parse_url($referer, PHP_URL_SCHEME) . '://' . parse_url($referer, PHP_URL_HOST);
            if (parse_url($referer, PHP_URL_PORT)) {
                $refererOrigin .= ':' . parse_url($referer, PHP_URL_PORT);
            }
            
            if (!$instance->isOriginAllowed($refererOrigin, $allowedOrigins)) {
                return false;
            }
        }
        
        return true;
    }
}
?>
