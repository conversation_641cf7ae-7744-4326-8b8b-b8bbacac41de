# DNS管理API集成文档

本文档描述了如何集成各种域名商的DNS管理API，以实现自动化DNS记录管理。

## 目录

1. [概述](#概述)
2. [API配置](#api配置)
3. [NameSilo API集成](#namesilo-api集成)
4. [GoDaddy API集成](#godaddy-api集成)
5. [Cloudflare API集成](#cloudflare-api集成)
6. [API错误处理](#api错误处理)
7. [测试与调试](#测试与调试)

## 概述

DNS管理API允许用户通过程序化方式管理其域名的DNS记录，包括：

- 获取DNS记录列表
- 添加新的DNS记录
- 更新现有DNS记录
- 删除DNS记录
- 获取和更新域名服务器

本系统已集成多家主流域名商的API，包括NameSilo、GoDaddy和Cloudflare等。

## API配置

### 环境变量配置

所有API密钥都应存储在`.env`文件中，不要直接硬编码在代码中。

1. 复制`.env.example`文件为`.env`
2. 填写相应的API密钥

```
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 配置文件

API相关常量定义在`public/config.php`文件中：

```php
// NameSilo API配置
define('NAMESILO_API_KEY', $_ENV['NAMESILO_API_KEY'] ?? 'your_namesilo_api_key_here');
define('NAMESILO_API_URL', 'https://www.namesilo.com/api/');

// 其他域名商API配置
define('GODADDY_API_KEY', $_ENV['GODADDY_API_KEY'] ?? '');
define('GODADDY_API_SECRET', $_ENV['GODADDY_API_SECRET'] ?? '');
```

## NameSilo API集成

### API密钥获取

1. 登录NameSilo账户
2. 访问 https://www.namesilo.com/account/api-manager
3. 生成新的API密钥或使用现有密钥

### 支持的API操作

| 操作 | API端点 | 说明 |
|-----|---------|-----|
| 获取DNS记录 | dnsListRecords | 获取域名的所有DNS记录 |
| 添加DNS记录 | dnsAddRecord | 添加新的DNS记录 |
| 更新DNS记录 | dnsUpdateRecord | 更新现有DNS记录 |
| 删除DNS记录 | dnsDeleteRecord | 删除DNS记录 |
| 获取域名服务器 | getDomainInfo | 获取域名的NS服务器信息 |

### 示例代码

```php
// 获取DNS记录
function getNamesiloDNSRecords($domain) {
    $apiKey = getNamesiloApiKey();
    $url = "https://www.namesilo.com/api/dnsListRecords?version=1&type=xml&key={$apiKey}&domain={$domain}";
    
    $response = makeApiRequest($url);
    // 处理响应...
}
```

## GoDaddy API集成

### API密钥获取

1. 登录GoDaddy开发者账户
2. 访问 https://developer.godaddy.com/keys
3. 创建生产或测试API密钥

### 支持的API操作

| 操作 | API端点 | 说明 |
|-----|---------|-----|
| 获取DNS记录 | GET /v1/domains/{domain}/records | 获取域名的所有DNS记录 |
| 添加DNS记录 | PATCH /v1/domains/{domain}/records | 添加新的DNS记录 |
| 更新DNS记录 | PUT /v1/domains/{domain}/records/{type}/{name} | 更新现有DNS记录 |
| 删除DNS记录 | DELETE /v1/domains/{domain}/records/{type}/{name} | 删除DNS记录 |

### 示例代码

```php
// 获取DNS记录
function getGoDaddyDNSRecords($domain) {
    $apiKey = getGoDaddyApiKey();
    $apiSecret = getGoDaddyApiSecret();
    $url = "https://api.godaddy.com/v1/domains/{$domain}/records";
    
    $headers = [
        "Authorization: sso-key {$apiKey}:{$apiSecret}",
        "Content-Type: application/json"
    ];
    
    // 发送请求并处理响应...
}
```

## Cloudflare API集成

### API密钥获取

1. 登录Cloudflare账户
2. 访问 https://dash.cloudflare.com/profile/api-tokens
3. 创建API令牌，确保有DNS编辑权限

### 支持的API操作

| 操作 | API端点 | 说明 |
|-----|---------|-----|
| 获取DNS记录 | GET zones/{zone_id}/dns_records | 获取域名的所有DNS记录 |
| 添加DNS记录 | POST zones/{zone_id}/dns_records | 添加新的DNS记录 |
| 更新DNS记录 | PUT zones/{zone_id}/dns_records/{id} | 更新现有DNS记录 |
| 删除DNS记录 | DELETE zones/{zone_id}/dns_records/{id} | 删除DNS记录 |

## API错误处理

所有API调用都应包含适当的错误处理：

1. 连接错误处理
2. API响应错误处理
3. 数据验证错误处理

示例：

```php
try {
    $result = callDomainAPI($domain);
    if ($result['success']) {
        // 处理成功响应
    } else {
        // 处理API错误
        logError("API错误: " . $result['error']);
    }
} catch (Exception $e) {
    // 处理异常
    logError("异常: " . $e->getMessage());
}
```

## 测试与调试

### 测试页面

系统提供了测试页面用于验证API集成：

- `/test_dns_api.php` - 测试DNS API功能
- `/user/dns.php?domain=example.com` - 测试用户DNS管理界面

### 调试技巧

1. 检查API响应原始数据
2. 验证API密钥是否正确
3. 确认域名是否在账户中
4. 检查DNS记录格式是否正确

### 常见问题

1. **API密钥无效** - 确保API密钥正确且未过期
2. **权限不足** - 确保API密钥有足够的权限
3. **请求频率限制** - 某些API有请求频率限制，需要适当延迟
4. **DNS记录格式错误** - 确保DNS记录格式符合域名商要求
