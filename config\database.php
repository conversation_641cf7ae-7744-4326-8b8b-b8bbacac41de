<?php
/**
 * 数据库配置文件
 * Database Configuration
 */

return [
    // 默认数据库连接
    'default' => $_ENV['DB_CONNECTION'] ?? 'mysql',

    // 数据库连接配置
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => 'localhost',
            'port' => '3306',
            'database' => 'www_bt_cn',
            'username' => 'www_bt_cn',
            'password' => 'YAfxfrB8nr6F84LP',
            'unix_socket' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]) : [],
        ],
        
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => ROOT_PATH . '/storage/database.sqlite',
            'prefix' => '',
            'foreign_key_constraints' => true,
        ],
    ],
    
    // 数据库迁移表名
    'migrations' => 'migrations',
    
    // Redis配置
    'redis' => [
        'client' => 'predis',
        'default' => [
            'host' => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
            'password' => $_ENV['REDIS_PASSWORD'] ?? null,
            'port' => $_ENV['REDIS_PORT'] ?? 6379,
            'database' => $_ENV['REDIS_DB'] ?? 0,
        ],
        'cache' => [
            'host' => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
            'password' => $_ENV['REDIS_PASSWORD'] ?? null,
            'port' => $_ENV['REDIS_PORT'] ?? 6379,
            'database' => $_ENV['REDIS_CACHE_DB'] ?? 1,
        ],
        'session' => [
            'host' => $_ENV['REDIS_HOST'] ?? '127.0.0.1',
            'password' => $_ENV['REDIS_PASSWORD'] ?? null,
            'port' => $_ENV['REDIS_PORT'] ?? 6379,
            'database' => $_ENV['REDIS_SESSION_DB'] ?? 2,
        ],
    ],
];
