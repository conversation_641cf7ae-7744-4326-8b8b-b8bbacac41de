<?php
session_start();

// 如果已经登录，重定向到仪表盘
if (isset($_SESSION['admin_id'])) {
    header('Location: index.php?page=dashboard');
    exit;
}

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('ADMIN_PATH', __DIR__);

// 引入统一的数据库连接
require_once dirname(__DIR__) . '/includes/database.php';

// 如果已经登录，重定向到仪表盘
if (isset($_SESSION['admin_id'])) {
    header('Location: index.php?page=dashboard');
    exit;
}

// 处理登录
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (!empty($username) && !empty($password)) {
        try {
            $db = getDatabase();

            // 检查表是否存在
            $tables = $db->query("SHOW TABLES LIKE 'users'")->fetchAll();

            if (empty($tables)) {
                // 如果数据库表不存在，使用默认管理员账户
                if ($username === 'admin' && $password === 'admin123') {
                    $_SESSION['admin_id'] = 1;
                    $_SESSION['admin_username'] = 'admin';
                    $_SESSION['admin_name'] = '系统管理员';

                    header('Location: index.php?page=dashboard');
                    exit;
                } else {
                    $error = '数据库未初始化，请使用默认账户：admin/admin123';
                }
            } else {
                $stmt = $db->prepare("SELECT * FROM users WHERE username = ? AND role = 'admin' AND status = 'active'");
                $stmt->execute([$username]);
                $admin = $stmt->fetch();

                if ($admin && password_verify($password, $admin['password_hash'])) {
                    $_SESSION['admin_id'] = $admin['id'];
                    $_SESSION['admin_username'] = $admin['username'];
                    $_SESSION['admin_name'] = trim($admin['first_name'] . ' ' . $admin['last_name']);

                    // 更新最后登录时间
                    $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?")->execute([$admin['id']]);

                    header('Location: index.php?page=dashboard');
                    exit;
                } else {
                    $error = '用户名或密码错误';
                }
            }
        } catch (Exception $e) {
            // 数据库连接失败，使用默认管理员账户
            if ($username === 'admin' && $password === 'admin123') {
                $_SESSION['admin_id'] = 1;
                $_SESSION['admin_username'] = 'admin';
                $_SESSION['admin_name'] = '系统管理员';

                header('Location: index.php?page=dashboard');
                exit;
            } else {
                $error = '数据库连接失败，请使用默认账户：admin/admin123';
            }
        }
    } else {
        $error = '请输入用户名和密码';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - NameSilo域名销售系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --white-color: #ffffff;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --bg-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --radius-2xl: 24px;
            --radius-lg: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .login-container {
            background: var(--white-color);
            border-radius: var(--radius-2xl);
            padding: 3rem;
            box-shadow: var(--shadow-2xl);
            max-width: 450px;
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--bg-gradient);
        }
        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }
        .login-header h1 {
            color: var(--text-primary);
            font-weight: 800;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .login-header p {
            color: #718096;
        }
        .form-control {
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .input-group-text {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 10px 0 0 10px;
        }
        .input-group .form-control {
            border-radius: 0 10px 10px 0;
            margin-bottom: 0;
        }
        .demo-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            text-align: center;
        }
        .demo-info small {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
            <h1>管理员登录</h1>
            <p>NameSilo域名销售系统</p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="input-group mb-3">
                <span class="input-group-text">
                    <i class="fas fa-user"></i>
                </span>
                <input type="text" class="form-control" name="username" placeholder="用户名" required>
            </div>
            
            <div class="input-group mb-4">
                <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                </span>
                <input type="password" class="form-control" name="password" placeholder="密码" required>
            </div>
            
            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                登录
            </button>
        </form>
        
        <div class="demo-info">
            <small>
                <i class="fas fa-info-circle"></i>
                <strong>演示账户</strong><br>
                用户名: admin<br>
                密码: admin123
            </small>
        </div>
        
        <?php if (!file_exists(ROOT_PATH . '/.setup_completed')): ?>
        <div class="alert alert-info mt-3">
            <i class="fas fa-magic"></i>
            首次使用？<a href="setup-wizard.php" class="alert-link">运行设置向导</a>
        </div>
        <?php endif; ?>

        <div class="text-center mt-3">
            <a href="../" class="text-decoration-none">
                <i class="fas fa-arrow-left"></i>
                返回前台
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
