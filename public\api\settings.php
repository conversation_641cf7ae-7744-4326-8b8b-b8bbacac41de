<?php
/**
 * 系统设置API
 * System Settings API
 */

session_start();
require_once '../config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 响应函数
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查管理员权限
function checkAdminPermission() {
    if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
        sendResponse(false, '需要管理员权限');
    }
}

// 获取设置值
function getSetting($key, $default = null) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("SELECT value, type FROM system_settings WHERE `key` = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        
        if (!$result) {
            return $default;
        }
        
        // 根据类型转换值
        switch ($result['type']) {
            case 'boolean':
                return (bool)$result['value'];
            case 'integer':
                return (int)$result['value'];
            case 'json':
                return json_decode($result['value'], true);
            default:
                return $result['value'];
        }
    } catch (Exception $e) {
        return $default;
    }
}

// 设置值
function setSetting($key, $value, $type = 'string', $description = '', $group = 'general') {
    try {
        $db = getDatabase();
        
        // 根据类型处理值
        switch ($type) {
            case 'boolean':
                $value = $value ? '1' : '0';
                break;
            case 'json':
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
                break;
            default:
                $value = (string)$value;
        }
        
        // 检查设置是否存在
        $stmt = $db->prepare("SELECT id FROM system_settings WHERE `key` = ?");
        $stmt->execute([$key]);
        
        if ($stmt->fetch()) {
            // 更新现有设置
            $stmt = $db->prepare("UPDATE system_settings SET value = ?, type = ?, description = ?, `group` = ?, updated_at = NOW() WHERE `key` = ?");
            $stmt->execute([$value, $type, $description, $group, $key]);
        } else {
            // 创建新设置
            $stmt = $db->prepare("INSERT INTO system_settings (`key`, value, type, description, `group`, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([$key, $value, $type, $description, $group]);
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// 获取请求方法和操作
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get':
            // 获取设置
            $key = $_GET['key'] ?? '';
            if (!$key) {
                throw new Exception('缺少设置键', 400);
            }
            
            $value = getSetting($key);
            sendResponse(true, '获取成功', ['key' => $key, 'value' => $value]);
            break;
            
        case 'set':
            // 设置值（需要管理员权限）
            checkAdminPermission();
            
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }
            
            $key = $_POST['key'] ?? '';
            $value = $_POST['value'] ?? '';
            $type = $_POST['type'] ?? 'string';
            $description = $_POST['description'] ?? '';
            $group = $_POST['group'] ?? 'general';
            
            if (!$key) {
                throw new Exception('缺少设置键', 400);
            }
            
            if (setSetting($key, $value, $type, $description, $group)) {
                sendResponse(true, '设置成功');
            } else {
                throw new Exception('设置失败', 500);
            }
            break;
            
        case 'list':
            // 获取设置列表
            $group = $_GET['group'] ?? '';
            
            $db = getDatabase();
            $sql = "SELECT `key`, value, type, description, `group`, updated_at FROM system_settings";
            $params = [];
            
            if ($group) {
                $sql .= " WHERE `group` = ?";
                $params[] = $group;
            }
            
            $sql .= " ORDER BY `group`, `key`";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $settings = $stmt->fetchAll();
            
            // 转换值类型
            foreach ($settings as &$setting) {
                switch ($setting['type']) {
                    case 'boolean':
                        $setting['value'] = (bool)$setting['value'];
                        break;
                    case 'integer':
                        $setting['value'] = (int)$setting['value'];
                        break;
                    case 'json':
                        $setting['value'] = json_decode($setting['value'], true);
                        break;
                }
            }
            
            sendResponse(true, '获取成功', $settings);
            break;
            
        case 'init-defaults':
            // 初始化默认设置（需要管理员权限）
            checkAdminPermission();
            
            $defaults = [
                ['key' => 'email_verification_required', 'value' => '1', 'type' => 'boolean', 'description' => '是否需要邮箱验证', 'group' => 'auth'],
                ['key' => 'site_name', 'value' => 'NameSilo域名销售系统', 'type' => 'string', 'description' => '网站名称', 'group' => 'general'],
                ['key' => 'admin_email', 'value' => '<EMAIL>', 'type' => 'string', 'description' => '管理员邮箱', 'group' => 'general'],
                ['key' => 'smtp_enabled', 'value' => '0', 'type' => 'boolean', 'description' => '是否启用SMTP邮件发送', 'group' => 'email'],
                ['key' => 'registration_enabled', 'value' => '1', 'type' => 'boolean', 'description' => '是否允许用户注册', 'group' => 'auth'],
            ];
            
            $count = 0;
            foreach ($defaults as $setting) {
                if (setSetting($setting['key'], $setting['value'], $setting['type'], $setting['description'], $setting['group'])) {
                    $count++;
                }
            }
            
            sendResponse(true, "成功初始化 {$count} 个默认设置");
            break;
            
        default:
            throw new Exception('未知的API操作', 400);
    }
    
} catch (Exception $e) {
    $code = $e->getCode() ?: 500;
    http_response_code($code);
    sendResponse(false, $e->getMessage());
}
?>
