-- 创建用户表
-- Create users table

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('user', 'admin') DEFAULT 'user' COMMENT '用户角色',
    first_name VARCHAR(50) COMMENT '名',
    last_name VARCHAR(50) COMMENT '姓',
    phone VARCHAR(20) COMMENT '电话',
    address TEXT COMMENT '地址',
    city VARCHAR(50) COMMENT '城市',
    state VARCHAR(50) COMMENT '省份',
    zip_code VARCHAR(20) COMMENT '邮编',
    country VARCHAR(50) DEFAULT 'CN' COMMENT '国家',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    email_verification_token VARCHAR(255) COMMENT '邮箱验证令牌',
    password_reset_token VARCHAR(255) COMMENT '密码重置令牌',
    password_reset_expires DATETIME COMMENT '密码重置过期时间',
    last_login DATETIME COMMENT '最后登录时间',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
