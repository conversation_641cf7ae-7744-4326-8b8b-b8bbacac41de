<?php
/**
 * 安全管理内容页面
 * Security Management Content Page
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// 添加错误处理
set_error_handler(function($severity, $message, $file, $line) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>PHP错误:</h4>";
    echo "<p><strong>错误信息:</strong> $message</p>";
    echo "<p><strong>文件:</strong> $file</p>";
    echo "<p><strong>行号:</strong> $line</p>";
    echo "</div>";
});

try {
    // 获取数据库连接
    $db = getDatabase();

    if (!$db) {
        throw new Exception("数据库连接失败");
    }

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>异常错误:</h4>";
    echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>文件:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>堆栈跟踪:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
    exit;
}

// 处理操作
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'reset_user_security':
                $userId = $_POST['user_id'];
                
                // 重置用户安全设置
                $stmt = $db->prepare("DELETE FROM user_security_settings WHERE user_id = ?");
                $stmt->execute([$userId]);
                
                // 清除登录日志
                $stmt = $db->prepare("DELETE FROM user_login_logs WHERE user_id = ?");
                $stmt->execute([$userId]);
                
                $message = '用户安全设置已重置！';
                break;
                
            case 'block_ip':
                $ipAddress = $_POST['ip_address'];
                $reason = $_POST['reason'] ?? '';
                
                // 这里应该有IP黑名单表，简化处理
                $message = "IP地址 {$ipAddress} 已被封禁！";
                break;
        }
    } catch (Exception $e) {
        $error = '操作失败：' . $e->getMessage();
    }
}

// 获取安全统计
try {
    $stmt = $db->query("
        SELECT 
            COUNT(DISTINCT uss.user_id) as users_with_2fa,
            COUNT(DISTINCT ull.user_id) as users_with_login_logs,
            COUNT(CASE WHEN ull.status = 'failed' AND ull.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as failed_logins_24h,
            COUNT(CASE WHEN ull.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as total_logins_24h
        FROM user_security_settings uss
        RIGHT JOIN user_login_logs ull ON uss.user_id = ull.user_id
    ");
    $securityStats = $stmt->fetch();
} catch (Exception $e) {
    $securityStats = [
        'users_with_2fa' => 0,
        'users_with_login_logs' => 0,
        'failed_logins_24h' => 0,
        'total_logins_24h' => 0
    ];
}

// 获取用户安全设置
$currentPage = (int)($_GET['p'] ?? 1); // 使用 'p' 参数避免与路由冲突
$limit = 20;
$offset = ($currentPage - 1) * $limit;

try {
    $stmt = $db->prepare("
        SELECT 
            u.id,
            u.username,
            u.email,
            u.created_at as user_created,
            uss.two_factor_enabled,
            uss.security_question,
            uss.created_at as security_created,
            COUNT(ull.id) as login_count,
            MAX(ull.created_at) as last_login,
            COUNT(CASE WHEN ull.status = 'failed' THEN 1 END) as failed_attempts
        FROM users u
        LEFT JOIN user_security_settings uss ON u.id = uss.user_id
        LEFT JOIN user_login_logs ull ON u.id = ull.user_id
        GROUP BY u.id
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $userSecurity = $stmt->fetchAll();
    
    // 获取总数
    $stmt = $db->query("SELECT COUNT(*) FROM users");
    $totalUsers = $stmt->fetchColumn();
    $totalPages = ceil($totalUsers / $limit);
    
} catch (Exception $e) {
    $userSecurity = [];
    $totalPages = 1;
}

// 获取最近的登录日志
try {
    $stmt = $db->query("
        SELECT 
            ull.*,
            u.username,
            u.email
        FROM user_login_logs ull
        LEFT JOIN users u ON ull.user_id = u.id
        ORDER BY ull.created_at DESC
        LIMIT 50
    ");
    $recentLogins = $stmt->fetchAll();
} catch (Exception $e) {
    $recentLogins = [];
}

// 获取可疑活动
try {
    $stmt = $db->query("
        SELECT 
            ull.ip_address,
            COUNT(*) as attempt_count,
            COUNT(CASE WHEN ull.status = 'failed' THEN 1 END) as failed_count,
            MAX(ull.created_at) as last_attempt,
            GROUP_CONCAT(DISTINCT u.username) as usernames
        FROM user_login_logs ull
        LEFT JOIN users u ON ull.user_id = u.id
        WHERE ull.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY ull.ip_address
        HAVING failed_count > 5
        ORDER BY failed_count DESC
        LIMIT 20
    ");
    $suspiciousIPs = $stmt->fetchAll();
} catch (Exception $e) {
    $suspiciousIPs = [];
}
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">安全管理</h1>
            <p class="text-muted">管理用户安全设置和监控系统安全</p>
        </div>
    </div>

    <!-- 消息提示 -->
    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 安全统计 -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-shield-alt fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($securityStats['users_with_2fa']) ?></h5>
                    <p class="card-text text-muted">启用2FA用户</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-sign-in-alt fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($securityStats['total_logins_24h']) ?></h5>
                    <p class="card-text text-muted">24小时登录</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($securityStats['failed_logins_24h']) ?></h5>
                    <p class="card-text text-muted">24小时失败登录</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-ban fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= count($suspiciousIPs) ?></h5>
                    <p class="card-text text-muted">可疑IP</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签页 -->
    <ul class="nav nav-tabs mb-4" id="securityTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                <i class="fas fa-users me-2"></i>用户安全设置
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                <i class="fas fa-history me-2"></i>登录日志
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="suspicious-tab" data-bs-toggle="tab" data-bs-target="#suspicious" type="button" role="tab">
                <i class="fas fa-exclamation-triangle me-2"></i>可疑活动
            </button>
        </li>
    </ul>

    <div class="tab-content" id="securityTabsContent">
        <!-- 用户安全设置标签页 -->
        <div class="tab-pane fade show active" id="users" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">用户安全设置</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($userSecurity)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无用户数据</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户</th>
                                    <th>双因子认证</th>
                                    <th>安全问题</th>
                                    <th>登录次数</th>
                                    <th>失败次数</th>
                                    <th>最后登录</th>
                                    <th>注册时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($userSecurity as $user): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($user['username']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($user['two_factor_enabled']): ?>
                                        <span class="badge bg-success">已启用</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">未启用</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['security_question']): ?>
                                        <span class="badge bg-info">已设置</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">未设置</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= number_format($user['login_count']) ?></td>
                                    <td>
                                        <?php if ($user['failed_attempts'] > 0): ?>
                                        <span class="text-danger"><?= number_format($user['failed_attempts']) ?></span>
                                        <?php else: ?>
                                        <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['last_login']): ?>
                                        <?= date('Y-m-d H:i:s', strtotime($user['last_login'])) ?>
                                        <?php else: ?>
                                        <span class="text-muted">从未登录</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($user['user_created'])) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-warning" onclick="resetUserSecurity(<?= $user['id'] ?>)">
                                            <i class="fas fa-redo"></i> 重置
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                    <nav aria-label="用户分页">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                                <a class="page-link" href="?page=security&p=<?= $i ?>"><?= $i ?></a>
                            </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 登录日志标签页 -->
        <div class="tab-pane fade" id="logs" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近登录日志</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recentLogins)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无登录日志</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户</th>
                                    <th>IP地址</th>
                                    <th>设备信息</th>
                                    <th>位置</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentLogins as $login): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($login['username']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= htmlspecialchars($login['email']) ?></small>
                                        </div>
                                    </td>
                                    <td><code><?= htmlspecialchars($login['ip_address']) ?></code></td>
                                    <td><?= htmlspecialchars($login['device'] ?? $login['user_agent']) ?></td>
                                    <td><?= htmlspecialchars($login['location'] ?? '未知') ?></td>
                                    <td>
                                        <?php if ($login['status'] === 'success'): ?>
                                        <span class="badge bg-success">成功</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">失败</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('Y-m-d H:i:s', strtotime($login['created_at'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 可疑活动标签页 -->
        <div class="tab-pane fade" id="suspicious" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">可疑IP活动（24小时内失败登录>5次）</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($suspiciousIPs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <p class="text-muted">暂无可疑活动</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>IP地址</th>
                                    <th>总尝试次数</th>
                                    <th>失败次数</th>
                                    <th>涉及用户</th>
                                    <th>最后尝试时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($suspiciousIPs as $ip): ?>
                                <tr>
                                    <td><code><?= htmlspecialchars($ip['ip_address']) ?></code></td>
                                    <td><?= number_format($ip['attempt_count']) ?></td>
                                    <td><span class="text-danger fw-bold"><?= number_format($ip['failed_count']) ?></span></td>
                                    <td><?= htmlspecialchars($ip['usernames']) ?></td>
                                    <td><?= date('Y-m-d H:i:s', strtotime($ip['last_attempt'])) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="blockIP('<?= htmlspecialchars($ip['ip_address']) ?>')">
                                            <i class="fas fa-ban"></i> 封禁
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 重置用户安全设置模态框 -->
<div class="modal fade" id="resetSecurityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重置用户安全设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reset_user_security">
                    <input type="hidden" name="user_id" id="reset_user_id">
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>此操作将清除用户的所有安全设置和登录日志，包括：
                        <ul class="mb-0 mt-2">
                            <li>双因子认证设置</li>
                            <li>安全问题和答案</li>
                            <li>所有登录历史记录</li>
                        </ul>
                    </div>
                    
                    <p>确定要重置该用户的安全设置吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">确认重置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 封禁IP模态框 -->
<div class="modal fade" id="blockIPModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">封禁IP地址</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="block_ip">
                    <input type="hidden" name="ip_address" id="block_ip_address">
                    
                    <div class="mb-3">
                        <label class="form-label">IP地址</label>
                        <input type="text" class="form-control" id="display_ip_address" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">封禁原因</label>
                        <textarea class="form-control" name="reason" rows="3" placeholder="请输入封禁原因..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认封禁</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function resetUserSecurity(userId) {
    document.getElementById('reset_user_id').value = userId;
    new bootstrap.Modal(document.getElementById('resetSecurityModal')).show();
}

function blockIP(ipAddress) {
    document.getElementById('block_ip_address').value = ipAddress;
    document.getElementById('display_ip_address').value = ipAddress;
    new bootstrap.Modal(document.getElementById('blockIPModal')).show();
}
</script>
