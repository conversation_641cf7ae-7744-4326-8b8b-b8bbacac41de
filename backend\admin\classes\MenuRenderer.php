<?php
/**
 * 菜单渲染器
 * Menu Renderer Class
 */

class MenuRenderer
{
    private $menuConfig;
    private $currentPage;
    private $userPermissions;
    
    public function __construct($currentPage = '', $userPermissions = [])
    {
        $this->menuConfig = include __DIR__ . '/../config/menu.php';
        $this->currentPage = $currentPage;
        $this->userPermissions = $userPermissions;
    }
    
    /**
     * 渲染完整菜单
     */
    public function render()
    {
        $html = '<ul class="list-unstyled components">';
        
        foreach ($this->menuConfig as $sectionKey => $section) {
            $html .= $this->renderSection($section);
        }
        
        $html .= '</ul>';
        return $html;
    }
    
    /**
     * 渲染菜单分组
     */
    private function renderSection($section)
    {
        $html = '';
        
        // 渲染分组标题
        if (isset($section['title'])) {
            $icon = isset($section['icon']) ? '<i class="' . $section['icon'] . '"></i>' : '';
            $html .= '<li class="nav-section">';
            $html .= '<span>' . $icon . $section['title'] . '</span>';
            $html .= '</li>';
        }
        
        // 渲染菜单项
        if (isset($section['items'])) {
            foreach ($section['items'] as $item) {
                if ($this->hasPermission($item)) {
                    $html .= $this->renderMenuItem($item);
                }
            }
        }
        
        return $html;
    }
    
    /**
     * 渲染菜单项
     */
    private function renderMenuItem($item)
    {
        $hasChildren = isset($item['children']) && !empty($item['children']);
        $isActive = $this->isActive($item);
        $activeClass = $isActive ? 'active' : '';
        
        $html = '<li class="nav-item ' . $activeClass . '">';
        
        if ($hasChildren) {
            // 有子菜单的项目
            $html .= $this->renderDropdownItem($item);
            $html .= $this->renderSubmenu($item['children']);
        } else {
            // 普通菜单项
            $html .= $this->renderSimpleItem($item);
        }
        
        $html .= '</li>';
        return $html;
    }
    
    /**
     * 渲染下拉菜单项
     */
    private function renderDropdownItem($item)
    {
        $icon = isset($item['icon']) ? '<i class="' . $item['icon'] . '"></i>' : '';
        $badge = $this->renderBadge($item);
        $tooltip = 'data-tooltip="' . htmlspecialchars($item['title']) . '"';
        
        $html = '<a href="#" class="nav-link nav-dropdown" ' . $tooltip . '>';
        $html .= '<div class="nav-icon">' . $icon . '</div>';
        $html .= '<span class="nav-text">' . htmlspecialchars($item['title']) . '</span>';
        $html .= $badge;
        $html .= '<div class="nav-arrow"><i class="fas fa-chevron-right"></i></div>';
        $html .= '</a>';
        
        return $html;
    }
    
    /**
     * 渲染简单菜单项
     */
    private function renderSimpleItem($item)
    {
        $icon = isset($item['icon']) ? '<i class="' . $item['icon'] . '"></i>' : '';
        $badge = $this->renderBadge($item);
        $url = isset($item['url']) ? $item['url'] : '#';
        $tooltip = 'data-tooltip="' . htmlspecialchars($item['title']) . '"';
        
        $html = '<a href="' . htmlspecialchars($url) . '" class="nav-link" ' . $tooltip . '>';
        $html .= '<div class="nav-icon">' . $icon . '</div>';
        $html .= '<span class="nav-text">' . htmlspecialchars($item['title']) . '</span>';
        $html .= $badge;
        $html .= '</a>';
        
        return $html;
    }
    
    /**
     * 渲染子菜单
     */
    private function renderSubmenu($children)
    {
        $html = '<ul class="nav-submenu">';
        
        foreach ($children as $child) {
            if ($this->hasPermission($child)) {
                $isActive = $this->isActive($child);
                $activeClass = $isActive ? 'active' : '';
                $icon = isset($child['icon']) ? '<i class="' . $child['icon'] . '"></i>' : '';
                $url = isset($child['url']) ? $child['url'] : '#';
                
                $html .= '<li class="' . $activeClass . '">';
                $html .= '<a href="' . htmlspecialchars($url) . '">';
                $html .= $icon;
                $html .= '<span>' . htmlspecialchars($child['title']) . '</span>';
                $html .= '</a>';
                $html .= '</li>';
            }
        }
        
        $html .= '</ul>';
        return $html;
    }
    
    /**
     * 渲染徽章
     */
    private function renderBadge($item)
    {
        if (!isset($item['badge'])) {
            return '';
        }
        
        $badge = $item['badge'];
        $html = '<div class="nav-badge">';
        
        if ($badge['type'] === 'dot') {
            $html .= '<span class="badge-dot"></span>';
        } elseif ($badge['type'] === 'count') {
            $html .= '<span class="badge-count">' . $badge['value'] . '</span>';
        }
        
        $html .= '</div>';
        return $html;
    }
    
    /**
     * 检查是否有权限
     */
    private function hasPermission($item)
    {
        if (!isset($item['permissions'])) {
            return true; // 没有权限要求的项目默认显示
        }
        
        foreach ($item['permissions'] as $permission) {
            if (in_array($permission, $this->userPermissions)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查菜单项是否激活
     */
    private function isActive($item)
    {
        // 检查当前页面
        if (isset($item['url'])) {
            // 获取当前请求的文件名
            $currentFile = basename($_SERVER['PHP_SELF']);

            // 检查URL参数
            $urlParams = parse_url($item['url'], PHP_URL_QUERY);
            if ($urlParams) {
                parse_str($urlParams, $params);
                if (isset($params['page']) && $params['page'] === $this->currentPage) {
                    return true;
                }
            }

            // 检查直接文件匹配
            $itemFile = basename(parse_url($item['url'], PHP_URL_PATH));
            if ($itemFile && $itemFile === $currentFile) {
                return true;
            }
        }

        // 检查子菜单
        if (isset($item['children'])) {
            foreach ($item['children'] as $child) {
                if ($this->isActive($child)) {
                    return true;
                }
            }
        }

        return false;
    }
    
    /**
     * 获取面包屑导航
     */
    public function getBreadcrumb()
    {
        $breadcrumb = [];
        $this->findBreadcrumb($this->menuConfig, $breadcrumb);
        return $breadcrumb;
    }
    
    /**
     * 查找面包屑路径
     */
    private function findBreadcrumb($items, &$breadcrumb, $path = [])
    {
        foreach ($items as $sectionKey => $section) {
            if (isset($section['items'])) {
                foreach ($section['items'] as $item) {
                    $currentPath = array_merge($path, [$item['title']]);
                    
                    if ($this->isActive($item)) {
                        $breadcrumb = $currentPath;
                        return true;
                    }
                    
                    if (isset($item['children'])) {
                        if ($this->findBreadcrumb(['children' => $item['children']], $breadcrumb, $currentPath)) {
                            return true;
                        }
                    }
                }
            } elseif (isset($items['children'])) {
                foreach ($items['children'] as $child) {
                    $currentPath = array_merge($path, [$child['title']]);
                    
                    if ($this->isActive($child)) {
                        $breadcrumb = $currentPath;
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取收藏菜单
     */
    public function getFavorites()
    {
        // 这里可以从数据库或缓存中获取用户收藏的菜单
        return [];
    }
    
    /**
     * 搜索菜单项
     */
    public function search($query)
    {
        $results = [];
        $this->searchInItems($this->menuConfig, $query, $results);
        return $results;
    }
    
    /**
     * 在菜单项中搜索
     */
    private function searchInItems($items, $query, &$results, $path = [])
    {
        foreach ($items as $sectionKey => $section) {
            if (isset($section['items'])) {
                foreach ($section['items'] as $item) {
                    $currentPath = array_merge($path, [$item['title']]);
                    
                    if (stripos($item['title'], $query) !== false) {
                        $results[] = [
                            'title' => $item['title'],
                            'url' => $item['url'] ?? '#',
                            'path' => implode(' > ', $currentPath),
                            'icon' => $item['icon'] ?? ''
                        ];
                    }
                    
                    if (isset($item['children'])) {
                        $this->searchInItems(['children' => $item['children']], $query, $results, $currentPath);
                    }
                }
            } elseif (isset($items['children'])) {
                foreach ($items['children'] as $child) {
                    if (stripos($child['title'], $query) !== false) {
                        $results[] = [
                            'title' => $child['title'],
                            'url' => $child['url'] ?? '#',
                            'path' => implode(' > ', array_merge($path, [$child['title']])),
                            'icon' => $child['icon'] ?? ''
                        ];
                    }
                }
            }
        }
    }
}
