<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录 - NameSilo域名销售系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .logout-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .logout-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logout-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--success-color), #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 2rem;
            animation: checkmark 0.6s ease-in-out;
        }

        @keyframes checkmark {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .logout-title {
            color: var(--dark-color);
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .logout-message {
            color: #6b7280;
            font-size: 1.1rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            border-radius: 12px;
            padding: 10px 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .countdown {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.1rem;
            margin-top: 20px;
        }

        .loading {
            display: none;
            margin-top: 20px;
        }

        .spinner-border {
            width: 2rem;
            height: 2rem;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-icon">
                <i class="fas fa-check"></i>
            </div>
            
            <h1 class="logout-title">退出成功</h1>
            <p class="logout-message">
                您已成功退出登录，感谢您使用 NameSilo 域名销售系统！<br>
                您的会话已安全结束。
            </p>
            
            <div class="loading" id="loading">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">正在退出...</span>
                </div>
                <p class="mt-2">正在安全退出...</p>
            </div>
            
            <div id="actions">
                <a href="login.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>重新登录
                </a>
                <a href="index.php" class="btn btn-outline-primary">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
            </div>
            
            <div class="countdown" id="countdown">
                <i class="fas fa-clock me-2"></i>
                <span id="timer">5</span> 秒后自动跳转到登录页面
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 执行退出操作
        async function performLogout() {
            try {
                const response = await fetch('api/auth.php?action=logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('退出成功:', result);
                } else {
                    console.warn('退出请求失败，但继续显示退出页面');
                }
            } catch (error) {
                console.error('退出请求错误:', error);
                // 即使API调用失败，也继续显示退出页面
            }
        }

        // 倒计时功能
        function startCountdown() {
            let timeLeft = 5;
            const timerElement = document.getElementById('timer');
            const countdownElement = document.getElementById('countdown');
            
            const countdown = setInterval(() => {
                timeLeft--;
                timerElement.textContent = timeLeft;
                
                if (timeLeft <= 0) {
                    clearInterval(countdown);
                    countdownElement.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在跳转...';
                    window.location.href = 'login.php';
                }
            }, 1000);
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 执行退出操作
            performLogout();
            
            // 开始倒计时
            startCountdown();
        });

        // 阻止浏览器后退
        history.pushState(null, null, location.href);
        window.addEventListener('popstate', function() {
            history.pushState(null, null, location.href);
        });
    </script>
</body>
</html>
