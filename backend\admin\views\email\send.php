<?php
/**
 * 邮件发送页面
 * Email Send Page
 */

// 获取数据库连接
$db = getDatabase();

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'send_email':
                $recipients = $_POST['recipients'] ?? '';
                $subject = $_POST['subject'] ?? '';
                $content = $_POST['content'] ?? '';
                $sendType = $_POST['send_type'] ?? 'manual';
                
                if (empty($recipients) || empty($subject) || empty($content)) {
                    throw new Exception('收件人、主题和内容不能为空');
                }
                
                // 解析收件人
                $emailList = [];
                if ($sendType === 'manual') {
                    $emails = preg_split('/[,;\s\n]+/', $recipients);
                    foreach ($emails as $email) {
                        $email = trim($email);
                        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            $emailList[] = $email;
                        }
                    }
                } else {
                    // 从用户表获取邮箱
                    $conditions = [];
                    $params = [];
                    
                    if ($sendType === 'verified') {
                        $conditions[] = "email_verified = 1";
                    } elseif ($sendType === 'unverified') {
                        $conditions[] = "email_verified = 0";
                    }
                    
                    $sql = "SELECT email FROM users WHERE status = 'active'";
                    if ($conditions) {
                        $sql .= " AND " . implode(" AND ", $conditions);
                    }
                    
                    $stmt = $db->prepare($sql);
                    $stmt->execute($params);
                    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    foreach ($users as $user) {
                        $emailList[] = $user['email'];
                    }
                }
                
                if (empty($emailList)) {
                    throw new Exception('没有找到有效的收件人邮箱');
                }
                
                // 发送邮件
                require_once '../includes/smtp-mailer.php';
                $successCount = 0;
                $failCount = 0;
                
                foreach ($emailList as $email) {
                    $result = sendEmailWithSMTP($email, $subject, $content);
                    if ($result['success']) {
                        $successCount++;
                        
                        // 记录发送日志
                        try {
                            $stmt = $db->prepare("INSERT INTO email_logs (recipient, subject, content, status, sent_at) VALUES (?, ?, ?, 'sent', NOW())");
                            $stmt->execute([$email, $subject, $content]);
                        } catch (Exception $e) {
                            // 忽略日志记录错误
                        }
                    } else {
                        $failCount++;
                        
                        // 记录失败日志
                        try {
                            $stmt = $db->prepare("INSERT INTO email_logs (recipient, subject, content, status, error_message, sent_at) VALUES (?, ?, ?, 'failed', ?, NOW())");
                            $stmt->execute([$email, $subject, $content, $result['message']]);
                        } catch (Exception $e) {
                            // 忽略日志记录错误
                        }
                    }
                }
                
                $success = "邮件发送完成！成功: {$successCount}，失败: {$failCount}";
                break;
                
            case 'load_template':
                $templateId = $_POST['template_id'] ?? '';
                if ($templateId) {
                    $stmt = $db->prepare("SELECT * FROM email_templates WHERE id = ?");
                    $stmt->execute([$templateId]);
                    $template = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($template) {
                        echo json_encode([
                            'success' => true,
                            'subject' => $template['subject'],
                            'content' => $template['content']
                        ]);
                    } else {
                        echo json_encode(['success' => false, 'message' => '模板不存在']);
                    }
                } else {
                    echo json_encode(['success' => false, 'message' => '请选择模板']);
                }
                exit;
        }
    } catch (Exception $e) {
        if ($_POST['action'] === 'load_template') {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
        $error = $e->getMessage();
    }
}

// 获取邮件模板
$templates = [];
try {
    $stmt = $db->prepare("SELECT id, name, subject FROM email_templates ORDER BY name");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // 忽略错误
}

// 获取用户统计
$userStats = ['total' => 0, 'verified' => 0, 'unverified' => 0];
try {
    $stmt = $db->prepare("SELECT COUNT(*) as total, SUM(email_verified) as verified FROM users WHERE status = 'active'");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $userStats['total'] = $stats['total'] ?? 0;
    $userStats['verified'] = $stats['verified'] ?? 0;
    $userStats['unverified'] = $userStats['total'] - $userStats['verified'];
} catch (Exception $e) {
    // 忽略错误
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">邮件发送</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="#">邮件管理</a></li>
                    <li class="breadcrumb-item active">邮件发送</li>
                </ol>
            </div>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paper-plane me-2"></i>发送邮件
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="sendEmailForm">
                        <input type="hidden" name="action" value="send_email">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="send_type" class="form-label">发送类型</label>
                                    <select class="form-select" id="send_type" name="send_type" onchange="toggleRecipients()">
                                        <option value="manual">手动输入邮箱</option>
                                        <option value="all">所有用户 (<?= $userStats['total'] ?>)</option>
                                        <option value="verified">已验证用户 (<?= $userStats['verified'] ?>)</option>
                                        <option value="unverified">未验证用户 (<?= $userStats['unverified'] ?>)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template_select" class="form-label">选择模板</label>
                                    <select class="form-select" id="template_select" onchange="loadTemplate()">
                                        <option value="">请选择模板</option>
                                        <?php foreach ($templates as $template): ?>
                                        <option value="<?= $template['id'] ?>"><?= htmlspecialchars($template['name']) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3" id="recipients_group">
                            <label for="recipients" class="form-label">收件人邮箱</label>
                            <textarea class="form-control" id="recipients" name="recipients" rows="3" 
                                      placeholder="输入邮箱地址，多个邮箱用逗号、分号或换行分隔"></textarea>
                            <div class="form-text">支持多个邮箱地址，用逗号、分号或换行分隔</div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">邮件主题</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   placeholder="请输入邮件主题" required>
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">邮件内容</label>
                            <textarea class="form-control" id="content" name="content" rows="15" 
                                      placeholder="请输入邮件内容，支持HTML格式" required></textarea>
                            <div class="form-text">支持HTML格式，可使用变量如 {{user_name}}, {{site_name}} 等</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>发送邮件
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewEmail()">
                                <i class="fas fa-eye me-1"></i>预览邮件
                            </button>
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>重置表单
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>用户统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border rounded p-2">
                                <h4 class="text-primary mb-1"><?= $userStats['total'] ?></h4>
                                <small class="text-muted">总用户</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border rounded p-2">
                                <h4 class="text-success mb-1"><?= $userStats['verified'] ?></h4>
                                <small class="text-muted">已验证</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border rounded p-2">
                                <h4 class="text-warning mb-1"><?= $userStats['unverified'] ?></h4>
                                <small class="text-muted">未验证</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>发送说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i>使用提示</h6>
                        <ul class="mb-0 small">
                            <li>选择模板可快速填充邮件内容</li>
                            <li>支持HTML格式的邮件内容</li>
                            <li>可使用变量如 {{user_name}}</li>
                            <li>建议先预览再发送</li>
                            <li>大量发送时请分批进行</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>注意事项</h6>
                        <ul class="mb-0 small">
                            <li>确保SMTP配置正确</li>
                            <li>避免发送垃圾邮件</li>
                            <li>遵守邮件发送规范</li>
                            <li>注意发送频率限制</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">邮件预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>主题：</strong>
                    <span id="previewSubject"></span>
                </div>
                <div class="border rounded p-3">
                    <div id="previewContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRecipients() {
    const sendType = document.getElementById('send_type').value;
    const recipientsGroup = document.getElementById('recipients_group');
    
    if (sendType === 'manual') {
        recipientsGroup.style.display = 'block';
        document.getElementById('recipients').required = true;
    } else {
        recipientsGroup.style.display = 'none';
        document.getElementById('recipients').required = false;
    }
}

function loadTemplate() {
    const templateId = document.getElementById('template_select').value;
    if (!templateId) return;
    
    const formData = new FormData();
    formData.append('action', 'load_template');
    formData.append('template_id', templateId);
    
    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('subject').value = data.subject;
            document.getElementById('content').value = data.content;
        } else {
            alert('加载模板失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('加载模板失败: ' + error.message);
    });
}

function previewEmail() {
    const subject = document.getElementById('subject').value;
    const content = document.getElementById('content').value;
    
    if (!subject || !content) {
        alert('请先输入邮件主题和内容');
        return;
    }
    
    document.getElementById('previewSubject').textContent = subject;
    document.getElementById('previewContent').innerHTML = content;
    
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// 表单验证
document.getElementById('sendEmailForm').addEventListener('submit', function(e) {
    const sendType = document.getElementById('send_type').value;
    const recipients = document.getElementById('recipients').value;
    
    if (sendType === 'manual' && !recipients.trim()) {
        e.preventDefault();
        alert('请输入收件人邮箱地址');
        return false;
    }
    
    if (!confirm('确定要发送邮件吗？')) {
        e.preventDefault();
        return false;
    }
});

// 初始化
toggleRecipients();
</script>
