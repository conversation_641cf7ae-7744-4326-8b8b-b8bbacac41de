<?php
/**
 * 远程域名商价格获取器
 * 支持从多个域名商API获取实时价格数据
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 错误处理
set_error_handler(function($severity, $message, $file, $line) {
    throw new ErrorException($message, 0, $severity, $file, $line);
});

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 引入统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database.php';
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

// 数据库连接
function getDatabase() {
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_NAME'] ?? 'domain_system';
    $username = $_ENV['DB_USER'] ?? 'root';
    $password = $_ENV['DB_PASS'] ?? '';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception('数据库连接失败: ' . $e->getMessage());
    }
}

/**
 * 域名商配置
 */
function getDomainProviders() {
    return [
        'namecheap' => [
            'name' => 'Namecheap',
            'description' => '知名域名注册商，价格实惠',
            'api_url' => 'https://api.namecheap.com/xml.response',
            'available' => true,
            'supported_tlds' => 150,
            'rate_limit' => 20, // 每分钟请求数
        ],
        'godaddy' => [
            'name' => 'GoDaddy',
            'description' => '全球最大域名注册商',
            'api_url' => 'https://api.godaddy.com/v1/domains/available',
            'available' => true,
            'supported_tlds' => 200,
            'rate_limit' => 60,
        ],
        'namesilo' => [
            'name' => 'NameSilo',
            'description' => '性价比高的域名注册商',
            'api_url' => 'https://www.namesilo.com/api/getDomainInfo',
            'available' => true,
            'supported_tlds' => 120,
            'rate_limit' => 30,
        ],
        'porkbun' => [
            'name' => 'Porkbun',
            'description' => '新兴域名注册商，价格优势明显',
            'api_url' => 'https://porkbun.com/api/json/v3/pricing/get',
            'available' => true,
            'supported_tlds' => 80,
            'rate_limit' => 100,
        ],
        'dynadot' => [
            'name' => 'Dynadot',
            'description' => '老牌域名注册商',
            'api_url' => 'https://api.dynadot.com/api3.xml',
            'available' => false, // 需要API密钥
            'supported_tlds' => 90,
            'rate_limit' => 50,
        ]
    ];
}

/**
 * 模拟获取远程价格数据
 * 在实际应用中，这里会调用真实的API
 */
function fetchProviderPrices($provider) {
    // 模拟API延迟
    usleep(500000); // 0.5秒
    
    // 模拟价格数据
    $mockPrices = [
        'namecheap' => [
            'com' => ['registration' => 8.88, 'renewal' => 12.98],
            'net' => ['registration' => 12.98, 'renewal' => 12.98],
            'org' => ['registration' => 12.98, 'renewal' => 14.98],
            'info' => ['registration' => 2.88, 'renewal' => 17.98],
            'biz' => ['registration' => 14.98, 'renewal' => 14.98],
            'co' => ['registration' => 28.88, 'renewal' => 32.98],
            'io' => ['registration' => 58.88, 'renewal' => 58.88],
            'me' => ['registration' => 18.88, 'renewal' => 19.98],
        ],
        'godaddy' => [
            'com' => ['registration' => 9.99, 'renewal' => 17.99],
            'net' => ['registration' => 12.99, 'renewal' => 17.99],
            'org' => ['registration' => 12.99, 'renewal' => 17.99],
            'info' => ['registration' => 2.99, 'renewal' => 19.99],
            'biz' => ['registration' => 12.99, 'renewal' => 19.99],
            'co' => ['registration' => 24.99, 'renewal' => 34.99],
            'io' => ['registration' => 54.99, 'renewal' => 74.99],
            'me' => ['registration' => 17.99, 'renewal' => 19.99],
        ],
        'namesilo' => [
            'com' => ['registration' => 8.99, 'renewal' => 8.99],
            'net' => ['registration' => 11.99, 'renewal' => 11.99],
            'org' => ['registration' => 11.99, 'renewal' => 11.99],
            'info' => ['registration' => 3.99, 'renewal' => 15.99],
            'biz' => ['registration' => 13.99, 'renewal' => 13.99],
            'co' => ['registration' => 27.99, 'renewal' => 27.99],
            'io' => ['registration' => 39.99, 'renewal' => 39.99],
            'me' => ['registration' => 16.99, 'renewal' => 16.99],
        ],
        'porkbun' => [
            'com' => ['registration' => 7.98, 'renewal' => 9.13],
            'net' => ['registration' => 10.98, 'renewal' => 12.76],
            'org' => ['registration' => 10.98, 'renewal' => 13.76],
            'info' => ['registration' => 2.98, 'renewal' => 16.98],
            'biz' => ['registration' => 12.98, 'renewal' => 12.98],
            'co' => ['registration' => 26.98, 'renewal' => 30.98],
            'io' => ['registration' => 48.98, 'renewal' => 48.98],
            'me' => ['registration' => 15.98, 'renewal' => 17.98],
        ]
    ];
    
    return $mockPrices[$provider] ?? [];
}

/**
 * 比较价格并找出最优选择
 */
function comparePrices($allPrices, $currentPrices) {
    $comparisons = [];
    
    foreach ($currentPrices as $tld => $currentPrice) {
        $bestPrice = $currentPrice['registration_price'];
        $bestProvider = 'current';
        
        foreach ($allPrices as $provider => $prices) {
            if (isset($prices[$tld]) && $prices[$tld]['registration'] < $bestPrice) {
                $bestPrice = $prices[$tld]['registration'];
                $bestProvider = $provider;
            }
        }
        
        if ($bestProvider !== 'current') {
            $comparisons[] = [
                'tld' => $tld,
                'current_price' => $currentPrice['registration_price'],
                'best_price' => $bestPrice,
                'provider' => $bestProvider,
                'savings' => $currentPrice['registration_price'] - $bestPrice
            ];
        }
    }
    
    // 按节省金额排序
    usort($comparisons, function($a, $b) {
        return $b['savings'] <=> $a['savings'];
    });
    
    return $comparisons;
}

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get-providers':
            // 获取支持的域名商列表
            $providers = getDomainProviders();
            $providerList = [];
            
            foreach ($providers as $key => $provider) {
                $providerList[] = [
                    'id' => $key,
                    'name' => $provider['name'],
                    'description' => $provider['description'],
                    'available' => $provider['available'],
                    'supported_tlds' => $provider['supported_tlds']
                ];
            }
            
            echo json_encode([
                'success' => true,
                'providers' => $providerList,
                'total' => count($providerList)
            ]);
            break;
            
        case 'fetch-prices':
            // 获取远程价格数据
            $db = getDatabase();
            $providers = getDomainProviders();
            
            // 获取当前数据库中的价格
            $stmt = $db->query("SELECT tld, registration_price FROM domain_prices");
            $currentPrices = [];
            while ($row = $stmt->fetch()) {
                $currentPrices[$row['tld']] = ['registration_price' => $row['registration_price']];
            }
            
            // 从各个域名商获取价格
            $allPrices = [];
            $totalProviders = 0;
            
            foreach ($providers as $providerId => $provider) {
                if (!$provider['available']) {
                    continue;
                }
                
                try {
                    $prices = fetchProviderPrices($providerId);
                    if (!empty($prices)) {
                        $allPrices[$providerId] = $prices;
                        $totalProviders++;
                    }
                } catch (Exception $e) {
                    // 忽略单个提供商的错误
                    continue;
                }
            }
            
            // 比较价格
            $priceComparisons = comparePrices($allPrices, $currentPrices);
            
            // 更新数据库中的价格（这里只是示例，实际应用中需要更谨慎）
            $updatedTlds = 0;
            foreach ($priceComparisons as $comparison) {
                if ($comparison['savings'] > 1.00) { // 只有节省超过1美元才更新
                    $stmt = $db->prepare("
                        UPDATE domain_prices 
                        SET registration_price = ?, updated_at = NOW() 
                        WHERE tld = ?
                    ");
                    $stmt->execute([$comparison['best_price'], $comparison['tld']]);
                    $updatedTlds++;
                }
            }
            
            echo json_encode([
                'success' => true,
                'total_providers' => $totalProviders,
                'updated_tlds' => $updatedTlds,
                'price_comparisons' => $priceComparisons,
                'message' => "成功从 {$totalProviders} 个域名商获取价格数据"
            ]);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
