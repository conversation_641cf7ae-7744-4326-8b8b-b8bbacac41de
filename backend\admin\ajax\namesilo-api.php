<?php
/**
 * NameSilo API 统一接口
 * Unified NameSilo API Interface
 * 
 * 提供所有NameSilo API功能的统一访问接口
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 关闭错误显示，避免破坏JSON

// 设置JSON响应头
header('Content-Type: application/json');

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 引入统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database.php';
}

// 引入NameSilo API类
require_once ROOT_PATH . '/public/api/NameSiloAPI.php';

// 获取NameSilo客户端
function getNameSiloClient() {
    $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
    $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
    $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

    if (empty($apiKey)) {
        throw new Exception('NameSilo API密钥未配置');
    }

    return new NameSiloAPI($apiKey, $apiUrl, $sandbox);
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? $_POST['action'] ?? '';
    
    if (empty($action)) {
        throw new Exception('未指定操作类型', 400);
    }
    
    $client = getNameSiloClient();
    
    switch ($action) {
        // ==================== 账户管理 ====================
        case 'get-balance':
            $result = $client->getAccountBalance();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'get-account-info':
            $result = $client->getAccountInfo();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'get-api-status':
            $result = $client->getAPIStatus();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        // ==================== 域名查询 ====================
        case 'check-availability':
            $domains = $_GET['domains'] ?? $_POST['domains'] ?? '';
            if (empty($domains)) {
                throw new Exception('未指定要查询的域名', 400);
            }
            
            if (is_string($domains)) {
                $domains = explode(',', $domains);
            }
            
            $result = $client->checkDomainAvailability($domains);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'batch-check-availability':
            $domains = $_POST['domains'] ?? [];
            $batchSize = (int)($_POST['batch_size'] ?? 10);
            
            if (empty($domains)) {
                throw new Exception('未指定要查询的域名', 400);
            }
            
            $result = $client->batchCheckAvailability($domains, $batchSize);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'get-domain-prices':
            $result = $client->getDomainPrices();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'generate-suggestions':
            $keyword = $_GET['keyword'] ?? $_POST['keyword'] ?? '';
            $tlds = $_GET['tlds'] ?? $_POST['tlds'] ?? ['com', 'net', 'org'];
            
            if (empty($keyword)) {
                throw new Exception('未指定关键词', 400);
            }
            
            if (is_string($tlds)) {
                $tlds = explode(',', $tlds);
            }
            
            $suggestions = $client->generateDomainSuggestions($keyword, $tlds);
            $result = $client->batchCheckAvailability($suggestions);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'keyword' => $keyword,
                    'suggestions' => $result
                ]
            ]);
            break;
            
        // ==================== 域名管理 ====================
        case 'list-domains':
            $result = $client->listDomains();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'get-domain-info':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }
            
            $result = $client->getDomainInfo($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'register-domain':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            
            $domain = $input['domain'] ?? '';
            $years = (int)($input['years'] ?? 1);
            $contacts = $input['contacts'] ?? [];
            $options = $input['options'] ?? [];
            
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }
            
            $result = $client->registerDomain($domain, $years, $contacts, $options);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'renew-domain':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            
            $domain = $input['domain'] ?? '';
            $years = (int)($input['years'] ?? 1);
            
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }
            
            $result = $client->renewDomain($domain, $years);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'transfer-domain':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            
            $domain = $input['domain'] ?? '';
            $authCode = $input['auth_code'] ?? '';
            $contacts = $input['contacts'] ?? [];
            
            if (empty($domain) || empty($authCode)) {
                throw new Exception('域名和授权码不能为空', 400);
            }
            
            $result = $client->transferDomain($domain, $authCode, $contacts);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        // ==================== 域名设置 ====================
        case 'change-nameservers':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }
            
            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            
            $domain = $input['domain'] ?? '';
            $nameservers = $input['nameservers'] ?? [];
            
            if (empty($domain) || empty($nameservers)) {
                throw new Exception('域名和域名服务器不能为空', 400);
            }
            
            $result = $client->changeNameServers($domain, $nameservers);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'domain-lock':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }
            
            $result = $client->domainLock($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'domain-unlock':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }
            
            $result = $client->domainUnlock($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'add-privacy':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->addPrivacy($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'remove-privacy':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->removePrivacy($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'add-auto-renewal':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->addAutoRenewal($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'remove-auto-renewal':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->removeAutoRenewal($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        // ==================== DNS管理 ====================
        case 'dns-list-records':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->dnsListRecords($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'dns-add-record':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            $type = $input['type'] ?? '';
            $host = $input['host'] ?? '';
            $value = $input['value'] ?? '';
            $ttl = (int)($input['ttl'] ?? 7207);
            $distance = isset($input['distance']) ? (int)$input['distance'] : null;

            if (empty($domain) || empty($type) || empty($host) || empty($value)) {
                throw new Exception('域名、记录类型、主机名和记录值不能为空', 400);
            }

            $result = $client->dnsAddRecord($domain, $type, $host, $value, $ttl, $distance);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'dns-update-record':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            $recordId = $input['record_id'] ?? '';
            $host = $input['host'] ?? '';
            $value = $input['value'] ?? '';
            $ttl = (int)($input['ttl'] ?? 7207);
            $distance = isset($input['distance']) ? (int)$input['distance'] : null;

            if (empty($domain) || empty($recordId) || empty($host) || empty($value)) {
                throw new Exception('域名、记录ID、主机名和记录值不能为空', 400);
            }

            $result = $client->dnsUpdateRecord($domain, $recordId, $host, $value, $ttl, $distance);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'dns-delete-record':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            $recordId = $input['record_id'] ?? '';

            if (empty($domain) || empty($recordId)) {
                throw new Exception('域名和记录ID不能为空', 400);
            }

            $result = $client->dnsDeleteRecord($domain, $recordId);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        // ==================== 联系人管理 ====================
        case 'contact-list':
            $result = $client->contactList();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'contact-add':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $result = $client->contactAdd($input);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'contact-update':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $contactId = $input['contact_id'] ?? '';
            if (empty($contactId)) {
                throw new Exception('未指定联系人ID', 400);
            }

            $result = $client->contactUpdate($contactId, $input);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'contact-delete':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $contactId = $input['contact_id'] ?? '';
            if (empty($contactId)) {
                throw new Exception('未指定联系人ID', 400);
            }

            $result = $client->contactDelete($contactId);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        // ==================== 市场销售 API ====================
        case 'list-marketplace-sales':
            $result = $client->listMarketplaceSales();
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'search-marketplace-domains':
            $filters = $_GET['filters'] ?? $_POST['filters'] ?? [];

            // 支持单独的过滤参数
            if (isset($_GET['keyword'])) $filters['keyword'] = $_GET['keyword'];
            if (isset($_GET['tld'])) $filters['tld'] = $_GET['tld'];
            if (isset($_GET['min_price'])) $filters['min_price'] = (float)$_GET['min_price'];
            if (isset($_GET['max_price'])) $filters['max_price'] = (float)$_GET['max_price'];
            if (isset($_GET['category'])) $filters['category'] = $_GET['category'];
            if (isset($_GET['length_min'])) $filters['length_min'] = (int)$_GET['length_min'];
            if (isset($_GET['length_max'])) $filters['length_max'] = (int)$_GET['length_max'];

            $result = $client->searchMarketplaceDomains($filters);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'add-marketplace-sale':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            $price = (float)($input['price'] ?? 0);
            $type = $input['type'] ?? 'buy_now';
            $options = $input['options'] ?? [];

            if (empty($domain) || $price <= 0) {
                throw new Exception('域名和价格不能为空', 400);
            }

            $result = $client->addMarketplaceSale($domain, $price, $type, $options);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'update-marketplace-sale':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            $updates = $input['updates'] ?? [];

            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->updateMarketplaceSale($domain, $updates);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'remove-marketplace-sale':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $result = $client->removeMarketplaceSale($domain);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        case 'update-marketplace-landing-page':
            if ($method !== 'POST') {
                throw new Exception('仅支持POST请求', 405);
            }

            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            $domain = $input['domain'] ?? '';
            $landingPage = $input['landing_page'] ?? '';

            if (empty($domain) || empty($landingPage)) {
                throw new Exception('域名和着陆页内容不能为空', 400);
            }

            $result = $client->updateMarketplaceLandingPage($domain, $landingPage);
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;

        // ==================== 测试功能 ====================
        case 'test-operation':
            $operation = $_GET['operation'] ?? '';
            if (empty($operation)) {
                throw new Exception('未指定操作名称', 400);
            }

            try {
                // 构建API URL
                $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
                $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';

                $url = $apiUrl . '/' . $operation . '?version=1&type=json&key=' . $apiKey;

                // 发送请求
                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => 10,
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false
                ]);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($httpCode === 200 && $response) {
                    $data = json_decode($response, true);
                    if (isset($data['reply']['code'])) {
                        $code = $data['reply']['code'];
                        if ($code == '107') {
                            throw new Exception("API操作不存在: {$operation}");
                        } else {
                            echo json_encode([
                                'success' => true,
                                'data' => [
                                    'operation' => $operation,
                                    'status' => 'API操作存在',
                                    'response_code' => $code,
                                    'message' => $data['reply']['detail'] ?? ''
                                ]
                            ]);
                            return;
                        }
                    }
                }

                throw new Exception("无法测试API操作: {$operation}");

            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage(),
                    'operation' => $operation
                ]);
            }
            break;

        // ==================== 辅助功能 ====================
        case 'validate-domain':
            $domain = $_GET['domain'] ?? $_POST['domain'] ?? '';
            if (empty($domain)) {
                throw new Exception('未指定域名', 400);
            }

            $isValid = $client->validateDomain($domain);
            $tld = $client->extractTLD($domain);

            echo json_encode([
                'success' => true,
                'data' => [
                    'domain' => $domain,
                    'valid' => $isValid,
                    'tld' => $tld
                ]
            ]);
            break;

        default:
            throw new Exception('不支持的操作: ' . $action, 400);
    }

} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => $e->getCode()
    ]);
}
