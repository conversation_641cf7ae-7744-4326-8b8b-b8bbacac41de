<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找回密码 - NameSilo域名销售系统</title>
    <meta name="description" content="重置您的账户密码">
    <meta name="keywords" content="找回密码,重置密码,NameSilo,域名管理">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/auth.css" rel="stylesheet">
    
    <style>
        /* 找回密码页面特定样式 */
        .auth-step {
            display: none;
        }
        
        .auth-step.active {
            display: block;
        }
        
        .success-icon {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: var(--spacing-lg);
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-bottom: var(--spacing-xl);
        }
        
        .step {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            transition: var(--transition);
        }
        
        .step.active {
            background: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background: var(--success-color);
            color: white;
        }
        
        .step.inactive {
            background: var(--border-light);
            color: var(--text-muted);
        }
        
        .step-line {
            width: 2rem;
            height: 2px;
            background: var(--border-light);
            transition: var(--transition);
        }
        
        .step-line.completed {
            background: var(--success-color);
        }
        
        .resend-timer {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-top: var(--spacing-sm);
        }
        
        .btn-secondary {
            background: var(--border-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background: var(--border-color);
            color: var(--text-color);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step active" id="step1Indicator">1</div>
            <div class="step-line" id="stepLine1"></div>
            <div class="step inactive" id="step2Indicator">2</div>
        </div>
        
        <!-- 步骤1：输入邮箱 -->
        <div id="step1" class="auth-step active">
            <div class="auth-header">
                <i class="fas fa-key icon"></i>
                <h1>找回密码</h1>
                <p>请输入您的邮箱地址，我们将发送重置密码的链接给您</p>
            </div>
            
            <!-- 错误/成功消息 -->
            <div id="messageContainer"></div>
            
            <form id="forgotForm">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" id="email" name="email" class="form-control" placeholder="请输入您注册时使用的邮箱地址" required>
                </div>
                
                <button type="submit" class="btn btn-primary" id="sendBtn">
                    <i class="fas fa-paper-plane"></i>
                    <span class="btn-text">发送重置链接</span>
                </button>
            </form>
            
            <div class="auth-footer">
                <p>记起密码了？</p>
                <a href="login.php" class="auth-link">返回登录</a>
                <span class="mx-2">|</span>
                <a href="register.php" class="auth-link">注册新账户</a>
            </div>
        </div>
        
        <!-- 步骤2：邮件发送成功 -->
        <div id="step2" class="auth-step">
            <div class="auth-header">
                <i class="fas fa-check-circle success-icon"></i>
                <h1>邮件已发送</h1>
                <p>我们已向 <strong id="sentEmail"></strong> 发送了重置密码的邮件</p>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                请检查您的邮箱（包括垃圾邮件文件夹），点击邮件中的链接来重置您的密码。
            </div>
            
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary" id="resendBtn">
                    <i class="fas fa-redo"></i>
                    <span class="btn-text">重新发送邮件</span>
                </button>
                
                <button type="button" class="btn btn-secondary" onclick="goBackToStep1()">
                    <i class="fas fa-arrow-left"></i>
                    返回上一步
                </button>
            </div>
            
            <div class="resend-timer" id="resendTimer"></div>
            
            <div class="auth-footer">
                <p>没有收到邮件？</p>
                <a href="#" class="auth-link" onclick="goBackToStep1()">更换邮箱地址</a>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/auth.js"></script>
    <script>
        let currentEmail = '';
        let resendCountdown = 0;
        let resendTimer = null;
        
        // 初始化表单验证器
        const validator = new FormValidator()
            .addRule('email', value => value.trim() !== '', '邮箱地址不能为空')
            .addRule('email', value => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value), '请输入有效的邮箱地址');
        
        // 显示步骤2
        function showStep2(email) {
            currentEmail = email;
            document.getElementById('sentEmail').textContent = email;
            
            // 更新步骤指示器
            document.getElementById('step1Indicator').classList.remove('active');
            document.getElementById('step1Indicator').classList.add('completed');
            document.getElementById('stepLine1').classList.add('completed');
            document.getElementById('step2Indicator').classList.remove('inactive');
            document.getElementById('step2Indicator').classList.add('active');
            
            // 切换步骤
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step2').classList.add('active');
            
            // 开始重发倒计时
            startResendCountdown();
        }
        
        // 返回步骤1
        function goBackToStep1() {
            // 更新步骤指示器
            document.getElementById('step1Indicator').classList.add('active');
            document.getElementById('step1Indicator').classList.remove('completed');
            document.getElementById('stepLine1').classList.remove('completed');
            document.getElementById('step2Indicator').classList.add('inactive');
            document.getElementById('step2Indicator').classList.remove('active');
            
            // 切换步骤
            document.getElementById('step2').classList.remove('active');
            document.getElementById('step1').classList.add('active');
            
            // 清除消息
            AuthUtils.clearMessage();
            
            // 停止倒计时
            if (resendTimer) {
                clearInterval(resendTimer);
                resendTimer = null;
            }
        }
        
        // 开始重发倒计时
        function startResendCountdown() {
            resendCountdown = 60;
            const resendBtn = document.getElementById('resendBtn');
            const timerElement = document.getElementById('resendTimer');
            
            resendBtn.disabled = true;
            
            resendTimer = setInterval(() => {
                resendCountdown--;
                timerElement.textContent = `${resendCountdown} 秒后可重新发送`;
                
                if (resendCountdown <= 0) {
                    clearInterval(resendTimer);
                    resendBtn.disabled = false;
                    timerElement.textContent = '';
                }
            }, 1000);
        }
        
        // 重新发送邮件
        async function resendEmail() {
            if (!currentEmail) return;
            
            const formData = new FormData();
            formData.append('email', currentEmail);
            
            const success = await AuthUtils.submitForm(
                { querySelector: () => null }, // 虚拟表单元素
                'api/auth.php?action=forgot-password',
                null,
                function(result) {
                    AuthUtils.showMessage('重置邮件已重新发送', 'success');
                    startResendCountdown();
                }
            );
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置实时验证
            AuthUtils.setupRealTimeValidation(validator, ['email']);
            
            // 自动聚焦到邮箱输入框
            const emailField = document.getElementById('email');
            if (emailField) {
                emailField.focus();
            }
            
            // 重新发送按钮事件
            document.getElementById('resendBtn').addEventListener('click', resendEmail);
        });
        
        // 找回密码表单提交
        document.getElementById('forgotForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value.trim();
            if (!email) {
                AuthUtils.showMessage('请输入邮箱地址');
                return;
            }
            
            const success = await AuthUtils.submitForm(
                this,
                'api/auth.php?action=forgot-password',
                validator,
                function(result) {
                    showStep2(email);
                },
                function(error) {
                    // 错误回调 - 添加震动效果
                    const sendBtn = document.getElementById('sendBtn');
                    sendBtn.style.animation = 'shake 0.5s ease-in-out';
                    setTimeout(() => {
                        sendBtn.style.animation = '';
                    }, 500);
                }
            );
        });
        
        // 添加震动动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
