<?php
/**
 * 订单API
 * Orders API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once '../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    // 获取数据库连接
    $db = getDatabase();
    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;

        case 'POST':
            handlePostRequest($db, $input);
            break;

        case 'PUT':
            handlePutRequest($db, $input);
            break;

        case 'DELETE':
            handleDeleteRequest($db, $input);
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误：' . $e->getMessage()]);
}

/**
 * 处理GET请求
 */
function handleGetRequest($db) {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'download_invoice':
            downloadInvoice($_GET['order_id'] ?? '');
            break;

        case 'export':
            exportOrders($db);
            break;

        case 'list':
            listOrders($db);
            break;

        default:
            // 默认返回订单列表
            listOrders($db);
            break;
    }
}

/**
 * 处理POST请求
 */
function handlePostRequest($db, $input) {
    // 检查用户登录
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'create_order':
            createOrder($db, $_SESSION['user_id'], $input);
            break;
            
        case 'cancel_order':
            cancelOrder($db, $_SESSION['user_id'], $input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 创建订单
 */
function createOrder($db, $userId, $input) {
    $contactInfo = $input['contact_info'] ?? [];
    $paymentMethod = $input['payment_method'] ?? 'balance';
    $items = $input['items'] ?? [];
    
    if (empty($items)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '订单商品不能为空']);
        return;
    }
    
    // 验证联系信息
    if (empty($contactInfo['name']) || empty($contactInfo['email'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '姓名和邮箱不能为空']);
        return;
    }
    
    try {
        $db->beginTransaction();
        
        // 计算订单总金额
        $totalAmount = 0;
        foreach ($items as $item) {
            $totalAmount += floatval($item['total_price']);
        }
        
        // 检查用户余额（如果使用余额支付）
        if ($paymentMethod === 'balance') {
            $stmt = $db->prepare("SELECT balance FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user || $user['balance'] < $totalAmount) {
                throw new Exception('余额不足，请先充值');
            }
        }
        
        // 为每个域名创建单独的订单
        $orderIds = [];
        foreach ($items as $item) {
            $orderNumber = 'ORD' . date('YmdHis') . rand(1000, 9999);
            $stmt = $db->prepare("
                INSERT INTO orders (user_id, order_number, order_type, domain_name, period, total_amount, payment_method, contact_info, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
            ");
            $stmt->execute([
                $userId, 
                $orderNumber, 
                $item['type'],
                $item['domain_name'],
                $item['period'],
                $item['total_price'], 
                $paymentMethod, 
                json_encode($contactInfo)
            ]);
            $orderIds[] = $db->lastInsertId();
        }
        
        // 处理支付
        if ($paymentMethod === 'balance') {
            // 扣除余额
            $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
            $stmt->execute([$totalAmount, $userId]);
            
            // 更新所有订单状态
            foreach ($orderIds as $orderId) {
                $stmt = $db->prepare("UPDATE orders SET status = 'completed', paid_at = NOW() WHERE id = ?");
                $stmt->execute([$orderId]);
            }
            
            // 清空购物车
            clearUserCart($db, $userId);
        }
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => '订单创建成功',
            'data' => [
                'order_id' => $orderIds[0], // 返回第一个订单ID
                'order_count' => count($orderIds),
                'total_amount' => $totalAmount,
                'payment_method' => $paymentMethod
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '订单创建失败：' . $e->getMessage()]);
    }
}

/**
 * 取消订单
 */
function cancelOrder($db, $userId, $input) {
    $orderId = intval($input['order_id'] ?? 0);
    
    if ($orderId <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '订单ID无效']);
        return;
    }
    
    try {
        $db->beginTransaction();
        
        // 获取订单信息
        $stmt = $db->prepare("SELECT * FROM orders WHERE id = ? AND user_id = ?");
        $stmt->execute([$orderId, $userId]);
        $order = $stmt->fetch();
        
        if (!$order) {
            throw new Exception('订单不存在');
        }
        
        if ($order['status'] !== 'pending') {
            throw new Exception('只能取消待处理的订单');
        }
        
        // 更新订单状态
        $stmt = $db->prepare("UPDATE orders SET status = 'cancelled', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$orderId]);
        
        // 如果已支付，退还余额
        if ($order['payment_method'] === 'balance' && $order['status'] === 'paid') {
            $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
            $stmt->execute([$order['total_amount'], $userId]);
        }
        
        $db->commit();
        
        echo json_encode([
            'success' => true,
            'message' => '订单已取消'
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '取消失败：' . $e->getMessage()]);
    }
}

/**
 * 清空用户购物车
 */
function clearUserCart($db, $userId) {
    $sessionId = session_id();
    $stmt = $db->prepare("DELETE FROM cart_items WHERE session_id = ? OR user_id = ?");
    $stmt->execute([$sessionId, $userId]);
}

/**
 * 下载发票
 */
function downloadInvoice($orderId) {
    // 这里应该生成真实的PDF发票
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="invoice_' . $orderId . '.pdf"');
    echo "PDF invoice content for order " . $orderId;
}

/**
 * 导出订单
 */
function exportOrders($db) {
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    $status = $_GET['status'] ?? '';
    $timeFilter = $_GET['time'] ?? '';
    
    // 构建查询条件
    $sql = "SELECT * FROM orders WHERE user_id = ?";
    $params = [$userId];
    
    if ($status) {
        $sql .= " AND status = ?";
        $params[] = $status;
    }
    
    if ($timeFilter) {
        switch ($timeFilter) {
            case 'today':
                $sql .= " AND DATE(created_at) = CURDATE()";
                break;
            case 'week':
                $sql .= " AND created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $sql .= " AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $sql .= " AND created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
                break;
        }
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    // 输出CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="orders_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // 输出表头
    fputcsv($output, ['订单号', '类型', '域名', '金额', '状态', '创建时间']);
    
    // 输出数据
    foreach ($orders as $order) {
        fputcsv($output, [
            $order['order_number'],
            $order['order_type'],
            $order['domain_name'] ?? '',
            $order['total_amount'],
            $order['status'],
            $order['created_at']
        ]);
    }
    
    fclose($output);
}

/**
 * 获取订单列表
 */
function listOrders($db) {
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $stmt = $db->prepare("
        SELECT * FROM orders
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ?, ?
    ");
    $stmt->execute([$userId, (int)$offset, (int)$limit]);
    $orders = $stmt->fetchAll();
    
    // 获取总数
    $stmt = $db->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
    $stmt->execute([$userId]);
    $total = $stmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'orders' => $orders,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]
    ]);
}
?>
