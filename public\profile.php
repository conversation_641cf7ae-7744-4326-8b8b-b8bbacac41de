<?php
// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// 加载系统配置
require_once 'config.php';

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // getDatabase() 函数已经在 config.php 中定义了
        $db = getDatabase();
        
        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if ($firstName && $lastName) {
            if ($newPassword) {
                if ($newPassword !== $confirmPassword) {
                    throw new Exception('两次输入的密码不一致');
                }
                if (strlen($newPassword) < 6) {
                    throw new Exception('密码长度至少为6个字符');
                }
                
                $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE users SET first_name = ?, last_name = ?, phone = ?, password_hash = ? WHERE id = ?");
                $stmt->execute([$firstName, $lastName, $phone, $passwordHash, $userId]);
            } else {
                $stmt = $db->prepare("UPDATE users SET first_name = ?, last_name = ?, phone = ? WHERE id = ?");
                $stmt->execute([$firstName, $lastName, $phone, $userId]);
            }
            
            $message = '个人信息更新成功！';
            $_SESSION['user_name'] = trim($firstName . ' ' . $lastName);
        } else {
            $error = '姓名不能为空';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取用户信息
try {
    // getDatabase() 函数已经在 config.php 中定义了
    $db = getDatabase();
    
    $stmt = $db->prepare("SELECT username, first_name, last_name, email, phone, balance, created_at FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $userInfo = $stmt->fetch();
    
    if (!$userInfo) {
        header('Location: login.php');
        exit;
    }
} catch (Exception $e) {
    $error = '无法加载用户信息';
    $userInfo = [
        'username' => '',
        'first_name' => '',
        'last_name' => '',
        'email' => $_SESSION['user_email'] ?? '',
        'phone' => '',
        'balance' => 0,
        'created_at' => date('Y-m-d H:i:s')
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - <?php echo htmlspecialchars($siteName); ?></title>
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .profile-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .profile-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 600;
            margin: 0 auto 1rem;
        }
        
        .form-section {
            padding: 2rem;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-globe me-2"></i>
                <?php echo htmlspecialchars($siteName); ?>
            </a>
            
            <div class="d-flex align-items-center">
                <a href="dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>返回仪表板
                </a>
            </div>
        </div>
    </nav>

    <div class="profile-container">
        <div class="profile-card">
            <!-- 个人资料头部 -->
            <div class="profile-header">
                <div class="profile-avatar">
                    <?= strtoupper(substr($userInfo['first_name'] ?: $userInfo['username'], 0, 1)) ?>
                </div>
                <h2><?= htmlspecialchars(trim($userInfo['first_name'] . ' ' . $userInfo['last_name']) ?: $userInfo['username']) ?></h2>
                <p><?= htmlspecialchars($userInfo['email']) ?></p>
                <small>会员时间：<?= date('Y年m月', strtotime($userInfo['created_at'])) ?></small>
            </div>

            <!-- 表单区域 -->
            <div class="form-section">
                <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($message) ?>
                </div>
                <?php endif; ?>

                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
                <?php endif; ?>

                <form method="POST">
                    <h3 class="section-title">基本信息</h3>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" value="<?= htmlspecialchars($userInfo['username']) ?>" readonly>
                            <div class="form-text">用户名不可修改</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" value="<?= htmlspecialchars($userInfo['email']) ?>" readonly>
                            <div class="form-text">邮箱地址不可修改</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">名字 *</label>
                            <input type="text" class="form-control" name="first_name" value="<?= htmlspecialchars($userInfo['first_name']) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">姓氏 *</label>
                            <input type="text" class="form-control" name="last_name" value="<?= htmlspecialchars($userInfo['last_name']) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">手机号码</label>
                            <input type="tel" class="form-control" name="phone" value="<?= htmlspecialchars($userInfo['phone']) ?>">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">账户余额</label>
                            <input type="text" class="form-control" value="$<?= number_format($userInfo['balance'], 2) ?>" readonly>
                            <div class="form-text">余额由管理员管理</div>
                        </div>
                    </div>

                    <h3 class="section-title">修改密码</h3>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <label class="form-label">新密码</label>
                            <input type="password" class="form-control" name="new_password" placeholder="留空则不修改密码">
                            <div class="form-text">密码长度至少6个字符</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">确认新密码</label>
                            <input type="password" class="form-control" name="confirm_password" placeholder="再次输入新密码">
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 密码确认验证
        document.addEventListener('DOMContentLoaded', function() {
            const newPassword = document.querySelector('input[name="new_password"]');
            const confirmPassword = document.querySelector('input[name="confirm_password"]');
            
            function validatePasswords() {
                if (newPassword.value && confirmPassword.value) {
                    if (newPassword.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('密码不匹配');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
            
            newPassword.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);
        });
    </script>
</body>
</html>
