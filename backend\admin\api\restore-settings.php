<?php
/**
 * 设置恢复API
 * Settings Restore API
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
define('ADMIN_PATH', dirname(__DIR__));

// 引入通用函数
require_once ADMIN_PATH . '/includes/functions.php';

// 设置响应头
header('Content-Type: application/json');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// 检查是否有上传文件
if (!isset($_FILES['backup_file']) || $_FILES['backup_file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => '请选择备份文件']);
    exit;
}

$uploadedFile = $_FILES['backup_file'];

// 检查文件类型
if ($uploadedFile['type'] !== 'application/json' && 
    pathinfo($uploadedFile['name'], PATHINFO_EXTENSION) !== 'json') {
    echo json_encode(['success' => false, 'message' => '只支持JSON格式的备份文件']);
    exit;
}

// 检查文件大小（最大1MB）
if ($uploadedFile['size'] > 1024 * 1024) {
    echo json_encode(['success' => false, 'message' => '备份文件过大']);
    exit;
}

try {
    // 读取备份文件
    $backupContent = file_get_contents($uploadedFile['tmp_name']);
    $backup = json_decode($backupContent, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode(['success' => false, 'message' => '备份文件格式错误']);
        exit;
    }
    
    // 验证备份文件结构
    if (!isset($backup['version']) || !isset($backup['settings']) || !is_array($backup['settings'])) {
        echo json_encode(['success' => false, 'message' => '备份文件结构不正确']);
        exit;
    }
    
    $db = getDatabase();
    
    // 检查system_settings表是否存在
    $tablesCheck = $db->query("SHOW TABLES LIKE 'system_settings'")->fetchAll();
    
    if (empty($tablesCheck)) {
        echo json_encode(['success' => false, 'message' => '系统设置表不存在']);
        exit;
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 清空现有设置（可选，这里我们选择覆盖）
        // $db->exec("DELETE FROM system_settings");
        
        // 恢复设置
        $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`, `group`) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `type` = VALUES(`type`), `group` = VALUES(`group`)");
        
        $restoredCount = 0;
        foreach ($backup['settings'] as $setting) {
            if (isset($setting['key']) && isset($setting['value'])) {
                $stmt->execute([
                    $setting['key'],
                    $setting['value'],
                    $setting['type'] ?? 'string',
                    $setting['group'] ?? 'general'
                ]);
                $restoredCount++;
            }
        }
        
        // 提交事务
        $db->commit();
        
        echo json_encode([
            'success' => true, 
            'message' => "成功恢复 {$restoredCount} 个设置项",
            'restored_count' => $restoredCount
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $db->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '恢复失败: ' . $e->getMessage()]);
}
?>
