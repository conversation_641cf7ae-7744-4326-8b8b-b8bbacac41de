<?php
/**
 * 数据库测试接口
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置JSON响应头
header('Content-Type: application/json');

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) return;
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

loadEnv(ROOT_PATH . '/.env');

// 数据库连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    return $pdo;
}

try {
    $action = $_GET['action'] ?? 'test-connection';
    
    switch ($action) {
        case 'test-connection':
            // 测试数据库连接
            $db = getDatabase();
            $stmt = $db->query("SELECT 1");
            $result = $stmt->fetch();
            
            echo json_encode([
                'success' => true,
                'message' => '数据库连接成功',
                'database' => $_ENV['DB_DATABASE'] ?? 'www_bt_cn'
            ]);
            break;
            
        case 'check-tables':
            // 检查数据表
            $db = getDatabase();
            
            // 检查domain_prices表
            $stmt = $db->query("SHOW TABLES LIKE 'domain_prices'");
            $domainPricesExists = $stmt->fetch() !== false;

            // 获取所有表
            $stmt = $db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // 如果domain_prices表不存在，创建它
            if (!$domainPricesExists) {
                $createTableSQL = "
                CREATE TABLE `domain_prices` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `tld` varchar(50) NOT NULL,
                    `registration_price` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `renewal_price` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `transfer_price` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `category` varchar(50) DEFAULT 'generic',
                    `enabled` tinyint(1) DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `tld` (`tld`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";

                $db->exec($createTableSQL);
                $tables[] = 'domain_prices (新创建)';
            }

            echo json_encode([
                'success' => true,
                'message' => '数据表检查完成',
                'tables' => $tables,
                'domain_prices_exists' => $domainPricesExists
            ]);
            break;
            
        case 'view-prices':
            // 查看价格数据
            $db = getDatabase();

            // 检查表是否存在
            $stmt = $db->query("SHOW TABLES LIKE 'domain_prices'");
            if (!$stmt->fetch()) {
                throw new Exception('domain_prices表不存在');
            }

            // 获取价格数据
            $stmt = $db->query("SELECT * FROM domain_prices ORDER BY tld");
            $prices = $stmt->fetchAll();

            // 获取总数
            $stmt = $db->query("SELECT COUNT(*) as total FROM domain_prices");
            $total = $stmt->fetch()['total'];

            echo json_encode([
                'success' => true,
                'message' => '价格数据获取成功',
                'total' => $total,
                'prices' => $prices
            ]);
            break;
            
        case 'clear-prices':
            // 清空价格数据
            $db = getDatabase();

            // 检查表是否存在
            $stmt = $db->query("SHOW TABLES LIKE 'domain_prices'");
            if (!$stmt->fetch()) {
                throw new Exception('domain_prices表不存在');
            }

            $stmt = $db->prepare("DELETE FROM domain_prices");
            $stmt->execute();
            $deletedCount = $stmt->rowCount();

            echo json_encode([
                'success' => true,
                'message' => "已清空 {$deletedCount} 条价格数据",
                'deleted_count' => $deletedCount
            ]);
            break;

        default:
            throw new Exception('不支持的操作');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
