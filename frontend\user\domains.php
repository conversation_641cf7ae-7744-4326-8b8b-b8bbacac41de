<?php
/**
 * 域名管理页面
 * Domain Management
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '域名管理';

// 获取用户域名列表
function getUserDomains($userId) {
    try {
        $db = getDatabase();

        // 从数据库获取用户的域名
        $stmt = $db->prepare("
            SELECT
                id,
                domain_name,
                status,
                registration_date,
                expiry_date,
                auto_renew,
                tld,
                price,
                featured,
                category,
                keywords,
                description,
                nameservers,
                created_at,
                updated_at
            FROM domains
            WHERE user_id = ?
            ORDER BY domain_name ASC
        ");
        $stmt->execute([$userId]);
        $domains = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 处理数据格式
        foreach ($domains as &$domain) {
            // 设置DNS状态（简化处理）
            $domain['dns_status'] = 'active';
            $domain['registrar'] = 'NameSilo';

            // 处理日期格式
            if ($domain['registration_date']) {
                $domain['registration_date'] = date('Y-m-d', strtotime($domain['registration_date']));
            }
            if ($domain['expiry_date']) {
                $domain['expiry_date'] = date('Y-m-d', strtotime($domain['expiry_date']));
            }

            // 转换布尔值
            $domain['auto_renew'] = (bool)$domain['auto_renew'];
            $domain['featured'] = (bool)$domain['featured'];

            // 解析nameservers
            $domain['nameservers_array'] = $domain['nameservers']
                ? (json_decode($domain['nameservers'], true) ?: [])
                : [];
        }

        return $domains;

    } catch (Exception $e) {
        error_log("获取用户域名失败: " . $e->getMessage());
        return [];
    }
}

$domains = getUserDomains($userId);

// 调试信息
if (empty($domains)) {
    error_log("用户 {$userId} 没有域名或查询失败");
}

// 检查是否有错误需要显示
$errorMessage = '';
if (isset($_GET['error'])) {
    $errorMessage = htmlspecialchars($_GET['error']);
}

// 开始输出缓冲
ob_start();
?>

<?php if ($errorMessage): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    操作失败：<?= $errorMessage ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">域名管理</h1>
            <p class="page-subtitle">管理您的域名组合和设置</p>
        </div>
        <div class="d-flex gap-2">
            <button id="syncDomainsBtn" class="btn btn-outline-primary" onclick="syncDomains()">
                <i class="fas fa-sync me-2"></i>
                同步域名
            </button>
            <a href="../index.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                注册新域名
            </a>
        </div>
    </div>
</div>

<!-- 域名统计 -->
<div class="row g-4 mb-4">
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                <i class="fas fa-globe"></i>
            </div>
            <h3 class="h4 mb-1" id="totalDomains"><?= count($domains) ?></h3>
            <p class="text-muted mb-0">总域名数</p>
        </div>
    </div>

    <div class="col-md-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card">
            <div class="stats-icon bg-success bg-opacity-10 text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="h4 mb-1" id="activeDomains"><?= count(array_filter($domains, fn($d) => in_array($d['status'], ['registered', 'active']))) ?></h3>
            <p class="text-muted mb-0">活跃域名</p>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="h4 mb-1" id="expiringSoon">
                <?php
                $expiringSoon = 0;
                foreach ($domains as $domain) {
                    if (!empty($domain['expiry_date'])) {
                        $expiryTimestamp = strtotime($domain['expiry_date']);
                        if ($expiryTimestamp !== false) {
                            $daysUntilExpiry = ($expiryTimestamp - time()) / (60 * 60 * 24);
                            if ($daysUntilExpiry <= 30 && $daysUntilExpiry > 0) $expiringSoon++;
                        }
                    }
                }
                echo $expiringSoon;
                ?>
            </h3>
            <p class="text-muted mb-0">即将到期</p>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-icon bg-info bg-opacity-10 text-info">
                <i class="fas fa-sync"></i>
            </div>
            <h3 class="h4 mb-1" id="autoRenewEnabled"><?= count(array_filter($domains, fn($d) => $d['auto_renew'])) ?></h3>
            <p class="text-muted mb-0">自动续费</p>
        </div>
    </div>
</div>

<!-- 域名列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            我的域名
        </h5>
        <div class="d-flex gap-2">
            <div class="input-group" style="width: 300px;">
                <input type="text" class="form-control" placeholder="搜索域名..." id="domainSearch">
                <button class="btn btn-outline-secondary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-filter me-2"></i>筛选
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="filterDomains('all')">全部域名</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterDomains('active')">活跃域名</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterDomains('expiring')">即将到期</a></li>
                    <li><a class="dropdown-item" href="#" onclick="filterDomains('auto-renew')">自动续费</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($domains)): ?>
            <div class="text-center py-5">
                <i class="fas fa-globe fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">暂无域名</h4>
                <p class="text-muted mb-4">您还没有注册任何域名，立即开始搜索并注册您的第一个域名吧！</p>
                <a href="../index.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>
                    搜索域名
                </a>
            </div>
        <?php else: ?>
            <!-- 批量操作工具栏 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary" onclick="performBatchAction('续费')">
                        <i class="fas fa-sync me-1"></i>批量续费
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="performBatchAction('开启自动续费')">
                        <i class="fas fa-toggle-on me-1"></i>开启自动续费
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="performBatchAction('关闭自动续费')">
                        <i class="fas fa-toggle-off me-1"></i>关闭自动续费
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="performBatchAction('删除')">
                        <i class="fas fa-trash me-1"></i>批量删除
                    </button>
                </div>
                <div>
                    <small class="text-muted">选择域名后可进行批量操作</small>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>域名</th>
                            <th>状态</th>
                            <th>注册日期</th>
                            <th>到期日期</th>
                            <th>自动续费</th>
                            <th>DNS状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($domains as $domain): ?>
                        <?php
                        $expiryTimestamp = !empty($domain['expiry_date']) ? strtotime($domain['expiry_date']) : false;
                        $daysUntilExpiry = $expiryTimestamp ? ($expiryTimestamp - time()) / (60 * 60 * 24) : 0;
                        $isExpiringSoon = $daysUntilExpiry <= 30 && $daysUntilExpiry > 0;
                        ?>
                        <tr class="domain-row" data-domain="<?= htmlspecialchars($domain['domain_name']) ?>">
                            <td>
                                <input type="checkbox" name="selected_domains" value="<?= htmlspecialchars($domain['domain_name']) ?>" class="form-check-input">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-globe text-primary me-2"></i>
                                    <div>
                                        <strong><?= htmlspecialchars($domain['domain_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($domain['registrar']) ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusClass = match($domain['status']) {
                                    'registered', 'active' => 'success',
                                    'expired' => 'danger',
                                    'suspended' => 'warning',
                                    'transferred' => 'info',
                                    'pending' => 'warning',
                                    default => 'secondary'
                                };
                                $statusText = match($domain['status']) {
                                    'registered', 'active' => '正常',
                                    'expired' => '已过期',
                                    'suspended' => '暂停',
                                    'transferred' => '已转移',
                                    'pending' => '处理中',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <?= !empty($domain['registration_date']) ? date('Y-m-d', strtotime($domain['registration_date'])) : '-' ?>
                                </span>
                            </td>
                            <td>
                                <div>
                                    <span class="<?= $isExpiringSoon ? 'text-warning fw-bold' : '' ?>">
                                        <?= !empty($domain['expiry_date']) ? date('Y-m-d', strtotime($domain['expiry_date'])) : '-' ?>
                                    </span>
                                    <?php if ($isExpiringSoon): ?>
                                        <br>
                                        <small class="text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <?= ceil($daysUntilExpiry) ?>天后到期
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" 
                                           <?= $domain['auto_renew'] ? 'checked' : '' ?>
                                           onchange="toggleAutoRenew(<?= $domain['id'] ?>, this.checked)">
                                </div>
                            </td>
                            <td>
                                <?php
                                $dnsClass = $domain['dns_status'] === 'active' ? 'success' : 'warning';
                                $dnsText = $domain['dns_status'] === 'active' ? '正常' : '异常';
                                ?>
                                <span class="badge bg-<?= $dnsClass ?>"><?= $dnsText ?></span>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        操作
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="manageDNS('<?= $domain['domain_name'] ?>')">
                                            <i class="fas fa-cog me-2"></i>DNS管理
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="renewDomain('<?= $domain['domain_name'] ?>')">
                                            <i class="fas fa-sync me-2"></i>续费
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="transferDomain('<?= $domain['domain_name'] ?>')">
                                            <i class="fas fa-exchange-alt me-2"></i>转移
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="toggleAutoRenew('<?= $domain['domain_name'] ?>', <?= $domain['auto_renew'] ? 'true' : 'false' ?>)">
                                            <i class="fas fa-<?= $domain['auto_renew'] ? 'toggle-on' : 'toggle-off' ?> me-2"></i>
                                            <?= $domain['auto_renew'] ? '关闭' : '开启' ?>自动续费
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="viewDomainInfo('<?= $domain['domain_name'] ?>')">
                                            <i class="fas fa-info-circle me-2"></i>详细信息
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteDomain('<?= $domain['domain_name'] ?>')">
                                            <i class="fas fa-trash me-2"></i>删除
                                        </a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 域名操作模态框 -->
<div class="modal fade" id="domainActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">域名操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>
</div>

<script>
// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 加载统计数据
    loadDomainStats();

    // 每5分钟刷新一次统计数据
    setInterval(loadDomainStats, 300000);
    // 域名搜索
    const domainSearchInput = document.getElementById('domainSearch');
    if (domainSearchInput) {
        domainSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('.domain-row');

            rows.forEach(row => {
                const domainName = row.dataset.domain.toLowerCase();
                if (domainName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // 筛选功能
    document.querySelectorAll('[data-filter]').forEach(filter => {
        filter.addEventListener('click', function(e) {
            e.preventDefault();
            const filterType = this.dataset.filter;
            const rows = document.querySelectorAll('.domain-row');

            rows.forEach(row => {
                row.style.display = '';
            });

            // 这里可以根据需要实现具体的筛选逻辑
        });
    });
});

// 加载域名统计数据
function loadDomainStats() {
    fetch('../api/domains.php?action=user_stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.data;
                document.getElementById('totalDomains').textContent = stats.total_domains;
                document.getElementById('activeDomains').textContent = stats.active_domains;
                document.getElementById('expiringSoon').textContent = stats.expiring_soon;
                document.getElementById('autoRenewEnabled').textContent = stats.auto_renew_enabled;
            }
        })
        .catch(error => {
            console.error('加载统计数据失败:', error);
        });
}

// 同步域名
function syncDomains() {
    const btn = document.getElementById('syncDomainsBtn');
    const originalText = btn.innerHTML;

    // 禁用按钮并显示加载状态
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在同步...';

    fetch('../api/domains.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'sync_user_domains'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);

            // 如果有新域名同步，刷新页面
            if (data.synced_count > 0) {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        } else {
            showAlert('danger', '同步失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('danger', '网络错误：' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// 显示提示信息
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 切换自动续费
function toggleAutoRenew(domainId, enabled) {
    // 这里应该发送AJAX请求到后端
    console.log(`域名 ${domainId} 自动续费设置为: ${enabled}`);
    
    // 模拟API调用
    fetch('../api/domains.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'toggle_auto_renew',
            domain_id: domainId,
            enabled: enabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '自动续费设置已更新');
        } else {
            showAlert('error', '操作失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误');
    });
}

// DNS管理
function manageDNS(domainName) {
    document.getElementById('modalTitle').textContent = `DNS管理 - ${domainName}`;
    document.getElementById('modalBody').innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h6 class="mb-1">域名: <strong>${domainName}</strong></h6>
                <small class="text-muted">您可以在这里管理域名的DNS记录，包括A记录、CNAME记录、MX记录等。</small>
            </div>
            <button class="btn btn-primary btn-sm" onclick="showAddDNSRecord('${domainName}')">
                <i class="fas fa-plus me-1"></i>
                添加记录
            </button>
        </div>

        <div id="dnsRecordsContainer">
            <div class="text-center py-3">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 mb-0">正在加载DNS记录...</p>
            </div>
        </div>
    `;

    new bootstrap.Modal(document.getElementById('domainActionModal')).show();

    // 加载DNS记录
    loadDNSRecords(domainName);
}

// 续费域名
function renewDomain(domainId) {
    document.getElementById('modalTitle').textContent = '域名续费';
    document.getElementById('modalBody').innerHTML = `
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            域名续费功能正在开发中...
        </div>
        <p>域名ID: <strong>${domainId}</strong></p>
        <p>续费功能将允许您延长域名的有效期。</p>
    `;
    new bootstrap.Modal(document.getElementById('domainActionModal')).show();
}

// 转移域名
function transferDomain(domainName) {
    document.getElementById('modalTitle').textContent = `域名转移 - ${domainName}`;
    document.getElementById('modalBody').innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            域名转移功能正在开发中...
        </div>
        <p>域名: <strong>${domainName}</strong></p>
        <p>域名转移功能将允许您将域名转移到其他注册商。</p>
    `;
    new bootstrap.Modal(document.getElementById('domainActionModal')).show();
}

// 删除域名
function deleteDomain(domainId) {
    if (confirm('确定要删除这个域名吗？此操作不可撤销！')) {
        // 这里应该发送删除请求
        console.log(`删除域名 ${domainId}`);
        showAlert('info', '删除功能正在开发中...');
    }
}

// 加载DNS记录
function loadDNSRecords(domainName) {
    fetch(`../api/dns.php?domain=${encodeURIComponent(domainName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDNSRecords(data.data, domainName);
            } else {
                document.getElementById('dnsRecordsContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        加载失败：${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('dnsRecordsContainer').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    网络错误，请稍后重试
                </div>
            `;
        });
}

// 显示DNS记录
function displayDNSRecords(records, domainName) {
    if (records.length === 0) {
        document.getElementById('dnsRecordsContainer').innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-dns fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">暂无DNS记录</h6>
                <p class="text-muted mb-3">还没有配置任何DNS记录</p>
                <button class="btn btn-primary" onclick="showAddDNSRecord('${domainName}')">
                    <i class="fas fa-plus me-2"></i>
                    添加第一条记录
                </button>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>类型</th>
                        <th>名称</th>
                        <th>值</th>
                        <th>TTL</th>
                        <th>优先级</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    records.forEach(record => {
        const typeClass = getRecordTypeClass(record.type);
        html += `
            <tr>
                <td>
                    <span class="badge ${typeClass}">${record.type}</span>
                </td>
                <td>
                    <code>${record.name}</code>
                </td>
                <td>
                    <span class="text-break">${record.value}</span>
                </td>
                <td>
                    <small class="text-muted">${record.ttl}s</small>
                </td>
                <td>
                    ${record.priority ? record.priority : '-'}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editDNSRecord(${record.id}, '${domainName}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteDNSRecord(${record.id}, '${domainName}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('dnsRecordsContainer').innerHTML = html;
}

// 获取记录类型样式
function getRecordTypeClass(type) {
    const classes = {
        'A': 'bg-primary',
        'AAAA': 'bg-info',
        'CNAME': 'bg-success',
        'MX': 'bg-warning',
        'TXT': 'bg-secondary',
        'NS': 'bg-dark'
    };
    return classes[type] || 'bg-secondary';
}

// 显示添加DNS记录表单
function showAddDNSRecord(domainName) {
    const modalHtml = `
        <div class="modal fade" id="addDNSModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-plus me-2"></i>
                            添加DNS记录
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="addDNSForm">
                        <div class="modal-body">
                            <input type="hidden" name="domain" value="${domainName}">

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">记录类型</label>
                                    <select class="form-select" name="type" required onchange="handleTypeChange(this.value)">
                                        <option value="">请选择</option>
                                        <option value="A">A - IPv4地址</option>
                                        <option value="AAAA">AAAA - IPv6地址</option>
                                        <option value="CNAME">CNAME - 别名</option>
                                        <option value="MX">MX - 邮件交换</option>
                                        <option value="TXT">TXT - 文本记录</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">名称</label>
                                    <input type="text" class="form-control" name="name" placeholder="@" required>
                                    <small class="text-muted">使用@表示根域名</small>
                                </div>
                                <div class="col-12">
                                    <label class="form-label">值</label>
                                    <input type="text" class="form-control" name="value" required>
                                    <small class="text-muted" id="valueHint">请输入记录值</small>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">TTL (秒)</label>
                                    <select class="form-select" name="ttl">
                                        <option value="300">5分钟</option>
                                        <option value="1800">30分钟</option>
                                        <option value="3600" selected>1小时</option>
                                        <option value="14400">4小时</option>
                                        <option value="86400">1天</option>
                                    </select>
                                </div>
                                <div class="col-md-6" id="priorityField" style="display: none;">
                                    <label class="form-label">优先级</label>
                                    <input type="number" class="form-control" name="priority" min="0" max="65535">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                添加记录
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('addDNSModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定表单提交事件
    document.getElementById('addDNSForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitDNSRecord(this, domainName);
    });

    // 显示模态框
    new bootstrap.Modal(document.getElementById('addDNSModal')).show();
}

// 处理记录类型变化
function handleTypeChange(type) {
    const priorityField = document.getElementById('priorityField');
    const valueHint = document.getElementById('valueHint');

    if (type === 'MX') {
        priorityField.style.display = 'block';
        valueHint.textContent = '请输入邮件服务器地址，如：mail.namesilo-namesilo-sample.com';
    } else {
        priorityField.style.display = 'none';

        switch (type) {
            case 'A':
                valueHint.textContent = '请输入IPv4地址，如：***********';
                break;
            case 'AAAA':
                valueHint.textContent = '请输入IPv6地址，如：2001:db8::1';
                break;
            case 'CNAME':
                valueHint.textContent = '请输入目标域名，如：www.namesilo-namesilo-sample.com';
                break;
            case 'TXT':
                valueHint.textContent = '请输入文本内容，如：v=spf1 include:_spf.namesilo-namesilo-sample.com ~all';
                break;
            default:
                valueHint.textContent = '请输入记录值';
        }
    }
}

// 提交DNS记录
function submitDNSRecord(form, domainName) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    fetch('../api/dns.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addDNSModal')).hide();
            showAlert('success', 'DNS记录添加成功');
            loadDNSRecords(domainName);
        } else {
            showAlert('error', '添加失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误，请稍后重试');
    });
}

// 编辑DNS记录
function editDNSRecord(recordId, domainName) {
    showAlert('info', '编辑功能正在开发中...');
}

// 删除DNS记录
function deleteDNSRecord(recordId, domainName) {
    if (confirm('确定要删除这条DNS记录吗？此操作不可撤销！')) {
        fetch('../api/dns.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: recordId,
                domain: domainName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'DNS记录删除成功');
                loadDNSRecords(domainName);
            } else {
                showAlert('error', '删除失败：' + data.message);
            }
        })
        .catch(error => {
            showAlert('error', '网络错误，请稍后重试');
        });
    }
}

// 域名续费
function renewDomain(domainName) {
    const years = prompt('请输入续费年数 (1-10):', '1');
    if (!years || isNaN(years) || years < 1 || years > 10) {
        showAlert('error', '请输入有效的续费年数 (1-10)');
        return;
    }

    if (!confirm(`确定要为域名 ${domainName} 续费 ${years} 年吗？`)) {
        return;
    }

    fetch('/api/domains.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'renew',
            domain: domainName,
            years: parseInt(years)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('error', '续费失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误，请稍后重试');
    });
}

// 域名转移
function transferDomain(domainName) {
    const authCode = prompt('请输入域名授权码 (Auth Code):');
    if (!authCode) {
        showAlert('error', '授权码不能为空');
        return;
    }

    if (!confirm(`确定要转移域名 ${domainName} 吗？`)) {
        return;
    }

    fetch('/api/domains.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'transfer',
            domain: domainName,
            auth_code: authCode
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('error', '转移失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误，请稍后重试');
    });
}

// 切换自动续费
function toggleAutoRenew(domainName, currentStatus) {
    const newStatus = !currentStatus;
    const action = newStatus ? '开启' : '关闭';

    if (!confirm(`确定要${action}域名 ${domainName} 的自动续费吗？`)) {
        return;
    }

    fetch('/api/domains.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'toggle_auto_renew',
            domain: domainName,
            auto_renew: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('error', '操作失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误，请稍后重试');
    });
}

// 搜索域名
function searchDomains() {
    const searchTerm = document.getElementById('domainSearch').value.toLowerCase();
    const rows = document.querySelectorAll('.domain-row');

    rows.forEach(row => {
        const domainName = row.dataset.domain.toLowerCase();
        if (domainName.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 筛选域名
function filterDomains(status) {
    const rows = document.querySelectorAll('.domain-row');

    rows.forEach(row => {
        if (status === 'all') {
            row.style.display = '';
        } else {
            const statusBadge = row.querySelector('.badge');
            const rowStatus = statusBadge ? statusBadge.textContent.trim() : '';
            const autoRenewToggle = row.querySelector('.form-check-input[type="checkbox"]:not([name="selected_domains"])');
            const isAutoRenew = autoRenewToggle ? autoRenewToggle.checked : false;

            if (status === 'active' && rowStatus === '正常') {
                row.style.display = '';
            } else if (status === 'expiring' && rowStatus === '即将到期') {
                row.style.display = '';
            } else if (status === 'expired' && rowStatus === '已过期') {
                row.style.display = '';
            } else if (status === 'auto-renew' && isAutoRenew) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    });
}

// 查看域名详细信息
function viewDomainInfo(domainName) {
    fetch(`/api/domains.php?action=info&domain=${encodeURIComponent(domainName)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const domain = data.domain;
            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>域名</td><td>${domain.domain_name}</td></tr>
                            <tr><td>状态</td><td><span class="badge bg-${domain.status === 'active' ? 'success' : 'warning'}">${domain.status}</span></td></tr>
                            <tr><td>注册商</td><td>${domain.registrar || 'NameSilo'}</td></tr>
                            <tr><td>注册日期</td><td>${domain.registration_date || '-'}</td></tr>
                            <tr><td>到期日期</td><td>${domain.expiry_date || '-'}</td></tr>
                            <tr><td>自动续费</td><td>${domain.auto_renew ? '已开启' : '已关闭'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>DNS信息</h6>
                        <table class="table table-sm">
                            <tr><td>DNS状态</td><td><span class="badge bg-${domain.dns_status === 'active' ? 'success' : 'warning'}">${domain.dns_status || 'active'}</span></td></tr>
                            <tr><td>名称服务器</td><td>${domain.nameservers || 'NameSilo默认'}</td></tr>
                        </table>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-primary" onclick="manageDNS('${domainName}')">
                                <i class="fas fa-cog me-1"></i>管理DNS
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('modalTitle').textContent = `域名信息 - ${domainName}`;
            new bootstrap.Modal(document.getElementById('domainActionModal')).show();
        } else {
            showAlert('error', '获取域名信息失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误，请稍后重试');
    });
}

// 删除域名
function deleteDomain(domainName) {
    if (!confirm(`确定要删除域名 ${domainName} 吗？此操作不可撤销！`)) {
        return;
    }

    fetch('/api/domains.php', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'delete',
            domain: domainName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', '域名删除成功');
            setTimeout(() => location.reload(), 2000);
        } else {
            showAlert('error', '删除失败：' + data.message);
        }
    })
    .catch(error => {
        showAlert('error', '网络错误，请稍后重试');
    });
}

// 批量操作
function performBatchAction(action) {
    const checkedBoxes = document.querySelectorAll('input[name="selected_domains"]:checked');
    if (checkedBoxes.length === 0) {
        showAlert('error', '请选择要操作的域名');
        return;
    }

    const domains = Array.from(checkedBoxes).map(cb => cb.value);

    if (!confirm(`确定要对选中的 ${domains.length} 个域名执行 ${action} 操作吗？`)) {
        return;
    }

    // 根据不同的action执行不同的批量操作
    switch(action) {
        case '续费':
            batchRenewDomains(domains);
            break;
        case '开启自动续费':
            batchToggleAutoRenew(domains, true);
            break;
        case '关闭自动续费':
            batchToggleAutoRenew(domains, false);
            break;
        case '删除':
            batchDeleteDomains(domains);
            break;
        default:
            showAlert('info', `批量${action}功能开发中...`);
    }
}

// 批量续费
function batchRenewDomains(domains) {
    const years = prompt('请输入续费年数 (1-10):', '1');
    if (!years || isNaN(years) || years < 1 || years > 10) {
        showAlert('error', '请输入有效的续费年数 (1-10)');
        return;
    }

    showAlert('info', '正在处理批量续费...');

    // 这里应该实现批量续费逻辑
    setTimeout(() => {
        showAlert('success', `已为 ${domains.length} 个域名提交续费请求`);
    }, 2000);
}

// 批量切换自动续费
function batchToggleAutoRenew(domains, enabled) {
    showAlert('info', `正在${enabled ? '开启' : '关闭'}自动续费...`);

    // 这里应该实现批量切换自动续费逻辑
    setTimeout(() => {
        showAlert('success', `已为 ${domains.length} 个域名${enabled ? '开启' : '关闭'}自动续费`);
    }, 2000);
}

// 显示提示消息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.content-area');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
    } else {
        console.error('找不到 .content-area 容器');
    }
}

// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 搜索框事件
    const searchInput = document.getElementById('domainSearch');
    if (searchInput) {
        searchInput.addEventListener('input', searchDomains);
    }

    // 全选/取消全选
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="selected_domains"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });
    }
});
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
