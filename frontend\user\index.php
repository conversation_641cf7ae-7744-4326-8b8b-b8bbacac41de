<?php
/**
 * 用户中心主页 - 仪表盘
 * User Dashboard
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '仪表盘';

// 获取用户统计数据
function getUserStats($userId) {
    try {
        $db = getDatabase();

        // 订单统计
        $stmt = $db->prepare("SELECT COUNT(*) as total,
                                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
                             FROM orders WHERE user_id = ?");
        $stmt->execute([$userId]);
        $orderStats = $stmt->fetch();

        // 域名统计
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM domains WHERE user_id = ?");
        $stmt->execute([$userId]);
        $totalDomains = $stmt->fetchColumn();

        $stmt = $db->prepare("SELECT COUNT(*) as active FROM domains WHERE user_id = ? AND status IN ('registered', 'active')");
        $stmt->execute([$userId]);
        $activeDomains = $stmt->fetchColumn();

        $stmt = $db->prepare("SELECT COUNT(*) as expiring FROM domains WHERE user_id = ? AND expiry_date <= DATE_ADD(NOW(), INTERVAL 30 DAY)");
        $stmt->execute([$userId]);
        $expiringDomains = $stmt->fetchColumn();

        $domainStats = [
            'total' => $totalDomains,
            'active' => $activeDomains,
            'expiring' => $expiringDomains
        ];

        // 最近订单
        $stmt = $db->prepare("SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
        $stmt->execute([$userId]);
        $recentOrders = $stmt->fetchAll();

        // 账户余额变动记录（如果有的话）
        $balanceHistory = [];

        return [
            'orders' => $orderStats ?: ['total' => 0, 'completed' => 0, 'pending' => 0],
            'domains' => $domainStats,
            'recent_orders' => $recentOrders,
            'balance_history' => $balanceHistory
        ];

    } catch (Exception $e) {
        return [
            'orders' => ['total' => 0, 'completed' => 0, 'pending' => 0],
            'domains' => ['total' => 0, 'active' => 0, 'expiring' => 0],
            'recent_orders' => [],
            'balance_history' => []
        ];
    }
}

$stats = getUserStats($userId);

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <h1 class="page-title">仪表盘</h1>
    <p class="page-subtitle">欢迎回到您的账户中心，这里是您的活动概览</p>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="h4 mb-1"><?= $stats['orders']['total'] ?></h3>
            <p class="text-muted mb-0">总订单数</p>
            <small class="text-success">
                <i class="fas fa-check-circle me-1"></i>
                <?= $stats['orders']['completed'] ?> 已完成
            </small>
        </div>
    </div>

    <div class="col-md-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card">
            <div class="stats-icon bg-success bg-opacity-10 text-success">
                <i class="fas fa-globe"></i>
            </div>
            <h3 class="h4 mb-1"><?= $stats['domains']['total'] ?></h3>
            <p class="text-muted mb-0">域名总数</p>
            <small class="text-warning">
                <i class="fas fa-clock me-1"></i>
                <?= $stats['domains']['expiring'] ?> 即将到期
            </small>
        </div>
    </div>

    <div class="col-md-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="h4 mb-1"><?= $stats['orders']['pending'] ?></h3>
            <p class="text-muted mb-0">待处理订单</p>
            <small class="text-info">
                <i class="fas fa-info-circle me-1"></i>
                需要您的关注
            </small>
        </div>
    </div>

    <div class="col-md-3" data-aos="fade-up" data-aos-delay="400">
        <div class="stats-card">
            <div class="stats-icon bg-info bg-opacity-10 text-info">
                <i class="fas fa-shield-check"></i>
            </div>
            <h3 class="h4 mb-1"><?= $stats['domains']['active'] ?></h3>
            <p class="text-muted mb-0">活跃域名</p>
            <small class="text-success">
                <i class="fas fa-check me-1"></i>
                正常运行
            </small>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- 最近订单 -->
    <div class="col-lg-8" data-aos="fade-up" data-aos-delay="500">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    最近订单
                </h5>
                <a href="orders.php" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                <?php if (empty($stats['recent_orders'])): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">暂无订单记录</h6>
                        <p class="text-muted mb-3">您还没有任何订单，开始购买域名吧！</p>
                        <a href="../index.php" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            搜索域名
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>域名</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>日期</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stats['recent_orders'] as $order): ?>
                                <tr>
                                    <td>
                                        <span class="font-monospace">#<?= htmlspecialchars($order['order_number'] ?? $order['id']) ?></span>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($order['domain_name'] ?? '未知域名') ?></strong>
                                    </td>
                                    <td>
                                        <span class="text-success">¥<?= number_format($order['total_amount'] ?? 0, 2) ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $order['status'] ?? 'unknown';
                                        $statusClass = match($status) {
                                            'completed' => 'success',
                                            'pending' => 'warning',
                                            'failed' => 'danger',
                                            'cancelled' => 'secondary',
                                            default => 'secondary'
                                        };
                                        $statusText = match($status) {
                                            'completed' => '已完成',
                                            'pending' => '待处理',
                                            'failed' => '失败',
                                            'cancelled' => '已取消',
                                            default => '未知'
                                        };
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('Y-m-d H:i', strtotime($order['created_at'])) ?>
                                        </small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4" data-aos="fade-up" data-aos-delay="600">
        <div class="card mb-4">
            <div class="card-header d-flex align-items-center">
                <i class="fas fa-bolt text-primary me-2"></i>
                <h5 class="card-title mb-0">快速操作</h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <a href="../index.php" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-circle bg-primary bg-opacity-10 text-primary">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">搜索域名</h6>
                            <small class="text-muted">查找可用域名</small>
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </a>
                    <a href="domains.php" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-circle bg-success bg-opacity-10 text-success">
                                <i class="fas fa-globe"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">管理域名</h6>
                            <small class="text-muted">域名设置与管理</small>
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </a>
                    <a href="billing.php" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-circle bg-warning bg-opacity-10 text-warning">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">账户充值</h6>
                            <small class="text-muted">增加账户余额</small>
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </a>
                    <a href="support.php" class="list-group-item list-group-item-action d-flex align-items-center">
                        <div class="me-3">
                            <div class="icon-circle bg-info bg-opacity-10 text-info">
                                <i class="fas fa-life-ring"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">联系支持</h6>
                            <small class="text-muted">获取技术帮助</small>
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- 账户信息 -->
        <div class="card" data-aos="fade-up" data-aos-delay="700">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    账户信息
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">注册时间</small>
                    <div class="fw-medium">
                        <?php
                        try {
                            $db = getDatabase();
                            $stmt = $db->prepare("SELECT created_at FROM users WHERE id = ?");
                            $stmt->execute([$userId]);
                            $user = $stmt->fetch();
                            echo $user ? date('Y年m月d日', strtotime($user['created_at'])) : '未知';
                        } catch (Exception $e) {
                            echo '未知';
                        }
                        ?>
                    </div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">账户状态</small>
                    <div>
                        <span class="badge bg-success">正常</span>
                    </div>
                </div>
                <div class="mb-0">
                    <small class="text-muted">安全等级</small>
                    <div class="d-flex align-items-center">
                        <div class="progress flex-grow-1 me-2" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 60%"></div>
                        </div>
                        <small class="text-warning">中等</small>
                    </div>
                    <small class="text-muted">
                        <a href="security.php" class="text-decoration-none">提升安全等级</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统通知 -->
<div class="row mt-4">
    <div class="col-12" data-aos="fade-up" data-aos-delay="800">
        <div class="card modern-card">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    系统通知
                </h5>
            </div>
            <div class="card-body">
                <div class="notification-item">
                    <div class="notification-icon bg-info">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notification-content">
                        <h6>欢迎使用NameSilo域名销售系统！</h6>
                        <p>如果您在使用过程中遇到任何问题，请随时联系我们的客服团队。我们提供7x24小时技术支持服务。</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            <?= date('Y-m-d H:i') ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<style>
/* 快速操作图标样式 */
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

/* 快速操作项悬停效果 */
.list-group-item-action:hover {
    background-color: var(--bs-gray-50);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

.list-group-item-action:hover .icon-circle {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* 统计卡片样式优化 */
.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 1rem;
}
</style>
<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
