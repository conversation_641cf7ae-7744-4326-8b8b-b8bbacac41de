<?php
/**
 * 账户安全页面
 * Account Security
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '账户安全';
$message = '';
$error = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDatabase();
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    throw new Exception('所有密码字段都不能为空');
                }
                
                if ($newPassword !== $confirmPassword) {
                    throw new Exception('新密码和确认密码不一致');
                }
                
                if (strlen($newPassword) < 8) {
                    throw new Exception('新密码长度至少为8个字符');
                }
                
                // 验证当前密码
                $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                
                if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
                    throw new Exception('当前密码不正确');
                }
                
                // 更新密码
                $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$newPasswordHash, $userId]);
                
                $message = '密码修改成功！';
                break;
                
            case 'update_security_settings':
                $twoFactorEnabled = isset($_POST['two_factor_enabled']) ? 1 : 0;
                $loginNotification = isset($_POST['login_notification']) ? 1 : 0;
                $securityQuestion = trim($_POST['security_question'] ?? '');
                $securityAnswer = trim($_POST['security_answer'] ?? '');
                
                // 更新安全设置到数据库
                $db = getDatabase();

                // 创建安全设置表（如果不存在）
                $db->exec("
                    CREATE TABLE IF NOT EXISTS user_security_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        two_factor_enabled BOOLEAN DEFAULT FALSE,
                        security_question VARCHAR(255),
                        security_answer VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                ");

                // 插入或更新安全设置
                $stmt = $db->prepare("
                    INSERT INTO user_security_settings (user_id, two_factor_enabled, security_question, security_answer)
                    VALUES (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    two_factor_enabled = VALUES(two_factor_enabled),
                    security_question = VALUES(security_question),
                    security_answer = VALUES(security_answer),
                    updated_at = CURRENT_TIMESTAMP
                ");
                $stmt->execute([$userId, $twoFactorEnabled, $securityQuestion, $securityAnswer]);

                $message = '安全设置已更新！';
                break;
                
            default:
                throw new Exception('无效的操作');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取用户安全信息
try {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $userInfo = $stmt->fetch();
    
    if (!$userInfo) {
        throw new Exception('用户信息不存在');
    }
} catch (Exception $e) {
    $error = '获取用户信息失败：' . $e->getMessage();
    $userInfo = [];
}

// 获取登录记录
try {
    $db = getDatabase();

    // 创建登录记录表（如果不存在）
    $db->exec("
        CREATE TABLE IF NOT EXISTS user_login_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            location VARCHAR(255),
            device VARCHAR(255),
            status VARCHAR(20) DEFAULT 'success',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
    ");

    $stmt = $db->prepare("SELECT * FROM user_login_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 20");
    $stmt->execute([$userId]);
    $loginHistory = $stmt->fetchAll();

} catch (Exception $e) {
    $loginHistory = [];
}

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">账户安全</h1>
    <p class="page-subtitle">保护您的账户安全，管理安全设置</p>
</div>

<!-- 消息提示 -->
<?php if ($message): ?>
<div class="alert alert-success alert-dismissible fade show">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 安全概览 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-success bg-opacity-10 text-success mx-auto">
                <i class="fas fa-shield-check"></i>
            </div>
            <h4 class="h6 mb-1">安全等级</h4>
            <p class="text-success fw-bold mb-0">中等</p>
            <small class="text-muted">可以提升</small>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning mx-auto">
                <i class="fas fa-key"></i>
            </div>
            <h4 class="h6 mb-1">密码强度</h4>
            <p class="text-warning fw-bold mb-0">中等</p>
            <small class="text-muted">建议加强</small>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-info bg-opacity-10 text-info mx-auto">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h4 class="h6 mb-1">双因素认证</h4>
            <p class="text-secondary fw-bold mb-0">未启用</p>
            <small class="text-muted">建议启用</small>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary mx-auto">
                <i class="fas fa-clock"></i>
            </div>
            <h4 class="h6 mb-1">最后登录</h4>
            <p class="text-primary fw-bold mb-0">今天</p>
            <small class="text-muted">14:30</small>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- 密码管理 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lock me-2"></i>
                    密码管理
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="mb-3">
                        <label class="form-label">当前密码</label>
                        <input type="password" class="form-control" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="new_password" 
                               minlength="8" required id="newPassword">
                        <div class="password-strength mt-2">
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar" id="strengthBar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted" id="strengthText">请输入至少8个字符的密码</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" name="confirm_password" 
                               minlength="8" required id="confirmPassword">
                    </div>
                    
                    <div class="alert alert-info">
                        <h6 class="alert-heading">密码安全建议</h6>
                        <ul class="mb-0 small">
                            <li>至少8个字符</li>
                            <li>包含大小写字母</li>
                            <li>包含数字和特殊字符</li>
                            <li>不要使用常见密码</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        更新密码
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 安全设置 -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    安全设置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_security_settings">
                    
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">双因素认证</h6>
                                <small class="text-muted">为您的账户添加额外的安全保护</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="two_factor_enabled" 
                                       id="twoFactorSwitch">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">登录通知</h6>
                                <small class="text-muted">新设备登录时发送邮件通知</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="login_notification" 
                                       id="loginNotificationSwitch" checked>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">安全问题</label>
                        <select class="form-select" name="security_question">
                            <option value="">请选择安全问题</option>
                            <option value="pet_name">您第一只宠物的名字是什么？</option>
                            <option value="school_name">您的小学校名是什么？</option>
                            <option value="mother_maiden">您母亲的姓氏是什么？</option>
                            <option value="birth_city">您出生的城市是哪里？</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">安全答案</label>
                        <input type="text" class="form-control" name="security_answer" 
                               placeholder="请输入安全问题的答案">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        保存设置
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 登录记录 -->
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>
            登录记录
        </h5>
        <button class="btn btn-sm btn-outline-primary" onclick="clearLoginHistory()">
            <i class="fas fa-trash me-1"></i>
            清除记录
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>登录时间</th>
                        <th>IP地址</th>
                        <th>地理位置</th>
                        <th>设备信息</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($loginHistory as $index => $login): ?>
                    <tr>
                        <td>
                            <div>
                                <?= date('Y-m-d', strtotime($login['time'])) ?>
                                <br>
                                <small class="text-muted"><?= date('H:i:s', strtotime($login['time'])) ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="font-monospace"><?= htmlspecialchars($login['ip']) ?></span>
                        </td>
                        <td>
                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                            <?= htmlspecialchars($login['location']) ?>
                        </td>
                        <td>
                            <i class="fas fa-desktop text-muted me-1"></i>
                            <?= htmlspecialchars($login['device']) ?>
                        </td>
                        <td>
                            <?php if ($login['status'] === 'success'): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    成功
                                </span>
                            <?php else: ?>
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>
                                    失败
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($index === 0): ?>
                                <span class="badge bg-primary">当前会话</span>
                            <?php else: ?>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="reportSuspicious('<?= $login['ip'] ?>')">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    举报
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 安全建议 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-lightbulb me-2"></i>
            安全建议
        </h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6">
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-shield-alt me-2"></i>
                        启用双因素认证
                    </h6>
                    <p class="mb-2">双因素认证可以大大提高您账户的安全性。</p>
                    <button class="btn btn-sm btn-warning" onclick="enableTwoFactor()">
                        立即启用
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-key me-2"></i>
                        定期更换密码
                    </h6>
                    <p class="mb-2">建议每3-6个月更换一次密码。</p>
                    <small class="text-muted">上次更换：30天前</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 密码强度检查
document.getElementById('newPassword').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('strengthBar');
    const strengthText = document.getElementById('strengthText');
    
    let strength = 0;
    let text = '';
    let color = '';
    
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 12.5;
    if (/[^A-Za-z0-9]/.test(password)) strength += 12.5;
    
    if (strength < 25) {
        text = '密码强度：弱';
        color = 'bg-danger';
    } else if (strength < 50) {
        text = '密码强度：一般';
        color = 'bg-warning';
    } else if (strength < 75) {
        text = '密码强度：中等';
        color = 'bg-info';
    } else {
        text = '密码强度：强';
        color = 'bg-success';
    }
    
    strengthBar.style.width = strength + '%';
    strengthBar.className = 'progress-bar ' + color;
    strengthText.textContent = text;
});

// 确认密码验证
document.getElementById('confirmPassword').addEventListener('input', function() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && confirmPassword !== newPassword) {
        this.setCustomValidity('密码不一致');
    } else {
        this.setCustomValidity('');
    }
});

// 启用双因素认证
function enableTwoFactor() {
    document.getElementById('twoFactorSwitch').checked = true;
    alert('双因素认证功能正在开发中，敬请期待...');
}

// 清除登录记录
function clearLoginHistory() {
    if (confirm('确定要清除所有登录记录吗？')) {
        alert('清除功能正在开发中...');
    }
}

// 举报可疑登录
function reportSuspicious(ip) {
    if (confirm(`确定要举报IP地址 ${ip} 的可疑登录吗？`)) {
        alert('举报功能正在开发中，如有紧急情况请联系客服');
    }
}
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
