<?php
/**
 * 支付SDK类
 * Payment SDK Class
 */

class PaymentSDK {
    private $config;
    private $method;
    
    public function __construct($method, $config) {
        $this->method = $method;
        $this->config = $config;
    }
    
    /**
     * 创建支付订单
     */
    public function createPayment($orderData) {
        switch ($this->method) {
            case 'wechat':
                return $this->createWechatPayment($orderData);
            case 'alipay':
                return $this->createAlipayPayment($orderData);
            case 'epay':
                return $this->createEpayPayment($orderData);
            default:
                throw new Exception('不支持的支付方式');
        }
    }
    
    /**
     * 验证支付通知
     */
    public function verifyNotify($data) {
        switch ($this->method) {
            case 'wechat':
                return $this->verifyWechatNotify($data);
            case 'alipay':
                return $this->verifyAlipayNotify($data);
            case 'epay':
                return $this->verifyEpayNotify($data);
            default:
                return false;
        }
    }
    
    /**
     * 微信支付
     */
    private function createWechatPayment($orderData) {
        $params = [
            'appid' => $this->config['app_id'],
            'mch_id' => $this->config['mch_id'],
            'nonce_str' => $this->generateNonceStr(),
            'body' => $orderData['description'],
            'out_trade_no' => $orderData['payment_number'],
            'total_fee' => intval($orderData['amount'] * 100), // 转换为分
            'spbill_create_ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'notify_url' => $this->getNotifyUrl(),
            'trade_type' => 'NATIVE'
        ];
        
        // 生成签名
        $params['sign'] = $this->generateWechatSign($params);
        
        // 构建XML
        $xml = $this->arrayToXml($params);
        
        // 发送请求
        $response = $this->httpPost('https://api.mch.weixin.qq.com/pay/unifiedorder', $xml);
        $result = $this->xmlToArray($response);
        
        if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
            return [
                'qr_code' => $this->generateQRCode($result['code_url']),
                'payment_url' => $result['code_url'],
                'expires_at' => date('Y-m-d H:i:s', time() + 900)
            ];
        } else {
            throw new Exception('微信支付创建失败: ' . ($result['err_code_des'] ?? $result['return_msg']));
        }
    }
    
    /**
     * 支付宝支付
     */
    private function createAlipayPayment($orderData) {
        $params = [
            'app_id' => $this->config['app_id'],
            'method' => 'alipay.trade.precreate',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'notify_url' => $this->getNotifyUrl(),
            'biz_content' => json_encode([
                'out_trade_no' => $orderData['payment_number'],
                'total_amount' => $orderData['amount'],
                'subject' => $orderData['description'],
                'store_id' => 'domain_store',
                'timeout_express' => '15m'
            ])
        ];
        
        // 生成签名
        $params['sign'] = $this->generateAlipaySign($params);
        
        // 发送请求
        $response = $this->httpPost('https://openapi.alipay.com/gateway.do', $params);
        $result = json_decode($response, true);
        
        $responseKey = 'alipay_trade_precreate_response';
        if (isset($result[$responseKey]) && $result[$responseKey]['code'] === '10000') {
            $data = $result[$responseKey];
            return [
                'qr_code' => $this->generateQRCode($data['qr_code']),
                'payment_url' => $data['qr_code'],
                'expires_at' => date('Y-m-d H:i:s', time() + 900)
            ];
        } else {
            throw new Exception('支付宝支付创建失败: ' . ($result[$responseKey]['sub_msg'] ?? '未知错误'));
        }
    }
    
    /**
     * 易支付
     */
    private function createEpayPayment($orderData) {
        $params = [
            'pid' => $this->config['pid'],
            'type' => 'alipay',
            'out_trade_no' => $orderData['payment_number'],
            'notify_url' => $this->getNotifyUrl(),
            'return_url' => $this->config['return_url'],
            'name' => $orderData['description'],
            'money' => $orderData['amount'],
            'sitename' => '域名销售系统'
        ];
        
        // 生成签名
        $params['sign'] = $this->generateEpaySign($params);
        $params['sign_type'] = 'MD5';
        
        $paymentUrl = $this->config['api_url'] . 'submit.php?' . http_build_query($params);
        
        return [
            'payment_url' => $paymentUrl,
            'expires_at' => date('Y-m-d H:i:s', time() + 900)
        ];
    }
    
    /**
     * 验证微信支付通知
     */
    private function verifyWechatNotify($data) {
        if ($data['return_code'] !== 'SUCCESS') {
            return false;
        }
        
        $sign = $data['sign'];
        unset($data['sign']);
        
        $expectedSign = $this->generateWechatSign($data);
        return $sign === $expectedSign;
    }
    
    /**
     * 验证支付宝通知
     */
    private function verifyAlipayNotify($data) {
        $sign = $data['sign'];
        $signType = $data['sign_type'];
        unset($data['sign'], $data['sign_type']);
        
        ksort($data);
        $signStr = '';
        foreach ($data as $key => $value) {
            if ($value !== '') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');
        
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" . 
                    wordwrap($this->config['public_key'], 64, "\n", true) . 
                    "\n-----END PUBLIC KEY-----";
        
        return openssl_verify($signStr, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256) === 1;
    }
    
    /**
     * 验证易支付通知
     */
    private function verifyEpayNotify($data) {
        $sign = $data['sign'];
        unset($data['sign']);
        
        ksort($data);
        $signStr = '';
        foreach ($data as $key => $value) {
            if ($value !== '') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&') . $this->config['key'];
        
        return md5($signStr) === $sign;
    }
    
    /**
     * 生成微信签名
     */
    private function generateWechatSign($params) {
        ksort($params);
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $key !== 'sign') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr .= 'key=' . $this->config['api_key'];
        return strtoupper(md5($signStr));
    }
    
    /**
     * 生成支付宝签名
     */
    private function generateAlipaySign($params) {
        ksort($params);
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $key !== 'sign') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&');
        
        $privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" . 
                     wordwrap($this->config['private_key'], 64, "\n", true) . 
                     "\n-----END RSA PRIVATE KEY-----";
        
        openssl_sign($signStr, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        return base64_encode($signature);
    }
    
    /**
     * 生成易支付签名
     */
    private function generateEpaySign($params) {
        ksort($params);
        $signStr = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $key !== 'sign') {
                $signStr .= $key . '=' . $value . '&';
            }
        }
        $signStr = rtrim($signStr, '&') . $this->config['key'];
        return md5($signStr);
    }
    
    /**
     * 生成随机字符串
     */
    private function generateNonceStr($length = 32) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $str;
    }
    
    /**
     * 生成二维码
     */
    private function generateQRCode($content) {
        // 这里可以集成二维码生成库，目前返回占位符
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }
    
    /**
     * 获取通知URL
     */
    private function getNotifyUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . '/api/payment.php/notify/' . $this->method;
    }
    
    /**
     * 数组转XML
     */
    private function arrayToXml($array) {
        $xml = '<xml>';
        foreach ($array as $key => $value) {
            $xml .= '<' . $key . '>' . $value . '</' . $key . '>';
        }
        $xml .= '</xml>';
        return $xml;
    }
    
    /**
     * XML转数组
     */
    private function xmlToArray($xml) {
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, is_array($data) ? http_build_query($data) : $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('HTTP请求失败: ' . $httpCode);
        }
        
        return $response;
    }
}
?>
