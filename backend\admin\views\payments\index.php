<?php
$title = '支付配置';
$page = 'payments';

$db = getDatabase();

// 处理支付配置保存
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'save_wechat':
            $config = [
                'app_id' => trim($_POST['wechat_app_id'] ?? ''),
                'mch_id' => trim($_POST['wechat_mch_id'] ?? ''),
                'api_key' => trim($_POST['wechat_api_key'] ?? ''),
                'cert_path' => trim($_POST['wechat_cert_path'] ?? ''),
                'key_path' => trim($_POST['wechat_key_path'] ?? ''),
                'notify_url' => '/api/payment/notify/wechat'
            ];
            
            $enabled = isset($_POST['wechat_enabled']) ? 1 : 0;
            
            $stmt = $db->prepare("UPDATE payment_configs SET enabled = ?, config = ? WHERE payment_method = 'wechat'");
            $stmt->execute([$enabled, json_encode($config)]);
            $message = '微信支付配置已保存';
            break;
            
        case 'save_alipay':
            $config = [
                'partner_id' => trim($_POST['alipay_partner_id'] ?? ''),
                'app_id' => trim($_POST['alipay_app_id'] ?? ''),
                'private_key' => trim($_POST['alipay_private_key'] ?? ''),
                'public_key' => trim($_POST['alipay_public_key'] ?? ''),
                'md5_key' => trim($_POST['alipay_md5_key'] ?? ''),
                'rsa_private_key' => trim($_POST['alipay_rsa_private_key'] ?? ''),
                'rsa_public_key' => trim($_POST['alipay_rsa_public_key'] ?? ''),
                'gateway_url' => trim($_POST['alipay_gateway_url'] ?? 'https://mapi.alipay.com/gateway.do'),
                'notify_url' => '/api/payment/notify/alipay',
                'return_url' => '/payment/return/alipay',
                'sign_type' => trim($_POST['alipay_sign_type'] ?? 'MD5'), // MD5 或 RSA
                'input_charset' => 'utf-8',
                'transport' => 'https'
            ];

            $enabled = isset($_POST['alipay_enabled']) ? 1 : 0;

            $stmt = $db->prepare("UPDATE payment_configs SET enabled = ?, config = ? WHERE payment_method = 'alipay'");
            $stmt->execute([$enabled, json_encode($config)]);
            $message = '支付宝配置已保存';
            break;
            
        case 'save_epay':
            $config = [
                'pid' => trim($_POST['epay_pid'] ?? ''),
                'key' => trim($_POST['epay_key'] ?? ''),
                'api_url' => trim($_POST['epay_api_url'] ?? ''),
                'notify_url' => '/api/payment/notify/epay',
                'return_url' => '/payment/return/epay'
            ];
            
            $enabled = isset($_POST['epay_enabled']) ? 1 : 0;
            
            $stmt = $db->prepare("UPDATE payment_configs SET enabled = ?, config = ? WHERE payment_method = 'epay'");
            $stmt->execute([$enabled, json_encode($config)]);
            $message = '易支付配置已保存';
            break;
            
        case 'test_payment':
            $method = $_POST['test_method'] ?? '';
            $amount = floatval($_POST['test_amount'] ?? 0.01);
            
            if ($method && $amount > 0) {
                // 这里可以实现支付测试逻辑
                $message = "支付测试已发起 - 方式: {$method}, 金额: ¥{$amount}";
            }
            break;
    }
}

// 获取支付配置
$paymentConfigs = [];
$stmt = $db->query("SELECT * FROM payment_configs ORDER BY sort_order");
while ($row = $stmt->fetch()) {
    $row['config'] = json_decode($row['config'], true);
    $paymentConfigs[$row['payment_method']] = $row;
}

// 获取支付统计
$paymentStats = $db->query("SELECT 
    COUNT(*) as total_payments,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as success_payments,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
    SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_payments
    FROM payments")->fetch();

// 获取最近支付记录
$recentPayments = $db->query("SELECT p.*, o.order_number 
    FROM payments p 
    LEFT JOIN orders o ON p.order_id = o.id 
    ORDER BY p.created_at DESC 
    LIMIT 10")->fetchAll();

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">支付配置</h1>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#testPaymentModal">
            <i class="fas fa-vial"></i>
            测试支付
        </button>
        <button class="btn btn-outline-success" onclick="exportPayments()">
            <i class="fas fa-download"></i>
            导出记录
        </button>
    </div>
</div>

<!-- 支付统计 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="stats-number"><?= number_format($paymentStats['total_payments'] ?? 0) ?></div>
            <p class="stats-label">总支付数</p>
            <div class="stats-trend up">
                今日: <?= $paymentStats['today_payments'] ?? 0 ?>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number"><?= number_format($paymentStats['success_payments'] ?? 0) ?></div>
            <p class="stats-label">成功支付</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number"><?= number_format($paymentStats['pending_payments'] ?? 0) ?></div>
            <p class="stats-label">待支付</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="fas fa-yen-sign"></i>
            </div>
            <div class="stats-number">¥<?= number_format($paymentStats['total_amount'] ?? 0, 2) ?></div>
            <p class="stats-label">总收入</p>
        </div>
    </div>
</div>

<?php if (isset($message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- 支付配置 -->
    <div class="col-lg-8">
        <!-- 微信支付配置 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fab fa-weixin text-success"></i>
                    微信支付配置
                </h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="wechatEnabled" 
                           <?= ($paymentConfigs['wechat']['enabled'] ?? 0) ? 'checked' : '' ?>>
                    <label class="form-check-label" for="wechatEnabled">启用</label>
                </div>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="save_wechat">
                    <input type="hidden" name="wechat_enabled" id="wechatEnabledInput" 
                           value="<?= ($paymentConfigs['wechat']['enabled'] ?? 0) ? '1' : '0' ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">应用ID (AppID)</label>
                                <input type="text" class="form-control" name="wechat_app_id" 
                                       value="<?= htmlspecialchars($paymentConfigs['wechat']['config']['app_id'] ?? '') ?>"
                                       placeholder="wx1234567890abcdef">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">商户号 (MchID)</label>
                                <input type="text" class="form-control" name="wechat_mch_id" 
                                       value="<?= htmlspecialchars($paymentConfigs['wechat']['config']['mch_id'] ?? '') ?>"
                                       placeholder="1234567890">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">API密钥 (API Key)</label>
                        <input type="password" class="form-control" name="wechat_api_key" 
                               value="<?= htmlspecialchars($paymentConfigs['wechat']['config']['api_key'] ?? '') ?>"
                               placeholder="32位API密钥">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">证书路径 (apiclient_cert.pem)</label>
                                <input type="text" class="form-control" name="wechat_cert_path" 
                                       value="<?= htmlspecialchars($paymentConfigs['wechat']['config']['cert_path'] ?? '') ?>"
                                       placeholder="/path/to/apiclient_cert.pem">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">私钥路径 (apiclient_key.pem)</label>
                                <input type="text" class="form-control" name="wechat_key_path" 
                                       value="<?= htmlspecialchars($paymentConfigs['wechat']['config']['key_path'] ?? '') ?>"
                                       placeholder="/path/to/apiclient_key.pem">
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i>
                        保存微信支付配置
                    </button>
                </form>
            </div>
        </div>
        
        <!-- 支付宝配置 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fab fa-alipay text-primary"></i>
                    支付宝配置
                </h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="alipayEnabled" 
                           <?= ($paymentConfigs['alipay']['enabled'] ?? 0) ? 'checked' : '' ?>>
                    <label class="form-check-label" for="alipayEnabled">启用</label>
                </div>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="save_alipay">
                    <input type="hidden" name="alipay_enabled" id="alipayEnabledInput"
                           value="<?= ($paymentConfigs['alipay']['enabled'] ?? 0) ? '1' : '0' ?>">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>配置说明：</strong>支持支付宝合作伙伴身份和开放平台两种接入方式
                    </div>

                    <!-- 签名方式选择 -->
                    <div class="mb-3">
                        <label class="form-label">签名方式</label>
                        <select class="form-select" name="alipay_sign_type" id="alipaySignType" onchange="toggleSignType()">
                            <option value="MD5" <?= ($paymentConfigs['alipay']['config']['sign_type'] ?? 'MD5') === 'MD5' ? 'selected' : '' ?>>MD5 (合作伙伴身份)</option>
                            <option value="RSA" <?= ($paymentConfigs['alipay']['config']['sign_type'] ?? 'MD5') === 'RSA' ? 'selected' : '' ?>>RSA (开放平台)</option>
                        </select>
                    </div>

                    <!-- 合作伙伴身份配置 -->
                    <div id="partnerConfig" style="display: <?= ($paymentConfigs['alipay']['config']['sign_type'] ?? 'MD5') === 'MD5' ? 'block' : 'none' ?>">
                        <h6 class="text-primary mb-3"><i class="fas fa-handshake"></i> 合作伙伴身份配置</h6>

                        <div class="mb-3">
                            <label class="form-label">合作伙伴身份 (PID) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="alipay_partner_id"
                                   value="<?= htmlspecialchars($paymentConfigs['alipay']['config']['partner_id'] ?? '') ?>"
                                   placeholder="2088532725053770">
                            <div class="form-text">16位数字，如：2088532725053770</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">MD5密钥 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="alipay_md5_key"
                                   value="<?= htmlspecialchars($paymentConfigs['alipay']['config']['md5_key'] ?? '') ?>"
                                   placeholder="32位MD5密钥">
                            <div class="form-text">32位字符串，在支付宝商户平台获取</div>
                        </div>
                    </div>

                    <!-- 开放平台配置 -->
                    <div id="openConfig" style="display: <?= ($paymentConfigs['alipay']['config']['sign_type'] ?? 'MD5') === 'RSA' ? 'block' : 'none' ?>">
                        <h6 class="text-success mb-3"><i class="fas fa-key"></i> 开放平台配置</h6>

                        <div class="mb-3">
                            <label class="form-label">应用ID (App ID) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="alipay_app_id"
                                   value="<?= htmlspecialchars($paymentConfigs['alipay']['config']['app_id'] ?? '') ?>"
                                   placeholder="2021001234567890">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">应用私钥 (RSA Private Key) <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="alipay_rsa_private_key" rows="4"
                                      placeholder="-----BEGIN RSA PRIVATE KEY-----"><?= htmlspecialchars($paymentConfigs['alipay']['config']['rsa_private_key'] ?? '') ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">支付宝公钥 (RSA Public Key) <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="alipay_rsa_public_key" rows="4"
                                      placeholder="-----BEGIN PUBLIC KEY-----"><?= htmlspecialchars($paymentConfigs['alipay']['config']['rsa_public_key'] ?? '') ?></textarea>
                        </div>
                    </div>

                    <!-- 通用配置 -->
                    <h6 class="text-secondary mb-3"><i class="fas fa-cog"></i> 通用配置</h6>

                    <div class="mb-3">
                        <label class="form-label">网关地址</label>
                        <input type="text" class="form-control" name="alipay_gateway_url"
                               value="<?= htmlspecialchars($paymentConfigs['alipay']['config']['gateway_url'] ?? 'https://mapi.alipay.com/gateway.do') ?>"
                               placeholder="https://mapi.alipay.com/gateway.do">
                        <div class="form-text">正式环境：https://mapi.alipay.com/gateway.do</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                保存支付宝配置
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-outline-success" onclick="testAlipayConfig()">
                                <i class="fas fa-vial"></i>
                                测试配置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 易支付配置 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card text-warning"></i>
                    易支付配置
                </h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="epayEnabled" 
                           <?= ($paymentConfigs['epay']['enabled'] ?? 0) ? 'checked' : '' ?>>
                    <label class="form-check-label" for="epayEnabled">启用</label>
                </div>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="save_epay">
                    <input type="hidden" name="epay_enabled" id="epayEnabledInput" 
                           value="<?= ($paymentConfigs['epay']['enabled'] ?? 0) ? '1' : '0' ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">商户ID (PID)</label>
                                <input type="text" class="form-control" name="epay_pid" 
                                       value="<?= htmlspecialchars($paymentConfigs['epay']['config']['pid'] ?? '') ?>"
                                       placeholder="10001">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">商户密钥 (Key)</label>
                                <input type="password" class="form-control" name="epay_key" 
                                       value="<?= htmlspecialchars($paymentConfigs['epay']['config']['key'] ?? '') ?>"
                                       placeholder="商户密钥">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">API地址</label>
                        <input type="url" class="form-control" name="epay_api_url" 
                               value="<?= htmlspecialchars($paymentConfigs['epay']['config']['api_url'] ?? '') ?>"
                               placeholder="https://pay.namesilo-namesilo-sample.com/">
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i>
                        保存易支付配置
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 最近支付记录 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i>
                    最近支付记录
                </h5>
                <a href="?page=payment-records" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recentPayments)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentPayments as $payment): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?= htmlspecialchars($payment['order_number']) ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?= htmlspecialchars($payment['payment_method']) ?> - 
                                            ¥<?= number_format($payment['amount'], 2) ?>
                                        </p>
                                        <small class="text-muted"><?= date('m-d H:i', strtotime($payment['created_at'])) ?></small>
                                    </div>
                                    <div>
                                        <?php
                                        $statusColors = [
                                            'pending' => 'warning',
                                            'processing' => 'info',
                                            'success' => 'success',
                                            'failed' => 'danger',
                                            'cancelled' => 'secondary'
                                        ];
                                        $statusLabels = [
                                            'pending' => '待支付',
                                            'processing' => '处理中',
                                            'success' => '成功',
                                            'failed' => '失败',
                                            'cancelled' => '取消'
                                        ];
                                        $color = $statusColors[$payment['status']] ?? 'secondary';
                                        $label = $statusLabels[$payment['status']] ?? $payment['status'];
                                        ?>
                                        <span class="badge bg-<?= $color ?>"><?= $label ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card text-muted fa-3x mb-3"></i>
                        <p class="text-muted">暂无支付记录</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 测试支付模态框 -->
<div class="modal fade" id="testPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">测试支付</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="test_payment">
                    
                    <div class="mb-3">
                        <label class="form-label">支付方式</label>
                        <select class="form-select" name="test_method" required>
                            <option value="">请选择支付方式</option>
                            <?php foreach ($paymentConfigs as $method => $config): ?>
                                <?php if ($config['enabled']): ?>
                                    <option value="<?= $method ?>"><?= htmlspecialchars($config['name']) ?></option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">测试金额</label>
                        <input type="number" class="form-control" name="test_amount" 
                               step="0.01" min="0.01" value="0.01" required>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        这是测试支付，请使用测试环境的配置参数。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">发起测试</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 支付方式开关
document.getElementById('wechatEnabled').addEventListener('change', function() {
    document.getElementById('wechatEnabledInput').value = this.checked ? '1' : '0';
});

document.getElementById('alipayEnabled').addEventListener('change', function() {
    document.getElementById('alipayEnabledInput').value = this.checked ? '1' : '0';
});

document.getElementById('epayEnabled').addEventListener('change', function() {
    document.getElementById('epayEnabledInput').value = this.checked ? '1' : '0';
});

// 导出支付记录
function exportPayments() {
    window.open('?page=payments&export=csv', '_blank');
}

// 切换支付宝签名方式
function toggleSignType() {
    const signType = document.getElementById('alipaySignType').value;
    const partnerConfig = document.getElementById('partnerConfig');
    const openConfig = document.getElementById('openConfig');

    if (signType === 'MD5') {
        partnerConfig.style.display = 'block';
        openConfig.style.display = 'none';
    } else {
        partnerConfig.style.display = 'none';
        openConfig.style.display = 'block';
    }
}

// 测试支付宝配置
function testAlipayConfig() {
    const signType = document.getElementById('alipaySignType').value;
    let requiredFields = [];

    if (signType === 'MD5') {
        const partnerId = document.querySelector('input[name="alipay_partner_id"]').value;
        const md5Key = document.querySelector('input[name="alipay_md5_key"]').value;

        if (!partnerId) requiredFields.push('合作伙伴身份(PID)');
        if (!md5Key) requiredFields.push('MD5密钥');
    } else {
        const appId = document.querySelector('input[name="alipay_app_id"]').value;
        const privateKey = document.querySelector('textarea[name="alipay_rsa_private_key"]').value;
        const publicKey = document.querySelector('textarea[name="alipay_rsa_public_key"]').value;

        if (!appId) requiredFields.push('应用ID');
        if (!privateKey) requiredFields.push('应用私钥');
        if (!publicKey) requiredFields.push('支付宝公钥');
    }

    if (requiredFields.length > 0) {
        alert('请先填写以下必填项：\n' + requiredFields.join('\n'));
        return;
    }

    // 发送测试请求
    const formData = new FormData();
    formData.append('action', 'test_alipay');
    formData.append('sign_type', signType);

    if (signType === 'MD5') {
        formData.append('partner_id', document.querySelector('input[name="alipay_partner_id"]').value);
        formData.append('md5_key', document.querySelector('input[name="alipay_md5_key"]').value);
    } else {
        formData.append('app_id', document.querySelector('input[name="alipay_app_id"]').value);
        formData.append('private_key', document.querySelector('textarea[name="alipay_rsa_private_key"]').value);
        formData.append('public_key', document.querySelector('textarea[name="alipay_rsa_public_key"]').value);
    }

    fetch('/api/payment.php?action=test-alipay', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ 支付宝配置测试成功！\n' + data.message);
        } else {
            alert('❌ 支付宝配置测试失败：\n' + data.message);
        }
    })
    .catch(error => {
        alert('❌ 测试请求失败: ' + error.message);
    });
}
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
