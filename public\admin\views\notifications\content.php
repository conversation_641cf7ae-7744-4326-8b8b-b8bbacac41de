<?php
/**
 * 通知管理内容页面
 * Notifications Management Content Page
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// 添加错误处理
set_error_handler(function($severity, $message, $file, $line) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>PHP错误:</h4>";
    echo "<p><strong>错误信息:</strong> $message</p>";
    echo "<p><strong>文件:</strong> $file</p>";
    echo "<p><strong>行号:</strong> $line</p>";
    echo "</div>";
});

try {
    // 获取数据库连接
    $db = getDatabase();

    if (!$db) {
        throw new Exception("数据库连接失败");
    }

    echo "<div class='alert alert-success'>数据库连接成功</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>异常错误:</h4>";
    echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>文件:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>堆栈跟踪:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
    exit;
}

// 处理操作
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'send_notification':
                $userIds = $_POST['user_ids'] ?? [];
                $title = trim($_POST['title']);
                $messageContent = trim($_POST['message']);
                $type = $_POST['type'] ?? 'info';
                
                if (empty($title) || empty($messageContent)) {
                    throw new Exception('标题和内容不能为空');
                }
                
                // 如果选择了"所有用户"
                if (in_array('all', $userIds)) {
                    $stmt = $db->query("SELECT id FROM users");
                    $userIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
                }
                
                // 批量插入通知
                $stmt = $db->prepare("INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, ?)");
                $count = 0;
                foreach ($userIds as $userId) {
                    if (is_numeric($userId)) {
                        $stmt->execute([$userId, $title, $messageContent, $type]);
                        $count++;
                    }
                }
                
                $message = "成功发送通知给 {$count} 个用户！";
                break;
                
            case 'delete_notification':
                $notificationId = $_POST['notification_id'];
                $stmt = $db->prepare("DELETE FROM notifications WHERE id = ?");
                $stmt->execute([$notificationId]);
                
                $message = '通知删除成功！';
                break;
                
            case 'mark_all_read':
                $stmt = $db->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
                $stmt->execute([$_POST['notification_id']]);
                
                $message = '通知已标记为已读！';
                break;
        }
    } catch (Exception $e) {
        $error = '操作失败：' . $e->getMessage();
    }
}

// 获取通知统计
try {
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_notifications,
            COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_notifications,
            COUNT(CASE WHEN type = 'info' THEN 1 END) as info_notifications,
            COUNT(CASE WHEN type = 'warning' THEN 1 END) as warning_notifications,
            COUNT(CASE WHEN type = 'error' THEN 1 END) as error_notifications,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_notifications
        FROM notifications
    ");
    $notificationStats = $stmt->fetch();
} catch (Exception $e) {
    $notificationStats = [
        'total_notifications' => 0,
        'unread_notifications' => 0,
        'info_notifications' => 0,
        'warning_notifications' => 0,
        'error_notifications' => 0,
        'today_notifications' => 0
    ];
}

// 获取通知列表
$currentPage = (int)($_GET['p'] ?? 1); // 使用 'p' 参数避免与路由冲突
$limit = 20;
$offset = ($currentPage - 1) * $limit;

$whereClause = '';
$params = [];

// 搜索过滤
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $whereClause = " WHERE (n.title LIKE ? OR n.message LIKE ? OR u.username LIKE ?)";
    $params = [$search, $search, $search];
}

if (!empty($_GET['type'])) {
    $typeFilter = $_GET['type'];
    $whereClause .= $whereClause ? " AND n.type = ?" : " WHERE n.type = ?";
    $params[] = $typeFilter;
}

if (!empty($_GET['status'])) {
    $statusFilter = $_GET['status'];
    if ($statusFilter === 'read') {
        $whereClause .= $whereClause ? " AND n.is_read = 1" : " WHERE n.is_read = 1";
    } elseif ($statusFilter === 'unread') {
        $whereClause .= $whereClause ? " AND n.is_read = 0" : " WHERE n.is_read = 0";
    }
}

try {
    $stmt = $db->prepare("
        SELECT 
            n.*,
            u.username,
            u.email
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        {$whereClause}
        ORDER BY n.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $notifications = $stmt->fetchAll();
    
    // 获取总数
    $countParams = array_slice($params, 0, -2);
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        {$whereClause}
    ");
    $stmt->execute($countParams);
    $totalNotifications = $stmt->fetchColumn();
    $totalPages = ceil($totalNotifications / $limit);
    
} catch (Exception $e) {
    $notifications = [];
    $totalPages = 1;
}

// 获取用户列表（用于发送通知）
try {
    $stmt = $db->query("SELECT id, username, email FROM users ORDER BY username");
    $users = $stmt->fetchAll();
} catch (Exception $e) {
    $users = [];
}
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">通知管理</h1>
            <p class="text-muted">管理系统通知和用户消息</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sendNotificationModal">
                <i class="fas fa-paper-plane me-2"></i>发送通知
            </button>
        </div>
    </div>

    <!-- 消息提示 -->
    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 通知统计 -->
    <div class="row g-4 mb-4">
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-bell fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($notificationStats['total_notifications']) ?></h5>
                    <p class="card-text text-muted">总通知数</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($notificationStats['unread_notifications']) ?></h5>
                    <p class="card-text text-muted">未读通知</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($notificationStats['info_notifications']) ?></h5>
                    <p class="card-text text-muted">信息通知</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($notificationStats['warning_notifications']) ?></h5>
                    <p class="card-text text-muted">警告通知</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($notificationStats['error_notifications']) ?></h5>
                    <p class="card-text text-muted">错误通知</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($notificationStats['today_notifications']) ?></h5>
                    <p class="card-text text-muted">今日通知</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">搜索</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="标题、内容或用户名">
                </div>
                <div class="col-md-2">
                    <label class="form-label">类型</label>
                    <select class="form-select" name="type">
                        <option value="">全部类型</option>
                        <option value="info" <?= ($_GET['type'] ?? '') === 'info' ? 'selected' : '' ?>>信息</option>
                        <option value="warning" <?= ($_GET['type'] ?? '') === 'warning' ? 'selected' : '' ?>>警告</option>
                        <option value="error" <?= ($_GET['type'] ?? '') === 'error' ? 'selected' : '' ?>>错误</option>
                        <option value="success" <?= ($_GET['type'] ?? '') === 'success' ? 'selected' : '' ?>>成功</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">状态</label>
                    <select class="form-select" name="status">
                        <option value="">全部状态</option>
                        <option value="unread" <?= ($_GET['status'] ?? '') === 'unread' ? 'selected' : '' ?>>未读</option>
                        <option value="read" <?= ($_GET['status'] ?? '') === 'read' ? 'selected' : '' ?>>已读</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <a href="?" class="btn btn-outline-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 通知列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">通知列表</h5>
        </div>
        <div class="card-body">
            <?php if (empty($notifications)): ?>
            <div class="text-center py-4">
                <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无通知记录</p>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户</th>
                            <th>标题</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($notifications as $notification): ?>
                        <tr class="<?= !$notification['is_read'] ? 'table-warning' : '' ?>">
                            <td><?= $notification['id'] ?></td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($notification['username']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars($notification['email']) ?></small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($notification['title']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars(mb_substr($notification['message'], 0, 50)) ?>...</small>
                                </div>
                            </td>
                            <td>
                                <?php
                                $typeClass = match($notification['type']) {
                                    'info' => 'primary',
                                    'warning' => 'warning',
                                    'error' => 'danger',
                                    'success' => 'success',
                                    default => 'secondary'
                                };
                                $typeText = match($notification['type']) {
                                    'info' => '信息',
                                    'warning' => '警告',
                                    'error' => '错误',
                                    'success' => '成功',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-<?= $typeClass ?>"><?= $typeText ?></span>
                            </td>
                            <td>
                                <?php if ($notification['is_read']): ?>
                                <span class="badge bg-success">已读</span>
                                <?php else: ?>
                                <span class="badge bg-warning">未读</span>
                                <?php endif; ?>
                            </td>
                            <td><?= date('Y-m-d H:i:s', strtotime($notification['created_at'])) ?></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="viewNotification(<?= $notification['id'] ?>)" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(<?= $notification['id'] ?>)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="通知分页">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                        <a class="page-link" href="?page=notifications&p=<?= $i ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?><?= !empty($_GET['type']) ? '&type=' . urlencode($_GET['type']) : '' ?><?= !empty($_GET['status']) ? '&status=' . urlencode($_GET['status']) : '' ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 发送通知模态框 -->
<div class="modal fade" id="sendNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">发送通知</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="send_notification">
                    
                    <div class="mb-3">
                        <label class="form-label">接收用户</label>
                        <select class="form-select" name="user_ids[]" multiple size="8" required>
                            <option value="all">所有用户</option>
                            <?php foreach ($users as $user): ?>
                            <option value="<?= $user['id'] ?>"><?= htmlspecialchars($user['username']) ?> (<?= htmlspecialchars($user['email']) ?>)</option>
                            <?php endforeach; ?>
                        </select>
                        <small class="form-text text-muted">按住Ctrl键可选择多个用户</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">通知类型</label>
                        <select class="form-select" name="type" required>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                            <option value="success">成功</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">标题</label>
                        <input type="text" class="form-control" name="title" required maxlength="255">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">内容</label>
                        <textarea class="form-control" name="message" rows="5" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">发送通知</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 查看通知详情模态框 -->
<div class="modal fade" id="viewNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">通知详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="notificationDetails">
                <!-- 通知详情将通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewNotification(id) {
    // 这里应该通过AJAX获取通知详情，简化处理
    document.getElementById('notificationDetails').innerHTML = '<p>加载中...</p>';
    new bootstrap.Modal(document.getElementById('viewNotificationModal')).show();
}

function deleteNotification(id) {
    if (confirm('确定要删除这条通知吗？此操作不可撤销！')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_notification">
            <input type="hidden" name="notification_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
