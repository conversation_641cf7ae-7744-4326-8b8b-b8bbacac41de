<?php
/**
 * 应用配置文件
 * Application Configuration
 */

return [
    // 应用基本信息
    'name' => $_ENV['APP_NAME'] ?? 'NameSilo域名管理系统',
    'version' => '2.0.0',
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
    'url' => $_ENV['APP_URL'] ?? 'http://127.0.0.1:777',
    'timezone' => $_ENV['APP_TIMEZONE'] ?? 'Asia/Shanghai',
    'locale' => $_ENV['APP_LOCALE'] ?? 'zh_CN',
    'fallback_locale' => 'en',
    
    // 安全配置
    'key' => $_ENV['APP_KEY'] ?? 'base64:' . base64_encode(random_bytes(32)),
    'cipher' => 'AES-256-CBC',
    
    // 会话配置
    'session' => [
        'driver' => $_ENV['SESSION_DRIVER'] ?? 'file',
        'lifetime' => (int)($_ENV['SESSION_LIFETIME'] ?? 120), // 分钟
        'expire_on_close' => false,
        'encrypt' => false,
        'files' => storage_path('sessions'),
        'connection' => null,
        'table' => 'sessions',
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => [
            'name' => $_ENV['SESSION_COOKIE'] ?? 'domain_session',
            'path' => '/',
            'domain' => $_ENV['SESSION_DOMAIN'] ?? null,
            'secure' => filter_var($_ENV['SESSION_SECURE_COOKIE'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'http_only' => true,
            'same_site' => 'lax',
        ],
    ],
    
    // 缓存配置
    'cache' => [
        'default' => $_ENV['CACHE_DRIVER'] ?? 'file',
        'stores' => [
            'file' => [
                'driver' => 'file',
                'path' => storage_path('cache'),
            ],
            'redis' => [
                'driver' => 'redis',
                'connection' => 'cache',
            ],
        ],
        'prefix' => $_ENV['CACHE_PREFIX'] ?? 'domain_cache',
    ],
    
    // 日志配置
    'logging' => [
        'default' => $_ENV['LOG_CHANNEL'] ?? 'stack',
        'channels' => [
            'stack' => [
                'driver' => 'stack',
                'channels' => ['single'],
                'ignore_exceptions' => false,
            ],
            'single' => [
                'driver' => 'single',
                'path' => storage_path('logs/app.log'),
                'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
            ],
            'daily' => [
                'driver' => 'daily',
                'path' => storage_path('logs/app.log'),
                'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
                'days' => 14,
            ],
        ],
    ],
    
    // 文件系统配置
    'filesystems' => [
        'default' => $_ENV['FILESYSTEM_DRIVER'] ?? 'local',
        'disks' => [
            'local' => [
                'driver' => 'local',
                'root' => storage_path('app'),
            ],
            'public' => [
                'driver' => 'local',
                'root' => storage_path('app/public'),
                'url' => $_ENV['APP_URL'] . '/storage',
                'visibility' => 'public',
            ],
            'uploads' => [
                'driver' => 'local',
                'root' => storage_path('uploads'),
                'url' => $_ENV['APP_URL'] . '/uploads',
                'visibility' => 'public',
            ],
        ],
    ],
    
    // 队列配置
    'queue' => [
        'default' => $_ENV['QUEUE_CONNECTION'] ?? 'sync',
        'connections' => [
            'sync' => [
                'driver' => 'sync',
            ],
            'database' => [
                'driver' => 'database',
                'table' => 'jobs',
                'queue' => 'default',
                'retry_after' => 90,
            ],
            'redis' => [
                'driver' => 'redis',
                'connection' => 'default',
                'queue' => $_ENV['REDIS_QUEUE'] ?? 'default',
                'retry_after' => 90,
                'block_for' => null,
            ],
        ],
        'failed' => [
            'driver' => $_ENV['QUEUE_FAILED_DRIVER'] ?? 'database',
            'database' => 'mysql',
            'table' => 'failed_jobs',
        ],
    ],
    
    // 服务提供者
    'providers' => [
        // 核心服务提供者
        'App\Providers\AppServiceProvider',
        'App\Providers\AuthServiceProvider',
        'App\Providers\DatabaseServiceProvider',
        'App\Providers\LogServiceProvider',
        
        // 第三方服务提供者
        'App\Providers\PaymentServiceProvider',
        'App\Providers\EmailServiceProvider',
        'App\Providers\DomainServiceProvider',
    ],
    
    // 别名
    'aliases' => [
        'App' => 'App\Facades\App',
        'Auth' => 'App\Facades\Auth',
        'Cache' => 'App\Facades\Cache',
        'Config' => 'App\Facades\Config',
        'DB' => 'App\Facades\DB',
        'Log' => 'App\Facades\Log',
        'Session' => 'App\Facades\Session',
        'Storage' => 'App\Facades\Storage',
    ],
    
    // 中间件
    'middleware' => [
        'global' => [
            'App\Middleware\TrustProxies',
            'App\Middleware\CheckForMaintenanceMode',
            'App\Middleware\ValidatePostSize',
            'App\Middleware\TrimStrings',
            'App\Middleware\ConvertEmptyStringsToNull',
        ],
        'web' => [
            'App\Middleware\EncryptCookies',
            'App\Middleware\AddQueuedCookiesToResponse',
            'App\Middleware\StartSession',
            'App\Middleware\ShareErrorsFromSession',
            'App\Middleware\VerifyCsrfToken',
            'App\Middleware\SubstituteBindings',
        ],
        'api' => [
            'throttle:api',
            'App\Middleware\SubstituteBindings',
        ],
    ],
    
    // 路由中间件
    'routeMiddleware' => [
        'auth' => 'App\Middleware\Authenticate',
        'auth.basic' => 'App\Middleware\AuthenticateWithBasicAuth',
        'bindings' => 'App\Middleware\SubstituteBindings',
        'cache.headers' => 'App\Middleware\SetCacheHeaders',
        'can' => 'App\Middleware\Authorize',
        'guest' => 'App\Middleware\RedirectIfAuthenticated',
        'password.confirm' => 'App\Middleware\RequirePassword',
        'signed' => 'App\Middleware\ValidateSignature',
        'throttle' => 'App\Middleware\ThrottleRequests',
        'verified' => 'App\Middleware\EnsureEmailIsVerified',
        'admin' => 'App\Middleware\AdminAuth',
        'cors' => 'App\Middleware\Cors',
    ],
    
    // 维护模式
    'maintenance' => [
        'enabled' => filter_var($_ENV['MAINTENANCE_MODE'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'message' => $_ENV['MAINTENANCE_MESSAGE'] ?? '系统维护中，请稍后访问',
        'allowed_ips' => array_filter(explode(',', $_ENV['MAINTENANCE_ALLOWED_IPS'] ?? '')),
        'retry_after' => (int)($_ENV['MAINTENANCE_RETRY_AFTER'] ?? 3600),
    ],
    
    // 性能配置
    'performance' => [
        'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status()['opcache_enabled'],
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
    ],
];

/**
 * 获取存储路径
 */
function storage_path($path = '') {
    $basePath = defined('STORAGE_PATH') ? STORAGE_PATH : dirname(__DIR__) . '/storage';
    return $path ? $basePath . '/' . ltrim($path, '/') : $basePath;
}

/**
 * 获取配置路径
 */
function config_path($path = '') {
    $basePath = __DIR__;
    return $path ? $basePath . '/' . ltrim($path, '/') : $basePath;
}

/**
 * 获取应用根路径
 */
function base_path($path = '') {
    $basePath = dirname(__DIR__);
    return $path ? $basePath . '/' . ltrim($path, '/') : $basePath;
}
?>
