/**
 * NameSilo域名销售系统 - 管理后台JavaScript
 * NameSilo Domain Sales System - Admin Panel JavaScript
 */

// 安全的存储访问函数
const SafeStorage = {
    // 检查存储是否可用
    isAvailable: function(type) {
        try {
            const storage = window[type];
            const test = '__storage_test__';
            storage.setItem(test, test);
            storage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    },

    // 安全的localStorage访问
    local: {
        get: function(key) {
            if (!SafeStorage.isAvailable('localStorage')) return null;
            try {
                return localStorage.getItem(key);
            } catch (e) {
                console.warn('localStorage access denied:', e);
                return null;
            }
        },

        set: function(key, value) {
            if (!SafeStorage.isAvailable('localStorage')) return false;
            try {
                localStorage.setItem(key, value);
                return true;
            } catch (e) {
                console.warn('localStorage access denied:', e);
                return false;
            }
        },

        remove: function(key) {
            if (!SafeStorage.isAvailable('localStorage')) return false;
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.warn('localStorage access denied:', e);
                return false;
            }
        }
    },

    // 安全的sessionStorage访问
    session: {
        get: function(key) {
            if (!SafeStorage.isAvailable('sessionStorage')) return null;
            try {
                return sessionStorage.getItem(key);
            } catch (e) {
                console.warn('sessionStorage access denied:', e);
                return null;
            }
        },

        set: function(key, value) {
            if (!SafeStorage.isAvailable('sessionStorage')) return false;
            try {
                sessionStorage.setItem(key, value);
                return true;
            } catch (e) {
                console.warn('sessionStorage access denied:', e);
                return false;
            }
        },

        remove: function(key) {
            if (!SafeStorage.isAvailable('sessionStorage')) return false;
            try {
                sessionStorage.removeItem(key);
                return true;
            } catch (e) {
                console.warn('sessionStorage access denied:', e);
                return false;
            }
        }
    }
};

// 错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error || e.message);
    // 可以发送错误报告到服务器
    if (typeof showErrorMessage === 'function') {
        showErrorMessage('页面发生错误，请刷新重试');
    }
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    e.preventDefault();
    if (typeof showErrorMessage === 'function') {
        showErrorMessage('网络请求失败，请检查网络连接');
    }
});

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 确保所有必要的函数都已定义
        if (typeof initializeAdmin === 'function') {
            initializeAdmin();
        } else {
            console.warn('initializeAdmin函数未定义，使用基础初始化');
            basicInitialization();
        }
    } catch (error) {
        console.error('Admin initialization failed:', error);
        // 显示友好的错误消息
        if (typeof showErrorMessage === 'function') {
            showErrorMessage('系统初始化失败，请刷新页面重试');
        } else {
            alert('系统初始化失败，请刷新页面重试');
        }
    }
});

// 基础初始化函数
function basicInitialization() {
    try {
        // 基础功能初始化
        console.log('执行基础初始化...');

        // 初始化工具提示
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // 初始化下拉菜单
        if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
        }

        console.log('基础初始化完成');
    } catch (error) {
        console.error('基础初始化失败:', error);
    }
}

// 显示错误消息
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
    `;
    errorDiv.innerHTML = `
        <strong>错误：</strong> ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(errorDiv);

    // 5秒后自动移除
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 5000);
}

/**
 * 初始化管理后台
 */
function initializeAdmin() {
    try {
        console.log('开始初始化管理后台...');

        // 性能优化
        if (typeof enableGPUAcceleration === 'function') {
            enableGPUAcceleration();
        }

        // 初始化侧边栏
        if (typeof initializeSidebar === 'function') {
            initializeSidebar();
        }

        // 初始化表格
        if (typeof initializeTables === 'function') {
            initializeTables();
        }

        // 初始化表单
        if (typeof initializeForms === 'function') {
            initializeForms();
        }

        // 初始化工具提示
        if (typeof initializeTooltips === 'function') {
            initializeTooltips();
        }

        // 初始化确认对话框
        if (typeof initializeConfirmDialogs === 'function') {
            initializeConfirmDialogs();
        }

        // 初始化搜索功能
        if (typeof initializeSearch === 'function') {
            initializeSearch();
        }

        // 初始化数据表格
        if (typeof initializeDataTable === 'function') {
            initializeDataTable();
        }

        // 初始化通知
        if (typeof initializeNotifications === 'function') {
            initializeNotifications();
        }

        // 初始化实时更新
        if (typeof initializeRealTimeUpdates === 'function') {
            initializeRealTimeUpdates();
        }

        // 初始化动画
        if (typeof initializeAnimations === 'function') {
            initializeAnimations();
        }

        // 初始化懒加载
        if (typeof initializeLazyLoading === 'function') {
            initializeLazyLoading();
        }

        // 初始化性能监控
        if (typeof initializePerformanceMonitoring === 'function') {
            initializePerformanceMonitoring();
        }

        console.log('管理后台初始化完成');
    } catch (error) {
        console.error('管理后台初始化失败:', error);
        throw error;
    }
}

/**
 * 启用GPU加速
 */
function enableGPUAcceleration() {
    const elementsToAccelerate = [
        '.sidebar',
        '.top-navbar',
        '.stats-card',
        '.card',
        '.btn',
        '.quick-action-card'
    ];

    elementsToAccelerate.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.transform = 'translateZ(0)';
            element.style.backfaceVisibility = 'hidden';
            element.style.perspective = '1000px';
        });
    });
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');

    // 检查必要元素是否存在
    if (!sidebar || !content) {
        console.warn('侧边栏或内容区域元素不存在，跳过侧边栏初始化');
        return;
    }

    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');

            // 保存侧边栏状态
            SafeStorage.local.set('sidebar_collapsed', sidebar.classList.contains('collapsed'));
        });
    }

    // 恢复侧边栏状态
    const sidebarCollapsed = SafeStorage.local.get('sidebar_collapsed');
    if (sidebarCollapsed === 'true') {
        sidebar.classList.add('collapsed');
        content.classList.add('expanded');
    }

    // 移动端自动收起侧边栏
    if (window.innerWidth <= 992) {
        sidebar.classList.add('collapsed');
        content.classList.add('expanded');
    }

    // 窗口大小变化时调整侧边栏
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 992) {
            sidebar.classList.add('collapsed');
            content.classList.add('expanded');
        } else if (SafeStorage.local.get('sidebar_collapsed') !== 'true') {
            sidebar.classList.remove('collapsed');
            content.classList.remove('expanded');
        }
    });

    // 初始化菜单功能
    initializeNavigation();
}

/**
 * 初始化导航菜单
 */
function initializeNavigation() {
    // 处理下拉菜单
    initializeDropdownMenus();

    // 处理菜单激活状态
    initializeMenuActiveStates();

    // 处理菜单搜索
    initializeMenuSearch();

    // 处理菜单收藏
    initializeMenuFavorites();
}

/**
 * 初始化下拉菜单
 */
function initializeDropdownMenus() {
    const dropdownLinks = document.querySelectorAll('.nav-dropdown');

    dropdownLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const navItem = this.closest('.nav-item');
            const submenu = navItem.querySelector('.nav-submenu');

            if (!submenu) return;

            // 切换当前菜单
            const isExpanded = this.classList.contains('expanded');

            // 关闭其他展开的菜单
            dropdownLinks.forEach(otherLink => {
                if (otherLink !== this) {
                    otherLink.classList.remove('expanded');
                }
            });

            // 切换当前菜单状态
            if (isExpanded) {
                this.classList.remove('expanded');
            } else {
                this.classList.add('expanded');
            }

            // 保存展开状态
            const menuId = this.getAttribute('href') || this.dataset.menu;
            if (menuId) {
                const expandedMenus = JSON.parse(SafeStorage.local.get('expanded_menus') || '[]');
                if (isExpanded) {
                    const index = expandedMenus.indexOf(menuId);
                    if (index > -1) expandedMenus.splice(index, 1);
                } else {
                    if (!expandedMenus.includes(menuId)) {
                        expandedMenus.push(menuId);
                    }
                }
                SafeStorage.local.set('expanded_menus', JSON.stringify(expandedMenus));
            }
        });
    });

    // 恢复展开状态
    restoreMenuStates();
}

/**
 * 恢复菜单状态
 */
function restoreMenuStates() {
    try {
        const expandedMenus = JSON.parse(SafeStorage.local.get('expanded_menus') || '[]');

        expandedMenus.forEach(menuId => {
            const link = document.querySelector(`[href="${menuId}"], [data-menu="${menuId}"]`);
            if (link && link.classList && link.classList.contains('nav-dropdown')) {
                link.classList.add('expanded');
            }
        });

        // 自动展开包含当前页面的菜单
        const currentPage = new URLSearchParams(window.location.search).get('page');
        if (currentPage) {
            const activeSubmenuItem = document.querySelector(`.nav-submenu li.active`);
            if (activeSubmenuItem) {
                const parentNavItem = activeSubmenuItem.closest('.nav-item');
                if (parentNavItem) {
                    const parentDropdown = parentNavItem.querySelector('.nav-dropdown');
                    if (parentDropdown && parentDropdown.classList) {
                        parentDropdown.classList.add('expanded');
                    }
                }
            }
        }
    } catch (error) {
        console.warn('恢复菜单状态失败:', error);
    }
}

/**
 * 初始化菜单激活状态
 */
function initializeMenuActiveStates() {
    const navLinks = document.querySelectorAll('.nav-link:not(.nav-dropdown)');

    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            // 移除所有激活状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加当前激活状态
            const navItem = this.closest('.nav-item');
            if (navItem) {
                navItem.classList.add('active');

                // 如果是子菜单项，也激活父菜单
                const parentNavItem = navItem.closest('.nav-item');
                if (parentNavItem && parentNavItem !== navItem) {
                    parentNavItem.classList.add('active');
                }
            }
        });
    });
}

/**
 * 初始化菜单搜索
 */
function initializeMenuSearch() {
    // 创建搜索框（如果需要）
    const searchContainer = document.createElement('div');
    searchContainer.className = 'nav-search';
    searchContainer.innerHTML = `
        <div class="search-input-wrapper">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="搜索菜单..." id="navSearch">
            <button type="button" class="search-clear" style="display: none;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // 可以选择是否添加搜索功能
    // document.querySelector('.sidebar-user').after(searchContainer);

    const searchInput = document.getElementById('navSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();
            filterMenuItems(query);

            const clearBtn = this.parentElement.querySelector('.search-clear');
            clearBtn.style.display = query ? 'block' : 'none';
        });

        // 清除搜索
        const clearBtn = searchContainer.querySelector('.search-clear');
        clearBtn.addEventListener('click', function() {
            searchInput.value = '';
            filterMenuItems('');
            this.style.display = 'none';
            searchInput.focus();
        });
    }
}

/**
 * 过滤菜单项
 */
function filterMenuItems(query) {
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        const shouldShow = !query || text.includes(query);

        item.style.display = shouldShow ? 'block' : 'none';

        // 如果搜索到子菜单项，展开父菜单
        if (shouldShow && query) {
            const submenuItem = item.querySelector('.nav-submenu li');
            if (submenuItem && submenuItem.textContent.toLowerCase().includes(query)) {
                const dropdown = item.querySelector('.nav-dropdown');
                if (dropdown) {
                    dropdown.classList.add('expanded');
                }
            }
        }
    });

    // 处理分组标题
    const sections = document.querySelectorAll('.nav-section');
    sections.forEach(section => {
        const nextItems = [];
        let nextElement = section.nextElementSibling;

        while (nextElement && !nextElement.classList.contains('nav-section')) {
            if (nextElement.classList.contains('nav-item')) {
                nextItems.push(nextElement);
            }
            nextElement = nextElement.nextElementSibling;
        }

        const hasVisibleItems = nextItems.some(item => item.style.display !== 'none');
        section.style.display = hasVisibleItems ? 'block' : 'none';
    });
}

/**
 * 初始化菜单收藏
 */
function initializeMenuFavorites() {
    // 添加收藏功能（可选）
    const navLinks = document.querySelectorAll('.nav-link:not(.nav-dropdown)');

    navLinks.forEach(link => {
        // 可以添加右键菜单来收藏/取消收藏菜单项
        link.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            showContextMenu(e, this);
        });
    });
}

/**
 * 显示右键菜单
 */
function showContextMenu(event, element) {
    // 移除现有的上下文菜单
    const existingMenu = document.querySelector('.nav-context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }

    const menu = document.createElement('div');
    menu.className = 'nav-context-menu';
    menu.innerHTML = `
        <div class="context-menu-item" onclick="toggleFavorite('${element.href}')">
            <i class="fas fa-star"></i>
            <span>添加到收藏</span>
        </div>
        <div class="context-menu-item" onclick="openInNewTab('${element.href}')">
            <i class="fas fa-external-link-alt"></i>
            <span>新标签页打开</span>
        </div>
    `;

    menu.style.cssText = `
        position: fixed;
        top: ${event.clientY}px;
        left: ${event.clientX}px;
        z-index: 9999;
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-xs);
        min-width: 150px;
    `;

    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    setTimeout(() => {
        document.addEventListener('click', function closeMenu() {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        });
    }, 0);
}

/**
 * 切换收藏状态
 */
function toggleFavorite(url) {
    const favorites = JSON.parse(SafeStorage.local.get('menu_favorites') || '[]');
    const index = favorites.indexOf(url);

    if (index > -1) {
        favorites.splice(index, 1);
    } else {
        favorites.push(url);
    }

    SafeStorage.local.set('menu_favorites', JSON.stringify(favorites));
    updateFavoriteIndicators();
}

/**
 * 更新收藏指示器
 */
function updateFavoriteIndicators() {
    const favorites = JSON.parse(SafeStorage.local.get('menu_favorites') || '[]');

    document.querySelectorAll('.nav-link').forEach(link => {
        const isFavorite = favorites.includes(link.href);
        link.classList.toggle('favorited', isFavorite);
    });
}

/**
 * 在新标签页打开
 */
function openInNewTab(url) {
    window.open(url, '_blank');
}

/**
 * 初始化表格
 */
function initializeTables() {
    // 表格行点击高亮
    document.querySelectorAll('.table tbody tr').forEach(row => {
        row.addEventListener('click', function(e) {
            // 如果点击的是按钮或链接，不执行高亮
            if (e.target.closest('button') || e.target.closest('a') || e.target.closest('form')) {
                return;
            }
            
            // 移除其他行的高亮
            document.querySelectorAll('.table tbody tr').forEach(r => {
                r.classList.remove('table-active');
            });
            
            // 高亮当前行
            this.classList.add('table-active');
        });
    });
    
    // 全选功能
    const selectAllCheckbox = document.querySelector('#selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }
    
    // 单选框变化
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
}

/**
 * 更新批量操作按钮状态
 */
function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    const bulkActions = document.querySelector('.bulk-actions');
    
    if (bulkActions) {
        if (checkedBoxes.length > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = checkedBoxes.length;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

/**
 * 初始化表单
 */
function initializeForms() {
    // 表单验证
    document.querySelectorAll('form[data-validate]').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
    
    // 自动保存功能
    document.querySelectorAll('[data-autosave]').forEach(element => {
        let timeout;
        element.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                autoSave(this);
            }, 2000);
        });
    });
    
    // 文件上传预览
    document.querySelectorAll('input[type="file"]').forEach(input => {
        input.addEventListener('change', function() {
            previewFile(this);
        });
    });
}

/**
 * 表单验证
 */
function validateForm(form) {
    let isValid = true;
    
    // 清除之前的错误信息
    form.querySelectorAll('.is-invalid').forEach(element => {
        element.classList.remove('is-invalid');
    });
    
    form.querySelectorAll('.invalid-feedback').forEach(element => {
        element.remove();
    });
    
    // 验证必填字段
    form.querySelectorAll('[required]').forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, '此字段为必填项');
            isValid = false;
        }
    });
    
    // 验证邮箱格式
    form.querySelectorAll('input[type="email"]').forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, '请输入有效的邮箱地址');
            isValid = false;
        }
    });
    
    // 验证密码确认
    const password = form.querySelector('input[name="password"]');
    const confirmPassword = form.querySelector('input[name="confirm_password"]');
    if (password && confirmPassword && password.value !== confirmPassword.value) {
        showFieldError(confirmPassword, '密码确认不匹配');
        isValid = false;
    }
    
    return isValid;
}

/**
 * 显示字段错误
 */
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    
    field.parentNode.appendChild(feedback);
}

/**
 * 验证邮箱格式
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 自动保存
 */
function autoSave(element) {
    const form = element.closest('form');
    if (!form) return;
    
    const formData = new FormData(form);
    formData.append('action', 'autosave');
    
    fetch(form.action || window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('已自动保存', 'success');
        }
    })
    .catch(error => {
        console.error('自动保存失败:', error);
    });
}

/**
 * 文件预览
 */
function previewFile(input) {
    const file = input.files[0];
    if (!file) return;
    
    const preview = input.parentNode.querySelector('.file-preview');
    if (!preview) return;
    
    if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px;">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = `<div class="alert alert-info">已选择文件: ${file.name}</div>`;
    }
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * 初始化确认对话框
 */
function initializeConfirmDialogs() {
    document.querySelectorAll('[data-confirm]').forEach(element => {
        element.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });
}

/**
 * 显示Toast消息
 */
function showToast(message, type = 'info') {
    // 移除现有的toast
    const existingToast = document.querySelector('.toast-custom');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `toast-custom alert alert-${type} alert-dismissible fade show`;
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.style.minWidth = '300px';
    
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // 自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}

/**
 * AJAX请求封装
 */
function ajaxRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
}

/**
 * 批量删除
 */
function bulkDelete(url) {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    const ids = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (ids.length === 0) {
        showToast('请选择要删除的项目', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${ids.length} 个项目吗？`)) {
        return;
    }
    
    ajaxRequest(url, {
        method: 'POST',
        body: JSON.stringify({ action: 'bulk_delete', ids: ids })
    })
    .then(data => {
        if (data.success) {
            showToast('删除成功', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showToast(data.error || '删除失败', 'danger');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showToast('删除失败', 'danger');
    });
}

/**
 * 导出数据
 */
function exportData(url, format = 'csv') {
    const params = new URLSearchParams(window.location.search);
    params.append('export', format);
    
    const exportUrl = url + '?' + params.toString();
    
    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = '';
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showToast('导出已开始', 'info');
}

/**
 * 刷新页面数据
 */
function refreshData() {
    showToast('正在刷新数据...', 'info');
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

/**
 * 格式化数字
 */
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

/**
 * 格式化日期
 */
function formatDate(date) {
    return new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

/**
 * 复制到剪贴板
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败', 'danger');
    });
}

/**
 * 搜索功能增强
 */
function initializeSearch() {
    const searchInput = document.getElementById('quickSearch');
    if (!searchInput) return;

    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                performQuickSearch(query);
            }, 500);
        } else {
            hideSearchResults();
        }
    });

    // 点击外部隐藏搜索结果
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-container')) {
            hideSearchResults();
        }
    });
}

/**
 * 执行快速搜索
 */
function performQuickSearch(query) {
    const searchResults = document.getElementById('searchResults');
    if (!searchResults) {
        createSearchResults();
    }

    // 显示加载状态
    showSearchLoading();

    // 模拟搜索请求
    setTimeout(() => {
        const mockResults = [
            { type: 'user', title: '用户: ' + query, url: 'index.php?page=users&search=' + query },
            { type: 'domain', title: '域名: ' + query, url: 'index.php?page=domains&search=' + query },
            { type: 'order', title: '订单: ' + query, url: 'index.php?page=orders&search=' + query }
        ];

        displaySearchResults(mockResults);
    }, 300);
}

/**
 * 创建搜索结果容器
 */
function createSearchResults() {
    const searchContainer = document.querySelector('.search-container');
    const resultsDiv = document.createElement('div');
    resultsDiv.id = 'searchResults';
    resultsDiv.className = 'search-results';
    resultsDiv.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    `;
    searchContainer.appendChild(resultsDiv);
}

/**
 * 显示搜索加载状态
 */
function showSearchLoading() {
    const searchResults = document.getElementById('searchResults');
    searchResults.innerHTML = `
        <div class="p-3 text-center">
            <div class="loading"></div>
            <small class="text-muted">搜索中...</small>
        </div>
    `;
    searchResults.style.display = 'block';
}

/**
 * 显示搜索结果
 */
function displaySearchResults(results) {
    const searchResults = document.getElementById('searchResults');

    if (results.length === 0) {
        searchResults.innerHTML = `
            <div class="p-3 text-center text-muted">
                <i class="fas fa-search"></i>
                <div>没有找到相关结果</div>
            </div>
        `;
    } else {
        const resultsHtml = results.map(result => `
            <a href="${result.url}" class="search-result-item">
                <i class="fas fa-${getResultIcon(result.type)}"></i>
                <span>${result.title}</span>
            </a>
        `).join('');

        searchResults.innerHTML = resultsHtml;
    }

    searchResults.style.display = 'block';
}

/**
 * 获取结果图标
 */
function getResultIcon(type) {
    const icons = {
        user: 'user',
        domain: 'globe',
        order: 'shopping-cart',
        setting: 'cog'
    };
    return icons[type] || 'search';
}

/**
 * 隐藏搜索结果
 */
function hideSearchResults() {
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

/**
 * 数据表格增强
 */
function initializeDataTable() {
    // 表格排序
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            const column = this.dataset.column;
            const currentSort = this.dataset.sort || 'asc';
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';

            // 更新URL参数
            const url = new URL(window.location);
            url.searchParams.set('sort', column);
            url.searchParams.set('order', newSort);
            window.location.href = url.toString();
        });
    });

    // 表格筛选
    document.querySelectorAll('.table-filter').forEach(filter => {
        filter.addEventListener('change', function() {
            const column = this.dataset.column;
            const value = this.value;

            const url = new URL(window.location);
            if (value) {
                url.searchParams.set(column, value);
            } else {
                url.searchParams.delete(column);
            }
            window.location.href = url.toString();
        });
    });
}

/**
 * 通知系统增强
 */
function initializeNotifications() {
    // 检查新通知
    setInterval(checkNotifications, 30000); // 每30秒检查一次

    // 标记通知为已读
    document.addEventListener('click', function(e) {
        if (e.target.closest('.notification-item')) {
            const notificationId = e.target.closest('.notification-item').dataset.id;
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        }
    });
}

/**
 * 检查新通知
 */
function checkNotifications() {
    ajaxRequest('api/notifications.php?action=check')
        .then(data => {
            if (data.success && data.count > 0) {
                updateNotificationBadge(data.count);
            }
        })
        .catch(error => {
            console.error('检查通知失败:', error);
        });
}

/**
 * 更新通知徽章
 */
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'flex' : 'none';
    }
}

/**
 * 标记通知为已读
 */
function markNotificationAsRead(notificationId) {
    ajaxRequest('api/notifications.php?action=mark_read', {
        method: 'POST',
        body: JSON.stringify({ id: notificationId })
    })
    .then(data => {
        if (data.success) {
            // 更新UI
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.add('read');
            }
        }
    })
    .catch(error => {
        console.error('标记通知失败:', error);
    });
}

/**
 * 实时数据更新
 */
function initializeRealTimeUpdates() {
    // 仅在仪表盘页面启用
    if (window.location.search.includes('page=dashboard')) {
        setInterval(updateDashboardStats, 60000); // 每分钟更新一次
    }
}

/**
 * 更新仪表盘统计数据
 */
function updateDashboardStats() {
    ajaxRequest('api/dashboard.php?action=stats')
        .then(data => {
            if (data.success) {
                updateStatsCards(data.stats);
            }
        })
        .catch(error => {
            console.error('更新统计数据失败:', error);
        });
}

/**
 * 更新统计卡片
 */
function updateStatsCards(stats) {
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            const currentValue = parseInt(element.textContent.replace(/[^\d]/g, ''));
            const newValue = stats[key];

            if (currentValue !== newValue) {
                animateNumber(element, currentValue, newValue);
            }
        }
    });
}

/**
 * 数字动画
 */
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = formatNumber(current);

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

/**
 * 初始化动画
 */
function initializeAnimations() {
    try {
        // 页面加载动画
        setTimeout(() => {
            if (document.body && document.body.classList) {
                document.body.classList.add('loaded');
            }
        }, 100);

        // 检查浏览器是否支持IntersectionObserver
        if (!window.IntersectionObserver) {
            console.warn('浏览器不支持IntersectionObserver，跳过滚动动画');
            return;
        }

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.target && entry.target.classList) {
                    entry.target.classList.add('animate-fadeInUp');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        const animatedElements = document.querySelectorAll('.stats-card, .card, .quick-action-card');
        animatedElements.forEach(el => {
            if (el && observer) {
                observer.observe(el);
            }
        });

        // 悬停效果增强
        if (typeof initializeHoverEffects === 'function') {
            initializeHoverEffects();
        }
    } catch (error) {
        console.warn('初始化动画失败:', error);
    }
}

/**
 * 初始化悬停效果
 */
function initializeHoverEffects() {
    // 卡片悬停效果
    document.querySelectorAll('.stats-card, .quick-action-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 按钮点击效果
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * 初始化懒加载
 */
function initializeLazyLoading() {
    // 图片懒加载
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });

    // 内容懒加载
    const contentObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                if (element.dataset.lazyLoad) {
                    loadContent(element);
                    contentObserver.unobserve(element);
                }
            }
        });
    });

    document.querySelectorAll('[data-lazy-load]').forEach(el => {
        contentObserver.observe(el);
    });
}

/**
 * 初始化性能监控
 */
function initializePerformanceMonitoring() {
    // 监控页面性能
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

                if (loadTime > 3000) {
                    console.warn('页面加载时间过长:', loadTime + 'ms');
                }
            }, 0);
        });
    }

    // 监控内存使用
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                console.warn('内存使用率过高');
            }
        }, 30000);
    }
}

/**
 * 加载内容
 */
function loadContent(element) {
    const url = element.dataset.lazyLoad;
    if (!url) return;

    // 显示加载状态
    element.innerHTML = '<div class="skeleton skeleton-text"></div>'.repeat(3);

    fetch(url)
        .then(response => response.text())
        .then(html => {
            element.innerHTML = html;
            element.classList.add('animate-fadeInUp');
        })
        .catch(error => {
            element.innerHTML = '<div class="alert alert-danger">加载失败</div>';
            console.error('内容加载失败:', error);
        });
}

// 重复的initializeAdmin函数已删除，使用前面定义的版本

// 添加缺失的函数
function setTheme(theme) {
    try {
        document.documentElement.setAttribute('data-theme', theme);
        SafeStorage.local.set('theme', theme);
        console.log('主题已设置为:', theme);
    } catch (error) {
        console.error('设置主题失败:', error);
    }
}

function handlePageSizeChange(size) {
    try {
        SafeStorage.local.set('pageSize', size);
        // 重新加载当前页面数据
        if (typeof refreshData === 'function') {
            refreshData();
        } else {
            location.reload();
        }
        console.log('页面大小已设置为:', size);
    } catch (error) {
        console.error('设置页面大小失败:', error);
    }
}

function asyncReadyCallback() {
    try {
        console.log('异步回调已执行');
        // 这里可以添加异步初始化逻辑
        if (typeof initializeAsyncComponents === 'function') {
            initializeAsyncComponents();
        }
    } catch (error) {
        console.error('异步回调执行失败:', error);
    }
}

// 初始化异步组件
function initializeAsyncComponents() {
    try {
        // 初始化图表
        if (typeof initializeCharts === 'function') {
            initializeCharts();
        }

        // 初始化实时数据
        if (typeof initializeRealTimeData === 'function') {
            initializeRealTimeData();
        }

        console.log('异步组件初始化完成');
    } catch (error) {
        console.error('异步组件初始化失败:', error);
    }
}

// 全局函数，供HTML调用
window.adminJS = {
    showToast,
    ajaxRequest,
    bulkDelete,
    exportData,
    refreshData,
    copyToClipboard,
    formatNumber,
    formatDate,
    performQuickSearch,
    hideSearchResults,
    updateNotificationBadge,
    animateNumber,
    setTheme,
    handlePageSizeChange,
    asyncReadyCallback
};

// 将函数和对象暴露到全局作用域
window.setTheme = setTheme;
window.handlePageSizeChange = handlePageSizeChange;
window.asyncReadyCallback = asyncReadyCallback;
window.SafeStorage = SafeStorage;

// 确保所有函数都可以在全局访问
window.showToast = showToast;
window.ajaxRequest = ajaxRequest;
window.initializeAdmin = initializeAdmin;
