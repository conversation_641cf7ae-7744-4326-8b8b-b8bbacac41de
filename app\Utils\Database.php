<?php
/**
 * 数据库工具类
 * Database Utility Class
 */

class Database {
    private static $instance = null;
    private $connection = null;
    private static $queryLog = [];
    private $config = [];
    
    /**
     * 私有构造函数
     */
    private function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        // 使用硬编码的数据库配置
        $this->config = [
            'host' => 'localhost',
            'port' => '3306',
            'database' => 'www_bt_cn',
            'username' => 'www_bt_cn',
            'password' => 'YAfxfrB8nr6F84LP',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ];
    }
    
    /**
     * 建立数据库连接
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            
            $this->connection = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                $this->config['options']
            );
            
            // 设置时区
            $this->connection->exec("SET time_zone = '+08:00'");
            
        } catch (PDOException $e) {
            error_log('数据库连接失败: ' . $e->getMessage());
            throw new Exception('数据库连接失败');
        }
    }
    
    /**
     * 获取数据库连接
     */
    public function getConnection() {
        if ($this->connection === null) {
            $this->connect();
        }
        return $this->connection;
    }
    
    /**
     * 执行查询
     */
    public function query($sql, $params = []) {
        $startTime = microtime(true);
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            
            $this->logQuery($sql, $params, microtime(true) - $startTime);
            
            return $stmt;
            
        } catch (PDOException $e) {
            $this->logQuery($sql, $params, microtime(true) - $startTime, $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取单行数据
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 获取多行数据
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 插入数据
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = array_map(function($field) { return ':' . $field; }, $fields);
        
        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $placeholders) . ")";
        
        $params = [];
        foreach ($data as $field => $value) {
            $params[':' . $field] = $value;
        }
        
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    /**
     * 更新数据
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        $params = [];
        
        foreach ($data as $field => $value) {
            $setClause[] = "`{$field}` = :{$field}";
            $params[':' . $field] = $value;
        }
        
        $sql = "UPDATE `{$table}` SET " . implode(', ', $setClause) . " WHERE {$where}";
        
        // 合并WHERE参数
        $params = array_merge($params, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 删除数据
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM `{$table}` WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * 执行事务
     */
    public function transaction($callback) {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 分页查询
     */
    public function paginate($sql, $params = [], $page = 1, $perPage = 20) {
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as count_table";
        $totalResult = $this->fetchOne($countSql, $params);
        $total = $totalResult['total'];
        
        // 计算偏移量
        $offset = ($page - 1) * $perPage;
        
        // 添加LIMIT子句
        $paginatedSql = $sql . " LIMIT {$offset}, {$perPage}";
        $data = $this->fetchAll($paginatedSql, $params);
        
        return [
            'data' => $data,
            'total' => (int)$total,
            'page' => (int)$page,
            'per_page' => (int)$perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * 记录查询日志
     */
    private function logQuery($sql, $params, $executionTime, $error = null) {
        $logEntry = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => round($executionTime * 1000, 2), // 毫秒
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $error
        ];
        
        self::$queryLog[] = $logEntry;
        
        // 记录慢查询
        if ($executionTime > 1.0) { // 超过1秒的查询
            error_log("慢查询: " . json_encode($logEntry, JSON_UNESCAPED_UNICODE));
        }
        
        // 记录错误查询
        if ($error) {
            error_log("SQL错误: " . json_encode($logEntry, JSON_UNESCAPED_UNICODE));
        }
    }
    
    /**
     * 获取查询日志
     */
    public static function getQueryLog() {
        return self::$queryLog;
    }
    
    /**
     * 清空查询日志
     */
    public static function clearQueryLog() {
        self::$queryLog = [];
    }
    
    /**
     * 检查表是否存在
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->fetchOne($sql, [$tableName]);
        return $result !== false;
    }
    
    /**
     * 获取表结构
     */
    public function getTableSchema($tableName) {
        $sql = "DESCRIBE `{$tableName}`";
        return $this->fetchAll($sql);
    }
    
    /**
     * 执行原生SQL
     */
    public function raw($sql) {
        return $this->connection->exec($sql);
    }
    
    /**
     * 获取数据库信息
     */
    public function getDatabaseInfo() {
        $info = [];
        
        // 数据库版本
        $version = $this->fetchOne("SELECT VERSION() as version");
        $info['version'] = $version['version'];
        
        // 数据库大小
        $size = $this->fetchOne("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = ?
        ", [$this->config['database']]);
        $info['size_mb'] = $size['size_mb'];
        
        // 表数量
        $tables = $this->fetchOne("
            SELECT COUNT(*) as table_count 
            FROM information_schema.tables 
            WHERE table_schema = ?
        ", [$this->config['database']]);
        $info['table_count'] = $tables['table_count'];
        
        return $info;
    }
    
    /**
     * 防止克隆
     */
    private function __clone() {}
    
    /**
     * 防止反序列化
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}
?>
