<?php
/**
 * NameSilo操作AJAX处理
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 关闭错误显示，避免破坏JSON

// 定义根路径
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 引入统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database.php';
}

// 数据库连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    return $pdo;
}

// 引入NameSilo API客户端
require_once ROOT_PATH . '/public/api/NameSiloAPI.php';

// 获取NameSilo客户端
function getNameSiloClient() {
    $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
    $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
    $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

    if (empty($apiKey)) {
        throw new Exception('NameSilo API密钥未配置');
    }

    return new NameSiloAPI($apiKey, $apiUrl, $sandbox);
}

// 设置JSON响应头
header('Content-Type: application/json');

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $_GET['action'] ?? ($input['action'] ?? '');

    if (empty($action)) {
        throw new Exception('缺少操作参数');
    }

    $domain = $input['domain'] ?? '';
    $domainId = $input['domain_id'] ?? 0;
    
    // 获取数据库连接和NameSilo客户端
    $db = getDatabase();
    $client = getNameSiloClient();
    
    switch ($action) {
        case 'renew_domain':
            // 续费域名
            $years = $input['years'] ?? 1;
            
            try {
                $result = $client->renewDomain($domain, $years);
                
                // 更新数据库中的到期时间
                if (isset($result['new_expiry_date'])) {
                    $stmt = $db->prepare("UPDATE domains SET expiry_date = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$result['new_expiry_date'], $domainId]);
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => '域名续费成功',
                    'data' => $result
                ]);
                
            } catch (Exception $e) {
                throw new Exception('续费失败: ' . $e->getMessage());
            }
            break;
            
        case 'toggle_auto_renew':
            // 切换自动续费
            $enabled = $input['enabled'] ?? false;
            
            try {
                $result = $client->setAutoRenew($domain, $enabled);
                
                // 更新数据库
                $stmt = $db->prepare("UPDATE domains SET auto_renew = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$enabled ? 1 : 0, $domainId]);
                
                echo json_encode([
                    'success' => true,
                    'message' => '自动续费设置已更新',
                    'enabled' => $enabled
                ]);
                
            } catch (Exception $e) {
                throw new Exception('设置失败: ' . $e->getMessage());
            }
            break;
            
        case 'get_domain_info':
            // 获取域名详细信息
            try {
                $result = $client->getDomainInfo($domain);
                
                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
                
            } catch (Exception $e) {
                throw new Exception('获取信息失败: ' . $e->getMessage());
            }
            break;
            
        case 'check_availability':
            // 检查域名可用性
            try {
                $result = $client->checkDomainAvailability($domain);
                
                echo json_encode([
                    'success' => true,
                    'available' => $result['available'],
                    'message' => $result['available'] ? '域名可注册' : '域名已被注册'
                ]);
                
            } catch (Exception $e) {
                throw new Exception('查询失败: ' . $e->getMessage());
            }
            break;
            
        case 'get_dns_records':
            // 获取DNS记录
            try {
                $result = $client->getDNSRecords($domain);

                echo json_encode([
                    'success' => true,
                    'records' => $result
                ]);

            } catch (Exception $e) {
                throw new Exception('获取DNS记录失败: ' . $e->getMessage());
            }
            break;

        case 'sync-prices':
            // 同步域名价格 - 使用专用价格API
            try {
                // 检查domain_tlds表是否存在
                $stmt = $db->query("SHOW TABLES LIKE 'domain_tlds'");
                if (!$stmt->fetch()) {
                    throw new Exception('domain_tlds表不存在，请先创建表');
                }

                // 调用专用价格API获取完整价格列表
                $apiUrl = 'http://127.0.0.1:777/public/admin/ajax/domain-price-api.php?action=sync-to-database';

                $ch = curl_init();
                curl_setopt_array($ch, [
                    CURLOPT_URL => $apiUrl,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_POST => true,
                    CURLOPT_HTTPHEADER => ['Content-Type: application/json']
                ]);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);

                if ($error) {
                    throw new Exception('API调用失败: ' . $error);
                }

                if ($httpCode !== 200) {
                    throw new Exception('API返回错误: HTTP ' . $httpCode);
                }

                $result = json_decode($response, true);
                if (!$result || !$result['success']) {
                    throw new Exception('API返回错误: ' . ($result['error'] ?? '未知错误'));
                }

                echo json_encode([
                    'success' => true,
                    'message' => '域名价格同步成功',
                    'added' => $result['added'],
                    'updated' => $result['updated'],
                    'total' => $result['total']
                ]);

            } catch (Exception $e) {
                throw new Exception('同步价格失败: ' . $e->getMessage());
            }
            break;

        case 'sync-domains':
            // 同步销售域名列表
            try {
                // 获取NameSilo域名列表
                $domains = $client->listDomains();

                if (empty($domains)) {
                    echo json_encode([
                        'success' => true,
                        'message' => '没有找到域名',
                        'added' => 0,
                        'updated' => 0
                    ]);
                    break;
                }

                // 检查domains表是否存在
                $stmt = $db->query("SHOW TABLES LIKE 'domains'");
                if (!$stmt->fetch()) {
                    // 创建domains表
                    $db->exec("
                        CREATE TABLE IF NOT EXISTS `domains` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `domain` varchar(255) NOT NULL,
                            `user_id` int(11) DEFAULT NULL,
                            `registrar` varchar(50) DEFAULT 'namesilo',
                            `status` varchar(50) DEFAULT 'active',
                            `registration_date` date DEFAULT NULL,
                            `expiry_date` date DEFAULT NULL,
                            `auto_renew` tinyint(1) DEFAULT 1,
                            `privacy_protection` tinyint(1) DEFAULT 1,
                            `locked` tinyint(1) DEFAULT 0,
                            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `domain` (`domain`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                }

                $added = 0;
                $updated = 0;

                foreach ($domains as $domain) {
                    // 获取域名详细信息
                    $info = $client->getDomainInfo($domain);

                    if (empty($info)) continue;

                    // 检查域名是否已存在
                    $stmt = $db->prepare("SELECT id FROM domains WHERE domain = ?");
                    $stmt->execute([$domain]);
                    $exists = $stmt->fetch();

                    if ($exists) {
                        // 更新现有域名
                        $stmt = $db->prepare("
                            UPDATE domains
                            SET status = ?,
                                registration_date = ?,
                                expiry_date = ?,
                                auto_renew = ?,
                                privacy_protection = ?,
                                locked = ?,
                                updated_at = NOW()
                            WHERE domain = ?
                        ");
                        $stmt->execute([
                            $info['status'] ?? 'active',
                            $info['created_date'] ?? null,
                            $info['expires'] ?? null,
                            $info['auto_renew'] ?? 1,
                            $info['private'] ?? 1,
                            $info['locked'] ?? 0,
                            $domain
                        ]);
                        $updated++;
                    } else {
                        // 添加新域名
                        $stmt = $db->prepare("
                            INSERT INTO domains (
                                domain, registrar, status, registration_date,
                                expiry_date, auto_renew, privacy_protection, locked,
                                created_at, updated_at
                            ) VALUES (?, 'namesilo', ?, ?, ?, ?, ?, ?, NOW(), NOW())
                        ");
                        $stmt->execute([
                            $domain,
                            $info['status'] ?? 'active',
                            $info['created_date'] ?? null,
                            $info['expires'] ?? null,
                            $info['auto_renew'] ?? 1,
                            $info['private'] ?? 1,
                            $info['locked'] ?? 0
                        ]);
                        $added++;
                    }
                }

                echo json_encode([
                    'success' => true,
                    'message' => '域名同步成功',
                    'added' => $added,
                    'updated' => $updated
                ]);

            } catch (Exception $e) {
                throw new Exception('同步域名失败: ' . $e->getMessage());
            }
            break;

        default:
            throw new Exception('不支持的操作: ' . $action);
    }
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("NameSilo操作错误: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
