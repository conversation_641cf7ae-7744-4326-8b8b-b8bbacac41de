<?php
/**
 * DNS管理API - 与域名商API交互
 * DNS Management API - Interact with Domain Registrar API
 */

session_start();
require_once '../../config.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未登录']);
    exit;
}

$userId = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];
$domain = $_GET['domain'] ?? $_POST['domain'] ?? '';

if (empty($domain)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => '域名参数缺失']);
    exit;
}

try {
    $db = getDatabase();
    
    // 验证域名所有权
    $stmt = $db->prepare("SELECT id, registrar, api_key FROM domains WHERE domain_name = ? AND user_id = ?");
    $stmt->execute([$domain, $userId]);
    $domainInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$domainInfo) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => '域名不存在或无权限']);
        exit;
    }
    
    // 根据请求方法处理
    switch ($method) {
        case 'GET':
            $action = $_GET['action'] ?? 'list';
            switch ($action) {
                case 'list':
                    getDNSRecords($domain, $domainInfo);
                    break;
                case 'nameservers':
                    getNameservers($domain, $domainInfo);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => '无效的操作']);
            }
            break;
            
        case 'POST':
            $action = $_POST['action'] ?? '';
            switch ($action) {
                case 'add':
                    addDNSRecord($domain, $domainInfo);
                    break;
                case 'update':
                    updateDNSRecord($domain, $domainInfo);
                    break;
                case 'delete':
                    deleteDNSRecord($domain, $domainInfo);
                    break;
                case 'update_nameservers':
                    updateNameservers($domain, $domainInfo);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => '无效的操作']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
    }
    
} catch (Exception $e) {
    error_log("DNS API错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => '服务器内部错误: ' . $e->getMessage()]);
}

/**
 * 获取DNS记录
 */
function getDNSRecords($domain, $domainInfo) {
    try {
        $registrar = $domainInfo['registrar'] ?? 'namesilo';
        
        switch ($registrar) {
            case 'namesilo':
                $records = getNamesiloDNSRecords($domain);
                break;
            default:
                // 默认使用模拟数据
                $records = getMockDNSRecords($domain);
        }
        
        echo json_encode([
            'success' => true,
            'domain' => $domain,
            'records' => $records,
            'registrar' => $registrar
        ]);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 获取域名服务器
 */
function getNameservers($domain, $domainInfo) {
    try {
        $registrar = $domainInfo['registrar'] ?? 'namesilo';
        
        switch ($registrar) {
            case 'namesilo':
                $nameservers = getNamesiloNameservers($domain);
                break;
            default:
                $nameservers = getMockNameservers($domain);
        }
        
        echo json_encode([
            'success' => true,
            'domain' => $domain,
            'nameservers' => $nameservers,
            'registrar' => $registrar
        ]);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * NameSilo API - 获取DNS记录
 */
function getNamesiloDNSRecords($domain) {
    $apiKey = getNamesiloApiKey();
    $url = "https://www.namesilo.com/api/dnsListRecords?version=1&type=xml&key={$apiKey}&domain={$domain}";
    
    $response = makeApiRequest($url);
    
    if (!$response) {
        throw new Exception('无法连接到NameSilo API');
    }
    
    $xml = simplexml_load_string($response);
    
    if (!$xml || (string)$xml->reply->code !== '300') {
        throw new Exception('NameSilo API返回错误: ' . (string)($xml->reply->detail ?? 'Unknown error'));
    }
    
    $records = [];
    if (isset($xml->reply->resource_record)) {
        foreach ($xml->reply->resource_record as $record) {
            $records[] = [
                'id' => (string)$record->record_id,
                'type' => (string)$record->type,
                'host' => (string)$record->host,
                'value' => (string)$record->value,
                'ttl' => (int)$record->ttl,
                'priority' => isset($record->priority) ? (int)$record->priority : null
            ];
        }
    }
    
    return $records;
}

/**
 * NameSilo API - 获取域名服务器
 */
function getNamesiloNameservers($domain) {
    $apiKey = getNamesiloApiKey();
    $url = "https://www.namesilo.com/api/getDomainInfo?version=1&type=xml&key={$apiKey}&domain={$domain}";
    
    $response = makeApiRequest($url);
    
    if (!$response) {
        throw new Exception('无法连接到NameSilo API');
    }
    
    $xml = simplexml_load_string($response);
    
    if (!$xml || (string)$xml->reply->code !== '300') {
        throw new Exception('NameSilo API返回错误: ' . (string)($xml->reply->detail ?? 'Unknown error'));
    }
    
    $nameservers = [];
    if (isset($xml->reply->nameservers->nameserver)) {
        foreach ($xml->reply->nameservers->nameserver as $ns) {
            $nameservers[] = (string)$ns;
        }
    }
    
    return $nameservers;
}

/**
 * 模拟DNS记录数据（用于测试）
 */
function getMockDNSRecords($domain) {
    return [
        [
            'id' => 'mock_1',
            'type' => 'A',
            'host' => '@',
            'value' => '*************',
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'mock_2',
            'type' => 'CNAME',
            'host' => 'www',
            'value' => $domain,
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'mock_3',
            'type' => 'MX',
            'host' => '@',
            'value' => 'mail.' . $domain,
            'ttl' => 3600,
            'priority' => 10
        ]
    ];
}

/**
 * 模拟域名服务器数据
 */
function getMockNameservers($domain) {
    return [
        'ns1.namesilo.com',
        'ns2.namesilo.com'
    ];
}

/**
 * 获取NameSilo API密钥
 */
function getNamesiloApiKey() {
    // 从配置文件或环境变量获取API密钥
    return defined('NAMESILO_API_KEY') ? NAMESILO_API_KEY : 'your_api_key_here';
}

/**
 * 发送API请求
 */
function makeApiRequest($url, $timeout = 30) {
    $context = stream_context_create([
        'http' => [
            'timeout' => $timeout,
            'user_agent' => 'NameSilo Domain Manager/1.0'
        ]
    ]);
    
    return @file_get_contents($url, false, $context);
}

/**
 * 添加DNS记录
 */
function addDNSRecord($domain, $domainInfo) {
    $type = $_POST['type'] ?? '';
    $name = $_POST['name'] ?? '';
    $value = $_POST['value'] ?? '';
    $ttl = (int)($_POST['ttl'] ?? 3600);
    $priority = $_POST['priority'] ?? null;

    if (empty($type) || empty($name) || empty($value)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '必填字段不能为空']);
        return;
    }

    $registrar = $domainInfo['registrar'] ?? 'namesilo';

    try {
        switch ($registrar) {
            case 'namesilo':
                $result = addNamesiloDNSRecord($domain, $type, $name, $value, $ttl, $priority);
                break;
            default:
                // 模拟成功
                $result = [
                    'success' => true,
                    'record_id' => 'mock_' . time(),
                    'message' => 'DNS记录添加成功（模拟）'
                ];
        }

        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'message' => $result['message'],
                'record' => [
                    'id' => $result['record_id'],
                    'type' => $type,
                    'host' => $name,
                    'value' => $value,
                    'ttl' => $ttl,
                    'priority' => $priority
                ]
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => $result['error']]);
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

/**
 * 更新DNS记录
 */
function updateDNSRecord($domain, $domainInfo) {
    $recordId = $_POST['record_id'] ?? '';
    $type = $_POST['type'] ?? '';
    $name = $_POST['name'] ?? '';
    $value = $_POST['value'] ?? '';
    $ttl = (int)($_POST['ttl'] ?? 3600);
    $priority = $_POST['priority'] ?? null;

    if (empty($recordId) || empty($type) || empty($name) || empty($value)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '必填字段不能为空']);
        return;
    }

    $registrar = $domainInfo['registrar'] ?? 'namesilo';

    try {
        switch ($registrar) {
            case 'namesilo':
                $result = updateNamesiloDNSRecord($domain, $recordId, $type, $name, $value, $ttl, $priority);
                break;
            default:
                $result = ['success' => true, 'message' => 'DNS记录更新成功（模拟）'];
        }

        echo json_encode($result);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

/**
 * 删除DNS记录
 */
function deleteDNSRecord($domain, $domainInfo) {
    $recordId = $_POST['record_id'] ?? '';

    if (empty($recordId)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '记录ID不能为空']);
        return;
    }

    $registrar = $domainInfo['registrar'] ?? 'namesilo';

    try {
        switch ($registrar) {
            case 'namesilo':
                $result = deleteNamesiloDNSRecord($domain, $recordId);
                break;
            default:
                $result = ['success' => true, 'message' => 'DNS记录删除成功（模拟）'];
        }

        echo json_encode($result);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

/**
 * NameSilo API - 添加DNS记录
 */
function addNamesiloDNSRecord($domain, $type, $host, $value, $ttl, $priority = null) {
    $apiKey = getNamesiloApiKey();
    $url = "https://www.namesilo.com/api/dnsAddRecord?version=1&type=xml&key={$apiKey}&domain={$domain}&rrtype={$type}&rrhost={$host}&rrvalue=" . urlencode($value) . "&rrttl={$ttl}";

    if ($priority !== null && in_array($type, ['MX', 'SRV'])) {
        $url .= "&rrpriority={$priority}";
    }

    $response = makeApiRequest($url);

    if (!$response) {
        throw new Exception('无法连接到NameSilo API');
    }

    $xml = simplexml_load_string($response);

    if (!$xml || (string)$xml->reply->code !== '300') {
        throw new Exception('NameSilo API返回错误: ' . (string)($xml->reply->detail ?? 'Unknown error'));
    }

    return [
        'success' => true,
        'record_id' => (string)$xml->reply->record_id,
        'message' => 'DNS记录添加成功'
    ];
}

/**
 * NameSilo API - 更新DNS记录
 */
function updateNamesiloDNSRecord($domain, $recordId, $type, $host, $value, $ttl, $priority = null) {
    $apiKey = getNamesiloApiKey();
    $url = "https://www.namesilo.com/api/dnsUpdateRecord?version=1&type=xml&key={$apiKey}&domain={$domain}&rrid={$recordId}&rrhost={$host}&rrvalue=" . urlencode($value) . "&rrttl={$ttl}";

    if ($priority !== null && in_array($type, ['MX', 'SRV'])) {
        $url .= "&rrpriority={$priority}";
    }

    $response = makeApiRequest($url);

    if (!$response) {
        throw new Exception('无法连接到NameSilo API');
    }

    $xml = simplexml_load_string($response);

    if (!$xml || (string)$xml->reply->code !== '300') {
        throw new Exception('NameSilo API返回错误: ' . (string)($xml->reply->detail ?? 'Unknown error'));
    }

    return [
        'success' => true,
        'message' => 'DNS记录更新成功'
    ];
}

/**
 * NameSilo API - 删除DNS记录
 */
function deleteNamesiloDNSRecord($domain, $recordId) {
    $apiKey = getNamesiloApiKey();
    $url = "https://www.namesilo.com/api/dnsDeleteRecord?version=1&type=xml&key={$apiKey}&domain={$domain}&rrid={$recordId}";

    $response = makeApiRequest($url);

    if (!$response) {
        throw new Exception('无法连接到NameSilo API');
    }

    $xml = simplexml_load_string($response);

    if (!$xml || (string)$xml->reply->code !== '300') {
        throw new Exception('NameSilo API返回错误: ' . (string)($xml->reply->detail ?? 'Unknown error'));
    }

    return [
        'success' => true,
        'message' => 'DNS记录删除成功'
    ];
}
?>
