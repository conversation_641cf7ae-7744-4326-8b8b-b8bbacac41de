<?php
$db = getDatabase();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'clear_logs':
            $days = intval($_POST['days'] ?? 30);
            $stmt = $db->prepare("DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
            $stmt->execute([$days]);
            $message = "已清理 {$days} 天前的日志";
            break;
            
        case 'clear_all_logs':
            $db->exec("TRUNCATE TABLE system_logs");
            $message = '已清空所有日志';
            break;
    }
}

// 获取日志列表
$level = $_GET['level'] ?? '';
$search = $_GET['search'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page_num = max(1, intval($_GET['page_num'] ?? 1));
$limit = 50;
$offset = ($page_num - 1) * $limit;

$where = [];
$params = [];

if ($level) {
    $where[] = "level = ?";
    $params[] = $level;
}

if ($search) {
    $where[] = "message LIKE ?";
    $params[] = "%$search%";
}

if ($date_from) {
    $where[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// 获取总数
$countSql = "SELECT COUNT(*) FROM system_logs $whereClause";
$totalLogs = $db->prepare($countSql);
$totalLogs->execute($params);
$totalCount = $totalLogs->fetchColumn();
$totalPages = ceil($totalCount / $limit);

// 获取日志列表
$sql = "SELECT * FROM system_logs $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $db->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();

// 获取统计数据
$stats = $db->query("SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN level = 'error' THEN 1 ELSE 0 END) as errors,
    SUM(CASE WHEN level = 'warning' THEN 1 ELSE 0 END) as warnings,
    SUM(CASE WHEN level = 'info' THEN 1 ELSE 0 END) as info,
    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_logs
    FROM system_logs")->fetch();
?>

<!-- 消息提示 -->
<?php if (isset($message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">系统日志</h1>
        <p class="text-muted">查看系统运行日志和错误信息</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
            <i class="fas fa-broom me-2"></i>
            清理日志
        </button>
        <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
            <i class="fas fa-sync-alt me-2"></i>
            刷新
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-list"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总日志数</h6>
                        <h4 class="mb-0"><?= number_format($stats['total'] ?? 0) ?></h4>
                        <small class="text-success">今日: <?= $stats['today_logs'] ?? 0 ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-danger bg-opacity-10 text-danger rounded-3 p-3 me-3">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">错误</h6>
                        <h4 class="mb-0"><?= number_format($stats['errors'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">警告</h6>
                        <h4 class="mb-0"><?= number_format($stats['warnings'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">信息</h6>
                        <h4 class="mb-0"><?= number_format($stats['info'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="logs">
            <div class="col-md-3">
                <label class="form-label">搜索</label>
                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="搜索日志内容...">
            </div>
            <div class="col-md-2">
                <label class="form-label">日志级别</label>
                <select class="form-select" name="level">
                    <option value="">全部级别</option>
                    <option value="debug" <?= $level === 'debug' ? 'selected' : '' ?>>调试</option>
                    <option value="info" <?= $level === 'info' ? 'selected' : '' ?>>信息</option>
                    <option value="notice" <?= $level === 'notice' ? 'selected' : '' ?>>通知</option>
                    <option value="warning" <?= $level === 'warning' ? 'selected' : '' ?>>警告</option>
                    <option value="error" <?= $level === 'error' ? 'selected' : '' ?>>错误</option>
                    <option value="critical" <?= $level === 'critical' ? 'selected' : '' ?>>严重</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">开始日期</label>
                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">结束日期</label>
                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>
                        搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 日志列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            系统日志 (共 <?= number_format($totalCount) ?> 条记录)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>时间</th>
                        <th>级别</th>
                        <th>消息</th>
                        <th>用户</th>
                        <th>IP地址</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($logs)): ?>
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无日志数据</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($logs as $log): ?>
                    <tr>
                        <td>
                            <span class="text-muted">
                                <?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            $levelClass = [
                                'debug' => 'secondary',
                                'info' => 'info',
                                'notice' => 'primary',
                                'warning' => 'warning',
                                'error' => 'danger',
                                'critical' => 'danger',
                                'alert' => 'danger',
                                'emergency' => 'danger'
                            ][$log['level']] ?? 'secondary';
                            $levelText = [
                                'debug' => '调试',
                                'info' => '信息',
                                'notice' => '通知',
                                'warning' => '警告',
                                'error' => '错误',
                                'critical' => '严重',
                                'alert' => '警报',
                                'emergency' => '紧急'
                            ][$log['level']] ?? $log['level'];
                            ?>
                            <span class="badge bg-<?= $levelClass ?>"><?= $levelText ?></span>
                        </td>
                        <td>
                            <div class="log-message">
                                <?= htmlspecialchars(mb_substr($log['message'], 0, 100)) ?>
                                <?php if (mb_strlen($log['message']) > 100): ?>
                                <span class="text-muted">...</span>
                                <button type="button" class="btn btn-link btn-sm p-0 ms-1" onclick="showFullMessage(<?= $log['id'] ?>)">
                                    查看完整
                                </button>
                                <?php endif; ?>
                            </div>
                            <?php if ($log['context']): ?>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                包含上下文数据
                            </small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($log['user_id']): ?>
                            <span class="text-primary">用户 #<?= $log['user_id'] ?></span>
                            <?php else: ?>
                            <span class="text-muted">系统</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="text-muted font-monospace">
                                <?= htmlspecialchars($log['ip_address'] ?? 'N/A') ?>
                            </span>
                        </td>
                        <td>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="showLogDetails(<?= $log['id'] ?>)">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="card-footer">
        <nav aria-label="日志列表分页">
            <ul class="pagination justify-content-center mb-0">
                <?php if ($page_num > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=logs&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&level=<?= urlencode($level) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                    <a class="page-link" href="?page=logs&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&level=<?= urlencode($level) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page_num < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=logs&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&level=<?= urlencode($level) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- 清理日志模态框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-broom me-2"></i>
                    清理日志
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        清理操作不可恢复，请谨慎操作！
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">清理选项</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="clear_type" id="clear_old" value="old" checked>
                            <label class="form-check-label" for="clear_old">
                                清理旧日志
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="clear_type" id="clear_all" value="all">
                            <label class="form-check-label" for="clear_all">
                                清空所有日志
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="daysInput">
                        <label class="form-label">保留天数</label>
                        <input type="number" class="form-control" name="days" value="30" min="1" max="365">
                        <small class="text-muted">清理指定天数之前的日志</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning" id="clearBtn" name="action" value="clear_logs">
                        <i class="fas fa-broom me-2"></i>
                        清理日志
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function refreshLogs() {
    window.location.reload();
}

function showLogDetails(logId) {
    alert('日志详情功能开发中...');
}

function showFullMessage(logId) {
    alert('完整消息功能开发中...');
}

// 清理类型切换
document.addEventListener('DOMContentLoaded', function() {
    const clearOld = document.getElementById('clear_old');
    const clearAll = document.getElementById('clear_all');
    const daysInput = document.getElementById('daysInput');
    const clearBtn = document.getElementById('clearBtn');
    
    function toggleClearType() {
        if (clearAll.checked) {
            daysInput.style.display = 'none';
            clearBtn.value = 'clear_all_logs';
            clearBtn.innerHTML = '<i class="fas fa-broom me-2"></i>清空所有日志';
        } else {
            daysInput.style.display = 'block';
            clearBtn.value = 'clear_logs';
            clearBtn.innerHTML = '<i class="fas fa-broom me-2"></i>清理日志';
        }
    }
    
    clearOld.addEventListener('change', toggleClearType);
    clearAll.addEventListener('change', toggleClearType);
});
</script>
