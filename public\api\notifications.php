<?php
/**
 * 通知API
 * Notifications API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once '../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDatabase();
    
    // 检查用户登录
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db, $userId);
            break;
            
        case 'POST':
            handlePostRequest($db, $userId, $input);
            break;
            
        case 'PUT':
            handlePutRequest($db, $userId, $input);
            break;
            
        case 'DELETE':
            handleDeleteRequest($db, $userId, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误：' . $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取通知列表
 */
function handleGetRequest($db, $userId) {
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'list':
            getNotifications($db, $userId);
            break;
            
        case 'unread_count':
            getUnreadCount($db, $userId);
            break;
            
        case 'recent':
            getRecentNotifications($db, $userId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 处理POST请求 - 创建通知
 */
function handlePostRequest($db, $userId, $input) {
    $action = $input['action'] ?? 'create';
    
    switch ($action) {
        case 'create':
            createNotification($db, $userId, $input);
            break;
            
        case 'mark_read':
            markAsRead($db, $userId, $input);
            break;
            
        case 'mark_all_read':
            markAllAsRead($db, $userId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 处理PUT请求 - 更新通知
 */
function handlePutRequest($db, $userId, $input) {
    $notificationId = intval($input['id'] ?? 0);
    
    if ($notificationId <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '通知ID无效']);
        return;
    }
    
    // 标记为已读
    $stmt = $db->prepare("UPDATE notifications SET read_at = NOW() WHERE id = ? AND user_id = ? AND read_at IS NULL");
    $stmt->execute([$notificationId, $userId]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => '通知已标记为已读']);
    } else {
        echo json_encode(['success' => false, 'message' => '通知不存在或已读']);
    }
}

/**
 * 处理DELETE请求 - 删除通知
 */
function handleDeleteRequest($db, $userId, $input) {
    $notificationId = intval($input['id'] ?? 0);
    
    if ($notificationId <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '通知ID无效']);
        return;
    }
    
    $stmt = $db->prepare("DELETE FROM notifications WHERE id = ? AND user_id = ?");
    $stmt->execute([$notificationId, $userId]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => true, 'message' => '通知已删除']);
    } else {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '通知不存在']);
    }
}

/**
 * 获取通知列表
 */
function getNotifications($db, $userId) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    $type = $_GET['type'] ?? '';
    $status = $_GET['status'] ?? ''; // all, read, unread
    
    // 构建查询条件
    $sql = "SELECT * FROM notifications WHERE user_id = ?";
    $params = [$userId];
    
    if ($type) {
        $sql .= " AND type = ?";
        $params[] = $type;
    }
    
    if ($status === 'read') {
        $sql .= " AND read_at IS NOT NULL";
    } elseif ($status === 'unread') {
        $sql .= " AND read_at IS NULL";
    }
    
    $sql .= " ORDER BY created_at DESC LIMIT ?, ?";
    $params[] = (int)$offset;
    $params[] = (int)$limit;
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll();
    
    // 获取总数
    $countSql = "SELECT COUNT(*) FROM notifications WHERE user_id = ?";
    $countParams = [$userId];
    
    if ($type) {
        $countSql .= " AND type = ?";
        $countParams[] = $type;
    }
    
    if ($status === 'read') {
        $countSql .= " AND read_at IS NOT NULL";
    } elseif ($status === 'unread') {
        $countSql .= " AND read_at IS NULL";
    }
    
    $stmt = $db->prepare($countSql);
    $stmt->execute($countParams);
    $total = $stmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'notifications' => $notifications,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]
    ]);
}

/**
 * 获取未读通知数量
 */
function getUnreadCount($db, $userId) {
    $stmt = $db->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND read_at IS NULL");
    $stmt->execute([$userId]);
    $count = $stmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'unread_count' => $count
        ]
    ]);
}

/**
 * 获取最近通知
 */
function getRecentNotifications($db, $userId) {
    $limit = min(10, max(5, intval($_GET['limit'] ?? 5)));
    
    $stmt = $db->prepare("
        SELECT * FROM notifications 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$userId, $limit]);
    $notifications = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'data' => $notifications
    ]);
}

/**
 * 创建通知
 */
function createNotification($db, $userId, $input) {
    $type = $input['type'] ?? '';
    $title = $input['title'] ?? '';
    $message = $input['message'] ?? '';
    $data = $input['data'] ?? null;
    
    if (empty($type) || empty($title) || empty($message)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '类型、标题和消息不能为空']);
        return;
    }
    
    $stmt = $db->prepare("
        INSERT INTO notifications (user_id, type, title, message, data, created_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$userId, $type, $title, $message, $data ? json_encode($data) : null]);
    
    echo json_encode([
        'success' => true,
        'message' => '通知创建成功',
        'data' => [
            'id' => $db->lastInsertId()
        ]
    ]);
}

/**
 * 标记通知为已读
 */
function markAsRead($db, $userId, $input) {
    $notificationIds = $input['ids'] ?? [];
    
    if (empty($notificationIds) || !is_array($notificationIds)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '通知ID列表不能为空']);
        return;
    }
    
    $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
    $sql = "UPDATE notifications SET read_at = NOW() WHERE id IN ($placeholders) AND user_id = ? AND read_at IS NULL";
    
    $params = array_merge($notificationIds, [$userId]);
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    
    echo json_encode([
        'success' => true,
        'message' => '通知已标记为已读',
        'data' => [
            'updated_count' => $stmt->rowCount()
        ]
    ]);
}

/**
 * 标记所有通知为已读
 */
function markAllAsRead($db, $userId) {
    $stmt = $db->prepare("UPDATE notifications SET read_at = NOW() WHERE user_id = ? AND read_at IS NULL");
    $stmt->execute([$userId]);
    
    echo json_encode([
        'success' => true,
        'message' => '所有通知已标记为已读',
        'data' => [
            'updated_count' => $stmt->rowCount()
        ]
    ]);
}

/**
 * 系统通知创建函数（供其他模块调用）
 */
function createSystemNotification($db, $userId, $type, $title, $message, $data = null) {
    try {
        $stmt = $db->prepare("
            INSERT INTO notifications (user_id, type, title, message, data, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$userId, $type, $title, $message, $data ? json_encode($data) : null]);
        return $db->lastInsertId();
    } catch (Exception $e) {
        error_log('创建系统通知失败: ' . $e->getMessage());
        return false;
    }
}
?>
