<?php
/**
 * 用户通知API
 * User Notifications API
 */

session_start();
require_once '../../config.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未登录']);
    exit;
}

$userId = $_SESSION['user_id'];
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $db = getDatabase();
    
    // 创建通知表（如果不存在）
    $db->exec("
        CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            title VARCHAR(255) NOT NULL,
            message TEXT,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    switch ($action) {
        case 'unread_count':
            getUnreadCount($userId, $db);
            break;
            
        case 'list':
            getNotifications($userId, $db);
            break;
            
        case 'mark_read':
            markAsRead($userId, $db);
            break;
            
        case 'mark_all_read':
            markAllAsRead($userId, $db);
            break;
            
        case 'delete':
            deleteNotification($userId, $db);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
            break;
    }
    
} catch (Exception $e) {
    error_log("通知API错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => '服务器内部错误']);
}

/**
 * 获取未读通知数量
 */
function getUnreadCount($userId, $db) {
    try {
        $stmt = $db->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
        $stmt->execute([$userId]);
        $count = $stmt->fetchColumn();
        
        echo json_encode([
            'success' => true,
            'count' => (int)$count
        ]);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 获取通知列表
 */
function getNotifications($userId, $db) {
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        // 获取总数
        $stmt = $db->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ?");
        $stmt->execute([$userId]);
        $total = $stmt->fetchColumn();
        
        // 获取通知列表
        $stmt = $db->prepare("
            SELECT id, type, title, message, is_read, created_at, updated_at
            FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化时间
        foreach ($notifications as &$notification) {
            $notification['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($notification['created_at']));
            $notification['is_read'] = (bool)$notification['is_read'];
        }
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_count' => (int)$total,
                'per_page' => $limit
            ]
        ]);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 标记通知为已读
 */
function markAsRead($userId, $db) {
    try {
        $notificationId = $_POST['notification_id'] ?? '';
        
        if (empty($notificationId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '通知ID不能为空']);
            return;
        }
        
        $stmt = $db->prepare("
            UPDATE notifications 
            SET is_read = 1, updated_at = NOW() 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$notificationId, $userId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => '标记成功']);
        } else {
            echo json_encode(['success' => false, 'error' => '通知不存在或已处理']);
        }
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 标记所有通知为已读
 */
function markAllAsRead($userId, $db) {
    try {
        $stmt = $db->prepare("
            UPDATE notifications 
            SET is_read = 1, updated_at = NOW() 
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$userId]);
        
        $count = $stmt->rowCount();
        
        echo json_encode([
            'success' => true, 
            'message' => "已标记 {$count} 条通知为已读"
        ]);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 删除通知
 */
function deleteNotification($userId, $db) {
    try {
        $notificationId = $_POST['notification_id'] ?? '';
        
        if (empty($notificationId)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '通知ID不能为空']);
            return;
        }
        
        $stmt = $db->prepare("DELETE FROM notifications WHERE id = ? AND user_id = ?");
        $stmt->execute([$notificationId, $userId]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => '删除成功']);
        } else {
            echo json_encode(['success' => false, 'error' => '通知不存在']);
        }
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 创建通知（供其他脚本调用）
 */
function createNotification($userId, $type, $title, $message = '') {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("
            INSERT INTO notifications (user_id, type, title, message, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$userId, $type, $title, $message]);
        return $db->lastInsertId();
    } catch (Exception $e) {
        error_log("创建通知失败: " . $e->getMessage());
        return false;
    }
}
?>
