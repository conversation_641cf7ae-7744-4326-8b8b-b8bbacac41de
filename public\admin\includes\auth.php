<?php
/**
 * 管理后台认证包含文件
 * Admin Authentication Include File
 */

// 启动会话（如果尚未启动）
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 定义项目根目录（如果尚未定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
}

if (!defined('ADMIN_PATH')) {
    define('ADMIN_PATH', dirname(__DIR__));
}

// 引入通用函数
require_once ADMIN_PATH . '/includes/functions.php';

// 加载环境变量
if (!function_exists('loadEnv')) {
    function loadEnv($path) {
        if (!file_exists($path)) return;
        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $_ENV[trim($name)] = trim($value, '"\'');
            }
        }
    }
}

loadEnv(ROOT_PATH . '/.env');

// 数据库连接函数（向后兼容）
if (!function_exists('getDatabase')) {
    function getDatabase() {
        return safeGetDatabase();
    }
}

// 检查管理员登录
function checkAdminAuth() {
    if (!isset($_SESSION['admin_id'])) {
        // 如果是AJAX请求，返回JSON错误
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => '未授权访问，请重新登录']);
            exit;
        }
        
        // 普通请求重定向到登录页
        header('Location: login.php');
        exit;
    }
}

// 执行认证检查
checkAdminAuth();
?>
