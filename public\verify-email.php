<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证 - NameSilo域名销售系统</title>
    <meta name="description" content="验证您的邮箱地址">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --white-color: #ffffff;
            --border-light: #f3f4f6;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;
            --bg-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 24px;
            --radius-full: 9999px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="white" stop-opacity="0.1"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .auth-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 500px;
            padding: 20px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 48px 40px;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            text-align: center;
        }

        .auth-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .auth-logo {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            margin-bottom: 32px;
        }

        .auth-logo i {
            font-size: 2rem;
        }

        .status-icon {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 3rem;
            color: white;
        }

        .status-icon.success {
            background: var(--success-color);
        }

        .status-icon.error {
            background: var(--danger-color);
        }

        .status-icon.loading {
            background: var(--primary-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .status-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 16px;
        }

        .status-message {
            color: var(--text-muted);
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 32px;
        }

        .btn-primary {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 16px 32px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 8px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .btn-secondary {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 16px 32px;
            background: transparent;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--dark-color);
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 8px;
        }

        .btn-secondary:hover {
            background: var(--light-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .back-home {
            position: absolute;
            top: 30px;
            left: 30px;
            z-index: 3;
        }

        .back-home a {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-home a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            color: white;
        }

        @media (max-width: 768px) {
            .auth-container {
                padding: 15px;
            }
            
            .auth-card {
                padding: 32px 24px;
            }
            
            .back-home {
                top: 15px;
                left: 15px;
            }

            .btn-primary, .btn-secondary {
                display: block;
                width: 100%;
                margin: 8px 0;
                text-align: center;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 返回首页 -->
    <div class="back-home">
        <a href="index.php">
            <i class="fas fa-arrow-left"></i>
            返回首页
        </a>
    </div>

    <div class="auth-container">
        <div class="auth-card">
            <a href="index.php" class="auth-logo">
                <i class="fas fa-globe"></i>
                NameSilo域名销售
            </a>

            <!-- 验证中状态 -->
            <div id="verifyingState">
                <div class="status-icon loading">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <h1 class="status-title">正在验证...</h1>
                <p class="status-message">
                    请稍候，我们正在验证您的邮箱地址。
                </p>
            </div>

            <!-- 验证成功状态 -->
            <div id="successState" style="display: none;">
                <div class="status-icon success">
                    <i class="fas fa-check"></i>
                </div>
                <h1 class="status-title">验证成功！</h1>
                <p class="status-message">
                    恭喜您！您的邮箱地址已成功验证。<br>
                    现在您可以登录并开始使用我们的服务了。
                </p>
                <div class="mt-4">
                    <a href="login.php" class="btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        立即登录
                    </a>
                    <a href="index.php" class="btn-secondary">
                        <i class="fas fa-home"></i>
                        返回首页
                    </a>
                </div>
            </div>

            <!-- 验证失败状态 -->
            <div id="errorState" style="display: none;">
                <div class="status-icon error">
                    <i class="fas fa-times"></i>
                </div>
                <h1 class="status-title">验证失败</h1>
                <p class="status-message" id="errorMessage">
                    验证链接无效或已过期。请重新注册或联系客服。
                </p>
                <div class="mt-4">
                    <a href="register.php" class="btn-primary">
                        <i class="fas fa-user-plus"></i>
                        重新注册
                    </a>
                    <a href="index.php" class="btn-secondary">
                        <i class="fas fa-home"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 显示状态
        function showState(state, message = '') {
            document.getElementById('verifyingState').style.display = 'none';
            document.getElementById('successState').style.display = 'none';
            document.getElementById('errorState').style.display = 'none';
            
            if (state === 'success') {
                document.getElementById('successState').style.display = 'block';
            } else if (state === 'error') {
                document.getElementById('errorState').style.display = 'block';
                if (message) {
                    document.getElementById('errorMessage').textContent = message;
                }
            }
        }

        // 验证邮箱
        async function verifyEmail() {
            const token = getUrlParameter('token');
            
            if (!token) {
                showState('error', '验证链接无效，缺少验证令牌。');
                return;
            }

            try {
                const response = await fetch('api/auth.php?action=verify-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `token=${encodeURIComponent(token)}`
                });

                const result = await response.json();

                if (result.success) {
                    showState('success');
                } else {
                    showState('error', result.message || '验证失败，请重试。');
                }
            } catch (error) {
                showState('error', '网络错误，请稍后重试。');
            }
        }

        // 页面加载完成后开始验证
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载动画
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
                
                // 开始验证
                setTimeout(verifyEmail, 1000);
            }, 100);
        });
    </script>
</body>
</html>
