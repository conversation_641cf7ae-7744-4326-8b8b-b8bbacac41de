<?php
$db = getDatabase();

// 引入NameSilo API客户端
require_once ROOT_PATH . '/public/api/NameSiloClient.php';

// 获取NameSilo客户端
function getNameSiloClient() {
    static $client = null;
    if ($client === null) {
        $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
        $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
        $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

        if (empty($apiKey)) {
            throw new Exception('NameSilo API密钥未配置，请先在系统设置中配置');
        }

        $client = new NameSiloClient($apiKey, $apiUrl, $sandbox);
    }
    return $client;
}

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'import_from_namesilo':
            try {
                $client = getNameSiloClient();
                $nameSiloDomains = $client->listDomains();

                $imported = 0;
                $skipped = 0;

                foreach ($nameSiloDomains as $domainData) {
                    $domainName = $domainData['domain'];
                    $status = $domainData['status'] === 'Active' ? 'registered' : 'expired';
                    $expiryDate = $domainData['expires'];
                    $autoRenew = $domainData['auto_renew'] ? 1 : 0;

                    // 提取TLD
                    $parts = explode('.', $domainName);
                    $tld = '.' . end($parts);

                    // 检查域名是否已存在
                    $stmt = $db->prepare("SELECT id FROM domains WHERE domain_name = ?");
                    $stmt->execute([$domainName]);

                    if ($stmt->fetch()) {
                        $skipped++;
                        continue;
                    }

                    // 获取默认价格
                    $stmt = $db->prepare("SELECT registration_price FROM domain_prices WHERE tld = ?");
                    $stmt->execute([$tld]);
                    $priceData = $stmt->fetch();
                    $price = $priceData ? $priceData['registration_price'] : 12.99;

                    // 插入域名
                    $stmt = $db->prepare("
                        INSERT INTO domains (domain_name, tld, price, status, expiry_date, auto_renew, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$domainName, $tld, $price, $status, $expiryDate, $autoRenew]);
                    $imported++;
                }

                $message = "成功导入 {$imported} 个域名，跳过 {$skipped} 个已存在的域名";

            } catch (Exception $e) {
                $error = '导入失败：' . $e->getMessage();
            }
            break;

        case 'add_domain':
            $domainName = trim($_POST['domain_name'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $status = $_POST['status'] ?? 'available';
            $featured = isset($_POST['featured']) ? 1 : 0;
            $description = trim($_POST['description'] ?? '');

            if ($domainName && $price > 0) {
                try {
                    // 提取TLD
                    $parts = explode('.', $domainName);
                    $tld = '.' . end($parts);

                    $stmt = $db->prepare("INSERT INTO domains (domain_name, tld, price, status, featured, description, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
                    $stmt->execute([$domainName, $tld, $price, $status, $featured, $description]);
                    $message = '域名添加成功';
                } catch (PDOException $e) {
                    $error = '添加失败：' . $e->getMessage();
                }
            } else {
                $error = '请填写域名和价格';
            }
            break;
            
        case 'update_status':
            $domainId = $_POST['domain_id'] ?? '';
            $newStatus = $_POST['new_status'] ?? '';
            
            if ($domainId && $newStatus) {
                $stmt = $db->prepare("UPDATE domains SET status = ? WHERE id = ?");
                $stmt->execute([$newStatus, $domainId]);
                $message = '域名状态已更新';
            }
            break;
            
        case 'delete_domain':
            $domainId = $_POST['domain_id'] ?? '';
            if ($domainId) {
                $stmt = $db->prepare("DELETE FROM domains WHERE id = ?");
                $stmt->execute([$domainId]);
                $message = '域名已删除';
            }
            break;
    }
}

// 获取域名列表
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$tld = $_GET['tld'] ?? '';
$page_num = max(1, intval($_GET['page_num'] ?? 1));
$limit = intval($_GET['per_page'] ?? 50); // 增加默认显示数量
$limit = min(max($limit, 10), 100); // 限制在10-100之间
$offset = ($page_num - 1) * $limit;

$where = [];
$params = [];

if ($search) {
    $where[] = "domain_name LIKE ?";
    $params[] = "%$search%";
}

if ($status) {
    $where[] = "status = ?";
    $params[] = $status;
}

if ($tld) {
    $where[] = "tld = ?";
    $params[] = $tld;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// 获取总数
$countSql = "SELECT COUNT(*) FROM domains $whereClause";
$totalDomains = $db->prepare($countSql);
$totalDomains->execute($params);
$totalCount = $totalDomains->fetchColumn();
$totalPages = ceil($totalCount / $limit);

// 获取域名列表（包含用户信息）
$sql = "SELECT d.*, u.username, u.email as user_email
        FROM domains d
        LEFT JOIN users u ON d.user_id = u.id
        $whereClause
        ORDER BY d.created_at DESC
        LIMIT $limit OFFSET $offset";
$stmt = $db->prepare($sql);
$stmt->execute($params);
$domains = $stmt->fetchAll();

// 获取TLD列表用于筛选
$tldList = $db->query("SELECT DISTINCT tld FROM domains ORDER BY tld")->fetchAll(PDO::FETCH_COLUMN);

// 获取统计数据
$stats = $db->query("SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available,
    SUM(CASE WHEN status = 'sold' THEN 1 ELSE 0 END) as sold,
    SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured
    FROM domains")->fetch();
?>

<!-- 消息提示 -->
<?php if (isset($message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 页面标题和操作 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">域名管理</h1>
        <p class="text-muted">管理域名库存和销售</p>
    </div>
    <div class="d-flex gap-2">
        <a href="index.php?page=domains&action=categories" class="btn btn-outline-primary">
            <i class="fas fa-tags me-2"></i>
            分类管理
        </a>
        <a href="index.php?page=domains&action=pricing" class="btn btn-outline-info">
            <i class="fas fa-dollar-sign me-2"></i>
            价格管理
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-warning dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-2"></i>
                API管理
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="setup-api.php">
                    <i class="fas fa-wrench me-2"></i>配置向导
                </a></li>
                <li><a class="dropdown-item" href="test-api.php">
                    <i class="fas fa-test me-2"></i>连接测试
                </a></li>
            </ul>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-plus me-2"></i>
                添加域名
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importDomainModal">
                        <i class="fas fa-cloud-download-alt me-2"></i>
                        从域名商导入
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="test-namesilo-import.php" target="_blank">
                        <i class="fas fa-vial me-2"></i>
                        测试API连接
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="database-update.php" target="_blank">
                        <i class="fas fa-database me-2"></i>
                        数据库更新
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="update-domains-table.php" target="_blank">
                        <i class="fas fa-database me-2"></i>
                        检查表结构
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#addDomainModal">
                        <i class="fas fa-edit me-2"></i>
                        手动添加
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总域名数</h6>
                        <h4 class="mb-0"><?= number_format($stats['total'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success bg-opacity-10 text-success rounded-3 p-3 me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">可售域名</h6>
                        <h4 class="mb-0"><?= number_format($stats['available'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">已售域名</h6>
                        <h4 class="mb-0"><?= number_format($stats['sold'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-star"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">推荐域名</h6>
                        <h4 class="mb-0"><?= number_format($stats['featured'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="domains">
            <div class="col-md-4">
                <label class="form-label">搜索域名</label>
                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="输入域名...">
            </div>
            <div class="col-md-3">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="available" <?= $status === 'available' ? 'selected' : '' ?>>可售</option>
                    <option value="sold" <?= $status === 'sold' ? 'selected' : '' ?>>已售</option>
                    <option value="reserved" <?= $status === 'reserved' ? 'selected' : '' ?>>保留</option>
                    <option value="expired" <?= $status === 'expired' ? 'selected' : '' ?>>过期</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">顶级域名</label>
                <select class="form-select" name="tld">
                    <option value="">全部TLD</option>
                    <?php foreach ($tldList as $tldOption): ?>
                    <option value="<?= htmlspecialchars($tldOption) ?>" <?= $tld === $tldOption ? 'selected' : '' ?>>
                        .<?= htmlspecialchars($tldOption) ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">每页显示</label>
                <select class="form-select" name="per_page" onchange="this.form.submit()">
                    <option value="20" <?= $limit === 20 ? 'selected' : '' ?>>20条</option>
                    <option value="50" <?= $limit === 50 ? 'selected' : '' ?>>50条</option>
                    <option value="100" <?= $limit === 100 ? 'selected' : '' ?>>100条</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 域名列表 -->
<div class="card mb-5">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            域名列表 (共 <?= number_format($totalCount) ?> 个域名)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>域名</th>
                        <th>域名归属人</th>
                        <th>注册时间</th>
                        <th>到期时间</th>
                        <th>域名设置</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($domains)): ?>
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-globe fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无域名数据</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($domains as $domain): ?>
                    <tr>
                        <td>
                            <span class="fw-medium">#<?= $domain['id'] ?></span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="domain-icon me-3">
                                    <i class="fas fa-globe text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-medium"><?= htmlspecialchars($domain['domain_name']) ?></div>
                                    <div class="text-muted small">TLD: .<?= htmlspecialchars($domain['tld']) ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user-circle text-secondary"></i>
                                </div>
                                <div>
                                    <?php if (!empty($domain['username'])): ?>
                                        <div class="fw-medium"><?= htmlspecialchars($domain['username']) ?></div>
                                        <div class="text-muted small">用户ID: <?= $domain['user_id'] ?? 'N/A' ?></div>
                                    <?php else: ?>
                                        <div class="text-muted">
                                            <i class="fas fa-user-slash me-1"></i>
                                            未分配
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="text-muted">
                                <i class="fas fa-calendar-plus me-1"></i>
                                <?php if (!empty($domain['registration_date'])): ?>
                                    <?= date('Y-m-d', strtotime($domain['registration_date'])) ?>
                                <?php else: ?>
                                    <span class="text-muted">未注册</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="text-muted">
                                <i class="fas fa-calendar-times me-1"></i>
                                <?php if (!empty($domain['expiry_date'])): ?>
                                    <?php
                                    $expiryDate = $domain['expiry_date'];
                                    $daysUntilExpiry = (strtotime($expiryDate) - time()) / (60 * 60 * 24);
                                    $isExpiringSoon = $daysUntilExpiry <= 30 && $daysUntilExpiry > 0;
                                    $isExpired = $daysUntilExpiry <= 0;
                                    ?>
                                    <span class="<?= $isExpired ? 'text-danger fw-medium' : ($isExpiringSoon ? 'text-warning fw-medium' : '') ?>">
                                        <?= date('Y-m-d', strtotime($expiryDate)) ?>
                                    </span>
                                    <?php if ($isExpired): ?>
                                        <br><small class="text-danger">已过期</small>
                                    <?php elseif ($isExpiringSoon): ?>
                                        <br><small class="text-warning">即将到期</small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">无到期日期</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="manageDomain(<?= $domain['id'] ?>)">
                                    <i class="fas fa-cog me-1"></i>
                                    管理
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                                    <span class="visually-hidden">更多选项</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="renewDomain(<?= $domain['id'] ?>)">
                                            <i class="fas fa-sync me-2"></i>
                                            续费域名
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="transferDomain(<?= $domain['id'] ?>)">
                                            <i class="fas fa-exchange-alt me-2"></i>
                                            转移域名
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="manageDNS(<?= $domain['id'] ?>)">
                                            <i class="fas fa-network-wired me-2"></i>
                                            DNS设置
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="deleteDomain(<?= $domain['id'] ?>)">
                                            <i class="fas fa-trash me-2"></i>
                                            删除域名
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="card-footer">
        <nav aria-label="域名列表分页">
            <ul class="pagination justify-content-center mb-0">
                <?php if ($page_num > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=domains&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&tld=<?= urlencode($tld) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                    <a class="page-link" href="?page=domains&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&tld=<?= urlencode($tld) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page_num < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=domains&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&tld=<?= urlencode($tld) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- 从NameSilo导入域名模态框 -->
<div class="modal fade" id="importDomainModal" tabindex="-1" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cloud-download-alt me-2"></i>
                    从NameSilo导入域名
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>导入说明</h6>
                    <ul class="mb-0">
                        <li>此功能将从您的NameSilo账户中获取所有域名</li>
                        <li>已存在的域名将被跳过，不会重复导入</li>
                        <li>导入的域名将包含到期时间和自动续费设置</li>
                        <li>请确保已在系统设置中正确配置NameSilo API密钥</li>
                    </ul>
                </div>

                <div class="text-center" id="importStatus">
                    <i class="fas fa-cloud-download-alt fa-3x text-primary mb-3"></i>
                    <h6>准备从NameSilo导入域名</h6>
                    <p class="text-muted">点击下方按钮开始导入过程</p>
                </div>

                <!-- 进度条 -->
                <div class="progress mb-3" id="importProgress" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">
                        <span class="progress-text">0%</span>
                    </div>
                </div>

                <!-- 详细进度信息 -->
                <div id="importDetails" style="display: none;" class="mt-3">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-1">总计</h6>
                                    <span class="badge bg-primary" id="totalDomains">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-1">已导入</h6>
                                    <span class="badge bg-success" id="importedCount">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-1">已跳过</h6>
                                    <span class="badge bg-warning" id="skippedCount">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 bg-light">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-1">错误</h6>
                                    <span class="badge bg-danger" id="errorCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 当前处理的域名 -->
                    <div class="mt-3">
                        <small class="text-muted">当前处理:</small>
                        <div class="bg-light p-2 rounded">
                            <code id="currentDomain">-</code>
                        </div>
                    </div>
                </div>

                <!-- 错误日志 -->
                <div id="errorLog" style="display: none;" class="mt-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                导入过程中的问题
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="errorList" class="small"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="closeBtn">关闭</button>
                <button type="button" class="btn btn-warning" id="cancelBtn" onclick="cancelImport()" style="display: none;">
                    <i class="fas fa-stop me-2"></i>
                    取消导入
                </button>
                <button type="button" class="btn btn-primary" id="importBtn" onclick="startImport()">
                    <i class="fas fa-download me-2"></i>
                    开始导入
                </button>
                <button type="button" class="btn btn-success" id="retryBtn" onclick="retryImport()" style="display: none;">
                    <i class="fas fa-redo me-2"></i>
                    重试导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加域名模态框 -->
<div class="modal fade" id="addDomainModal" tabindex="-1" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加新域名
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_domain">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">域名 *</label>
                            <input type="text" class="form-control" name="domain_name" placeholder="namesilo-namesilo-sample.com" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">价格 (USD) *</label>
                            <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">状态</label>
                            <select class="form-select" name="status">
                                <option value="available">可售</option>
                                <option value="reserved">保留</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="featured" id="featured">
                                <label class="form-check-label" for="featured">
                                    设为推荐域名
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" name="description" rows="3" placeholder="域名描述..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        添加域名
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.dropdown-item-form {
    margin: 0;
}
.dropdown-item-form .dropdown-item {
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    padding: 0.375rem 1rem;
}
.dropdown-item-form .dropdown-item:hover {
    background-color: var(--bs-dropdown-link-hover-bg);
}
</style>

<script>
// 域名管理函数
function manageDomain(domainId) {
    // 跳转到域名详细管理页面
    window.location.href = `?page=domains&action=manage&id=${domainId}`;
}

// 续费域名
function renewDomain(domainId) {
    // 跳转到域名续费页面
    window.location.href = `?page=domains&action=renew&id=${domainId}`;
}

// 转移域名
function transferDomain(domainId) {
    // 跳转到域名转移页面
    window.location.href = `?page=domains&action=transfer&id=${domainId}`;
}

// DNS设置
function manageDNS(domainId) {
    // 跳转到DNS管理页面
    window.location.href = `?page=domains&action=dns&id=${domainId}`;
}

// 删除域名
function deleteDomain(domainId) {
    if (confirm('确定要删除这个域名吗？此操作不可恢复！')) {
        // 发送删除请求
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_domain">
            <input type="hidden" name="domain_id" value="${domainId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// 全局变量
let importController = null;
let importErrors = [];

// AJAX域名导入功能
function startImport() {
    const importBtn = document.getElementById('importBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const closeBtn = document.getElementById('closeBtn');
    const retryBtn = document.getElementById('retryBtn');
    const importStatus = document.getElementById('importStatus');
    const importProgress = document.getElementById('importProgress');
    const importDetails = document.getElementById('importDetails');
    const progressBar = importProgress.querySelector('.progress-bar');

    // 重置状态
    importErrors = [];
    hideErrorLog();

    // 创建AbortController用于取消请求
    importController = new AbortController();

    // 更新按钮状态
    importBtn.style.display = 'none';
    cancelBtn.style.display = 'inline-block';
    retryBtn.style.display = 'none';
    closeBtn.disabled = true;

    // 显示进度条和详细信息
    importProgress.style.display = 'block';
    importDetails.style.display = 'block';
    progressBar.style.width = '0%';
    progressBar.querySelector('.progress-text').textContent = '0%';

    // 重置计数器
    updateCounters(0, 0, 0, 0);

    // 更新状态
    importStatus.innerHTML = `
        <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
        <h6>正在连接NameSilo API...</h6>
        <p class="text-muted">请稍候，正在验证API连接</p>
        <div class="mt-2">
            <small class="text-info">
                <i class="fas fa-info-circle me-1"></i>
                正在验证API密钥和网络连接...
            </small>
        </div>
    `;

    // 发送AJAX请求
    fetch('ajax/import-domains.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        signal: importController.signal
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        function readStream() {
            return reader.read().then(({ done, value }) => {
                if (done) {
                    return;
                }

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim());

                lines.forEach(line => {
                    try {
                        const data = JSON.parse(line);
                        updateImportProgress(data);
                    } catch (e) {
                        console.error('解析响应失败:', e, '原始数据:', line);
                        const errorMsg = line ? `数据解析错误: ${line.substring(0, 100)}...` : '数据解析错误: 空响应';
                        addError(errorMsg);
                    }
                });

                return readStream();
            });
        }

        return readStream();
    })
    .catch(error => {
        if (error.name === 'AbortError') {
            // 用户取消了导入
            importStatus.innerHTML = `
                <i class="fas fa-ban fa-3x text-warning mb-3"></i>
                <h6>导入已取消</h6>
                <p class="text-muted">用户主动取消了导入过程</p>
            `;
        } else {
            console.error('导入失败:', error);
            console.error('错误堆栈:', error.stack);

            let errorMessage = error.message || '未知错误';

            // 处理常见的错误情况
            if (errorMessage === 'undefined' || errorMessage.includes('undefined')) {
                errorMessage = 'NameSilo API配置错误，请检查API密钥是否正确配置';
            } else if (errorMessage.includes('网络连接错误')) {
                errorMessage = '网络连接失败，请检查网络连接或稍后重试';
            } else if (errorMessage.includes('API密钥')) {
                errorMessage = 'NameSilo API密钥配置错误，请联系管理员检查配置';
            } else if (errorMessage.includes('HTTP')) {
                errorMessage = 'NameSilo API服务暂时不可用，请稍后重试';
            }

            importStatus.innerHTML = `
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h6>域名同步失败</h6>
                <p class="text-danger">${errorMessage}</p>
                <small class="text-muted">请检查NameSilo API配置或稍后重试</small>
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary me-2" onclick="retryImport()">
                        <i class="fas fa-redo me-1"></i>重试
                    </button>
                    <a href="setup-api.php" class="btn btn-sm btn-outline-warning me-2">
                        <i class="fas fa-wrench me-1"></i>配置API
                    </a>
                    <a href="test-api.php" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-test me-1"></i>测试连接
                    </a>
                </div>
            `;
            addError('域名同步失败: ' + errorMessage);
        }
        resetImportButtons();
    });
}

// 取消导入
function cancelImport() {
    if (importController) {
        importController.abort();
        importController = null;
    }
}

// 重试导入
function retryImport() {
    startImport();
}

// 更新计数器
function updateCounters(total, imported, skipped, errors) {
    document.getElementById('totalDomains').textContent = total;
    document.getElementById('importedCount').textContent = imported;
    document.getElementById('skippedCount').textContent = skipped;
    document.getElementById('errorCount').textContent = errors;
}

// 更新当前处理的域名
function updateCurrentDomain(domain) {
    document.getElementById('currentDomain').textContent = domain || '-';
}

// 添加错误信息
function addError(error) {
    importErrors.push(error);
    updateCounters(
        parseInt(document.getElementById('totalDomains').textContent) || 0,
        parseInt(document.getElementById('importedCount').textContent) || 0,
        parseInt(document.getElementById('skippedCount').textContent) || 0,
        importErrors.length
    );
    showErrorLog();
}

// 显示错误日志
function showErrorLog() {
    const errorLog = document.getElementById('errorLog');
    const errorList = document.getElementById('errorList');

    if (importErrors.length > 0) {
        errorList.innerHTML = importErrors.map((error, index) =>
            `<div class="mb-1"><strong>${index + 1}.</strong> ${error}</div>`
        ).join('');
        errorLog.style.display = 'block';
    }
}

// 隐藏错误日志
function hideErrorLog() {
    document.getElementById('errorLog').style.display = 'none';
}

function updateImportProgress(data) {
    const importStatus = document.getElementById('importStatus');
    const progressBar = document.querySelector('#importProgress .progress-bar');
    const progressText = progressBar.querySelector('.progress-text');

    if (data.success) {
        // 更新进度条
        progressBar.style.width = data.progress + '%';
        progressText.textContent = data.progress + '%';

        // 更新计数器
        if (data.total_domains !== undefined) {
            updateCounters(data.total_domains, data.imported || 0, data.skipped || 0, importErrors.length);
        }
        if (data.imported !== undefined && data.skipped !== undefined) {
            updateCounters(
                parseInt(document.getElementById('totalDomains').textContent) || 0,
                data.imported,
                data.skipped,
                importErrors.length
            );
        }

        // 更新当前处理的域名
        if (data.current_domain) {
            updateCurrentDomain(data.current_domain);
        }

        // 根据步骤更新状态
        switch (data.step) {
            case 'connecting':
                importStatus.innerHTML = `
                    <i class="fas fa-wifi fa-3x text-primary mb-3"></i>
                    <h6>API连接成功</h6>
                    <p class="text-muted">${data.message}</p>
                `;
                break;

            case 'fetching':
                importStatus.innerHTML = `
                    <i class="fas fa-list fa-3x text-primary mb-3"></i>
                    <h6>获取域名列表</h6>
                    <p class="text-muted">${data.message}</p>
                `;
                break;

            case 'importing':
                importStatus.innerHTML = `
                    <i class="fas fa-download fa-3x text-primary mb-3"></i>
                    <h6>正在导入域名</h6>
                    <p class="text-muted">${data.message}</p>
                `;
                break;

            case 'completed':
                updateCurrentDomain('导入完成');
                importStatus.innerHTML = `
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6>导入完成</h6>
                    <p class="text-success">${data.message}</p>
                `;

                // 显示汇总信息
                const summary = `
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-info-circle me-2"></i>导入汇总</h6>
                        <ul class="mb-0">
                            <li>总计域名: ${data.total_domains || 0}</li>
                            <li>成功导入: ${data.imported || 0}</li>
                            <li>跳过重复: ${data.skipped || 0}</li>
                            <li>处理错误: ${importErrors.length}</li>
                        </ul>
                    </div>
                `;
                importStatus.innerHTML += summary;

                resetImportButtons();

                // 5秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
                break;
        }

        // 处理错误信息
        if (data.error_message) {
            addError(data.error_message);
        }
    } else {
        importStatus.innerHTML = `
            <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
            <h6>导入失败</h6>
            <p class="text-danger">${data.error || '未知错误'}</p>
        `;
        addError(data.error || '未知错误');
        resetImportButtons();
    }
}

function resetImportButtons() {
    const importBtn = document.getElementById('importBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const closeBtn = document.getElementById('closeBtn');
    const retryBtn = document.getElementById('retryBtn');

    // 重置按钮状态
    importBtn.style.display = 'inline-block';
    importBtn.innerHTML = '<i class="fas fa-download me-2"></i>开始导入';

    cancelBtn.style.display = 'none';
    closeBtn.disabled = false;

    // 如果有错误，显示重试按钮
    if (importErrors.length > 0) {
        retryBtn.style.display = 'inline-block';
    } else {
        retryBtn.style.display = 'none';
    }

    // 清理控制器
    importController = null;
}

// 页面加载完成后确保模态框隐藏
document.addEventListener('DOMContentLoaded', function() {
    try {
        // 确保所有模态框都是隐藏的
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                modal.setAttribute('aria-hidden', 'true');
            }
        });

        // 移除可能的背景遮罩
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop && backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });

        // 确保body没有modal-open类
        if (document.body) {
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    } catch (error) {
        console.warn('模态框初始化警告:', error);
    }
});
</script>
