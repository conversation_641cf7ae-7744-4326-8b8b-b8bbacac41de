<?php
/**
 * API路由器
 * API Router
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    define('API_ACCESS', true);
}

// 加载配置和依赖
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../app/Utils/Database.php';
require_once __DIR__ . '/middleware/auth.php';
require_once __DIR__ . '/middleware/cors.php';
require_once __DIR__ . '/middleware/admin.php';

/**
 * API路由器类
 */
class ApiRouter {
    private $routes = [];
    private $middleware = [];
    private $currentGroup = '';
    private $currentMiddleware = [];
    
    public function __construct() {
        $this->setupDefaultRoutes();
    }
    
    /**
     * 设置默认路由
     */
    private function setupDefaultRoutes() {
        // 认证路由
        $this->group('auth', function() {
            $this->post('login', 'v1/auth/login.php');
            $this->post('register', 'v1/auth/register.php');
            $this->post('logout', 'v1/auth/logout.php', ['auth']);
            $this->post('refresh', 'v1/auth/refresh.php', ['auth']);
            $this->post('forgot-password', 'v1/auth/forgot-password.php');
            $this->post('reset-password', 'v1/auth/reset-password.php');
        });
        
        // 域名路由
        $this->group('domains', ['auth'], function() {
            $this->get('search', 'v1/domains/search.php');
            $this->get('list', 'v1/domains/list.php');
            $this->post('register', 'v1/domains/register.php');
            $this->get('{id}', 'v1/domains/show.php');
            $this->put('{id}', 'v1/domains/update.php');
            $this->delete('{id}', 'v1/domains/delete.php');
            $this->get('{id}/dns', 'v1/domains/dns.php');
            $this->post('{id}/dns', 'v1/domains/dns.php');
            $this->put('{id}/dns/{record_id}', 'v1/domains/dns.php');
            $this->delete('{id}/dns/{record_id}', 'v1/domains/dns.php');
        });
        
        // 订单路由
        $this->group('orders', ['auth'], function() {
            $this->get('list', 'v1/orders/list.php');
            $this->post('create', 'v1/orders/create.php');
            $this->get('{id}', 'v1/orders/show.php');
            $this->put('{id}', 'v1/orders/update.php');
            $this->delete('{id}', 'v1/orders/cancel.php');
        });
        
        // 支付路由
        $this->group('payments', function() {
            $this->post('create', 'v1/payments/create.php', ['auth']);
            $this->get('{id}/status', 'v1/payments/status.php', ['auth']);
            $this->post('alipay/notify', 'v1/payments/alipay/notify.php');
            $this->post('wechat/notify', 'v1/payments/wechat/notify.php');
            $this->post('paypal/notify', 'v1/payments/paypal/notify.php');
        });
        
        // 用户路由
        $this->group('user', ['auth'], function() {
            $this->get('profile', 'v1/user/profile.php');
            $this->put('profile', 'v1/user/profile.php');
            $this->post('change-password', 'v1/user/change-password.php');
            $this->get('notifications', 'v1/user/notifications.php');
            $this->put('notifications/{id}/read', 'v1/user/notifications.php');
        });
        
        // 管理员路由
        $this->group('admin', ['auth', 'admin'], function() {
            $this->get('dashboard', 'v1/admin/dashboard.php');
            $this->get('users', 'v1/admin/users.php');
            $this->post('users', 'v1/admin/users.php');
            $this->get('users/{id}', 'v1/admin/users.php');
            $this->put('users/{id}', 'v1/admin/users.php');
            $this->delete('users/{id}', 'v1/admin/users.php');
            $this->get('domains', 'v1/admin/domains.php');
            $this->post('domains', 'v1/admin/domains.php');
            $this->get('orders', 'v1/admin/orders.php');
            $this->get('settings', 'v1/admin/settings.php');
            $this->put('settings', 'v1/admin/settings.php');
        });
    }
    
    /**
     * 路由分组
     */
    public function group($prefix, $middleware = [], $callback = null) {
        if (is_callable($middleware)) {
            $callback = $middleware;
            $middleware = [];
        }
        
        $oldGroup = $this->currentGroup;
        $oldMiddleware = $this->currentMiddleware;
        
        $this->currentGroup = $oldGroup ? $oldGroup . '/' . $prefix : $prefix;
        $this->currentMiddleware = array_merge($oldMiddleware, $middleware);
        
        if ($callback) {
            $callback();
        }
        
        $this->currentGroup = $oldGroup;
        $this->currentMiddleware = $oldMiddleware;
    }
    
    /**
     * GET路由
     */
    public function get($path, $handler, $middleware = []) {
        $this->addRoute('GET', $path, $handler, $middleware);
    }
    
    /**
     * POST路由
     */
    public function post($path, $handler, $middleware = []) {
        $this->addRoute('POST', $path, $handler, $middleware);
    }
    
    /**
     * PUT路由
     */
    public function put($path, $handler, $middleware = []) {
        $this->addRoute('PUT', $path, $handler, $middleware);
    }
    
    /**
     * DELETE路由
     */
    public function delete($path, $handler, $middleware = []) {
        $this->addRoute('DELETE', $path, $handler, $middleware);
    }
    
    /**
     * 添加路由
     */
    private function addRoute($method, $path, $handler, $middleware = []) {
        $fullPath = $this->currentGroup ? $this->currentGroup . '/' . ltrim($path, '/') : $path;
        $fullMiddleware = array_merge($this->currentMiddleware, $middleware);
        
        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'handler' => $handler,
            'middleware' => $fullMiddleware,
            'pattern' => $this->pathToPattern($fullPath)
        ];
    }
    
    /**
     * 路径转换为正则表达式
     */
    private function pathToPattern($path) {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }
    
    /**
     * 处理请求
     */
    public function handle() {
        try {
            // 设置CORS头
            $this->handleCors();
            
            // 获取请求信息
            $method = $_SERVER['REQUEST_METHOD'];
            $path = $this->getRequestPath();
            
            // 处理OPTIONS请求
            if ($method === 'OPTIONS') {
                $this->sendResponse(['message' => 'OK'], 200);
                return;
            }
            
            // 查找匹配的路由
            $route = $this->findRoute($method, $path);
            
            if (!$route) {
                $this->sendError('路由不存在', 404);
                return;
            }
            
            // 执行中间件
            $this->executeMiddleware($route['middleware']);
            
            // 执行路由处理器
            $this->executeHandler($route);
            
        } catch (Exception $e) {
            error_log('API错误: ' . $e->getMessage());
            $this->sendError('服务器内部错误', 500);
        }
    }
    
    /**
     * 获取请求路径
     */
    private function getRequestPath() {
        $path = $_SERVER['REQUEST_URI'];
        $path = parse_url($path, PHP_URL_PATH);
        $path = trim($path, '/');
        
        // 移除API前缀
        if (strpos($path, 'api/v1/') === 0) {
            $path = substr($path, 7);
        } elseif (strpos($path, 'api/') === 0) {
            $path = substr($path, 4);
        }
        
        return $path;
    }
    
    /**
     * 查找路由
     */
    private function findRoute($method, $path) {
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                $route['params'] = array_slice($matches, 1);
                return $route;
            }
        }
        return null;
    }
    
    /**
     * 执行中间件
     */
    private function executeMiddleware($middleware) {
        foreach ($middleware as $name) {
            $middlewareFile = __DIR__ . '/middleware/' . $name . '.php';
            if (file_exists($middlewareFile)) {
                require_once $middlewareFile;
                $className = ucfirst($name) . 'Middleware';
                if (class_exists($className)) {
                    $instance = new $className();
                    $instance->handle();
                }
            }
        }
    }
    
    /**
     * 执行处理器
     */
    private function executeHandler($route) {
        $handlerFile = __DIR__ . '/' . $route['handler'];
        
        if (!file_exists($handlerFile)) {
            $this->sendError('处理器不存在', 500);
            return;
        }
        
        // 设置路由参数
        if (!empty($route['params'])) {
            $_REQUEST['route_params'] = $route['params'];
        }
        
        require $handlerFile;
    }
    
    /**
     * 处理CORS
     */
    private function handleCors() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
        header('Access-Control-Max-Age: 86400');
    }
    
    /**
     * 发送成功响应
     */
    public function sendResponse($data, $code = 200, $message = 'success') {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        
        echo json_encode([
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 发送错误响应
     */
    public function sendError($message, $code = 400, $data = null) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        
        echo json_encode([
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 创建路由器实例并处理请求
$router = new ApiRouter();
$router->handle();
?>
