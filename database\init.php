<?php
/**
 * 数据库初始化脚本
 * Database Initialization Script
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception('.env file not found');
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

// 数据库连接类
class DatabaseManager {
    private $pdo;
    private $config;
    
    public function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    private function loadConfig() {
        $this->config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'www_bt_cn',
            'username' => $_ENV['DB_USERNAME'] ?? 'www_bt_cn',
            'password' => $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci'
        ];
    }
    
    private function connect() {
        try {
            // 首先连接到MySQL服务器（不指定数据库）
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};charset={$this->config['charset']}";
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']} COLLATE {$this->config['collation']}"
            ]);
            
            echo "✓ 成功连接到MySQL服务器\n";
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }
    
    public function createDatabase() {
        $database = $this->config['database'];
        $collation = $this->config['collation'];
        
        try {
            $this->pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE {$collation}");
            echo "✓ 数据库 '{$database}' 创建成功\n";
            
            // 选择数据库
            $this->pdo->exec("USE `{$database}`");
            echo "✓ 已选择数据库 '{$database}'\n";
        } catch (PDOException $e) {
            throw new Exception("创建数据库失败: " . $e->getMessage());
        }
    }
    
    public function runMigrations() {
        echo "\n开始执行数据库迁移...\n";
        
        // 创建迁移记录表
        $this->createMigrationsTable();
        
        // 获取所有迁移文件
        $migrationFiles = glob(ROOT_PATH . '/database/migrations/*.sql');
        sort($migrationFiles);
        
        foreach ($migrationFiles as $file) {
            $filename = basename($file);
            
            // 检查是否已经执行过
            if ($this->isMigrationExecuted($filename)) {
                echo "- 跳过已执行的迁移: {$filename}\n";
                continue;
            }
            
            echo "- 执行迁移: {$filename}\n";
            $this->executeMigration($file, $filename);
        }
        
        echo "✓ 数据库迁移完成\n";
    }
    
    private function createMigrationsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL,
            batch INT NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_migration (migration)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->pdo->exec($sql);
    }
    
    private function isMigrationExecuted($filename) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM migrations WHERE migration = ?");
        $stmt->execute([$filename]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function executeMigration($file, $filename) {
        $sql = file_get_contents($file);
        
        // 分割SQL语句
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $this->pdo->beginTransaction();
        
        try {
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    $this->pdo->exec($statement);
                }
            }
            
            // 记录迁移
            $stmt = $this->pdo->prepare("INSERT INTO migrations (migration, batch) VALUES (?, 1)");
            $stmt->execute([$filename]);
            
            $this->pdo->commit();
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception("执行迁移 {$filename} 失败: " . $e->getMessage());
        }
    }
    
    public function runSeeds() {
        echo "\n开始执行数据填充...\n";
        
        $seedFiles = glob(ROOT_PATH . '/database/seeds/*.sql');
        sort($seedFiles);
        
        foreach ($seedFiles as $file) {
            $filename = basename($file);
            echo "- 执行数据填充: {$filename}\n";
            
            $sql = file_get_contents($file);
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $this->pdo->exec($statement);
                    } catch (PDOException $e) {
                        // 忽略重复数据错误
                        if ($e->getCode() != 23000) {
                            throw $e;
                        }
                    }
                }
            }
        }
        
        echo "✓ 数据填充完成\n";
    }
    
    public function showTables() {
        echo "\n数据库表列表:\n";
        $tables = $this->pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            $count = $this->pdo->query("SELECT COUNT(*) FROM `{$table}`")->fetchColumn();
            echo "- {$table} ({$count} 条记录)\n";
        }
    }
    
    public function checkConnection() {
        try {
            $this->pdo->query("SELECT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

// 主执行逻辑
try {
    echo "NameSilo域名销售系统 - 数据库初始化\n";
    echo "=====================================\n\n";
    
    // 加载环境变量
    loadEnv(ROOT_PATH . '/.env');
    echo "✓ 环境变量加载成功\n";
    
    // 初始化数据库管理器
    $db = new DatabaseManager();
    
    // 创建数据库
    $db->createDatabase();
    
    // 执行迁移
    $db->runMigrations();
    
    // 执行数据填充
    $db->runSeeds();
    
    // 显示表信息
    $db->showTables();
    
    echo "\n=====================================\n";
    echo "✓ 数据库初始化完成！\n";
    echo "您现在可以访问系统了。\n";
    
} catch (Exception $e) {
    echo "\n❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
