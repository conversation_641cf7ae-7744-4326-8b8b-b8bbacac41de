<?php
/**
 * 邮件配置文件
 * Mail Configuration
 */

return [
    // 默认邮件驱动
    'default' => $_ENV['MAIL_MAILER'] ?? 'smtp',
    
    // 邮件驱动配置
    'mailers' => [
        'smtp' => [
            'transport' => 'smtp',
            'host' => $_ENV['MAIL_HOST'] ?? 'smtp.qq.com',
            'port' => $_ENV['MAIL_PORT'] ?? 587,
            'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
            'username' => $_ENV['MAIL_USERNAME'] ?? '',
            'password' => $_ENV['MAIL_PASSWORD'] ?? '',
            'timeout' => null,
            'auth_mode' => null,
        ],
        
        'ses' => [
            'transport' => 'ses',
            'key' => $_ENV['AWS_ACCESS_KEY_ID'] ?? '',
            'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'] ?? '',
            'region' => $_ENV['AWS_DEFAULT_REGION'] ?? 'us-east-1',
        ],
        
        'mailgun' => [
            'transport' => 'mailgun',
            'domain' => $_ENV['MAILGUN_DOMAIN'] ?? '',
            'secret' => $_ENV['MAILGUN_SECRET'] ?? '',
            'endpoint' => $_ENV['MAILGUN_ENDPOINT'] ?? 'api.mailgun.net',
        ],
        
        'postmark' => [
            'transport' => 'postmark',
            'token' => $_ENV['POSTMARK_TOKEN'] ?? '',
        ],
        
        'sendmail' => [
            'transport' => 'sendmail',
            'path' => $_ENV['MAIL_SENDMAIL_PATH'] ?? '/usr/sbin/sendmail -bs',
        ],
        
        'log' => [
            'transport' => 'log',
            'channel' => $_ENV['MAIL_LOG_CHANNEL'] ?? null,
        ],
        
        'array' => [
            'transport' => 'array',
        ],
        
        'failover' => [
            'transport' => 'failover',
            'mailers' => [
                'smtp',
                'log',
            ],
        ],
    ],
    
    // 全局发件人配置
    'from' => [
        'address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
        'name' => $_ENV['MAIL_FROM_NAME'] ?? 'NameSilo域名管理系统',
    ],
    
    // 邮件模板配置
    'templates' => [
        // 用户注册
        'user_register' => [
            'subject' => '欢迎注册 {site_name}',
            'template' => 'emails.user.register',
            'variables' => ['username', 'email', 'verification_link', 'site_name'],
        ],
        
        // 邮箱验证
        'email_verification' => [
            'subject' => '请验证您的邮箱地址',
            'template' => 'emails.user.verification',
            'variables' => ['username', 'verification_link', 'expire_time'],
        ],
        
        // 密码重置
        'password_reset' => [
            'subject' => '重置您的密码',
            'template' => 'emails.user.password_reset',
            'variables' => ['username', 'reset_link', 'expire_time'],
        ],
        
        // 订单确认
        'order_confirmation' => [
            'subject' => '订单确认 - {order_number}',
            'template' => 'emails.order.confirmation',
            'variables' => ['username', 'order_number', 'total_amount', 'order_items'],
        ],
        
        // 支付成功
        'payment_success' => [
            'subject' => '支付成功 - {order_number}',
            'template' => 'emails.payment.success',
            'variables' => ['username', 'order_number', 'amount', 'payment_method'],
        ],
        
        // 域名注册成功
        'domain_registered' => [
            'subject' => '域名注册成功 - {domain_name}',
            'template' => 'emails.domain.registered',
            'variables' => ['username', 'domain_name', 'expiry_date', 'nameservers'],
        ],
        
        // 域名即将过期
        'domain_expiring' => [
            'subject' => '域名即将过期提醒 - {domain_name}',
            'template' => 'emails.domain.expiring',
            'variables' => ['username', 'domain_name', 'expiry_date', 'renewal_link'],
        ],
        
        // 系统通知
        'system_notification' => [
            'subject' => '系统通知 - {title}',
            'template' => 'emails.system.notification',
            'variables' => ['username', 'title', 'content', 'action_link'],
        ],
    ],
    
    // 邮件队列配置
    'queue' => [
        'enabled' => filter_var($_ENV['MAIL_QUEUE_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'connection' => $_ENV['MAIL_QUEUE_CONNECTION'] ?? 'default',
        'queue' => $_ENV['MAIL_QUEUE_NAME'] ?? 'emails',
        'retry_after' => (int)($_ENV['MAIL_QUEUE_RETRY_AFTER'] ?? 90),
        'max_tries' => (int)($_ENV['MAIL_QUEUE_MAX_TRIES'] ?? 3),
    ],
    
    // 邮件限制配置
    'limits' => [
        // 每小时发送限制
        'hourly_limit' => (int)($_ENV['MAIL_HOURLY_LIMIT'] ?? 100),
        
        // 每日发送限制
        'daily_limit' => (int)($_ENV['MAIL_DAILY_LIMIT'] ?? 1000),
        
        // 单个用户每日限制
        'user_daily_limit' => (int)($_ENV['MAIL_USER_DAILY_LIMIT'] ?? 10),
        
        // 批量发送限制
        'batch_size' => (int)($_ENV['MAIL_BATCH_SIZE'] ?? 50),
        
        // 发送间隔（秒）
        'send_interval' => (int)($_ENV['MAIL_SEND_INTERVAL'] ?? 1),
    ],
    
    // 邮件验证配置
    'verification' => [
        'enabled' => filter_var($_ENV['MAIL_VERIFICATION_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'expire_minutes' => (int)($_ENV['MAIL_VERIFICATION_EXPIRE'] ?? 60),
        'throttle_minutes' => (int)($_ENV['MAIL_VERIFICATION_THROTTLE'] ?? 1),
        'max_attempts' => (int)($_ENV['MAIL_VERIFICATION_MAX_ATTEMPTS'] ?? 3),
    ],
    
    // 邮件安全配置
    'security' => [
        // DKIM签名
        'dkim' => [
            'enabled' => filter_var($_ENV['MAIL_DKIM_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'domain' => $_ENV['MAIL_DKIM_DOMAIN'] ?? '',
            'selector' => $_ENV['MAIL_DKIM_SELECTOR'] ?? 'default',
            'private_key' => $_ENV['MAIL_DKIM_PRIVATE_KEY'] ?? '',
        ],
        
        // SPF验证
        'spf' => [
            'enabled' => filter_var($_ENV['MAIL_SPF_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'record' => $_ENV['MAIL_SPF_RECORD'] ?? 'v=spf1 include:_spf.google.com ~all',
        ],
        
        // 反垃圾邮件
        'anti_spam' => [
            'enabled' => filter_var($_ENV['MAIL_ANTI_SPAM_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'blacklist_domains' => explode(',', $_ENV['MAIL_BLACKLIST_DOMAINS'] ?? ''),
            'whitelist_domains' => explode(',', $_ENV['MAIL_WHITELIST_DOMAINS'] ?? ''),
        ],
    ],
    
    // 邮件统计配置
    'tracking' => [
        'enabled' => filter_var($_ENV['MAIL_TRACKING_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'open_tracking' => filter_var($_ENV['MAIL_OPEN_TRACKING'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'click_tracking' => filter_var($_ENV['MAIL_CLICK_TRACKING'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'bounce_tracking' => filter_var($_ENV['MAIL_BOUNCE_TRACKING'] ?? true, FILTER_VALIDATE_BOOLEAN),
    ],
    
    // 邮件日志配置
    'logging' => [
        'enabled' => filter_var($_ENV['MAIL_LOG_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'level' => $_ENV['MAIL_LOG_LEVEL'] ?? 'info',
        'channel' => $_ENV['MAIL_LOG_CHANNEL'] ?? 'mail',
        'log_content' => filter_var($_ENV['MAIL_LOG_CONTENT'] ?? false, FILTER_VALIDATE_BOOLEAN),
    ],
    
    // 测试配置
    'testing' => [
        'enabled' => filter_var($_ENV['MAIL_TESTING'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'test_email' => $_ENV['MAIL_TEST_EMAIL'] ?? '<EMAIL>',
        'mock_send' => filter_var($_ENV['MAIL_MOCK_SEND'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'save_to_file' => filter_var($_ENV['MAIL_SAVE_TO_FILE'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'file_path' => $_ENV['MAIL_TEST_FILE_PATH'] ?? storage_path('mail_test.log'),
    ],
];

/**
 * 获取存储路径
 */
function storage_path($path = '') {
    $basePath = defined('STORAGE_PATH') ? STORAGE_PATH : dirname(__DIR__) . '/storage';
    return $path ? $basePath . '/' . ltrim($path, '/') : $basePath;
}
?>
