<?php
$title = '订单管理';
$page = 'orders';

$db = getDatabase();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $orderId = $_POST['order_id'] ?? '';
    
    switch ($action) {
        case 'update_status':
            if ($orderId && isset($_POST['status'])) {
                $db->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?")->execute([$_POST['status'], $orderId]);
                $message = '订单状态已更新';
            }
            break;
            
        case 'delete_order':
            if ($orderId) {
                $db->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
                $message = '订单已删除';
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$payment_status = $_GET['payment_status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page_num = max(1, (int)($_GET['page_num'] ?? 1));
$per_page = 20;
$offset = ($page_num - 1) * $per_page;

// 构建查询条件
$where = ['1=1'];
$params = [];

if ($search) {
    $where[] = "(o.order_number LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
    $searchTerm = "%{$search}%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

if ($status) {
    $where[] = "o.status = ?";
    $params[] = $status;
}

if ($payment_status) {
    $where[] = "o.payment_status = ?";
    $params[] = $payment_status;
}

if ($date_from) {
    $where[] = "DATE(o.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where[] = "DATE(o.created_at) <= ?";
    $params[] = $date_to;
}

$whereClause = implode(' AND ', $where);

// 获取总数
$totalQuery = "SELECT COUNT(*) as total FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE {$whereClause}";
$stmt = $db->prepare($totalQuery);
$stmt->execute($params);
$total = $stmt->fetch()['total'];

// 获取订单列表
$ordersQuery = "SELECT o.*, u.username, u.email FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE {$whereClause} ORDER BY o.created_at DESC LIMIT {$offset}, {$per_page}";
$stmt = $db->prepare($ordersQuery);
$stmt->execute($params);
$orders = $stmt->fetchAll();

$totalPages = ceil($total / $per_page);

// 获取统计数据
$stats = $db->query("SELECT 
    COUNT(*) as total_orders,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
    SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue,
    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount ELSE 0 END) as today_revenue
    FROM orders")->fetch();

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">订单管理</h1>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="exportOrders()">
            <i class="fas fa-download"></i>
            导出订单
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['total_orders'] ?? 0) ?></div>
            <p class="stats-label">总订单数</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['completed_orders'] ?? 0) ?></div>
            <p class="stats-label">已完成</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['pending_orders'] ?? 0) ?></div>
            <p class="stats-label">待处理</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-number">¥<?= number_format($stats['total_revenue'] ?? 0, 2) ?></div>
            <p class="stats-label">总收入</p>
            <div class="stats-trend up">
                今日: ¥<?= number_format($stats['today_revenue'] ?? 0, 2) ?>
            </div>
        </div>
    </div>
</div>

<?php if (isset($message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="orders">
            <div class="col-md-3">
                <input type="text" class="form-control" name="search" placeholder="搜索订单号、用户..." value="<?= htmlspecialchars($search) ?>">
            </div>
            <div class="col-md-2">
                <select class="form-select" name="status">
                    <option value="">订单状态</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>待处理</option>
                    <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>处理中</option>
                    <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>已完成</option>
                    <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>已取消</option>
                    <option value="refunded" <?= $status === 'refunded' ? 'selected' : '' ?>>已退款</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="payment_status">
                    <option value="">支付状态</option>
                    <option value="pending" <?= $payment_status === 'pending' ? 'selected' : '' ?>>待支付</option>
                    <option value="paid" <?= $payment_status === 'paid' ? 'selected' : '' ?>>已支付</option>
                    <option value="failed" <?= $payment_status === 'failed' ? 'selected' : '' ?>>支付失败</option>
                    <option value="refunded" <?= $payment_status === 'refunded' ? 'selected' : '' ?>>已退款</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>" placeholder="开始日期">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>" placeholder="结束日期">
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 订单列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-shopping-cart"></i>
            订单列表 (共 <?= number_format($total) ?> 个订单)
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($orders)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>用户</th>
                            <th>金额</th>
                            <th>支付方式</th>
                            <th>订单状态</th>
                            <th>支付状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <code><?= htmlspecialchars($order['order_number']) ?></code>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($order['username'] ?? '未知用户') ?></strong>
                                        <?php if ($order['email']): ?>
                                            <br>
                                            <small class="text-muted"><?= htmlspecialchars($order['email']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <strong class="text-success">¥<?= number_format($order['total_amount'], 2) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= $order['currency'] ?></small>
                                </td>
                                <td>
                                    <?= $order['payment_method'] ? htmlspecialchars($order['payment_method']) : '<span class="text-muted">-</span>' ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?= 
                                        $order['status'] === 'completed' ? 'success' : 
                                        ($order['status'] === 'pending' ? 'warning' : 
                                        ($order['status'] === 'processing' ? 'info' : 
                                        ($order['status'] === 'cancelled' ? 'danger' : 'secondary'))) ?>">
                                        <?= [
                                            'pending' => '待处理',
                                            'processing' => '处理中',
                                            'completed' => '已完成',
                                            'cancelled' => '已取消',
                                            'refunded' => '已退款'
                                        ][$order['status']] ?? $order['status'] ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?= 
                                        $order['payment_status'] === 'paid' ? 'success' : 
                                        ($order['payment_status'] === 'pending' ? 'warning' : 
                                        ($order['payment_status'] === 'failed' ? 'danger' : 'secondary')) ?>">
                                        <?= [
                                            'pending' => '待支付',
                                            'paid' => '已支付',
                                            'failed' => '支付失败',
                                            'refunded' => '已退款'
                                        ][$order['payment_status']] ?? $order['payment_status'] ?>
                                    </span>
                                </td>
                                <td><?= date('Y-m-d H:i', strtotime($order['created_at'])) ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#orderModal<?= $order['id'] ?>" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <div class="dropdown d-inline">
                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                                        <input type="hidden" name="status" value="completed">
                                                        <button type="submit" class="dropdown-item">标记为已完成</button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                                        <input type="hidden" name="status" value="cancelled">
                                                        <button type="submit" class="dropdown-item">标记为已取消</button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="action" value="delete_order">
                                            <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="删除"
                                                    onclick="return confirm('确定要删除此订单吗？')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer">
                    <nav>
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page_num > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=orders&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&payment_status=<?= urlencode($payment_status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        上一页
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=orders&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&payment_status=<?= urlencode($payment_status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page_num < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=orders&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&payment_status=<?= urlencode($payment_status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        下一页
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-shopping-cart"></i>
                <p>没有找到订单</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function exportOrders() {
    // 导出订单功能
    alert('导出功能开发中...');
}
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
