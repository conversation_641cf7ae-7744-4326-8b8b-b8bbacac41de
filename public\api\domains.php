<?php
/**
 * 域名API接口
 * Domain API Endpoints
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));

// 引入统一的数据库连接
require_once dirname(__DIR__) . '/includes/database.php';

// 引入 NameSilo API 客户端
require_once __DIR__ . '/NameSiloClient.php';

// 初始化 NameSilo 客户端
function getNameSiloClient() {
    static $client = null;
    if ($client === null) {
        $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
        $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
        $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

        if (empty($apiKey)) {
            throw new Exception('NameSilo API密钥未配置');
        }

        $client = new NameSiloClient($apiKey, $apiUrl, $sandbox);
    }
    return $client;
}

// 数据库连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => '数据库连接失败']);
            exit;
        }
    }
    return $pdo;
}

// 记录日志
function logActivity($action, $resourceType = null, $resourceId = null, $data = null) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("INSERT INTO logs (user_id, action, resource_type, resource_id, ip_address, user_agent, request_data, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['user_id'] ?? null,
            $action,
            $resourceType,
            $resourceId,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $data ? json_encode($data) : null
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主要功能
    }
}

// 获取域名价格
function getDomainPrice($tld) {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT registration_price, renewal_price, transfer_price FROM domain_prices WHERE tld = ?");
    $stmt->execute([$tld]);
    return $stmt->fetch();
}

// 检查域名可用性（使用NameSilo API）
function checkDomainAvailability($domain) {
    try {
        $client = getNameSiloClient();
        $results = $client->checkDomainAvailability($domain);

        if (!isset($results[$domain])) {
            return [
                'domain' => $domain,
                'available' => false,
                'reason' => 'API查询失败'
            ];
        }

        $result = $results[$domain];

        // 如果域名可用，获取价格信息
        if ($result['available']) {
            $tld = substr($domain, strrpos($domain, '.'));
            $price = getDomainPrice($tld);

            if ($price) {
                $result['price'] = $price['registration_price'];
                $result['currency'] = 'CNY';
            } else {
                // 如果本地没有价格信息，尝试从NameSilo获取
                $result['price'] = null;
                $result['currency'] = 'USD';
            }
        }

        return $result;

    } catch (Exception $e) {
        // 如果API调用失败，记录错误并返回错误信息
        error_log('NameSilo API Error: ' . $e->getMessage());

        return [
            'domain' => $domain,
            'available' => false,
            'reason' => 'API服务暂时不可用，请稍后重试'
        ];
    }
}

// 获取域名建议
function getDomainSuggestions($keyword) {
    $db = getDatabase();
    
    // 获取支持的TLD
    $tlds = $db->query("SELECT tld, registration_price FROM domain_prices ORDER BY registration_price ASC")->fetchAll();
    
    $suggestions = [];
    foreach ($tlds as $tld) {
        $domain = $keyword . $tld['tld'];
        $availability = checkDomainAvailability($domain);
        
        if ($availability['available']) {
            $suggestions[] = [
                'domain' => $domain,
                'price' => $tld['registration_price'],
                'currency' => 'CNY',
                'popular' => in_array($tld['tld'], ['.com', '.cn', '.net', '.org'])
            ];
        }
        
        // 限制建议数量
        if (count($suggestions) >= 10) {
            break;
        }
    }
    
    return $suggestions;
}

// 处理API请求
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

// 对于POST请求，也检查请求体中的action
if ($method === 'POST' && empty($action)) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
}

try {
    switch ($action) {
        case 'check':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }
            
            $domain = trim($_GET['domain'] ?? '');
            if (!$domain) {
                throw new Exception('域名参数不能为空', 400);
            }
            
            // 验证域名格式
            if (!filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
                throw new Exception('域名格式不正确', 400);
            }
            
            $result = checkDomainAvailability($domain);
            
            // 记录查询日志
            logActivity('domain_check', 'domain', null, ['domain' => $domain]);
            
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'suggest':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }
            
            $keyword = trim($_GET['keyword'] ?? '');
            if (!$keyword) {
                throw new Exception('关键词参数不能为空', 400);
            }
            
            // 验证关键词
            if (!preg_match('/^[a-zA-Z0-9\-]+$/', $keyword)) {
                throw new Exception('关键词只能包含字母、数字和连字符', 400);
            }
            
            $suggestions = getDomainSuggestions($keyword);
            
            // 记录建议查询日志
            logActivity('domain_suggest', 'domain', null, ['keyword' => $keyword]);
            
            echo json_encode([
                'success' => true,
                'data' => $suggestions
            ]);
            break;
            
        case 'prices':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            $db = getDatabase();
            $prices = $db->query("SELECT tld, registration_price, renewal_price, transfer_price FROM domain_prices ORDER BY tld")->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $prices
            ]);
            break;

        case 'popular':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            $db = getDatabase();
            // 获取热门域名后缀（这里可以根据实际需求调整）
            $popularTlds = ['.com', '.cn', '.net', '.org', '.app', '.io'];
            $placeholders = str_repeat('?,', count($popularTlds) - 1) . '?';
            $stmt = $db->prepare("SELECT tld, registration_price, renewal_price, transfer_price FROM domain_prices WHERE tld IN ($placeholders) ORDER BY FIELD(tld, " . $placeholders . ")");
            $stmt->execute(array_merge($popularTlds, $popularTlds));
            $popular = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $popular
            ]);
            break;
            
        case 'sync-prices':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            try {
                $client = getNameSiloClient();
                $namesiloPrices = $client->getDomainPrices();

                $db = getDatabase();
                $updated = 0;
                $added = 0;

                foreach ($namesiloPrices as $tld => $prices) {
                    // 检查是否已存在
                    $stmt = $db->prepare("SELECT id FROM domain_prices WHERE tld = ?");
                    $stmt->execute([$tld]);
                    $existing = $stmt->fetch();

                    if ($existing) {
                        // 更新现有价格
                        $stmt = $db->prepare("UPDATE domain_prices SET registration_price = ?, renewal_price = ?, transfer_price = ?, updated_at = NOW() WHERE tld = ?");
                        $stmt->execute([$prices['registration'], $prices['renewal'], $prices['transfer'], $tld]);
                        $updated++;
                    } else {
                        // 添加新的TLD价格
                        $stmt = $db->prepare("INSERT INTO domain_prices (tld, registration_price, renewal_price, transfer_price, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
                        $stmt->execute([$tld, $prices['registration'], $prices['renewal'], $prices['transfer']]);
                        $added++;
                    }
                }

                // 记录同步日志
                logActivity('sync_prices', 'system', null, [
                    'updated' => $updated,
                    'added' => $added,
                    'total' => count($namesiloPrices)
                ]);

                echo json_encode([
                    'success' => true,
                    'data' => [
                        'message' => '价格同步完成',
                        'updated' => $updated,
                        'added' => $added,
                        'total' => count($namesiloPrices)
                    ]
                ]);

            } catch (Exception $e) {
                throw new Exception('价格同步失败: ' . $e->getMessage(), 500);
            }
            break;

        case 'sync-domains':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            try {
                $client = getNameSiloClient();
                $namesiloDomains = $client->getDomainList();

                $db = getDatabase();
                $updated = 0;
                $added = 0;

                foreach ($namesiloDomains as $domainData) {
                    $domain = $domainData['domain'];

                    // 检查是否已存在
                    $stmt = $db->prepare("SELECT id FROM domains WHERE domain_name = ?");
                    $stmt->execute([$domain]);
                    $existing = $stmt->fetch();

                    if ($existing) {
                        // 更新现有域名信息
                        $stmt = $db->prepare("
                            UPDATE domains SET
                                status = ?,
                                expires_at = ?,
                                auto_renew = ?,
                                updated_at = NOW()
                            WHERE domain_name = ?
                        ");
                        $stmt->execute([
                            $domainData['status'],
                            $domainData['expires'],
                            $domainData['auto_renew'] ? 1 : 0,
                            $domain
                        ]);
                        $updated++;
                    } else {
                        // 添加新域名
                        $stmt = $db->prepare("
                            INSERT INTO domains (
                                domain_name,
                                status,
                                expires_at,
                                auto_renew,
                                provider,
                                created_at,
                                updated_at
                            ) VALUES (?, ?, ?, ?, 'namesilo', NOW(), NOW())
                        ");
                        $stmt->execute([
                            $domain,
                            $domainData['status'],
                            $domainData['expires'],
                            $domainData['auto_renew'] ? 1 : 0
                        ]);
                        $added++;
                    }
                }

                // 记录同步日志
                logActivity('sync_domains', 'system', null, [
                    'updated' => $updated,
                    'added' => $added,
                    'total' => count($namesiloDomains)
                ]);

                echo json_encode([
                    'success' => true,
                    'data' => [
                        'message' => '域名同步完成',
                        'updated' => $updated,
                        'added' => $added,
                        'total' => count($namesiloDomains)
                    ]
                ]);

            } catch (Exception $e) {
                throw new Exception('域名同步失败: ' . $e->getMessage(), 500);
            }
            break;

        case 'save-config':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            // 获取POST数据
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                $input = $_POST;
            }

            $apiKey = trim($input['api_key'] ?? '');
            $sandbox = ($input['sandbox'] ?? false) === true;

            if (!$apiKey) {
                throw new Exception('API密钥不能为空', 400);
            }

            // 读取现有的 .env 文件
            $envPath = ROOT_PATH . '/.env';
            $envContent = '';

            if (file_exists($envPath)) {
                $envContent = file_get_contents($envPath);
            }

            // 更新或添加 NameSilo 配置
            $newConfig = [
                'NAMESILO_API_KEY' => $apiKey,
                'NAMESILO_API_URL' => 'https://www.namesilo.com/api',
                'NAMESILO_SANDBOX' => $sandbox ? 'true' : 'false'
            ];

            foreach ($newConfig as $key => $value) {
                $pattern = "/^{$key}=.*$/m";
                $replacement = "{$key}={$value}";

                if (preg_match($pattern, $envContent)) {
                    // 更新现有配置
                    $envContent = preg_replace($pattern, $replacement, $envContent);
                } else {
                    // 添加新配置
                    $envContent .= "\n{$replacement}";
                }
            }

            // 保存 .env 文件
            if (file_put_contents($envPath, $envContent) === false) {
                throw new Exception('无法保存配置文件', 500);
            }

            // 重新加载环境变量
            loadEnv($envPath);

            echo json_encode([
                'success' => true,
                'data' => [
                    'message' => 'API配置保存成功',
                    'sandbox' => $sandbox
                ]
            ]);
            break;

        case 'test-api':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            try {
                $client = getNameSiloClient();
                $connected = $client->testConnection();

                if ($connected) {
                    $balance = $client->getAccountBalance();
                    echo json_encode([
                        'success' => true,
                        'data' => [
                            'connected' => true,
                            'balance' => $balance['balance'],
                            'message' => 'NameSilo API连接正常'
                        ]
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'data' => [
                            'connected' => false,
                            'message' => 'NameSilo API连接失败'
                        ]
                    ]);
                }

            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => 'API测试失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'register':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }
            
            // 获取POST数据
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $domain = trim($input['domain'] ?? '');
            $years = (int)($input['years'] ?? 1);
            $userId = $input['user_id'] ?? null;
            
            if (!$domain) {
                throw new Exception('域名参数不能为空', 400);
            }
            
            if ($years < 1 || $years > 10) {
                throw new Exception('注册年限必须在1-10年之间', 400);
            }
            
            // 检查域名可用性
            $availability = checkDomainAvailability($domain);
            if (!$availability['available']) {
                throw new Exception($availability['reason'] ?? '域名不可用', 400);
            }
            
            // 计算总价
            $totalPrice = $availability['price'] * $years;
            
            // 创建订单（简化版）
            $db = getDatabase();
            $orderNumber = 'ORD' . date('YmdHis') . rand(1000, 9999);
            
            $db->beginTransaction();
            try {
                // 创建订单
                $stmt = $db->prepare("INSERT INTO orders (order_number, user_id, subtotal, total_amount, status, created_at) VALUES (?, ?, ?, ?, 'pending', NOW())");
                $stmt->execute([$orderNumber, $userId, $totalPrice, $totalPrice]);
                $orderId = $db->lastInsertId();
                
                // 创建订单项目
                $stmt = $db->prepare("INSERT INTO order_items (order_id, domain_name, service_type, years, unit_price, total_price) VALUES (?, ?, 'registration', ?, ?, ?)");
                $stmt->execute([$orderId, $domain, $years, $availability['price'], $totalPrice]);
                
                $db->commit();
                
                // 记录注册日志
                logActivity('domain_register_request', 'order', $orderId, [
                    'domain' => $domain,
                    'years' => $years,
                    'total_price' => $totalPrice
                ]);
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'order_id' => $orderId,
                        'order_number' => $orderNumber,
                        'domain' => $domain,
                        'years' => $years,
                        'total_price' => $totalPrice,
                        'currency' => 'CNY',
                        'payment_url' => '/payment.php?order_id=' . $orderId
                    ]
                ]);
                
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            break;
            
        case 'renew':
            // 域名续费
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            $domainName = $input['domain'] ?? '';
            $years = (int)($input['years'] ?? 1);

            if (empty($domainName)) {
                throw new Exception('域名不能为空');
            }

            if ($years < 1 || $years > 10) {
                throw new Exception('续费年限必须在1-10年之间');
            }

            // 检查域名是否存在
            $stmt = $db->prepare("SELECT * FROM domains WHERE domain_name = ?");
            $stmt->execute([$domainName]);
            $domain = $stmt->fetch();

            if (!$domain) {
                throw new Exception('域名不存在');
            }

            // 调用NameSilo API续费
            $client = getNameSiloClient();
            $result = $client->renewDomain($domainName, $years);

            if ($result['success']) {
                // 更新数据库中的到期日期
                $newExpiryDate = date('Y-m-d', strtotime($domain['expiry_date'] . " +{$years} years"));
                $stmt = $db->prepare("UPDATE domains SET expiry_date = ?, updated_at = NOW() WHERE domain_name = ?");
                $stmt->execute([$newExpiryDate, $domainName]);

                echo json_encode([
                    'success' => true,
                    'message' => "域名 {$domainName} 续费成功，续费 {$years} 年",
                    'new_expiry_date' => $newExpiryDate,
                    'order_id' => $result['order_id'] ?? null
                ]);
            } else {
                throw new Exception($result['message'] ?? '续费失败');
            }
            break;

        case 'transfer':
            // 域名转移
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            $domainName = $input['domain'] ?? '';
            $authCode = $input['auth_code'] ?? '';

            if (empty($domainName) || empty($authCode)) {
                throw new Exception('域名和授权码不能为空');
            }

            // 调用NameSilo API转移
            $client = getNameSiloClient();
            $result = $client->transferDomain($domainName, $authCode);

            if ($result['success']) {
                // 更新数据库状态
                $stmt = $db->prepare("UPDATE domains SET status = 'pending', updated_at = NOW() WHERE domain_name = ?");
                $stmt->execute([$domainName]);

                echo json_encode([
                    'success' => true,
                    'message' => "域名 {$domainName} 转移请求已提交",
                    'order_id' => $result['order_id'] ?? null
                ]);
            } else {
                throw new Exception($result['message'] ?? '转移失败');
            }
            break;

        case 'dns_records':
            // DNS记录管理
            if ($method === 'GET') {
                // 获取DNS记录
                $domainName = $_GET['domain'] ?? '';

                if (empty($domainName)) {
                    throw new Exception('域名不能为空');
                }

                $client = getNameSiloClient();
                $records = $client->getDNSRecords($domainName);

                echo json_encode([
                    'success' => true,
                    'domain' => $domainName,
                    'records' => $records
                ]);

            } elseif ($method === 'POST') {
                // 添加/更新DNS记录
                $domainName = $input['domain'] ?? '';
                $recordType = $input['type'] ?? '';
                $recordName = $input['name'] ?? '';
                $recordValue = $input['value'] ?? '';
                $recordTTL = (int)($input['ttl'] ?? 3600);

                if (empty($domainName) || empty($recordType) || empty($recordValue)) {
                    throw new Exception('域名、记录类型和记录值不能为空');
                }

                $client = getNameSiloClient();
                $result = $client->addDNSRecord($domainName, $recordType, $recordName, $recordValue, $recordTTL);

                if ($result['success']) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'DNS记录添加成功',
                        'record_id' => $result['record_id'] ?? null
                    ]);
                } else {
                    throw new Exception($result['message'] ?? 'DNS记录添加失败');
                }

            } elseif ($method === 'DELETE') {
                // 删除DNS记录
                $domainName = $input['domain'] ?? '';
                $recordId = $input['record_id'] ?? '';

                if (empty($domainName) || empty($recordId)) {
                    throw new Exception('域名和记录ID不能为空');
                }

                $client = getNameSiloClient();
                $result = $client->deleteDNSRecord($domainName, $recordId);

                if ($result['success']) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'DNS记录删除成功'
                    ]);
                } else {
                    throw new Exception($result['message'] ?? 'DNS记录删除失败');
                }
            }
            break;

        case 'toggle_auto_renew':
            // 切换自动续费
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            $domainName = $input['domain'] ?? '';
            $autoRenew = (bool)($input['auto_renew'] ?? false);

            if (empty($domainName)) {
                throw new Exception('域名不能为空');
            }

            // 更新数据库
            $stmt = $db->prepare("UPDATE domains SET auto_renew = ?, updated_at = NOW() WHERE domain_name = ?");
            $stmt->execute([$autoRenew ? 1 : 0, $domainName]);

            // 调用NameSilo API设置自动续费
            $client = getNameSiloClient();
            $result = $client->setAutoRenewal($domainName, $autoRenew);

            echo json_encode([
                'success' => true,
                'message' => $autoRenew ? '自动续费已开启' : '自动续费已关闭',
                'auto_renew' => $autoRenew
            ]);
            break;

        case 'info':
            // 获取域名详细信息
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            $domainName = $_GET['domain'] ?? '';

            if (empty($domainName)) {
                throw new Exception('域名不能为空');
            }

            $db = getDatabase();

            // 从数据库获取域名信息
            $stmt = $db->prepare("SELECT * FROM domains WHERE domain_name = ?");
            $stmt->execute([$domainName]);
            $domain = $stmt->fetch();

            if (!$domain) {
                throw new Exception('域名不存在');
            }

            // 尝试从NameSilo API获取更详细的信息
            try {
                $client = getNameSiloClient();
                $apiInfo = $client->getDomainInfo($domainName);

                if ($apiInfo['success']) {
                    // 合并API信息
                    $domain = array_merge($domain, $apiInfo['domain']);
                }
            } catch (Exception $e) {
                // API调用失败时使用数据库信息
            }

            echo json_encode([
                'success' => true,
                'domain' => $domain
            ]);
            break;

        case 'delete':
            // 删除域名
            if ($method !== 'DELETE') {
                throw new Exception('Method not allowed', 405);
            }

            $domainName = $input['domain'] ?? '';

            if (empty($domainName)) {
                throw new Exception('域名不能为空');
            }

            $db = getDatabase();

            // 检查域名是否存在
            $stmt = $db->prepare("SELECT * FROM domains WHERE domain_name = ?");
            $stmt->execute([$domainName]);
            $domain = $stmt->fetch();

            if (!$domain) {
                throw new Exception('域名不存在');
            }

            // 从数据库中删除域名记录
            $stmt = $db->prepare("DELETE FROM domains WHERE domain_name = ?");
            $stmt->execute([$domainName]);

            echo json_encode([
                'success' => true,
                'message' => "域名 {$domainName} 已从系统中删除"
            ]);
            break;

        case '':
        case 'list':
            // 默认返回域名列表
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            // 模拟域名列表数据
            echo json_encode([
                'success' => true,
                'domains' => [
                    [
                        'id' => 1,
                        'domain' => 'namesilo-namesilo-sample.com',
                        'status' => 'active',
                        'expiry_date' => '2025-12-31',
                        'auto_renew' => true
                    ],
                    [
                        'id' => 2,
                        'domain' => 'test.net',
                        'status' => 'active',
                        'expiry_date' => '2025-06-15',
                        'auto_renew' => false
                    ]
                ],
                'total' => 2
            ]);
            break;

        case 'user_stats':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            // 检查用户登录
            session_start();
            if (!isset($_SESSION['user_id'])) {
                throw new Exception('未登录', 401);
            }

            $userId = $_SESSION['user_id'];
            $db = getDatabase();

            // 获取用户域名统计
            $stats = [
                'total_domains' => 0,
                'active_domains' => 0,
                'expiring_soon' => 0,
                'auto_renew_enabled' => 0
            ];

            // 总域名数
            $stmt = $db->prepare("SELECT COUNT(*) FROM domains WHERE user_id = ?");
            $stmt->execute([$userId]);
            $stats['total_domains'] = (int)$stmt->fetchColumn();

            // 活跃域名数
            $stmt = $db->prepare("SELECT COUNT(*) FROM domains WHERE user_id = ? AND status IN ('registered', 'active')");
            $stmt->execute([$userId]);
            $stats['active_domains'] = (int)$stmt->fetchColumn();

            // 即将到期的域名数（30天内）
            $stmt = $db->prepare("SELECT COUNT(*) FROM domains WHERE user_id = ? AND expiry_date <= DATE_ADD(NOW(), INTERVAL 30 DAY) AND expiry_date > NOW()");
            $stmt->execute([$userId]);
            $stats['expiring_soon'] = (int)$stmt->fetchColumn();

            // 启用自动续费的域名数
            $stmt = $db->prepare("SELECT COUNT(*) FROM domains WHERE user_id = ? AND auto_renew = 1");
            $stmt->execute([$userId]);
            $stats['auto_renew_enabled'] = (int)$stmt->fetchColumn();

            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
            break;

        case 'sync_user_domains':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            // 检查用户登录
            session_start();
            if (!isset($_SESSION['user_id'])) {
                throw new Exception('未登录', 401);
            }

            $userId = $_SESSION['user_id'];
            $db = getDatabase();

            try {
                // 检查API密钥是否配置
                $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
                if (empty($apiKey) || strlen($apiKey) < 25) {
                    // API未配置，使用模拟数据进行演示
                    $mockDomains = [
                        [
                            'domain' => 'example-demo.com',
                            'status' => 'Active',
                            'expires' => '2025-12-31',
                            'created' => '2024-01-01',
                            'auto_renew' => false
                        ],
                        [
                            'domain' => 'test-domain.net',
                            'status' => 'Active',
                            'expires' => '2025-06-15',
                            'created' => '2024-06-15',
                            'auto_renew' => true
                        ]
                    ];

                    $syncedCount = 0;
                    $errors = [];

                    foreach ($mockDomains as $domain) {
                        try {
                            // 检查域名是否已存在
                            $stmt = $db->prepare("SELECT id FROM domains WHERE domain_name = ? AND user_id = ?");
                            $stmt->execute([$domain['domain'], $userId]);

                            if (!$stmt->fetch()) {
                                // 提取TLD
                                $tld = substr($domain['domain'], strrpos($domain['domain'], '.'));

                                // 插入域名记录
                                $stmt = $db->prepare("
                                    INSERT INTO domains (
                                        user_id, domain_name, tld, status,
                                        registration_date, expiry_date, auto_renew,
                                        registrar, created_at, updated_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 'NameSilo', NOW(), NOW())
                                ");

                                $stmt->execute([
                                    $userId,
                                    $domain['domain'],
                                    $tld,
                                    'registered',
                                    $domain['created'],
                                    $domain['expires'],
                                    $domain['auto_renew'] ? 1 : 0
                                ]);

                                $syncedCount++;
                            }
                        } catch (Exception $e) {
                            $errors[] = "域名 {$domain['domain']} 同步失败: " . $e->getMessage();
                        }
                    }

                    echo json_encode([
                        'success' => true,
                        'message' => "演示模式：成功同步 {$syncedCount} 个模拟域名（API密钥未配置）",
                        'synced_count' => $syncedCount,
                        'errors' => $errors,
                        'demo_mode' => true
                    ]);

                } else {
                    // 使用真实API
                    $client = getNameSiloClient();
                    $domains = $client->listDomains();

                    $syncedCount = 0;
                    $errors = [];

                    foreach ($domains as $domain) {
                        try {
                            // 检查域名是否已存在
                            $stmt = $db->prepare("SELECT id FROM domains WHERE domain_name = ? AND user_id = ?");
                            $stmt->execute([$domain['domain'], $userId]);

                            if (!$stmt->fetch()) {
                                // 获取域名详细信息
                                $domainInfo = $client->getDomainInfo($domain['domain']);

                                // 提取TLD
                                $tld = substr($domain['domain'], strrpos($domain['domain'], '.'));

                                // 插入域名记录
                                $stmt = $db->prepare("
                                    INSERT INTO domains (
                                        user_id, domain_name, tld, status,
                                        registration_date, expiry_date, auto_renew,
                                        registrar, created_at, updated_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 'NameSilo', NOW(), NOW())
                                ");

                                $stmt->execute([
                                    $userId,
                                    $domain['domain'],
                                    $tld,
                                    'registered',
                                    $domainInfo['registration_date'] ?? $domain['created'],
                                    $domainInfo['expiry_date'] ?? $domain['expires'],
                                    $domainInfo['auto_renew'] ?? $domain['auto_renew']
                                ]);

                                $syncedCount++;
                            }
                        } catch (Exception $e) {
                            $errors[] = "域名 {$domain['domain']} 同步失败: " . $e->getMessage();
                        }
                    }

                    echo json_encode([
                        'success' => true,
                        'message' => "成功同步 {$syncedCount} 个域名",
                        'synced_count' => $syncedCount,
                        'errors' => $errors
                    ]);
                }

            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '同步域名失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'account_info':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed', 405);
            }

            // 检查用户登录
            session_start();
            if (!isset($_SESSION['user_id'])) {
                throw new Exception('未登录', 401);
            }

            try {
                // 检查API密钥是否配置
                $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
                if (empty($apiKey) || strlen($apiKey) < 25) {
                    // API未配置，返回模拟数据
                    echo json_encode([
                        'success' => true,
                        'data' => [
                            'balance' => 125.50,
                            'currency' => 'USD',
                            'account_status' => 'Active',
                            'demo_mode' => true
                        ]
                    ]);
                } else {
                    // 使用真实API
                    $client = getNameSiloClient();
                    $balance = $client->getAccountBalance();

                    echo json_encode([
                        'success' => true,
                        'data' => [
                            'balance' => $balance['balance'] ?? 0,
                            'currency' => 'USD',
                            'account_status' => 'Active',
                            'demo_mode' => false
                        ]
                    ]);
                }

            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => '获取账户信息失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'test-connection':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            try {
                $client = getNameSiloClient();

                // 测试API连接 - 获取账户余额
                $response = $client->getAccountBalance();

                echo json_encode([
                    'success' => true,
                    'message' => 'NameSilo连接成功！账户余额: $' . number_format($response['balance'], 2),
                    'data' => $response
                ]);

            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'NameSilo连接失败: ' . $e->getMessage()
                ]);
            }
            break;

        default:
            throw new Exception("未知的API操作: {$action}", 400);
    }
    
} catch (Exception $e) {
    $code = $e->getCode() ?: 500;
    http_response_code($code);
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'code' => $code
    ]);
    
    // 记录错误日志
    logActivity('api_error', 'api', null, [
        'action' => $action,
        'error' => $e->getMessage(),
        'code' => $code
    ]);
}
