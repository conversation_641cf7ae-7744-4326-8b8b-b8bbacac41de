<?php
/**
 * 测试NameSilo API连接
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 关闭错误显示，避免破坏JSON

// 设置JSON响应头
header('Content-Type: application/json');

try {
    // 定义项目根目录
    if (!defined('ROOT_PATH')) {
        define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
    }
    
    // 引入统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database.php';
    }
    
    // 引入NameSilo API客户端
    require_once ROOT_PATH . '/public/api/NameSiloClient.php';
    
    // 获取NameSilo客户端
    function getNameSiloClient() {
        $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
        $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
        $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

        if (empty($apiKey)) {
            throw new Exception('NameSilo API密钥未配置，请先在系统设置中配置');
        }

        return new NameSiloClient($apiKey, $apiUrl, $sandbox);
    }
    
    // 测试API连接
    $client = getNameSiloClient();
    
    // 测试获取域名列表
    $domains = $client->listDomains();
    
    echo json_encode([
        'success' => true,
        'message' => 'NameSilo API连接成功',
        'domain_count' => count($domains),
        'domains' => array_slice($domains, 0, 3) // 只显示前3个域名作为示例
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
