<?php
/**
 * 邮件测试API
 * Email Test API
 */

// 关闭错误显示，避免影响JSON输出
error_reporting(0);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
define('ADMIN_PATH', dirname(__DIR__));

// 简化的数据库连接函数
function getDatabase() {
    try {
        // 加载环境变量
        $envFile = ROOT_PATH . '/.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) continue;
                if (strpos($line, '=') !== false) {
                    [$name, $value] = explode('=', $line, 2);
                    $_ENV[trim($name)] = trim($value, '"\'');
                }
            }
        }

        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $dbname = $_ENV['DB_DATABASE'] ?? $_ENV['DB_NAME'] ?? 'namesilo';
        $username = $_ENV['DB_USERNAME'] ?? $_ENV['DB_USER'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? $_ENV['DB_PASS'] ?? '';

        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        return $pdo;
    } catch (Exception $e) {
        throw new Exception('数据库连接失败: ' . $e->getMessage());
    }
}

// 引入SMTP邮件发送类
require_once ADMIN_PATH . '/includes/smtp-mailer.php';

// SMTP邮件发送函数
function sendEmailViaSMTP($to, $emailSettings, $smtpSettings) {
    try {
        $subject = '邮件配置测试 - NameSilo';
        $message = "邮件配置测试\n\n";
        $message .= "恭喜！您的SMTP邮件配置测试成功。\n";
        $message .= "这是一封来自 NameSilo 域名销售系统的测试邮件。\n";
        $message .= "发送时间: " . date('Y-m-d H:i:s') . "\n\n";
        $message .= "SMTP服务器: " . $smtpSettings['smtp_host'] . "\n";
        $message .= "端口: " . ($smtpSettings['smtp_port'] ?? 587) . "\n";
        $message .= "加密方式: " . ($smtpSettings['smtp_encryption'] ?? 'tls') . "\n\n";
        $message .= "如果您收到此邮件，说明SMTP配置完全正确！";

        // 创建SMTP邮件发送器
        $mailer = new SMTPMailer($smtpSettings);

        // 发送邮件
        $result = $mailer->sendMail(
            $emailSettings['email_from_address'],
            $to,
            $subject,
            $message,
            $emailSettings['email_from_name']
        );

        if ($result) {
            return ['success' => true, 'message' => '测试邮件发送成功！请检查您的邮箱收件箱。'];
        } else {
            return ['success' => false, 'message' => '邮件发送失败，请检查SMTP配置。'];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'SMTP发送失败: ' . $e->getMessage()];
    }
}

try {

    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => '只允许POST请求']);
        exit;
    }

    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    $testEmail = $input['email'] ?? '';

    if (empty($testEmail)) {
        echo json_encode(['success' => false, 'message' => '请提供测试邮箱地址']);
        exit;
    }

    if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => '邮箱地址格式不正确']);
        exit;
    }

    // 尝试连接数据库
    try {
        $db = getDatabase();

        // 获取邮件设置
        $emailSettings = [
            'email_from_name' => 'NameSilo测试',
            'email_from_address' => '<EMAIL>'
        ];

        // 尝试从数据库获取设置
        $stmt = $db->query("SHOW TABLES LIKE 'system_settings'");
        if ($stmt->fetch()) {
            $settings = $db->query("SELECT `key`, `value` FROM system_settings WHERE `key` IN ('email_from_name', 'email_from_address')")->fetchAll(PDO::FETCH_KEY_PAIR);
            $emailSettings = array_merge($emailSettings, $settings);
        }

    } catch (Exception $e) {
        // 数据库连接失败，使用默认设置
        $emailSettings = [
            'email_from_name' => 'NameSilo测试',
            'email_from_address' => '<EMAIL>'
        ];
    }

    // 获取SMTP配置
    $smtpSettings = [];
    try {
        if (isset($db)) {
            $smtpKeys = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption'];
            $stmt = $db->prepare("SELECT `key`, `value` FROM system_settings WHERE `key` IN ('" . implode("','", $smtpKeys) . "')");
            $stmt->execute();
            $smtpSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        }
    } catch (Exception $e) {
        // 数据库查询失败，使用空配置
    }

    // 检查是否配置了SMTP
    if (!empty($smtpSettings['smtp_host']) && !empty($smtpSettings['smtp_username'])) {
        // 使用SMTP发送
        $result = sendEmailViaSMTP($testEmail, $emailSettings, $smtpSettings);
        echo json_encode($result);
    } else {
        // 提供配置建议
        $configStatus = [];
        $configStatus[] = "当前邮件配置状态:";
        $configStatus[] = "- SMTP服务器: " . (empty($smtpSettings['smtp_host']) ? "❌ 未配置" : "✅ " . $smtpSettings['smtp_host']);
        $configStatus[] = "- SMTP用户名: " . (empty($smtpSettings['smtp_username']) ? "❌ 未配置" : "✅ 已配置");
        $configStatus[] = "- 发件人邮箱: " . (empty($emailSettings['email_from_address']) ? "❌ 未配置" : "✅ " . $emailSettings['email_from_address']);

        if (empty($smtpSettings['smtp_host']) || empty($smtpSettings['smtp_username'])) {
            echo json_encode([
                'success' => false,
                'message' => "邮件配置不完整，无法发送测试邮件。\n\n" . implode("\n", $configStatus) . "\n\n请先在邮件设置中配置SMTP服务器信息。"
            ]);
        } else {
            // 尝试使用PHP内置mail函数
            $subject = '邮件配置测试 - NameSilo';
            $message = "邮件配置测试\n\n";
            $message .= "恭喜！您的邮件配置测试成功。\n";
            $message .= "这是一封来自 NameSilo 域名销售系统的测试邮件。\n";
            $message .= "发送时间: " . date('Y-m-d H:i:s') . "\n\n";
            $message .= "如果您收到此邮件，说明邮件功能正常。";

            $headers = "From: {$emailSettings['email_from_name']} <{$emailSettings['email_from_address']}>\r\n";
            $headers .= "Reply-To: {$emailSettings['email_from_address']}\r\n";
            $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

            if (mail($testEmail, $subject, $message, $headers)) {
                echo json_encode(['success' => true, 'message' => '测试邮件发送成功！请检查您的邮箱。']);
            } else {
                echo json_encode(['success' => false, 'message' => '邮件发送失败。建议配置SMTP服务器以获得更好的邮件发送效果。']);
            }
        }
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '系统错误: ' . $e->getMessage()]);
}
?>
