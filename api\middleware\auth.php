<?php
/**
 * 认证中间件
 * Authentication Middleware
 */

class AuthMiddleware {
    
    /**
     * 处理认证
     */
    public function handle() {
        // 检查会话
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // 检查用户是否已登录
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            // 检查API Token
            $token = $this->getTokenFromRequest();
            
            if ($token) {
                $user = $this->validateToken($token);
                if ($user) {
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['role'] = $user['role'];
                    return;
                }
            }
            
            $this->unauthorized();
        }
        
        // 验证会话有效性
        if (!$this->validateSession()) {
            $this->unauthorized();
        }
    }
    
    /**
     * 从请求中获取Token
     */
    private function getTokenFromRequest() {
        // 从Authorization头获取
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $auth = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $auth, $matches)) {
                return $matches[1];
            }
        }
        
        // 从查询参数获取
        if (isset($_GET['token'])) {
            return $_GET['token'];
        }
        
        // 从POST参数获取
        if (isset($_POST['token'])) {
            return $_POST['token'];
        }
        
        return null;
    }
    
    /**
     * 验证Token
     */
    private function validateToken($token) {
        try {
            // 这里应该实现JWT或其他Token验证逻辑
            // 目前使用简单的数据库查询
            
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            // 查询用户API Token（假设有api_tokens表）
            $stmt = $db->prepare("
                SELECT u.id, u.username, u.role, u.status 
                FROM users u 
                JOIN api_tokens t ON u.id = t.user_id 
                WHERE t.token = ? AND t.expires_at > NOW() AND u.status = 'active'
            ");
            $stmt->execute([$token]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                // 更新Token最后使用时间
                $updateStmt = $db->prepare("UPDATE api_tokens SET last_used_at = NOW() WHERE token = ?");
                $updateStmt->execute([$token]);
                
                return $user;
            }
            
        } catch (Exception $e) {
            error_log('Token验证失败: ' . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * 验证会话
     */
    private function validateSession() {
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("SELECT id, status FROM users WHERE id = ? AND status = 'active'");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $user !== false;
            
        } catch (Exception $e) {
            error_log('会话验证失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 返回未授权错误
     */
    private function unauthorized() {
        http_response_code(401);
        header('Content-Type: application/json; charset=utf-8');
        
        echo json_encode([
            'success' => false,
            'code' => 401,
            'message' => '未授权访问，请先登录',
            'data' => null,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        
        exit;
    }
    
    /**
     * 获取当前用户ID
     */
    public static function getCurrentUserId() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * 获取当前用户信息
     */
    public static function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT id, username, email, role, first_name, last_name, status, created_at 
                FROM users 
                WHERE id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log('获取当前用户失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 检查用户权限
     */
    public static function hasPermission($permission) {
        $user = self::getCurrentUser();
        if (!$user) {
            return false;
        }
        
        // 管理员拥有所有权限
        if ($user['role'] === 'admin') {
            return true;
        }
        
        // 这里可以实现更复杂的权限检查逻辑
        // 例如基于角色的权限控制(RBAC)
        
        return false;
    }
    
    /**
     * 生成API Token
     */
    public static function generateApiToken($userId, $expiresIn = 86400) {
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            // 生成随机Token
            $token = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', time() + $expiresIn);
            
            // 创建api_tokens表（如果不存在）
            $db->exec("
                CREATE TABLE IF NOT EXISTS api_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    token VARCHAR(255) NOT NULL UNIQUE,
                    name VARCHAR(100) DEFAULT 'API Token',
                    expires_at DATETIME NOT NULL,
                    last_used_at DATETIME NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_token (token),
                    INDEX idx_user_id (user_id),
                    INDEX idx_expires_at (expires_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // 插入Token
            $stmt = $db->prepare("
                INSERT INTO api_tokens (user_id, token, expires_at) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$userId, $token, $expiresAt]);
            
            return [
                'token' => $token,
                'expires_at' => $expiresAt,
                'expires_in' => $expiresIn
            ];
            
        } catch (Exception $e) {
            error_log('生成API Token失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 撤销API Token
     */
    public static function revokeApiToken($token) {
        try {
            require_once __DIR__ . '/../../app/Utils/Database.php';
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("DELETE FROM api_tokens WHERE token = ?");
            $stmt->execute([$token]);
            
            return $stmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log('撤销API Token失败: ' . $e->getMessage());
            return false;
        }
    }
}
?>
