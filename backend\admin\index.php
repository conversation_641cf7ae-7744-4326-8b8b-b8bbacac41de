<?php
/**
 * NameSilo域名销售系统 - 管理后台
 * NameSilo Domain Sales System - Admin Panel
 */

session_start();

// 设置错误报告（生产环境应关闭）
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('ADMIN_PATH', __DIR__);

// 引入通用函数
require_once ADMIN_PATH . '/includes/functions.php';

// 简单的自动加载器
spl_autoload_register(function ($class) {
    $file = ROOT_PATH . '/src/' . str_replace('\\', '/', str_replace('App\\', '', $class)) . '.php';
    if (file_exists($file)) {
        require $file;
    }
});

// 加载环境变量
loadEnv(ROOT_PATH . '/.env');

// 数据库连接（向后兼容）
function getDatabase() {
    return safeGetDatabase();
}

// 检查管理员登录
function checkAdminAuth() {
    if (!isset($_SESSION['admin_id'])) {
        header('Location: login.php');
        exit;
    }
}

// 获取当前页面
$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? 'index';

// 路由处理
switch ($page) {
    case 'login':
        include 'views/auth/login.php';
        break;

    case 'logout':
        session_destroy();
        header('Location: login.php');
        exit;

    default:
        // 所有需要认证的页面都使用统一布局
        checkAdminAuth();

        // 设置页面标题和内容
        $title = '管理后台';
        $contentFile = '';

        switch ($page) {
            case 'dashboard':
                $title = '仪表盘';
                $contentFile = 'views/dashboard/content.php';
                break;

            case 'users':
                $title = '用户管理';
                $contentFile = 'views/users/content.php';
                break;

            case 'domains':
                $action = $_GET['action'] ?? 'list';
                switch ($action) {
                    case 'manage':
                        $title = '域名管理';
                        $contentFile = 'views/domains/manage.php';
                        break;
                    case 'renew':
                        $title = '域名续费';
                        $contentFile = 'views/domains/renew.php';
                        break;
                    case 'transfer':
                        $title = '域名转移';
                        $contentFile = 'views/domains/transfer.php';
                        break;
                    case 'dns':
                        $title = 'DNS设置';
                        $contentFile = 'views/domains/dns.php';
                        break;
                    case 'pricing':
                        $title = '域名价格管理';
                        $contentFile = 'views/domains/pricing-content.php';
                        break;
                    case 'pricing-import':
                        $title = 'TLD数据导入';
                        $contentFile = 'views/domains/pricing-import-content.php';
                        break;
                    case 'categories':
                        $title = '域名分类管理';
                        $contentFile = 'views/domains/categories-content.php';
                        break;
                    case 'test':
                        $title = '测试页面';
                        $contentFile = 'views/domains/test.php';
                        break;
                    default:
                        $title = '域名管理';
                        $contentFile = 'views/domains/content.php';
                        break;
                }
                break;

            case 'orders':
                $title = '订单管理';
                $contentFile = 'views/orders/content.php';
                break;

            case 'settings':
                $title = '系统设置';
                $contentFile = 'views/settings/content.php';
                break;

            case 'logs':
                $title = '系统日志';
                $contentFile = 'views/logs/content.php';
                break;

            case 'payments':
                $title = '支付配置';
                $contentFile = 'views/payments/content.php';
                break;

            case 'payment-records':
                $title = '支付记录';
                $contentFile = 'views/payment-records/content.php';
                break;

            case 'database-status':
                $title = '数据库状态';
                $contentFile = 'views/database/status-content.php';
                break;

            case 'billing':
                $title = '账单管理';
                $contentFile = 'views/billing/content.php';
                break;

            case 'security':
                $title = '安全管理';
                $contentFile = 'views/security/content.php';
                break;

            case 'notifications':
                $title = '通知管理';
                $contentFile = 'views/notifications/content.php';
                break;

            case 'support':
                $title = '支持中心管理';
                $contentFile = 'views/support/content.php';
                break;

            case 'email-config':
                $title = '邮件配置';
                $contentFile = 'views/email/config.php';
                break;

            case 'email-templates':
                $title = '邮件模板';
                $contentFile = 'views/email/templates.php';
                break;

            case 'email-send':
                $title = '邮件发送';
                $contentFile = 'views/email/send.php';
                break;

            case 'email-logs':
                $title = '邮件日志';
                $contentFile = 'views/email/logs.php';
                break;

            case 'email-test':
                $title = '邮件测试';
                $contentFile = 'views/email/test.php';
                break;

            default:
                $title = '仪表盘';
                $contentFile = 'views/dashboard/content.php';
                break;
        }

        // 使用统一布局
        include 'views/layouts/admin.php';
        break;
}
