<?php
/**
 * 邮件配置页面
 * Email Configuration Page
 */

// 初始化变量
$success = '';
$error = '';

// 获取数据库连接
try {
    $db = getDatabase();
} catch (Exception $e) {
    $error = '数据库连接失败: ' . $e->getMessage();
    $db = null;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $db) {
    try {
        switch ($_POST['action']) {
            case 'save_smtp':
                // 保存SMTP配置
                $smtpSettings = [
                    'smtp_host' => $_POST['smtp_host'] ?? '',
                    'smtp_port' => $_POST['smtp_port'] ?? '587',
                    'smtp_username' => $_POST['smtp_username'] ?? '',
                    'smtp_password' => $_POST['smtp_password'] ?? '',
                    'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                    'from_email' => $_POST['from_email'] ?? '',
                    'from_name' => $_POST['from_name'] ?? 'NameSilo域名销售系统'
                ];

                foreach ($smtpSettings as $key => $value) {
                    $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`) VALUES (?, ?, 'string') ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)");
                    $stmt->execute([$key, $value]);
                }

                $success = 'SMTP配置已保存';
                break;

            case 'test_smtp':
                // 测试SMTP连接
                $testEmail = $_POST['test_email'] ?? '';
                if (empty($testEmail)) {
                    throw new Exception('请输入测试邮箱地址');
                }

                // 这里调用测试邮件发送功能
                require_once __DIR__ . '/../../includes/smtp-mailer.php';
                $result = testSMTPConnection($testEmail);

                if ($result['success']) {
                    $success = '测试邮件发送成功！';
                } else {
                    throw new Exception('测试邮件发送失败: ' . $result['message']);
                }
                break;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取当前SMTP设置
function getEmailSetting($key, $default = '') {
    global $db;
    if (!$db) return $default;

    try {
        $stmt = $db->prepare("SELECT `value` FROM system_settings WHERE `key` = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : $default;
    } catch (Exception $e) {
        return $default;
    }
}

$smtpSettings = [
    'smtp_host' => getEmailSetting('smtp_host'),
    'smtp_port' => getEmailSetting('smtp_port', '587'),
    'smtp_username' => getEmailSetting('smtp_username'),
    'smtp_password' => getEmailSetting('smtp_password'),
    'smtp_encryption' => getEmailSetting('smtp_encryption', 'tls'),
    'from_email' => getEmailSetting('from_email'),
    'from_name' => getEmailSetting('from_name', 'NameSilo域名销售系统')
];
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">邮件配置</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="#">邮件管理</a></li>
                    <li class="breadcrumb-item active">邮件配置</li>
                </ol>
            </div>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>SMTP服务器配置
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="smtpForm">
                        <input type="hidden" name="action" value="save_smtp">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_host" class="form-label">SMTP服务器</label>
                                    <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                           value="<?= htmlspecialchars($smtpSettings['smtp_host']) ?>" 
                                           placeholder="smtp.qq.com" required>
                                    <div class="form-text">邮件服务商的SMTP服务器地址</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_port" class="form-label">SMTP端口</label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                           value="<?= htmlspecialchars($smtpSettings['smtp_port']) ?>" 
                                           placeholder="587" required>
                                    <div class="form-text">通常为587(TLS)或465(SSL)</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_username" class="form-label">SMTP用户名</label>
                                    <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                                           value="<?= htmlspecialchars($smtpSettings['smtp_username']) ?>" 
                                           placeholder="<EMAIL>" required>
                                    <div class="form-text">发送邮件的邮箱地址</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_password" class="form-label">SMTP密码</label>
                                    <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                           value="<?= htmlspecialchars($smtpSettings['smtp_password']) ?>" 
                                           placeholder="邮箱密码或授权码">
                                    <div class="form-text">邮箱密码或第三方客户端授权码</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="smtp_encryption" class="form-label">加密方式</label>
                                    <select class="form-select" id="smtp_encryption" name="smtp_encryption" required>
                                        <option value="tls" <?= $smtpSettings['smtp_encryption'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                                        <option value="ssl" <?= $smtpSettings['smtp_encryption'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                        <option value="none" <?= $smtpSettings['smtp_encryption'] === 'none' ? 'selected' : '' ?>>无加密</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="from_name" class="form-label">发件人名称</label>
                                    <input type="text" class="form-control" id="from_name" name="from_name" 
                                           value="<?= htmlspecialchars($smtpSettings['from_name']) ?>" 
                                           placeholder="NameSilo域名销售系统" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="from_email" class="form-label">发件人邮箱</label>
                            <input type="email" class="form-control" id="from_email" name="from_email" 
                                   value="<?= htmlspecialchars($smtpSettings['from_email']) ?>" 
                                   placeholder="<EMAIL>" required>
                            <div class="form-text">显示给收件人的发件人邮箱地址</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存配置
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="showTestModal()">
                                <i class="fas fa-paper-plane me-1"></i>测试邮件发送
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>配置说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i>常用邮件服务商配置</h6>
                        <hr>
                        <strong>QQ邮箱：</strong><br>
                        SMTP: smtp.qq.com<br>
                        端口: 587 (TLS)<br>
                        <small class="text-muted">需要开启SMTP服务并使用授权码</small>
                        <hr>
                        <strong>163邮箱：</strong><br>
                        SMTP: smtp.163.com<br>
                        端口: 587 (TLS)<br>
                        <hr>
                        <strong>Gmail：</strong><br>
                        SMTP: smtp.gmail.com<br>
                        端口: 587 (TLS)<br>
                        <small class="text-muted">需要开启两步验证并使用应用专用密码</small>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>注意事项</h6>
                        <ul class="mb-0 small">
                            <li>QQ邮箱需要在设置中开启SMTP服务</li>
                            <li>密码应使用授权码而非登录密码</li>
                            <li>建议使用TLS加密方式</li>
                            <li>配置完成后请进行测试</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 测试邮件模态框 -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">测试邮件发送</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="testEmailForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="test_smtp">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">测试邮箱地址</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" 
                               placeholder="<EMAIL>" required>
                        <div class="form-text">将向此邮箱发送测试邮件</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>发送测试邮件
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showTestModal() {
    // 先保存当前配置
    const form = document.getElementById('smtpForm');
    const formData = new FormData(form);
    
    fetch('', {
        method: 'POST',
        body: formData
    }).then(() => {
        // 显示测试模态框
        const modal = new bootstrap.Modal(document.getElementById('testEmailModal'));
        modal.show();
    });
}

// 表单验证
document.getElementById('smtpForm').addEventListener('submit', function(e) {
    const password = document.getElementById('smtp_password').value;
    if (!password) {
        e.preventDefault();
        alert('请输入SMTP密码');
        return false;
    }
});
</script>
