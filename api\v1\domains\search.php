<?php
/**
 * 域名搜索API
 * Domain Search API
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 直接使用硬编码数据库连接
function getDatabase() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=localhost;port=3306;dbname=www_bt_cn;charset=utf8mb4";
            $pdo = new PDO($dsn, 'www_bt_cn', 'YAfxfrB8nr6F84LP', [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    return $pdo;
}

// API响应函数
function sendResponse($success, $code, $message, $data = null) {
    http_response_code($code);
    
    $response = [
        'success' => $success,
        'code' => $code,
        'message' => $message,
        'timestamp' => date('c')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = '操作成功', $code = 200) {
    sendResponse(true, $code, $message, $data);
}

function sendError($message = '操作失败', $code = 400, $data = null) {
    sendResponse(false, $code, $message, $data);
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // 获取搜索参数
        $query = trim($_GET['q'] ?? '');
        $tlds = $_GET['tlds'] ?? ['com', 'net', 'org', 'cn'];
        
        if (empty($query)) {
            sendError('请输入要搜索的域名', 400);
        }
        
        // 验证域名格式
        if (!preg_match('/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$/', $query)) {
            sendError('域名格式不正确', 400);
        }
        
        // 如果tlds是字符串，转换为数组
        if (is_string($tlds)) {
            $tlds = explode(',', $tlds);
        }
        
        $results = [];
        $db = getDatabase();
        
        foreach ($tlds as $tld) {
            $tld = trim($tld);
            $domainName = $query . '.' . $tld;
            
            // 检查域名是否已存在于数据库中
            $stmt = $db->prepare("SELECT id, status, price, renewal_price FROM domains WHERE domain_name = ?");
            $stmt->execute([$domainName]);
            $existingDomain = $stmt->fetch();
            
            if ($existingDomain) {
                // 域名已存在
                $results[] = [
                    'domain' => $domainName,
                    'tld' => $tld,
                    'available' => false,
                    'status' => $existingDomain['status'],
                    'price' => $existingDomain['price'] ? number_format($existingDomain['price'], 2) : null,
                    'renewal_price' => $existingDomain['renewal_price'] ? number_format($existingDomain['renewal_price'], 2) : null,
                    'currency' => 'USD',
                    'message' => '域名已被注册'
                ];
            } else {
                // 域名可用，获取价格信息
                $price = getDomainPrice($tld);
                
                $results[] = [
                    'domain' => $domainName,
                    'tld' => $tld,
                    'available' => true,
                    'status' => 'available',
                    'price' => $price['registration'],
                    'renewal_price' => $price['renewal'],
                    'currency' => 'USD',
                    'message' => '域名可注册'
                ];
            }
        }
        
        // 返回搜索结果
        sendSuccess([
            'query' => $query,
            'results' => $results,
            'total' => count($results),
            'available_count' => count(array_filter($results, function($r) { return $r['available']; }))
        ], '域名搜索完成');
        
    } else {
        sendError('不支持的请求方法', 405);
    }
    
} catch (Exception $e) {
    error_log('域名搜索API错误: ' . $e->getMessage());
    sendError('搜索失败: ' . $e->getMessage(), 500);
}

/**
 * 获取域名价格
 */
function getDomainPrice($tld) {
    // 默认价格表（实际应该从数据库或配置文件获取）
    $prices = [
        'com' => ['registration' => '12.99', 'renewal' => '14.99'],
        'net' => ['registration' => '14.99', 'renewal' => '16.99'],
        'org' => ['registration' => '13.99', 'renewal' => '15.99'],
        'cn' => ['registration' => '9.99', 'renewal' => '11.99'],
        'info' => ['registration' => '11.99', 'renewal' => '13.99'],
        'biz' => ['registration' => '15.99', 'renewal' => '17.99'],
        'me' => ['registration' => '19.99', 'renewal' => '21.99'],
        'co' => ['registration' => '29.99', 'renewal' => '31.99']
    ];
    
    return $prices[$tld] ?? ['registration' => '19.99', 'renewal' => '21.99'];
}

/**
 * 验证域名格式
 */
function validateDomain($domain) {
    // 基本格式验证
    if (strlen($domain) < 3 || strlen($domain) > 253) {
        return false;
    }
    
    // 检查是否包含有效字符
    if (!preg_match('/^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]$/', $domain)) {
        return false;
    }
    
    // 检查是否以点开头或结尾
    if (substr($domain, 0, 1) === '.' || substr($domain, -1) === '.') {
        return false;
    }
    
    // 检查是否包含连续的点
    if (strpos($domain, '..') !== false) {
        return false;
    }
    
    return true;
}

/**
 * 获取支持的TLD列表
 */
function getSupportedTlds() {
    return [
        'com', 'net', 'org', 'cn', 'info', 'biz', 'me', 'co',
        'io', 'ai', 'app', 'dev', 'tech', 'online', 'store',
        'shop', 'blog', 'news', 'site', 'website'
    ];
}
?>
