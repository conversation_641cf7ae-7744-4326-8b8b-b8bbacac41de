<?php
/**
 * 账单管理内容页面
 * Billing Management Content Page
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// 添加错误处理
set_error_handler(function($severity, $message, $file, $line) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>PHP错误:</h4>";
    echo "<p><strong>错误信息:</strong> $message</p>";
    echo "<p><strong>文件:</strong> $file</p>";
    echo "<p><strong>行号:</strong> $line</p>";
    echo "</div>";
});

try {
    // 获取数据库连接
    $db = getDatabase();

    if (!$db) {
        throw new Exception("数据库连接失败");
    }

    echo "<div class='alert alert-success'>数据库连接成功</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>异常错误:</h4>";
    echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>文件:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>堆栈跟踪:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
    exit;
}

// 处理操作
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($_POST['action']) {
            case 'update_transaction':
                $transactionId = $_POST['transaction_id'];
                $status = $_POST['status'];
                $description = $_POST['description'] ?? '';
                
                $stmt = $db->prepare("UPDATE billing_transactions SET status = ?, description = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$status, $description, $transactionId]);
                
                $message = '交易记录更新成功！';
                break;
                
            case 'add_transaction':
                $userId = $_POST['user_id'];
                $type = $_POST['type'];
                $amount = $_POST['amount'];
                $description = $_POST['description'];
                $orderNumber = $_POST['order_number'] ?? '';
                $paymentMethod = $_POST['payment_method'] ?? '';
                
                $stmt = $db->prepare("
                    INSERT INTO billing_transactions (user_id, type, amount, description, order_number, payment_method, status) 
                    VALUES (?, ?, ?, ?, ?, ?, 'completed')
                ");
                $stmt->execute([$userId, $type, $amount, $description, $orderNumber, $paymentMethod]);
                
                $message = '交易记录添加成功！';
                break;
        }
    } catch (Exception $e) {
        $error = '操作失败：' . $e->getMessage();
    }
}

// 获取统计数据
try {
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_transactions,
            SUM(CASE WHEN type = 'recharge' AND status = 'completed' THEN amount ELSE 0 END) as total_recharge,
            SUM(CASE WHEN type = 'payment' AND status = 'completed' THEN amount ELSE 0 END) as total_payment,
            SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_transactions
        FROM billing_transactions
    ");
    $stats = $stmt->fetch();
} catch (Exception $e) {
    $stats = [
        'total_transactions' => 0,
        'total_recharge' => 0,
        'total_payment' => 0,
        'pending_amount' => 0,
        'today_transactions' => 0
    ];
}

// 获取交易记录
$currentPage = (int)($_GET['p'] ?? 1); // 使用 'p' 参数避免与路由冲突
$limit = 20;
$offset = ($currentPage - 1) * $limit;

$whereClause = '';
$params = [];

// 搜索过滤
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $whereClause = " WHERE (u.username LIKE ? OR u.email LIKE ? OR bt.order_number LIKE ?)";
    $params = [$search, $search, $search];
}

if (!empty($_GET['status'])) {
    $statusFilter = $_GET['status'];
    $whereClause .= $whereClause ? " AND bt.status = ?" : " WHERE bt.status = ?";
    $params[] = $statusFilter;
}

if (!empty($_GET['type'])) {
    $typeFilter = $_GET['type'];
    $whereClause .= $whereClause ? " AND bt.type = ?" : " WHERE bt.type = ?";
    $params[] = $typeFilter;
}

try {
    $stmt = $db->prepare("
        SELECT 
            bt.*,
            u.username,
            u.email
        FROM billing_transactions bt
        LEFT JOIN users u ON bt.user_id = u.id
        {$whereClause}
        ORDER BY bt.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $transactions = $stmt->fetchAll();
    
    // 获取总数
    $countParams = array_slice($params, 0, -2);
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM billing_transactions bt
        LEFT JOIN users u ON bt.user_id = u.id
        {$whereClause}
    ");
    $stmt->execute($countParams);
    $totalTransactions = $stmt->fetchColumn();
    $totalPages = ceil($totalTransactions / $limit);
    
} catch (Exception $e) {
    $transactions = [];
    $totalPages = 1;
}

// 获取用户列表（用于添加交易）
try {
    $stmt = $db->query("SELECT id, username, email FROM users ORDER BY username");
    $users = $stmt->fetchAll();
} catch (Exception $e) {
    $users = [];
}
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">账单管理</h1>
            <p class="text-muted">管理用户交易记录和账单信息</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                <i class="fas fa-plus me-2"></i>添加交易记录
            </button>
        </div>
    </div>

    <!-- 消息提示 -->
    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 统计卡片 -->
    <div class="row g-4 mb-4">
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($stats['total_transactions']) ?></h5>
                    <p class="card-text text-muted">总交易数</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-arrow-up fa-2x"></i>
                    </div>
                    <h5 class="card-title">¥<?= number_format($stats['total_recharge'], 2) ?></h5>
                    <p class="card-text text-muted">总充值</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-arrow-down fa-2x"></i>
                    </div>
                    <h5 class="card-title">¥<?= number_format($stats['total_payment'], 2) ?></h5>
                    <p class="card-text text-muted">总支出</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h5 class="card-title">¥<?= number_format($stats['pending_amount'], 2) ?></h5>
                    <p class="card-text text-muted">待处理金额</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($stats['today_transactions']) ?></h5>
                    <p class="card-text text-muted">今日交易</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">搜索</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="用户名、邮箱或订单号">
                </div>
                <div class="col-md-2">
                    <label class="form-label">状态</label>
                    <select class="form-select" name="status">
                        <option value="">全部状态</option>
                        <option value="pending" <?= ($_GET['status'] ?? '') === 'pending' ? 'selected' : '' ?>>待处理</option>
                        <option value="completed" <?= ($_GET['status'] ?? '') === 'completed' ? 'selected' : '' ?>>已完成</option>
                        <option value="failed" <?= ($_GET['status'] ?? '') === 'failed' ? 'selected' : '' ?>>失败</option>
                        <option value="cancelled" <?= ($_GET['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>已取消</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">类型</label>
                    <select class="form-select" name="type">
                        <option value="">全部类型</option>
                        <option value="recharge" <?= ($_GET['type'] ?? '') === 'recharge' ? 'selected' : '' ?>>充值</option>
                        <option value="payment" <?= ($_GET['type'] ?? '') === 'payment' ? 'selected' : '' ?>>支付</option>
                        <option value="refund" <?= ($_GET['type'] ?? '') === 'refund' ? 'selected' : '' ?>>退款</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <a href="?" class="btn btn-outline-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 交易记录表格 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">交易记录</h5>
        </div>
        <div class="card-body">
            <?php if (empty($transactions)): ?>
            <div class="text-center py-4">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无交易记录</p>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户</th>
                            <th>类型</th>
                            <th>金额</th>
                            <th>状态</th>
                            <th>订单号</th>
                            <th>支付方式</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($transactions as $transaction): ?>
                        <tr>
                            <td><?= $transaction['id'] ?></td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($transaction['username']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars($transaction['email']) ?></small>
                                </div>
                            </td>
                            <td>
                                <?php
                                $typeClass = match($transaction['type']) {
                                    'recharge' => 'success',
                                    'payment' => 'primary',
                                    'refund' => 'warning',
                                    default => 'secondary'
                                };
                                $typeText = match($transaction['type']) {
                                    'recharge' => '充值',
                                    'payment' => '支付',
                                    'refund' => '退款',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-<?= $typeClass ?>"><?= $typeText ?></span>
                            </td>
                            <td>
                                <span class="fw-bold <?= $transaction['type'] === 'recharge' ? 'text-success' : 'text-danger' ?>">
                                    <?= $transaction['type'] === 'recharge' ? '+' : '-' ?>¥<?= number_format($transaction['amount'], 2) ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $statusClass = match($transaction['status']) {
                                    'completed' => 'success',
                                    'pending' => 'warning',
                                    'failed' => 'danger',
                                    'cancelled' => 'secondary',
                                    default => 'secondary'
                                };
                                $statusText = match($transaction['status']) {
                                    'completed' => '已完成',
                                    'pending' => '待处理',
                                    'failed' => '失败',
                                    'cancelled' => '已取消',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                            </td>
                            <td><?= htmlspecialchars($transaction['order_number']) ?></td>
                            <td><?= htmlspecialchars($transaction['payment_method']) ?></td>
                            <td><?= date('Y-m-d H:i:s', strtotime($transaction['created_at'])) ?></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary"
                                        data-bs-toggle="modal"
                                        data-bs-target="#editTransactionModal"
                                        data-transaction-id="<?= $transaction['id'] ?>"
                                        data-transaction-status="<?= $transaction['status'] ?>"
                                        data-transaction-description="<?= htmlspecialchars($transaction['description']) ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="交易记录分页">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                        <a class="page-link" href="?page=billing&p=<?= $i ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?><?= !empty($_GET['status']) ? '&status=' . urlencode($_GET['status']) : '' ?><?= !empty($_GET['type']) ? '&type=' . urlencode($_GET['type']) : '' ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 添加交易记录模态框 -->
<div class="modal fade" id="addTransactionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加交易记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_transaction">
                    
                    <div class="mb-3">
                        <label class="form-label">用户</label>
                        <select class="form-select" name="user_id" required>
                            <option value="">选择用户</option>
                            <?php foreach ($users as $user): ?>
                            <option value="<?= $user['id'] ?>"><?= htmlspecialchars($user['username']) ?> (<?= htmlspecialchars($user['email']) ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">类型</label>
                        <select class="form-select" name="type" required>
                            <option value="recharge">充值</option>
                            <option value="payment">支付</option>
                            <option value="refund">退款</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">金额</label>
                        <input type="number" class="form-control" name="amount" step="0.01" min="0" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">订单号</label>
                        <input type="text" class="form-control" name="order_number">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">支付方式</label>
                        <select class="form-select" name="payment_method">
                            <option value="">选择支付方式</option>
                            <option value="alipay">支付宝</option>
                            <option value="wechat">微信支付</option>
                            <option value="bank_transfer">银行转账</option>
                            <option value="paypal">PayPal</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑交易记录模态框 -->
<div class="modal fade" id="editTransactionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑交易记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_transaction">
                    <input type="hidden" name="transaction_id" id="edit_transaction_id">
                    
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status" id="edit_status" required>
                            <option value="pending">待处理</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">更新</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 编辑交易模态框
    const editTransactionModal = document.getElementById('editTransactionModal');
    if (editTransactionModal) {
        editTransactionModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const transactionId = button.getAttribute('data-transaction-id');
            const transactionStatus = button.getAttribute('data-transaction-status');
            const transactionDescription = button.getAttribute('data-transaction-description');

            document.getElementById('edit_transaction_id').value = transactionId;

            // 设置当前状态
            const statusSelect = document.querySelector('#editTransactionModal select[name="status"]');
            if (statusSelect) {
                statusSelect.value = transactionStatus;
            }

            // 设置描述
            const descriptionTextarea = document.getElementById('edit_description');
            if (descriptionTextarea) {
                descriptionTextarea.value = transactionDescription;
            }
        });
    }
});
</script>
