<?php
/**
 * DNS管理页面 - 使用真实API数据
 * DNS Management Page - Using Real API Data
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = 'DNS管理';
$domain = $_GET['domain'] ?? '';

if (empty($domain)) {
    header('Location: domains.php');
    exit;
}

// 验证域名所有权并获取域名信息
$domainInfo = null;
try {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT id, domain_name, registrar, status FROM domains WHERE domain_name = ? AND user_id = ?");
    $stmt->execute([$domain, $userId]);
    $domainInfo = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("验证域名所有权失败: " . $e->getMessage());
}

if (!$domainInfo) {
    header('Location: domains.php?error=no_permission');
    exit;
}

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">DNS管理</h1>
            <p class="page-subtitle">管理 <strong><?= htmlspecialchars($domain) ?></strong> 的DNS记录</p>
        </div>
        <div>
            <a href="domains.php" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>
                返回域名列表
            </a>
            <button class="btn btn-primary" onclick="showAddDNSRecord()">
                <i class="fas fa-plus me-2"></i>
                添加记录
            </button>
        </div>
    </div>
</div>

<!-- DNS记录统计 -->
<div class="row g-4 mb-4">
    <div class="col-md-2" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card text-center">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary mx-auto">
                <i class="fas fa-list"></i>
            </div>
            <h4 class="h6 mb-1">总记录</h4>
            <p class="fw-bold mb-0" id="totalRecords">-</p>
        </div>
    </div>
    
    <div class="col-md-2" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card text-center">
            <div class="stats-icon bg-success bg-opacity-10 text-success mx-auto">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <h4 class="h6 mb-1">A记录</h4>
            <p class="fw-bold mb-0" id="aRecords">-</p>
        </div>
    </div>
    
    <div class="col-md-2" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card text-center">
            <div class="stats-icon bg-info bg-opacity-10 text-info mx-auto">
                <i class="fas fa-link"></i>
            </div>
            <h4 class="h6 mb-1">CNAME</h4>
            <p class="fw-bold mb-0" id="cnameRecords">-</p>
        </div>
    </div>
    
    <div class="col-md-2" data-aos="fade-up" data-aos-delay="400">
        <div class="stats-card text-center">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning mx-auto">
                <i class="fas fa-envelope"></i>
            </div>
            <h4 class="h6 mb-1">MX记录</h4>
            <p class="fw-bold mb-0" id="mxRecords">-</p>
        </div>
    </div>
    
    <div class="col-md-2" data-aos="fade-up" data-aos-delay="500">
        <div class="stats-card text-center">
            <div class="stats-icon bg-secondary bg-opacity-10 text-secondary mx-auto">
                <i class="fas fa-file-alt"></i>
            </div>
            <h4 class="h6 mb-1">TXT记录</h4>
            <p class="fw-bold mb-0" id="txtRecords">-</p>
        </div>
    </div>
    
    <div class="col-md-2" data-aos="fade-up" data-aos-delay="600">
        <div class="stats-card text-center">
            <div class="stats-icon bg-dark bg-opacity-10 text-dark mx-auto">
                <i class="fas fa-server"></i>
            </div>
            <h4 class="h6 mb-1">其他</h4>
            <p class="fw-bold mb-0" id="otherRecords">-</p>
        </div>
    </div>
</div>

<!-- DNS记录列表 -->
<div class="row">
    <div class="col-12" data-aos="fade-up" data-aos-delay="700">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-dns me-2"></i>
                    DNS记录
                </h5>
                <div class="d-flex gap-2">
                    <div class="input-group" style="width: 300px;">
                        <input type="text" class="form-control" placeholder="搜索记录..." id="recordSearch">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>类型
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-filter="all">全部类型</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="A">A记录</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="AAAA">AAAA记录</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="CNAME">CNAME记录</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="MX">MX记录</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="TXT">TXT记录</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-outline-info" onclick="refreshDNSRecords()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="dnsRecordsContainer">
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-3 mb-0">正在加载DNS记录...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加DNS记录模态框 -->
<div class="modal fade" id="addDNSModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加DNS记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addDNSForm">
                <div class="modal-body">
                    <input type="hidden" name="domain" value="<?= htmlspecialchars($domain) ?>">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">记录类型 <span class="text-danger">*</span></label>
                            <select class="form-select" name="type" required onchange="handleTypeChange(this.value)">
                                <option value="">请选择记录类型</option>
                                <option value="A">A - IPv4地址</option>
                                <option value="AAAA">AAAA - IPv6地址</option>
                                <option value="CNAME">CNAME - 别名记录</option>
                                <option value="MX">MX - 邮件交换记录</option>
                                <option value="TXT">TXT - 文本记录</option>
                                <option value="NS">NS - 名称服务器</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">记录名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" placeholder="@" required>
                            <small class="text-muted">使用 @ 表示根域名，www 表示 www.<?= htmlspecialchars($domain) ?></small>
                        </div>
                        <div class="col-12">
                            <label class="form-label">记录值 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="value" required>
                            <small class="text-muted" id="valueHint">请输入记录值</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">TTL (生存时间)</label>
                            <select class="form-select" name="ttl">
                                <option value="300">5分钟 (300秒)</option>
                                <option value="1800">30分钟 (1800秒)</option>
                                <option value="3600" selected>1小时 (3600秒)</option>
                                <option value="14400">4小时 (14400秒)</option>
                                <option value="43200">12小时 (43200秒)</option>
                                <option value="86400">1天 (86400秒)</option>
                            </select>
                        </div>
                        <div class="col-md-6" id="priorityField" style="display: none;">
                            <label class="form-label">优先级</label>
                            <input type="number" class="form-control" name="priority" min="0" max="65535" placeholder="10">
                            <small class="text-muted">数值越小优先级越高</small>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>
                                DNS记录说明
                            </h6>
                            <ul class="mb-0 small">
                                <li><strong>A记录</strong>：将域名指向IPv4地址</li>
                                <li><strong>AAAA记录</strong>：将域名指向IPv6地址</li>
                                <li><strong>CNAME记录</strong>：将域名指向另一个域名</li>
                                <li><strong>MX记录</strong>：指定邮件服务器</li>
                                <li><strong>TXT记录</strong>：存储文本信息，常用于验证</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        添加记录
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑DNS记录模态框 -->
<div class="modal fade" id="editDNSModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    编辑DNS记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editDNSForm">
                <div class="modal-body">
                    <input type="hidden" name="id" id="editRecordId">
                    <input type="hidden" name="domain" value="<?= htmlspecialchars($domain) ?>">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">记录类型</label>
                            <input type="text" class="form-control" id="editRecordType" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">记录名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" id="editRecordName" required>
                        </div>
                        <div class="col-12">
                            <label class="form-label">记录值 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="value" id="editRecordValue" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">TTL (生存时间)</label>
                            <select class="form-select" name="ttl" id="editRecordTTL">
                                <option value="300">5分钟 (300秒)</option>
                                <option value="1800">30分钟 (1800秒)</option>
                                <option value="3600">1小时 (3600秒)</option>
                                <option value="14400">4小时 (14400秒)</option>
                                <option value="43200">12小时 (43200秒)</option>
                                <option value="86400">1天 (86400秒)</option>
                            </select>
                        </div>
                        <div class="col-md-6" id="editPriorityField" style="display: none;">
                            <label class="form-label">优先级</label>
                            <input type="number" class="form-control" name="priority" id="editRecordPriority" min="0" max="65535">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
const domainName = '<?= htmlspecialchars($domain) ?>';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadDNSRecords();
    
    // 绑定表单提交事件
    document.getElementById('addDNSForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitDNSRecord(this);
    });
    
    document.getElementById('editDNSForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateDNSRecord(this);
    });
    
    // 绑定搜索功能
    document.getElementById('recordSearch').addEventListener('input', function() {
        filterRecords();
    });
});

// 加载DNS记录
function loadDNSRecords() {
    const container = document.getElementById('dnsRecordsContainer');
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3 mb-0">正在从域名商API加载DNS记录...</p>
        </div>
    `;

    fetch(`api/dns.php?domain=${encodeURIComponent(domainName)}&action=list`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDNSRecords(data.records);
                updateStatistics(data.records);
                showRegistrarInfo(data.registrar);
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载DNS记录失败：${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('DNS API Error:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    网络错误，无法连接到域名商API。请检查网络连接或稍后重试。
                </div>
            `;
        });
}

// 显示域名商信息
function showRegistrarInfo(registrar) {
    const registrarName = registrar === 'namesilo' ? 'NameSilo' : registrar.toUpperCase();
    const pageSubtitle = document.querySelector('.page-subtitle');
    if (pageSubtitle) {
        pageSubtitle.innerHTML = `管理 <strong>${domainName}</strong> 的DNS记录 <span class="badge bg-info ms-2">${registrarName}</span>`;
    }
}

// 显示DNS记录
function displayDNSRecords(records) {
    if (records.length === 0) {
        document.getElementById('dnsRecordsContainer').innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-dns fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">暂无DNS记录</h4>
                <p class="text-muted mb-4">还没有配置任何DNS记录，立即添加第一条记录吧！</p>
                <button class="btn btn-primary btn-lg" onclick="showAddDNSRecord()">
                    <i class="fas fa-plus me-2"></i>
                    添加DNS记录
                </button>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>类型</th>
                        <th>名称</th>
                        <th>值</th>
                        <th>TTL</th>
                        <th>优先级</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    records.forEach(record => {
        const typeClass = getRecordTypeClass(record.type);
        const fullName = record.name === '@' ? domainName : `${record.name}.${domainName}`;
        
        html += `
            <tr data-type="${record.type}" data-name="${record.name}" data-value="${record.value}">
                <td>
                    <span class="badge ${typeClass}">${record.type}</span>
                </td>
                <td>
                    <div>
                        <code class="text-primary">${record.name}</code>
                        <br>
                        <small class="text-muted">${fullName}</small>
                    </div>
                </td>
                <td>
                    <div class="text-break" style="max-width: 300px;">
                        <code>${record.value}</code>
                    </div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">${record.ttl}s</span>
                </td>
                <td>
                    ${record.priority ? `<span class="badge bg-warning">${record.priority}</span>` : '-'}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editDNSRecord(${JSON.stringify(record).replace(/"/g, '&quot;')})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteDNSRecord(${record.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('dnsRecordsContainer').innerHTML = html;
}

// 更新统计信息
function updateStatistics(records) {
    const stats = {
        total: records.length,
        A: 0,
        AAAA: 0,
        CNAME: 0,
        MX: 0,
        TXT: 0,
        other: 0
    };
    
    records.forEach(record => {
        if (stats.hasOwnProperty(record.type)) {
            stats[record.type]++;
        } else {
            stats.other++;
        }
    });
    
    document.getElementById('totalRecords').textContent = stats.total;
    document.getElementById('aRecords').textContent = stats.A;
    document.getElementById('cnameRecords').textContent = stats.CNAME;
    document.getElementById('mxRecords').textContent = stats.MX;
    document.getElementById('txtRecords').textContent = stats.TXT;
    document.getElementById('otherRecords').textContent = stats.other;
}

// 获取记录类型样式
function getRecordTypeClass(type) {
    const classes = {
        'A': 'bg-primary',
        'AAAA': 'bg-info',
        'CNAME': 'bg-success',
        'MX': 'bg-warning text-dark',
        'TXT': 'bg-secondary',
        'NS': 'bg-dark'
    };
    return classes[type] || 'bg-secondary';
}

// 显示添加DNS记录模态框
function showAddDNSRecord() {
    new bootstrap.Modal(document.getElementById('addDNSModal')).show();
}

// 处理记录类型变化
function handleTypeChange(type) {
    const priorityField = document.getElementById('priorityField');
    const valueHint = document.getElementById('valueHint');
    
    if (type === 'MX') {
        priorityField.style.display = 'block';
        valueHint.textContent = '请输入邮件服务器地址，如：mail.namesilo-namesilo-sample.com';
    } else {
        priorityField.style.display = 'none';
        
        switch (type) {
            case 'A':
                valueHint.textContent = '请输入IPv4地址，如：***********';
                break;
            case 'AAAA':
                valueHint.textContent = '请输入IPv6地址，如：2001:db8::1';
                break;
            case 'CNAME':
                valueHint.textContent = '请输入目标域名，如：www.namesilo-namesilo-sample.com';
                break;
            case 'TXT':
                valueHint.textContent = '请输入文本内容，如：v=spf1 include:_spf.namesilo-namesilo-sample.com ~all';
                break;
            case 'NS':
                valueHint.textContent = '请输入名称服务器，如：ns1.namesilo-namesilo-sample.com';
                break;
            default:
                valueHint.textContent = '请输入记录值';
        }
    }
}

// 提交DNS记录
function submitDNSRecord(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // 显示加载状态
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>添加中...';
    submitBtn.disabled = true;
    
    // 添加action参数
    const formDataWithAction = new FormData();
    formDataWithAction.append('action', 'add');
    for (const [key, value] of formData.entries()) {
        formDataWithAction.append(key, value);
    }

    fetch('api/dns.php', {
        method: 'POST',
        body: formDataWithAction
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addDNSModal')).hide();
            showSuccess('DNS记录添加成功');
            loadDNSRecords();
            form.reset();
        } else {
            showError('添加失败：' + data.message);
        }
    })
    .catch(error => {
        showError('网络错误，请稍后重试');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 编辑DNS记录
function editDNSRecord(record) {
    document.getElementById('editRecordId').value = record.id;
    document.getElementById('editRecordType').value = record.type;
    document.getElementById('editRecordName').value = record.name;
    document.getElementById('editRecordValue').value = record.value;
    document.getElementById('editRecordTTL').value = record.ttl;
    
    if (record.type === 'MX') {
        document.getElementById('editPriorityField').style.display = 'block';
        document.getElementById('editRecordPriority').value = record.priority || '';
    } else {
        document.getElementById('editPriorityField').style.display = 'none';
    }
    
    new bootstrap.Modal(document.getElementById('editDNSModal')).show();
}

// 更新DNS记录
function updateDNSRecord(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    data.type = document.getElementById('editRecordType').value;
    
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>保存中...';
    submitBtn.disabled = true;
    
    // 添加action参数
    const formDataWithAction = new FormData();
    formDataWithAction.append('action', 'update');
    for (const [key, value] of Object.entries(data)) {
        formDataWithAction.append(key, value);
    }

    fetch('api/dns.php', {
        method: 'POST',
        body: formDataWithAction
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('editDNSModal')).hide();
            showSuccess('DNS记录更新成功');
            loadDNSRecords();
        } else {
            showError('更新失败：' + data.message);
        }
    })
    .catch(error => {
        showError('网络错误，请稍后重试');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 删除DNS记录
function deleteDNSRecord(recordId) {
    if (confirm('确定要删除这条DNS记录吗？\n\n删除后可能会影响域名解析，此操作不可撤销！')) {
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('record_id', recordId);
        formData.append('domain', domainName);

        fetch('api/dns.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('DNS记录删除成功');
                loadDNSRecords();
            } else {
                showError('删除失败：' + data.message);
            }
        })
        .catch(error => {
            showError('网络错误，请稍后重试');
        });
    }
}

// 刷新DNS记录
function refreshDNSRecords() {
    document.getElementById('dnsRecordsContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">刷新中...</span>
            </div>
            <p class="mt-3 mb-0">正在刷新DNS记录...</p>
        </div>
    `;
    loadDNSRecords();
}

// 筛选记录
function filterRecords() {
    const searchTerm = document.getElementById('recordSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#dnsRecordsContainer tbody tr');
    
    rows.forEach(row => {
        const type = row.dataset.type.toLowerCase();
        const name = row.dataset.name.toLowerCase();
        const value = row.dataset.value.toLowerCase();
        
        if (type.includes(searchTerm) || name.includes(searchTerm) || value.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 显示成功消息
function showSuccess(message) {
    showAlert('success', message);
}

// 显示错误消息
function showError(message) {
    showAlert('error', message);
}

// 显示提示消息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show">
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.content-area');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            const alert = container.querySelector('.alert-success');
            if (alert) {
                bootstrap.Alert.getInstance(alert)?.close();
            }
        }, 3000);
    }
}
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
