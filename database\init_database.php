<?php
/**
 * 数据库初始化脚本
 * Database Initialization Script
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 加载环境变量
if (!function_exists('loadEnv')) {
    function loadEnv($path) {
        if (!file_exists($path)) {
            echo "环境配置文件不存在: $path\n";
            return;
        }

        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $_ENV[trim($name)] = trim($value, '"\'');
            }
        }
    }
}

// 定义项目根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载环境变量
$envPath = ROOT_PATH . '/.env';
loadEnv($envPath);

// 数据库配置
$config = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'namesilo_sales',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? ''
];

echo "=== NameSilo域名销售系统数据库初始化 ===\n\n";

try {
    // 连接MySQL服务器（不指定数据库）
    $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✓ 成功连接到MySQL服务器\n";
    
    // 创建数据库
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ 数据库 '{$config['database']}' 创建成功\n";
    
    // 选择数据库
    $pdo->exec("USE `{$config['database']}`");
    echo "✓ 已选择数据库 '{$config['database']}'\n\n";
    
    // 执行表结构创建脚本
    echo "正在创建数据表...\n";
    $createTablesSQL = file_get_contents(__DIR__ . '/migrations/create_tables.sql');
    
    // 移除USE语句，因为我们已经选择了数据库
    $createTablesSQL = preg_replace('/CREATE DATABASE.*?;/s', '', $createTablesSQL);
    $createTablesSQL = preg_replace('/USE.*?;/s', '', $createTablesSQL);
    
    // 分割SQL语句并执行
    $statements = array_filter(array_map('trim', explode(';', $createTablesSQL)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                // 提取表名
                if (preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "  ✓ 创建表: {$matches[1]}\n";
                }
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
    }
    
    echo "\n正在插入示例数据...\n";
    
    // 执行示例数据插入脚本
    $sampleDataSQL = file_get_contents(__DIR__ . '/seeds/sample_data.sql');
    
    // 移除USE语句
    $sampleDataSQL = preg_replace('/USE.*?;/s', '', $sampleDataSQL);
    
    // 分割SQL语句并执行
    $statements = array_filter(array_map('trim', explode(';', $sampleDataSQL)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', trim($statement))) {
            try {
                $pdo->exec($statement);
                // 提取插入的表名
                if (preg_match('/INSERT INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "  ✓ 插入数据到: {$matches[1]}\n";
                }
            } catch (PDOException $e) {
                // 忽略重复键错误
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "  ⚠ 警告: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "\n=== 数据库初始化完成 ===\n\n";
    
    // 显示统计信息
    echo "数据统计:\n";
    $tables = ['users', 'domain_categories', 'domains', 'orders', 'payment_records', 'system_logs', 'system_settings'];
    
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            echo "  $table: $count 条记录\n";
        } catch (PDOException $e) {
            echo "  $table: 表不存在或查询失败\n";
        }
    }
    
    echo "\n默认管理员账号:\n";
    echo "  用户名: admin\n";
    echo "  密码: password (请及时修改)\n";
    echo "  邮箱: <EMAIL>\n\n";
    
    echo "数据库初始化成功！您现在可以访问系统了。\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "\n";
    echo "\n请检查以下配置:\n";
    echo "  - 数据库服务器是否运行\n";
    echo "  - 数据库连接信息是否正确\n";
    echo "  - 用户是否有足够的权限\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ 系统错误: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 验证数据库连接
 */
function verifyDatabaseConnection($config) {
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // 测试查询
        $pdo->query("SELECT 1");
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * 创建.env文件示例
 */
function createEnvExample() {
    $envExample = <<<ENV
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=namesilo_sales
DB_USERNAME=root
DB_PASSWORD=

# 应用配置
APP_NAME="NameSilo域名销售系统"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="NameSilo"

# 支付配置
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=
PAYPAL_MODE=sandbox

STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY=

# 其他配置
SESSION_LIFETIME=120
TIMEZONE=UTC
LOCALE=zh-CN
ENV;

    $envPath = dirname(__DIR__) . '/.env.example';
    file_put_contents($envPath, $envExample);
    echo "✓ 已创建 .env.example 文件\n";
}

// 如果.env文件不存在，创建示例文件
if (!file_exists(dirname(__DIR__) . '/.env')) {
    echo "⚠ .env 文件不存在，正在创建示例文件...\n";
    createEnvExample();
    echo "请复制 .env.example 为 .env 并配置相应参数\n";
}
?>
