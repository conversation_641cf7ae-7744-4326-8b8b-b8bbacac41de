# NameSilo域名销售系统 - 数据库设置指南

## 📋 目录结构

```
database/
├── migrations/
│   └── create_tables.sql      # 数据库表结构
├── seeds/
│   └── sample_data.sql        # 示例数据
├── init_database.php          # 数据库初始化脚本
└── README.md                  # 本文件
```

## 🚀 快速开始

### 1. 环境配置

首先复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接信息：
```env
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=namesilo_sales
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 2. 数据库初始化

#### 方法一：使用PHP脚本（推荐）
```bash
php database/init_database.php
```

#### 方法二：使用Web界面
访问：`http://your-domain/admin/database-manager.php`
点击"初始化数据库"按钮

#### 方法三：手动执行SQL
```bash
# 1. 创建数据库和表结构
mysql -u root -p < database/migrations/create_tables.sql

# 2. 插入示例数据
mysql -u root -p namesilo_sales < database/seeds/sample_data.sql
```

## 📊 数据库表结构

### 核心表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `users` | 用户表 | id, username, email, role, status |
| `domains` | 域名表 | id, domain_name, price, status, category_id |
| `orders` | 订单表 | id, user_id, domain_id, amount, status |
| `payment_records` | 支付记录表 | id, order_id, amount, status, gateway |

### 辅助表

| 表名 | 说明 | 主要字段 |
|------|------|----------|
| `domain_categories` | 域名分类表 | id, name, slug, description |
| `system_logs` | 系统日志表 | id, level, message, user_id |
| `system_settings` | 系统设置表 | id, key, value, type |
| `user_sessions` | 用户会话表 | id, user_id, payload |

## 👤 默认账户

初始化完成后，系统会创建以下默认账户：

### 管理员账户
- **用户名**: `admin`
- **密码**: `password`
- **邮箱**: `<EMAIL>`
- **角色**: `admin`

### 测试用户账户
- **用户名**: `john_doe`
- **密码**: `password`
- **邮箱**: `<EMAIL>`
- **角色**: `user`

> ⚠️ **安全提醒**: 请在生产环境中立即修改默认密码！

## 📈 示例数据

系统包含以下示例数据：

- **用户**: 11个用户（1个管理员 + 10个普通用户）
- **域名分类**: 10个分类（科技、商业、教育等）
- **域名**: 20个域名（包含不同价格和状态）
- **订单**: 10个订单（不同状态和支付方式）
- **支付记录**: 对应的支付记录
- **系统日志**: 示例日志记录
- **系统设置**: 基础配置项

## 🔧 数据库管理

### 使用Web管理工具

访问 `http://your-domain/admin/database-manager.php` 可以：

- 查看数据库连接状态
- 查看表结构和数据统计
- 重置管理员密码
- 清空系统日志
- 备份数据库
- 重新初始化数据库

### 常用SQL命令

```sql
-- 查看所有表
SHOW TABLES;

-- 查看用户数量
SELECT COUNT(*) FROM users;

-- 查看域名统计
SELECT status, COUNT(*) FROM domains GROUP BY status;

-- 查看订单统计
SELECT status, COUNT(*), SUM(total_amount) FROM orders GROUP BY status;

-- 重置管理员密码
UPDATE users SET password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'admin';
```

## 🔄 数据库维护

### 备份数据库
```bash
mysqldump -u root -p namesilo_sales > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 恢复数据库
```bash
mysql -u root -p namesilo_sales < backup_file.sql
```

### 清理日志
```sql
-- 清理30天前的日志
DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理所有日志
TRUNCATE TABLE system_logs;
```

## 🐛 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否运行
- 验证数据库连接信息
- 确认用户权限

### 2. 表已存在错误
- 删除现有数据库：`DROP DATABASE namesilo_sales;`
- 重新运行初始化脚本

### 3. 权限不足
```sql
-- 授予用户权限
GRANT ALL PRIVILEGES ON namesilo_sales.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 字符编码问题
确保数据库和表使用 `utf8mb4` 编码：
```sql
ALTER DATABASE namesilo_sales CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📝 开发说明

### 添加新表
1. 在 `migrations/create_tables.sql` 中添加表结构
2. 在 `seeds/sample_data.sql` 中添加示例数据
3. 更新相关的PHP模型类

### 修改表结构
1. 创建新的迁移文件
2. 在生产环境中谨慎执行ALTER语句
3. 备份数据后再进行结构修改

### 数据迁移
- 使用事务确保数据一致性
- 大量数据迁移时分批处理
- 记录迁移日志便于回滚

## 🔒 安全建议

1. **生产环境**:
   - 修改默认密码
   - 限制数据库访问IP
   - 定期备份数据
   - 启用SSL连接

2. **开发环境**:
   - 不要在版本控制中提交 `.env` 文件
   - 使用不同的数据库名称
   - 定期更新示例数据

3. **权限管理**:
   - 为应用创建专用数据库用户
   - 只授予必要的权限
   - 定期审查用户权限

## 📞 技术支持

如果遇到问题，请：
1. 查看系统日志：`system_logs` 表
2. 检查PHP错误日志
3. 验证数据库连接配置
4. 联系技术支持团队
