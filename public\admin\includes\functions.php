<?php
/**
 * 通用工具函数
 * Common Utility Functions
 */

// 如果没有定义路径常量，自动定义
if (!defined('ADMIN_PATH')) {
    define('ADMIN_PATH', __DIR__);
}
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
}

/**
 * 加载环境变量
 */
if (!function_exists('loadEnv')) {
    function loadEnv($path) {
        if (!file_exists($path)) return false;

        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                [$name, $value] = explode('=', $line, 2);
                $_ENV[trim($name)] = trim($value, '"\'');
            }
        }
        return true;
    }
}

/**
 * 安全的数据库连接
 */
if (!function_exists('safeGetDatabase')) {
    function safeGetDatabase() {
        static $pdo = null;

        if ($pdo === null) {
            $host = 'localhost';
            $port = '3306';
            $database = 'www_bt_cn';
            $username = 'www_bt_cn';
            $password = 'YAfxfrB8nr6F84LP';

            try {
                $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]);
            } catch (PDOException $e) {
                throw new Exception('数据库连接失败: ' . $e->getMessage());
            }
        }

        return $pdo;
    }
}

/**
 * 获取数据库连接（向后兼容）
 */
if (!function_exists('getDatabase')) {
    function getDatabase() {
        return safeGetDatabase();
    }
}

/**
 * 检查数据库表是否存在
 */
if (!function_exists('checkDatabaseTables')) {
    function checkDatabaseTables() {
        try {
            $db = safeGetDatabase();
            $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            $requiredTables = ['users', 'domains', 'orders', 'payment_records'];
            return !empty(array_intersect($requiredTables, $tables));
        } catch (Exception $e) {
            return false;
        }
    }
}

/**
 * 安全的HTML输出
 */
if (!function_exists('e')) {
    function e($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
}

/**
 * 格式化文件大小
 */
if (!function_exists('formatBytes')) {
    function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}

/**
 * 生成随机字符串
 */
if (!function_exists('generateRandomString')) {
    function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
}

/**
 * 检查是否为AJAX请求
 */
if (!function_exists('isAjax')) {
    function isAjax() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}

/**
 * 重定向函数
 */
if (!function_exists('redirect')) {
    function redirect($url, $statusCode = 302) {
        header("Location: {$url}", true, $statusCode);
        exit;
    }
}

/**
 * 获取客户端IP地址
 */
if (!function_exists('getClientIP')) {
    function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

/**
 * 记录系统日志
 */
if (!function_exists('logSystem')) {
    function logSystem($level, $message, $context = [], $userId = null) {
        try {
            $db = safeGetDatabase();
            $stmt = $db->prepare("INSERT INTO system_logs (level, message, context, user_id, ip_address, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                $level,
                $message,
                json_encode($context),
                $userId ?? $_SESSION['admin_id'] ?? null,
                getClientIP()
            ]);
        } catch (Exception $e) {
            // 静默失败，避免日志记录影响主要功能
            error_log("Failed to log system message: " . $e->getMessage());
        }
    }
}

/**
 * 验证CSRF令牌
 */
if (!function_exists('verifyCsrfToken')) {
    function verifyCsrfToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

/**
 * 生成CSRF令牌
 */
if (!function_exists('generateCsrfToken')) {
    function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = generateRandomString(32);
        }
        return $_SESSION['csrf_token'];
    }
}

/**
 * 检查用户权限
 */
if (!function_exists('hasPermission')) {
    function hasPermission($permission) {
        // 简单的权限检查，可以根据需要扩展
        return isset($_SESSION['admin_id']);
    }
}

/**
 * 获取系统配置
 */
if (!function_exists('getSystemConfig')) {
    function getSystemConfig($key, $default = null) {
        static $config = null;
        
        if ($config === null) {
            try {
                $db = safeGetDatabase();
                $stmt = $db->query("SELECT `key`, `value`, `type` FROM system_settings");
                $settings = $stmt->fetchAll();
                
                $config = [];
                foreach ($settings as $setting) {
                    $value = $setting['value'];
                    
                    // 根据类型转换值
                    switch ($setting['type']) {
                        case 'boolean':
                            $value = (bool) $value;
                            break;
                        case 'integer':
                            $value = (int) $value;
                            break;
                        case 'json':
                            $value = json_decode($value, true);
                            break;
                    }
                    
                    $config[$setting['key']] = $value;
                }
            } catch (Exception $e) {
                $config = [];
            }
        }
        
        return $config[$key] ?? $default;
    }
}

/**
 * 设置系统配置
 */
if (!function_exists('setSystemConfig')) {
    function setSystemConfig($key, $value, $type = 'string') {
        try {
            $db = safeGetDatabase();
            
            // 根据类型处理值
            switch ($type) {
                case 'boolean':
                    $value = $value ? '1' : '0';
                    break;
                case 'json':
                    $value = json_encode($value);
                    break;
                default:
                    $value = (string) $value;
            }
            
            $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE `value` = ?, `type` = ?");
            $stmt->execute([$key, $value, $type, $value, $type]);
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

/**
 * 清理旧文件
 */
if (!function_exists('cleanupOldFiles')) {
    function cleanupOldFiles($directory, $maxAge = 86400) {
        if (!is_dir($directory)) return;
        
        $files = glob($directory . '/*');
        $now = time();
        
        foreach ($files as $file) {
            if (is_file($file) && ($now - filemtime($file)) > $maxAge) {
                unlink($file);
            }
        }
    }
}
