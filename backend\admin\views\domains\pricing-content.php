<?php
$db = getDatabase();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_price':
            $id = $_POST['id'] ?? '';
            $registrationPrice = floatval($_POST['registration_price'] ?? 0);
            $renewalPrice = floatval($_POST['renewal_price'] ?? 0);
            $transferPrice = floatval($_POST['transfer_price'] ?? 0);
            $minYears = intval($_POST['min_years'] ?? 1);
            $maxYears = intval($_POST['max_years'] ?? 10);
            $enabled = isset($_POST['enabled']) ? 1 : 0;
            
            if ($id && $registrationPrice > 0) {
                try {
                    $stmt = $db->prepare("UPDATE tld_pricing SET register_price = ?, renew_price = ?, transfer_price = ?, min_years = ?, max_years = ?, is_active = ? WHERE id = ?");
                    $stmt->execute([$registrationPrice, $renewalPrice, $transferPrice, $minYears, $maxYears, $enabled, $id]);
                    $message = '价格更新成功';
                } catch (PDOException $e) {
                    $error = '更新失败：' . $e->getMessage();
                }
            } else {
                $error = '请填写有效的价格信息';
            }
            break;
            
        case 'add_tld':
            $tld = trim($_POST['tld'] ?? '');
            $registrationPrice = floatval($_POST['registration_price'] ?? 0);
            $renewalPrice = floatval($_POST['renewal_price'] ?? 0);
            $transferPrice = floatval($_POST['transfer_price'] ?? 0);
            $minYears = intval($_POST['min_years'] ?? 1);
            $maxYears = intval($_POST['max_years'] ?? 10);
            $enabled = isset($_POST['enabled']) ? 1 : 0;
            
            if ($tld && $registrationPrice > 0) {
                try {
                    // 确保TLD以点开头
                    if (!str_starts_with($tld, '.')) {
                        $tld = '.' . $tld;
                    }
                    
                    $stmt = $db->prepare("INSERT INTO tld_pricing (tld, register_price, renew_price, transfer_price, min_years, max_years, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$tld, $registrationPrice, $renewalPrice, $transferPrice, $minYears, $maxYears, $enabled]);
                    $message = '新TLD添加成功';
                } catch (PDOException $e) {
                    $error = '添加失败：' . $e->getMessage();
                }
            } else {
                $error = '请填写有效的TLD和价格信息';
            }
            break;
            
        case 'delete_tld':
            $id = $_POST['id'] ?? '';
            if ($id) {
                try {
                    $stmt = $db->prepare("DELETE FROM tld_pricing WHERE id = ?");
                    $stmt->execute([$id]);
                    $message = 'TLD删除成功';
                } catch (PDOException $e) {
                    $error = '删除失败：' . $e->getMessage();
                }
            }
            break;
    }
}

// 获取域名价格列表
$search = $_GET['search'] ?? '';
$enabled = $_GET['enabled'] ?? '';
$page_num = max(1, intval($_GET['page_num'] ?? 1));
$limit = 20;
$offset = ($page_num - 1) * $limit;

$where = [];
$params = [];

if ($search) {
    $where[] = "tld LIKE ?";
    $params[] = "%$search%";
}

if ($enabled !== '') {
    $where[] = "is_active = ?";
    $params[] = $enabled;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// 获取总数
$countSql = "SELECT COUNT(*) FROM tld_pricing $whereClause";
$totalPrices = $db->prepare($countSql);
$totalPrices->execute($params);
$totalCount = $totalPrices->fetchColumn();
$totalPages = ceil($totalCount / $limit);

// 获取价格列表
$sql = "SELECT * FROM tld_pricing $whereClause ORDER BY tld LIMIT $limit OFFSET $offset";
$stmt = $db->prepare($sql);
$stmt->execute($params);
$prices = $stmt->fetchAll();

// 获取统计数据
$stats = $db->query("SELECT
    COUNT(*) as total,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as enabled_count,
    MIN(register_price) as min_price,
    MAX(register_price) as max_price,
    AVG(register_price) as avg_price
    FROM tld_pricing")->fetch();
?>

<!-- 消息提示 -->
<?php if (isset($message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 面包屑导航 -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php?page=domains">域名管理</a></li>
        <li class="breadcrumb-item active">价格设置</li>
    </ol>
</nav>

<!-- 页面标题和操作 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">域名价格管理</h1>
        <p class="text-muted">管理各种TLD的注册、续费和转移价格</p>
    </div>
    <div class="d-flex gap-2">
        <a href="index.php?page=domains&action=pricing-import" class="btn btn-outline-primary">
            <i class="fas fa-upload me-2"></i>
            TLD数据导入
        </a>
        <a href="index.php?page=domains" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            返回域名管理
        </a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTldModal">
            <i class="fas fa-plus me-2"></i>
            添加TLD
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总TLD数</h6>
                        <h4 class="mb-0"><?= number_format($stats['total']) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success bg-opacity-10 text-success rounded-3 p-3 me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">启用TLD</h6>
                        <h4 class="mb-0"><?= number_format($stats['enabled_count']) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">最低价格</h6>
                        <h4 class="mb-0">$<?= number_format($stats['min_price'], 2) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">平均价格</h6>
                        <h4 class="mb-0">$<?= number_format($stats['avg_price'], 2) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="domains">
            <input type="hidden" name="action" value="pricing">
            <div class="col-md-6">
                <label class="form-label">搜索TLD</label>
                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="输入TLD名称...">
            </div>
            <div class="col-md-4">
                <label class="form-label">状态</label>
                <select class="form-select" name="enabled">
                    <option value="">全部状态</option>
                    <option value="1" <?= $enabled === '1' ? 'selected' : '' ?>>启用</option>
                    <option value="0" <?= $enabled === '0' ? 'selected' : '' ?>>禁用</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 价格列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            TLD价格列表 (共 <?= number_format($totalCount) ?> 个)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>TLD</th>
                        <th>注册价格</th>
                        <th>续费价格</th>
                        <th>转移价格</th>
                        <th>年限范围</th>
                        <th>状态</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($prices)): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无价格数据</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($prices as $price): ?>
                    <tr>
                        <td>
                            <span class="fw-medium text-primary"><?= htmlspecialchars($price['tld']) ?></span>
                        </td>
                        <td>
                            <span class="fw-medium text-success">$<?= number_format($price['register_price'], 2) ?></span>
                        </td>
                        <td>
                            <span class="text-muted">$<?= number_format($price['renew_price'], 2) ?></span>
                        </td>
                        <td>
                            <span class="text-muted">$<?= number_format($price['transfer_price'], 2) ?></span>
                        </td>
                        <td>
                            <span class="badge bg-info"><?= $price['min_years'] ?>-<?= $price['max_years'] ?> 年</span>
                        </td>
                        <td>
                            <?php if ($price['is_active']): ?>
                            <span class="badge bg-success">启用</span>
                            <?php else: ?>
                            <span class="badge bg-secondary">禁用</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= date('Y-m-d H:i', strtotime($price['updated_at'])) ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="editPrice(<?= htmlspecialchars(json_encode($price)) ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <form method="POST" class="d-inline" onsubmit="return confirm('确定要删除这个TLD吗？')">
                                    <input type="hidden" name="action" value="delete_tld">
                                    <input type="hidden" name="id" value="<?= $price['id'] ?>">
                                    <button type="submit" class="btn btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="card-footer">
        <nav aria-label="价格列表分页">
            <ul class="pagination justify-content-center mb-0">
                <?php if ($page_num > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=domains&action=pricing&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&enabled=<?= urlencode($enabled) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                    <a class="page-link" href="?page=domains&action=pricing&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&enabled=<?= urlencode($enabled) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page_num < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=domains&action=pricing&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&enabled=<?= urlencode($enabled) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- 添加TLD模态框 -->
<div class="modal fade" id="addTldModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加新TLD
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_tld">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">TLD名称 *</label>
                            <input type="text" class="form-control" name="tld" placeholder=".example" required>
                            <small class="text-muted">如果不以点开头，系统会自动添加</small>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">注册价格 (USD) *</label>
                            <input type="number" class="form-control" name="registration_price" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">续费价格 (USD) *</label>
                            <input type="number" class="form-control" name="renewal_price" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">转移价格 (USD) *</label>
                            <input type="number" class="form-control" name="transfer_price" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">最小年数</label>
                            <input type="number" class="form-control" name="min_years" value="1" min="1" max="10">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">最大年数</label>
                            <input type="number" class="form-control" name="max_years" value="10" min="1" max="10">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enabled" id="enabled" checked>
                                <label class="form-check-label" for="enabled">
                                    启用此TLD
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        添加TLD
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑价格模态框 -->
<div class="modal fade" id="editPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    编辑价格
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editPriceForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_price">
                    <input type="hidden" name="id" id="editId">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">TLD名称</label>
                            <input type="text" class="form-control" id="editTld" readonly>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">注册价格 (USD) *</label>
                            <input type="number" class="form-control" name="registration_price" id="editRegistrationPrice" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">续费价格 (USD) *</label>
                            <input type="number" class="form-control" name="renewal_price" id="editRenewalPrice" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">转移价格 (USD) *</label>
                            <input type="number" class="form-control" name="transfer_price" id="editTransferPrice" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">最小年数</label>
                            <input type="number" class="form-control" name="min_years" id="editMinYears" min="1" max="10">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">最大年数</label>
                            <input type="number" class="form-control" name="max_years" id="editMaxYears" min="1" max="10">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enabled" id="editEnabled">
                                <label class="form-check-label" for="editEnabled">
                                    启用此TLD
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        更新价格
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPrice(price) {
    document.getElementById('editId').value = price.id;
    document.getElementById('editTld').value = price.tld;
    document.getElementById('editRegistrationPrice').value = price.register_price;
    document.getElementById('editRenewalPrice').value = price.renew_price;
    document.getElementById('editTransferPrice').value = price.transfer_price;
    document.getElementById('editMinYears').value = price.min_years;
    document.getElementById('editMaxYears').value = price.max_years;
    document.getElementById('editEnabled').checked = price.is_active == 1;
    
    const modal = new bootstrap.Modal(document.getElementById('editPriceModal'));
    modal.show();
}
</script>
