-- 创建域名表
-- Create domains table

CREATE TABLE IF NOT EXISTS domains (
    id INT AUTO_INCREMENT PRIMARY KEY,
    domain_name VARCHAR(255) NOT NULL UNIQUE COMMENT '域名',
    tld VARCHAR(20) NOT NULL COMMENT '顶级域名',
    user_id INT COMMENT '所有者用户ID',
    status ENUM('available', 'registered', 'expired', 'pending', 'transferred') DEFAULT 'available' COMMENT '域名状态',
    registration_date DATE COMMENT '注册日期',
    expiry_date DATE COMMENT '过期日期',
    auto_renew B<PERSON><PERSON>EA<PERSON> DEFAULT FALSE COMMENT '自动续费',
    privacy_protection BOOLEAN DEFAULT TRUE COMMENT '隐私保护',
    nameservers JSON COMMENT 'DNS服务器',
    registrar_order_id VARCHAR(100) COMMENT '注册商订单ID',
    price DECIMAL(10,2) COMMENT '价格',
    renewal_price DECIMAL(10,2) COMMENT '续费价格',
    transfer_price DECIMAL(10,2) COMMENT '转移价格',
    featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    description TEXT COMMENT '描述',
    category VARCHAR(50) COMMENT '分类',
    keywords TEXT COMMENT '关键词',
    views INT DEFAULT 0 COMMENT '浏览次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_domain_name (domain_name),
    INDEX idx_tld (tld),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_featured (featured),
    INDEX idx_category (category),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名表';
