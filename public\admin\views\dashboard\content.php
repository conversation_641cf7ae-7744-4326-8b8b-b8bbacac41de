<?php
// 初始化变量
$userStats = [
    'total_users' => 0,
    'admin_users' => 0,
    'active_users' => 0,
    'today_users' => 0
];
$domainStats = [
    'total_domains' => 0,
    'available_domains' => 0,
    'sold_domains' => 0,
    'featured_domains' => 0
];
$orderStats = [
    'total_orders' => 0,
    'completed_orders' => 0,
    'pending_orders' => 0,
    'today_orders' => 0,
    'total_revenue' => 0
];
$recentOrders = [];
$recentUsers = [];
$usingDatabase = false;
$error = '';

try {
    // 尝试获取数据库数据
    $db = getDatabase();

    // 检查表是否存在
    $tables = $db->query("SHOW TABLES LIKE 'users'")->fetchAll();

    if (!empty($tables)) {
        $usingDatabase = true;

        // 获取用户统计
        $userStats = $db->query("SELECT
            COUNT(*) as total_users,
            SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_users,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_users
            FROM users")->fetch();

        // 检查其他表是否存在
        $ordersTable = $db->query("SHOW TABLES LIKE 'orders'")->fetchAll();
        $domainsTable = $db->query("SHOW TABLES LIKE 'domains'")->fetchAll();

        // 获取订单统计（如果表存在）
        if (!empty($ordersTable)) {
            $orderStats = $db->query("SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_orders,
                SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue
                FROM orders")->fetch();

            // 获取最近订单
            $recentOrders = $db->query("SELECT o.*, u.username
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                ORDER BY o.created_at DESC
                LIMIT 10")->fetchAll();
        }

        // 获取域名统计（如果表存在）
        if (!empty($domainsTable)) {
            $domainStats = $db->query("SELECT
                COUNT(*) as total_domains,
                SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_domains,
                SUM(CASE WHEN status = 'sold' THEN 1 ELSE 0 END) as sold_domains,
                SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured_domains
                FROM domains")->fetch();
        }

        // 获取最近用户
        $recentUsers = $db->query("SELECT * FROM users
            WHERE role = 'user'
            ORDER BY created_at DESC
            LIMIT 10")->fetchAll();
    }

} catch (Exception $e) {
    // 数据库连接失败
    $error = '数据库连接失败: ' . $e->getMessage();
}
?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" data-aos="fade-down">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-circle fa-2x me-3"></i>
        <div class="flex-grow-1">
            <h5 class="alert-heading mb-1">数据库错误</h5>
            <p class="mb-2"><?= htmlspecialchars($error) ?></p>
            <div class="d-flex gap-2">
                <a href="check-database.php" class="btn btn-danger btn-sm">
                    <i class="fas fa-database"></i> 检查数据库
                </a>
                <a href="create-admin.php" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-user-plus"></i> 创建管理员
                </a>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
<?php elseif (!$usingDatabase): ?>
<div class="alert alert-warning alert-dismissible fade show" data-aos="fade-down">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
        <div class="flex-grow-1">
            <h5 class="alert-heading mb-1">数据库未配置</h5>
            <p class="mb-2">数据库表不存在或未正确配置。系统显示默认数据。</p>
            <div class="d-flex gap-2">
                <a href="check-database.php" class="btn btn-warning btn-sm">
                    <i class="fas fa-database"></i> 检查数据库
                </a>
                <a href="create-admin.php" class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-user-plus"></i> 创建管理员
                </a>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
<?php endif; ?>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <!-- 用户统计 -->
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card border-0 shadow-sm h-100" data-aos="fade-up" data-aos-delay="100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">总用户数</h6>
                        <h3 class="mb-0 fw-bold"><?= number_format($userStats['total_users'] ?? 0) ?></h3>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i>
                            今日新增: <?= $userStats['today_users'] ?? 0 ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 域名统计 -->
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card border-0 shadow-sm h-100" data-aos="fade-up" data-aos-delay="200">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success bg-opacity-10 text-success rounded-3 p-3 me-3">
                        <i class="fas fa-globe fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">域名总数</h6>
                        <h3 class="mb-0 fw-bold"><?= number_format($domainStats['total_domains'] ?? 0) ?></h3>
                        <small class="text-info">
                            <i class="fas fa-check-circle"></i>
                            可售: <?= number_format($domainStats['available_domains'] ?? 0) ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单统计 -->
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card border-0 shadow-sm h-100" data-aos="fade-up" data-aos-delay="300">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">订单总数</h6>
                        <h3 class="mb-0 fw-bold"><?= number_format($orderStats['total_orders'] ?? 0) ?></h3>
                        <small class="text-primary">
                            <i class="fas fa-clock"></i>
                            待处理: <?= number_format($orderStats['pending_orders'] ?? 0) ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 收入统计 -->
    <div class="col-xl-3 col-md-6">
        <div class="card stat-card border-0 shadow-sm h-100" data-aos="fade-up" data-aos-delay="400">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="text-muted mb-1">总收入</h6>
                        <h3 class="mb-0 fw-bold">$<?= number_format($orderStats['total_revenue'] ?? 0, 2) ?></h3>
                        <small class="text-success">
                            <i class="fas fa-chart-line"></i>
                            今日: <?= $orderStats['today_orders'] ?? 0 ?> 单
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row g-4">
    <!-- 最近订单 -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100" data-aos="fade-up" data-aos-delay="500">
            <div class="card-header bg-white border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shopping-cart text-primary me-2"></i>
                        最近订单
                    </h5>
                    <a href="index.php?page=orders" class="btn btn-outline-primary btn-sm">
                        查看全部
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>订单号</th>
                                <th>用户</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recentOrders)): ?>
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <br>暂无订单数据
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($recentOrders as $order): ?>
                            <tr>
                                <td>
                                    <span class="fw-medium">#<?= htmlspecialchars($order['order_number'] ?? $order['id']) ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="fas fa-user text-primary"></i>
                                        </div>
                                        <?= htmlspecialchars($order['username'] ?? 'N/A') ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-medium">$<?= number_format($order['total_amount'] ?? 0, 2) ?></span>
                                </td>
                                <td>
                                    <?php
                                    $status = $order['status'] ?? 'pending';
                                    $statusClass = [
                                        'completed' => 'success',
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'cancelled' => 'danger'
                                    ][$status] ?? 'secondary';
                                    $statusText = [
                                        'completed' => '已完成',
                                        'pending' => '待处理',
                                        'processing' => '处理中',
                                        'cancelled' => '已取消'
                                    ][$status] ?? $status;
                                    ?>
                                    <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('m-d H:i', strtotime($order['created_at'] ?? 'now')) ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近用户 -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100" data-aos="fade-up" data-aos-delay="600">
            <div class="card-header bg-white border-0 pb-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus text-success me-2"></i>
                        新注册用户
                    </h5>
                    <a href="index.php?page=users" class="btn btn-outline-success btn-sm">
                        查看全部
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($recentUsers)): ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-user-slash fa-2x mb-2"></i>
                    <br>暂无用户数据
                </div>
                <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($recentUsers, 0, 8) as $user): ?>
                    <div class="list-group-item border-0 px-0">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-user text-success"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0"><?= htmlspecialchars($user['username'] ?? 'N/A') ?></h6>
                                <small class="text-muted"><?= htmlspecialchars($user['email'] ?? 'N/A') ?></small>
                            </div>
                            <small class="text-muted">
                                <?= date('m-d', strtotime($user['created_at'] ?? 'now')) ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
