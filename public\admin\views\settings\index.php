<?php
$title = '系统设置';
$page = 'settings';

// 加载必要的函数
require_once dirname(dirname(__DIR__)) . '/includes/functions.php';
$db = safeGetDatabase();

// 获取设置描述
function getSettingDescription($key) {
    $descriptions = [
        'site_name' => '网站名称',
        'site_description' => '网站描述',
        'admin_email' => '管理员邮箱',
        'currency' => '货币代码',
        'currency_symbol' => '货币符号',
        'domain_markup' => '域名价格加成比例',
        'enable_registration' => '是否允许用户注册',
        'email_verification_required' => '是否需要邮箱验证',
        'enable_cart' => '是否启用购物车功能',
        'namesilo_api_key' => 'NameSilo API密钥',
        'namesilo_api_url' => 'NameSilo API URL',
        'namesilo_sandbox' => '是否启用沙盒模式',
        'mail_host' => 'SMTP服务器',
        'mail_port' => 'SMTP端口',
        'mail_encryption' => '邮件加密方式',
        'mail_username' => 'SMTP用户名',
        'mail_password' => 'SMTP密码'
    ];
    return $descriptions[$key] ?? $key;
}

// 获取设置分组
function getSettingGroup($key) {
    $groups = [
        'site_name' => 'general',
        'site_description' => 'general',
        'admin_email' => 'general',
        'currency' => 'general',
        'currency_symbol' => 'general',
        'domain_markup' => 'general',
        'enable_registration' => 'auth',
        'email_verification_required' => 'auth',
        'enable_cart' => 'general',
        'namesilo_api_key' => 'api',
        'namesilo_api_url' => 'api',
        'namesilo_sandbox' => 'api',
        'mail_host' => 'email',
        'mail_port' => 'email',
        'mail_encryption' => 'email',
        'mail_username' => 'email',
        'mail_password' => 'email'
    ];
    return $groups[$key] ?? 'general';
}

// 处理设置更新
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_settings':
            foreach ($_POST as $key => $value) {
                if ($key !== 'action' && strpos($key, 'setting_') === 0) {
                    $settingKey = substr($key, 8); // 移除 'setting_' 前缀

                    // 处理复选框值
                    if (!isset($_POST[$key]) && in_array($settingKey, ['enable_registration', 'email_verification_required', 'enable_cart', 'namesilo_sandbox'])) {
                        $value = '0';
                    }

                    // 更新 settings 表
                    $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, updated_at) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");
                    $stmt->execute([$settingKey, trim($value), trim($value)]);

                    // 同时更新 system_settings 表（用于新的API）
                    $type = in_array($settingKey, ['enable_registration', 'email_verification_required', 'enable_cart', 'namesilo_sandbox']) ? 'boolean' : 'string';
                    $description = getSettingDescription($settingKey);
                    $group = getSettingGroup($settingKey);

                    $stmt = $db->prepare("INSERT INTO system_settings (`key`, value, type, description, `group`, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW()) ON DUPLICATE KEY UPDATE value = ?, updated_at = NOW()");
                    $stmt->execute([$settingKey, trim($value), $type, $description, $group, trim($value)]);
                }
            }

            // 处理未选中的复选框
            $checkboxes = ['enable_registration', 'email_verification_required', 'enable_cart', 'namesilo_sandbox'];
            foreach ($checkboxes as $checkbox) {
                if (!isset($_POST['setting_' . $checkbox])) {
                    // 更新 settings 表
                    $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, updated_at) VALUES (?, '0', NOW()) ON DUPLICATE KEY UPDATE setting_value = '0', updated_at = NOW()");
                    $stmt->execute([$checkbox]);

                    // 更新 system_settings 表
                    $description = getSettingDescription($checkbox);
                    $group = getSettingGroup($checkbox);
                    $stmt = $db->prepare("INSERT INTO system_settings (`key`, value, type, description, `group`, created_at, updated_at) VALUES (?, '0', 'boolean', ?, ?, NOW(), NOW()) ON DUPLICATE KEY UPDATE value = '0', updated_at = NOW()");
                    $stmt->execute([$checkbox, $description, $group]);
                }
            }

            $message = '设置已更新';
            break;
            
        case 'update_prices':
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'price_') === 0) {
                    $tld = substr($key, 6); // 移除 'price_' 前缀
                    $price = floatval($value);
                    if ($price > 0) {
                        $stmt = $db->prepare("UPDATE domain_prices SET registration_price = ?, renewal_price = ?, updated_at = NOW() WHERE tld = ?");
                        $stmt->execute([$price, $price, $tld]);
                    }
                }
            }
            $message = '域名价格已更新';
            break;
            
        case 'add_tld':
            $tld = trim($_POST['new_tld'] ?? '');
            $price = floatval($_POST['new_price'] ?? 0);
            
            if ($tld && $price > 0) {
                if (!str_starts_with($tld, '.')) {
                    $tld = '.' . $tld;
                }
                
                $stmt = $db->prepare("INSERT INTO domain_prices (tld, registration_price, renewal_price, transfer_price) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE registration_price = ?, renewal_price = ?, transfer_price = ?");
                $stmt->execute([$tld, $price, $price, $price, $price, $price, $price]);
                $message = '域名后缀已添加';
            }
            break;
    }
}

// 获取系统设置
$settings = [];

// 从 settings 表获取设置
try {
    $stmt = $db->query("SELECT * FROM settings ORDER BY setting_key");
    foreach ($stmt->fetchAll() as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    // settings 表可能不存在，忽略错误
}

// 从 system_settings 表获取设置（优先级更高）
try {
    $stmt = $db->query("SELECT `key`, value, type FROM system_settings ORDER BY `key`");
    foreach ($stmt->fetchAll() as $setting) {
        // 根据类型转换值
        switch ($setting['type']) {
            case 'boolean':
                $settings[$setting['key']] = $setting['value'] ? '1' : '0';
                break;
            case 'integer':
                $settings[$setting['key']] = (string)$setting['value'];
                break;
            default:
                $settings[$setting['key']] = $setting['value'];
        }
    }
} catch (Exception $e) {
    // system_settings 表可能不存在，忽略错误
}

// 获取域名价格
$domainPrices = $db->query("SELECT * FROM domain_prices ORDER BY tld")->fetchAll();

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">系统设置</h1>
</div>

<?php if (isset($message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- 基本设置 -->
    <div class="col-lg-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-cog"></i>
                    基本设置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <div class="mb-3">
                        <label class="form-label">网站名称</label>
                        <input type="text" class="form-control" name="setting_site_name" 
                               value="<?= htmlspecialchars($settings['site_name'] ?? 'NameSilo域名销售系统') ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">网站描述</label>
                        <textarea class="form-control" name="setting_site_description" rows="3"><?= htmlspecialchars($settings['site_description'] ?? 'NameSilo专业域名注册服务') ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">管理员邮箱</label>
                        <input type="email" class="form-control" name="setting_admin_email" 
                               value="<?= htmlspecialchars($settings['admin_email'] ?? '<EMAIL>') ?>">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">货币代码</label>
                                <input type="text" class="form-control" name="setting_currency" 
                                       value="<?= htmlspecialchars($settings['currency'] ?? 'CNY') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">货币符号</label>
                                <input type="text" class="form-control" name="setting_currency_symbol" 
                                       value="<?= htmlspecialchars($settings['currency_symbol'] ?? '¥') ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">域名价格加成比例</label>
                        <input type="number" class="form-control" name="setting_domain_markup" 
                               step="0.01" min="0" max="1"
                               value="<?= htmlspecialchars($settings['domain_markup'] ?? '0.15') ?>">
                        <div class="form-text">0.15 表示在成本基础上加价15%</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="setting_enable_registration"
                                   value="1" <?= ($settings['enable_registration'] ?? '1') === '1' ? 'checked' : '' ?>>
                            <label class="form-check-label">允许用户注册</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="setting_email_verification_required"
                                   value="1" <?= ($settings['email_verification_required'] ?? '1') === '1' ? 'checked' : '' ?>>
                            <label class="form-check-label">需要邮箱验证</label>
                            <div class="form-text">关闭后，用户注册后可以直接登录，无需验证邮箱</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="setting_enable_cart"
                                   value="1" <?= ($settings['enable_cart'] ?? '1') === '1' ? 'checked' : '' ?>>
                            <label class="form-check-label">启用购物车功能</label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- API设置 -->
    <div class="col-lg-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-plug"></i>
                    API设置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <div class="mb-3">
                        <label class="form-label">NameSilo API密钥</label>
                        <input type="password" class="form-control" name="setting_namesilo_api_key" 
                               value="<?= htmlspecialchars($settings['namesilo_api_key'] ?? '') ?>"
                               placeholder="输入您的NameSilo API密钥">
                        <div class="form-text">
                            <a href="https://www.namesilo.com/account/api-manager" target="_blank">
                                获取NameSilo API密钥
                            </a>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">NameSilo API URL</label>
                        <input type="url" class="form-control" name="setting_namesilo_api_url" 
                               value="<?= htmlspecialchars($settings['namesilo_api_url'] ?? 'https://www.namesilo.com/api') ?>">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="setting_namesilo_sandbox" 
                                   value="1" <?= ($settings['namesilo_sandbox'] ?? '0') === '1' ? 'checked' : '' ?>>
                            <label class="form-check-label">启用沙盒模式</label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存API设置
                    </button>
                </form>
            </div>
        </div>
        
        <!-- 邮件设置 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-envelope"></i>
                    邮件设置
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <div class="mb-3">
                        <label class="form-label">SMTP服务器</label>
                        <input type="text" class="form-control" name="setting_mail_host" 
                               value="<?= htmlspecialchars($settings['mail_host'] ?? 'smtp.gmail.com') ?>">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">SMTP端口</label>
                                <input type="number" class="form-control" name="setting_mail_port" 
                                       value="<?= htmlspecialchars($settings['mail_port'] ?? '587') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">加密方式</label>
                                <select class="form-select" name="setting_mail_encryption">
                                    <option value="tls" <?= ($settings['mail_encryption'] ?? 'tls') === 'tls' ? 'selected' : '' ?>>TLS</option>
                                    <option value="ssl" <?= ($settings['mail_encryption'] ?? 'tls') === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">SMTP用户名</label>
                        <input type="text" class="form-control" name="setting_mail_username" 
                               value="<?= htmlspecialchars($settings['mail_username'] ?? '') ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">SMTP密码</label>
                        <input type="password" class="form-control" name="setting_mail_password" 
                               value="<?= htmlspecialchars($settings['mail_password'] ?? '') ?>">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存邮件设置
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 域名价格设置 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title">
            <i class="fas fa-dollar-sign"></i>
            域名价格设置
        </h5>
        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTldModal">
            <i class="fas fa-plus"></i>
            添加后缀
        </button>
    </div>
    <div class="card-body">
        <form method="POST">
            <input type="hidden" name="action" value="update_prices">
            
            <div class="row">
                <?php foreach ($domainPrices as $price): ?>
                    <div class="col-md-4 mb-3">
                        <div class="input-group">
                            <span class="input-group-text"><?= htmlspecialchars($price['tld']) ?></span>
                            <input type="number" class="form-control" 
                                   name="price_<?= htmlspecialchars($price['tld']) ?>" 
                                   value="<?= $price['registration_price'] ?>" 
                                   step="0.01" min="0">
                            <span class="input-group-text">¥</span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i>
                更新价格
            </button>
        </form>
    </div>
</div>

<!-- 添加域名后缀模态框 -->
<div class="modal fade" id="addTldModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加域名后缀</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_tld">
                    <div class="mb-3">
                        <label class="form-label">域名后缀</label>
                        <input type="text" class="form-control" name="new_tld" placeholder="com" required>
                        <div class="form-text">不需要输入点号，系统会自动添加</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">价格 (¥)</label>
                        <input type="number" class="form-control" name="new_price" step="0.01" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
