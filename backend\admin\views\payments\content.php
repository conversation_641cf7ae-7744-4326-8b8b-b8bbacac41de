<?php
// 支付配置页面内容
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">支付配置</h1>
        <p class="text-muted">配置支付网关和支付方式</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-info" onclick="testPayment()">
            <i class="fas fa-vial me-2"></i>
            测试支付
        </button>
        <button type="button" class="btn btn-outline-success" onclick="syncPaymentMethods()">
            <i class="fas fa-sync-alt me-2"></i>
            同步支付方式
        </button>
    </div>
</div>

<!-- 支付网关状态 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="payment-gateway-icon mb-3">
                    <i class="fab fa-paypal fa-3x text-primary"></i>
                </div>
                <h5>PayPal</h5>
                <span class="badge bg-secondary">未配置</span>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="configurePayPal()">
                        <i class="fas fa-cog"></i> 配置
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="payment-gateway-icon mb-3">
                    <i class="fab fa-stripe fa-3x text-info"></i>
                </div>
                <h5>Stripe</h5>
                <span class="badge bg-secondary">未配置</span>
                <div class="mt-3">
                    <button class="btn btn-outline-info btn-sm" onclick="configureStripe()">
                        <i class="fas fa-cog"></i> 配置
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="payment-gateway-icon mb-3">
                    <i class="fab fa-alipay fa-3x text-success"></i>
                </div>
                <h5>支付宝</h5>
                <span id="alipayStatus" class="badge bg-secondary">检查中...</span>
                <div id="alipayInfo" class="mt-2 small text-muted"></div>
                <div class="mt-3">
                    <button class="btn btn-outline-success btn-sm" onclick="configureAlipay()">
                        <i class="fas fa-cog"></i> 配置
                    </button>
                    <button id="alipayTestBtn" class="btn btn-outline-info btn-sm ms-1" onclick="quickTestAlipay()" style="display: none;">
                        <i class="fas fa-vial"></i> 测试
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="payment-gateway-icon mb-3">
                    <i class="fab fa-weixin fa-3x text-warning"></i>
                </div>
                <h5>微信支付</h5>
                <span class="badge bg-secondary">未配置</span>
                <div class="mt-3">
                    <button class="btn btn-outline-warning btn-sm" onclick="configureWechat()">
                        <i class="fas fa-cog"></i> 配置
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支付设置 -->
<div class="row g-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    支付设置
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    支付功能正在开发中，敬请期待完整的支付网关集成...
                </div>
                
                <form>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">默认货币</label>
                            <select class="form-select" disabled>
                                <option value="USD">美元 (USD)</option>
                                <option value="CNY">人民币 (CNY)</option>
                                <option value="EUR">欧元 (EUR)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">支付超时时间（分钟）</label>
                            <input type="number" class="form-control" value="30" disabled>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoRefund" disabled>
                                <label class="form-check-label" for="autoRefund">
                                    启用自动退款
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emailNotification" disabled>
                                <label class="form-check-label" for="emailNotification">
                                    支付成功后发送邮件通知
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="button" class="btn btn-primary" disabled>
                            <i class="fas fa-save me-2"></i>
                            保存设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    支付统计
                </h5>
            </div>
            <div class="card-body">
                <div class="payment-stat mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">今日交易</span>
                        <span class="fw-bold">$0.00</span>
                    </div>
                </div>
                <div class="payment-stat mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">本月交易</span>
                        <span class="fw-bold">$0.00</span>
                    </div>
                </div>
                <div class="payment-stat mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">总交易额</span>
                        <span class="fw-bold">$0.00</span>
                    </div>
                </div>
                <div class="payment-stat">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">成功率</span>
                        <span class="fw-bold text-success">0%</span>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewPaymentReports()">
                        <i class="fas fa-chart-bar me-2"></i>
                        查看详细报表
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>
                    开发工具
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="generateTestData()">
                        <i class="fas fa-database me-2"></i>
                        生成测试数据
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="viewWebhookLogs()">
                        <i class="fas fa-webhook me-2"></i>
                        Webhook日志
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearPaymentCache()">
                        <i class="fas fa-trash me-2"></i>
                        清理缓存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支付方式配置模态框 -->
<div class="modal fade" id="paymentConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog me-2"></i>
                    <span id="modalTitle">支付网关配置</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    支付网关配置功能正在开发中...
                </div>
                
                <form id="paymentConfigForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">网关名称</label>
                            <input type="text" class="form-control" id="gatewayName" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">API密钥</label>
                            <input type="password" class="form-control" placeholder="输入API密钥" disabled>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">API密码</label>
                            <input type="password" class="form-control" placeholder="输入API密码" disabled>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Webhook URL</label>
                            <input type="url" class="form-control" placeholder="https://yourdomain.com/webhook" disabled>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableSandbox" disabled>
                                <label class="form-check-label" for="enableSandbox">
                                    启用沙盒模式
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableGateway" disabled>
                                <label class="form-check-label" for="enableGateway">
                                    启用此支付网关
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" disabled>
                    <i class="fas fa-save me-2"></i>
                    保存配置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 支付宝专用配置模态框 -->
<div class="modal fade" id="alipayConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fab fa-alipay me-2 text-primary"></i>
                    支付宝配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>配置说明：</strong>支持合作伙伴身份(PID+MD5)和开放平台(RSA)两种接入方式
                </div>

                <form id="alipayConfigForm">
                    <!-- 签名方式选择 -->
                    <div class="mb-3">
                        <label class="form-label">签名方式</label>
                        <select class="form-select" id="alipaySignType" onchange="toggleAlipaySignType()">
                            <option value="MD5">MD5 (合作伙伴身份)</option>
                            <option value="RSA2">RSA2 (开放平台)</option>
                        </select>
                    </div>

                    <!-- 合作伙伴身份配置 -->
                    <div id="alipayPartnerConfig">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-handshake me-2"></i>合作伙伴身份配置
                        </h6>

                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">合作伙伴身份 (PID) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="alipayPartnerId"
                                       placeholder="2088532725053770" value="2088532725053770">
                                <div class="form-text">16位数字，如：2088532725053770</div>
                            </div>

                            <div class="col-12">
                                <label class="form-label">MD5密钥 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="alipayMd5Key"
                                       placeholder="32位MD5密钥">
                                <div class="form-text">32位字符串，在支付宝商户平台获取</div>
                            </div>

                            <div class="col-12">
                                <label class="form-label">收款邮箱</label>
                                <input type="email" class="form-control" id="alipaySellerEmail"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>
                    </div>

                    <!-- 开放平台配置 -->
                    <div id="alipayOpenConfig" style="display: none;">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-key me-2"></i>开放平台配置
                        </h6>

                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">应用ID (App ID) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="alipayAppId"
                                       placeholder="2021001234567890">
                            </div>

                            <div class="col-12">
                                <label class="form-label">应用私钥 (RSA Private Key) <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="alipayPrivateKey" rows="4"
                                          placeholder="-----BEGIN RSA PRIVATE KEY-----"></textarea>
                            </div>

                            <div class="col-12">
                                <label class="form-label">支付宝公钥 (RSA Public Key) <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="alipayPublicKey" rows="4"
                                          placeholder="-----BEGIN PUBLIC KEY-----"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 通用配置 -->
                    <hr class="my-4">
                    <h6 class="text-secondary mb-3">
                        <i class="fas fa-cog me-2"></i>通用配置
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">环境选择</label>
                            <select class="form-select" id="alipayEnvironment" onchange="updateAlipayGateway()">
                                <option value="production">正式环境</option>
                                <option value="sandbox">沙箱环境</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">网关地址</label>
                            <input type="url" class="form-control" id="alipayGateway"
                                   value="https://mapi.alipay.com/gateway.do" readonly>
                        </div>

                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="alipayEnabled">
                                <label class="form-check-label" for="alipayEnabled">
                                    启用支付宝支付
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-outline-success" onclick="testAlipayConnection()">
                    <i class="fas fa-vial me-2"></i>测试配置
                </button>
                <button type="button" class="btn btn-primary" onclick="saveAlipayConfig()">
                    <i class="fas fa-save me-2"></i>保存配置
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function testPayment() {
    alert('支付测试功能开发中...');
}

function syncPaymentMethods() {
    alert('同步支付方式功能开发中...');
}

function configurePayPal() {
    showPaymentConfig('PayPal');
}

function configureStripe() {
    showPaymentConfig('Stripe');
}

function configureAlipay() {
    showAlipayConfig();
}

function configureWechat() {
    showPaymentConfig('微信支付');
}

function showPaymentConfig(gatewayName) {
    document.getElementById('modalTitle').textContent = gatewayName + ' 配置';
    document.getElementById('gatewayName').value = gatewayName;
    
    const modal = new bootstrap.Modal(document.getElementById('paymentConfigModal'));
    modal.show();
}

function viewPaymentReports() {
    alert('支付报表功能开发中...');
}

function generateTestData() {
    alert('生成测试数据功能开发中...');
}

function viewWebhookLogs() {
    alert('Webhook日志功能开发中...');
}

function clearPaymentCache() {
    if (confirm('确定要清理支付缓存吗？')) {
        alert('清理缓存功能开发中...');
    }
}

// 显示支付宝配置模态框
function showAlipayConfig() {
    console.log('显示支付宝配置模态框');

    // 检查模态框元素是否存在
    const modalElement = document.getElementById('alipayConfigModal');
    if (!modalElement) {
        console.error('支付宝配置模态框元素未找到');
        alert('支付宝配置模态框未找到，请刷新页面重试');
        return;
    }

    // 加载当前配置
    loadAlipayConfig();

    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// 切换支付宝签名方式
function toggleAlipaySignType() {
    const signType = document.getElementById('alipaySignType').value;
    const partnerConfig = document.getElementById('alipayPartnerConfig');
    const openConfig = document.getElementById('alipayOpenConfig');

    if (signType === 'MD5') {
        partnerConfig.style.display = 'block';
        openConfig.style.display = 'none';
    } else {
        partnerConfig.style.display = 'none';
        openConfig.style.display = 'block';
    }

    updateAlipayGateway();
}

// 更新支付宝网关地址
function updateAlipayGateway() {
    const signType = document.getElementById('alipaySignType').value;
    const environment = document.getElementById('alipayEnvironment').value;
    const gateway = document.getElementById('alipayGateway');

    if (signType === 'MD5') {
        if (environment === 'sandbox') {
            gateway.value = 'https://openapi.alipaydev.com/gateway.do';
        } else {
            gateway.value = 'https://mapi.alipay.com/gateway.do';
        }
    } else {
        if (environment === 'sandbox') {
            gateway.value = 'https://openapi.alipaydev.com/gateway.do';
        } else {
            gateway.value = 'https://openapi.alipay.com/gateway.do';
        }
    }
}

// 加载支付宝配置
function loadAlipayConfig() {
    fetch('/api/payment-config.php?method=alipay')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.config) {
                const config = data.config;

                // 设置签名方式
                document.getElementById('alipaySignType').value = config.sign_type || 'MD5';
                toggleAlipaySignType();

                // 合作伙伴身份配置
                if (config.partner_id) document.getElementById('alipayPartnerId').value = config.partner_id;
                if (config.md5_key) document.getElementById('alipayMd5Key').value = config.md5_key;
                if (config.seller_email) document.getElementById('alipaySellerEmail').value = config.seller_email;

                // 开放平台配置
                if (config.app_id) document.getElementById('alipayAppId').value = config.app_id;
                if (config.rsa_private_key) document.getElementById('alipayPrivateKey').value = config.rsa_private_key;
                if (config.rsa_public_key) document.getElementById('alipayPublicKey').value = config.rsa_public_key;

                // 通用配置
                if (config.environment) document.getElementById('alipayEnvironment').value = config.environment;
                if (config.gateway_url) document.getElementById('alipayGateway').value = config.gateway_url;

                document.getElementById('alipayEnabled').checked = data.enabled || false;
            }
        })
        .catch(error => {
            console.error('加载支付宝配置失败:', error);
        });
}

// 保存支付宝配置
function saveAlipayConfig() {
    const signType = document.getElementById('alipaySignType').value;
    const config = {
        sign_type: signType,
        gateway_url: document.getElementById('alipayGateway').value,
        environment: document.getElementById('alipayEnvironment').value,
        notify_url: '/api/payment/notify/alipay',
        return_url: '/payment/return/alipay',
        input_charset: 'utf-8',
        transport: 'https'
    };

    if (signType === 'MD5') {
        config.partner_id = document.getElementById('alipayPartnerId').value;
        config.md5_key = document.getElementById('alipayMd5Key').value;
        config.seller_email = document.getElementById('alipaySellerEmail').value;

        if (!config.partner_id || !config.md5_key) {
            alert('请填写合作伙伴身份和MD5密钥');
            return;
        }
    } else {
        config.app_id = document.getElementById('alipayAppId').value;
        config.rsa_private_key = document.getElementById('alipayPrivateKey').value;
        config.rsa_public_key = document.getElementById('alipayPublicKey').value;

        if (!config.app_id || !config.rsa_private_key || !config.rsa_public_key) {
            alert('请填写应用ID、私钥和公钥');
            return;
        }
    }

    const enabled = document.getElementById('alipayEnabled').checked;

    fetch('/api/payment-config.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            method: 'alipay',
            enabled: enabled,
            config: config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('支付宝配置保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('alipayConfigModal')).hide();
            location.reload(); // 刷新页面显示最新状态
        } else {
            alert('保存失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('保存支付宝配置失败:', error);
        alert('保存失败，请重试');
    });
}

// 测试支付宝配置
function testAlipayConnection() {
    const signType = document.getElementById('alipaySignType').value;
    const testData = { sign_type: signType };

    if (signType === 'MD5') {
        testData.partner_id = document.getElementById('alipayPartnerId').value;
        testData.md5_key = document.getElementById('alipayMd5Key').value;
    } else {
        testData.app_id = document.getElementById('alipayAppId').value;
        testData.private_key = document.getElementById('alipayPrivateKey').value;
        testData.public_key = document.getElementById('alipayPublicKey').value;
    }

    fetch('/api/payment.php?action=test-alipay', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams(testData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ 支付宝配置测试成功！\n' + data.message);
        } else {
            alert('❌ 支付宝配置测试失败：\n' + data.message);
        }
    })
    .catch(error => {
        alert('❌ 测试请求失败: ' + error.message);
    });
}

// 快速测试支付宝
function quickTestAlipay() {
    window.open('/test-alipay.php', '_blank');
}

// 检查支付宝配置状态
function checkAlipayStatus() {
    fetch('/api/payment-config.php?method=alipay')
        .then(response => response.json())
        .then(data => {
            const statusBadge = document.getElementById('alipayStatus');
            const infoDev = document.getElementById('alipayInfo');
            const testBtn = document.getElementById('alipayTestBtn');

            if (data.success && data.enabled && data.config) {
                const config = data.config;
                const signType = config.sign_type || 'MD5';

                statusBadge.className = 'badge bg-success';
                statusBadge.textContent = '已启用';

                if (signType === 'MD5') {
                    infoDev.textContent = `合作伙伴身份: ${config.partner_id || '未设置'}`;
                } else {
                    infoDev.textContent = `开放平台: ${config.app_id || '未设置'}`;
                }

                testBtn.style.display = 'inline-block';
            } else if (data.success && data.config && Object.keys(data.config).length > 0) {
                statusBadge.className = 'badge bg-warning';
                statusBadge.textContent = '已配置未启用';
                infoDev.textContent = '点击配置按钮启用';
                testBtn.style.display = 'inline-block';
            } else {
                statusBadge.className = 'badge bg-secondary';
                statusBadge.textContent = '未配置';
                infoDev.textContent = '点击配置按钮开始设置';
                testBtn.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('检查支付宝状态失败:', error);
            document.getElementById('alipayStatus').textContent = '检查失败';
        });
}

// 页面加载完成后检查状态
document.addEventListener('DOMContentLoaded', function() {
    checkAlipayStatus();
});
</script>

<style>
.payment-gateway-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-stat {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.payment-stat:last-child {
    border-bottom: none;
}
</style>
