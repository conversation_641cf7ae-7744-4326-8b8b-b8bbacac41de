<?php
// 加载系统配置和数据库
require_once 'config.php';
require_once 'includes/database.php';

// 获取真实的TLD价格
$tldPrices = [];
try {
    $pdo = getDatabase();
    $stmt = $pdo->query("SELECT tld, register_price, renew_price FROM tld_pricing WHERE is_active = 1 ORDER BY is_featured DESC, register_price ASC");
    while ($row = $stmt->fetch()) {
        $tldPrices[$row['tld']] = [
            'register' => $row['register_price'],
            'renew' => $row['renew_price']
        ];
    }
} catch (Exception $e) {
    // 如果数据库查询失败，使用默认价格
    $tldPrices = [
        '.com' => ['register' => 12.99, 'renew' => 14.99],
        '.cn' => ['register' => 8.99, 'renew' => 8.99],
        '.net' => ['register' => 14.99, 'renew' => 16.99],
        '.org' => ['register' => 13.99, 'renew' => 15.99],
        '.info' => ['register' => 8.99, 'renew' => 18.99],
        '.biz' => ['register' => 9.99, 'renew' => 19.99]
    ];
}

// 汇率转换 (USD to CNY)
$usdToCny = 7.2;
function formatPrice($usdPrice, $currency = 'CNY') {
    global $usdToCny;
    if ($currency === 'CNY') {
        return '¥' . number_format($usdPrice * $usdToCny, 0);
    }
    return '$' . number_format($usdPrice, 2);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名搜索 - NameSilo域名销售系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .extension-badge {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .extension-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .domain-result-card {
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .domain-result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .available {
            border-left: 4px solid #28a745;
        }
        .unavailable {
            border-left: 4px solid #dc3545;
        }
        .loading-spinner {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.php">
                <i class="fas fa-globe me-2"></i>NameSilo域名销售系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">首页</a>
                <a class="nav-link active" href="domain-search.php">域名搜索</a>
                <a class="nav-link" href="cart.php" id="cartLink">
                    <i class="fas fa-shopping-cart me-1"></i>
                    购物车 <span class="badge bg-primary" id="cartCount">0</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- 搜索区域 -->
    <section class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="text-center mb-4">
                        <h1 class="display-4 fw-bold mb-3">找到您的完美域名</h1>
                        <p class="lead">搜索数百万个可用域名，找到最适合您业务的域名</p>
                    </div>
                    
                    <div class="search-box">
                        <form id="domainSearchForm" class="mb-4">
                            <div class="input-group input-group-lg">
                                <input type="text" id="domainInput" class="form-control" 
                                       placeholder="输入您想要的域名..." required>
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-search me-2"></i>搜索
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center">
                            <p class="mb-2 text-muted">热门后缀：</p>
                            <div class="d-flex flex-wrap justify-content-center gap-2">
                                <?php
                                // 显示前8个最受欢迎的TLD
                                $popularTlds = array_slice(array_keys($tldPrices), 0, 8);
                                foreach ($popularTlds as $tld):
                                    $price = $tldPrices[$tld]['register'];
                                ?>
                                <span class="badge bg-light text-dark extension-badge" data-ext="<?php echo $tld; ?>" title="注册价格: <?php echo formatPrice($price); ?>">
                                    <?php echo $tld; ?> <small>(<?php echo formatPrice($price); ?>)</small>
                                </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 搜索结果区域 -->
    <section class="py-5">
        <div class="container">
            <!-- 加载状态 -->
            <div id="searchLoading" class="text-center loading-spinner">
                <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">搜索中...</span>
                </div>
                <p class="mt-3 text-muted">正在搜索域名，请稍候...</p>
            </div>

            <!-- 搜索结果 -->
            <div id="searchResults" style="display: none;">
                <div class="row">
                    <div class="col-12">
                        <h3 class="mb-4">搜索结果</h3>
                        <div id="searchResultsContainer">
                            <!-- 搜索结果将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索建议 -->
            <div id="searchSuggestions" style="display: none;">
                <div class="row">
                    <div class="col-12">
                        <h4 class="mb-4">您可能还喜欢</h4>
                        <div id="suggestionsContainer">
                            <!-- 搜索建议将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 域名价格说明 -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h3 class="mb-4">域名价格说明</h3>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle text-primary fa-3x mb-3"></i>
                                    <h5>注册</h5>
                                    <p class="text-muted">首次注册域名的价格</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-redo text-success fa-3x mb-3"></i>
                                    <h5>续费</h5>
                                    <p class="text-muted">域名到期后续费的价格</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-exchange-alt text-info fa-3x mb-3"></i>
                                    <h5>转移</h5>
                                    <p class="text-muted">从其他注册商转移域名的价格</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // 域名搜索功能
        document.getElementById('domainSearchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            searchDomain();
        });

        // 热门后缀点击事件
        document.querySelectorAll('.extension-badge').forEach(badge => {
            badge.addEventListener('click', function() {
                const domainInput = document.getElementById('domainInput');
                const currentValue = domainInput.value.trim();
                const extension = this.dataset.ext;
                
                if (currentValue && !currentValue.includes('.')) {
                    domainInput.value = currentValue + extension;
                    searchDomain();
                } else if (!currentValue) {
                    domainInput.focus();
                    showAlert('请先输入域名前缀', 'warning');
                }
            });
        });

        // 搜索域名
        function searchDomain() {
            const domainInput = document.getElementById('domainInput');
            const domain = domainInput.value.trim();

            if (!domain) {
                showAlert('请输入要搜索的域名', 'warning');
                return;
            }

            // 验证域名格式
            const domainName = domain.includes('.') ? domain.split('.')[0] : domain;
            if (!/^[a-zA-Z0-9\-]+$/.test(domainName)) {
                showAlert('域名只能包含字母、数字和连字符', 'warning');
                return;
            }

            // 显示加载状态
            const searchLoading = document.getElementById('searchLoading');
            const searchResults = document.getElementById('searchResults');
            const searchSuggestions = document.getElementById('searchSuggestions');
            
            searchLoading.style.display = 'block';
            searchResults.style.display = 'none';
            searchSuggestions.style.display = 'none';

            // 搜索多个常用后缀
            const extensions = '<?php echo implode(",", array_keys($tldPrices)); ?>';
            
            fetch(`api/domain-search.php?domain=${encodeURIComponent(domainName)}&extensions=${encodeURIComponent(extensions)}`)
                .then(response => response.json())
                .then(data => {
                    searchLoading.style.display = 'none';
                    
                    if (data.success) {
                        displaySearchResults(data.results, domainName);
                        searchResults.style.display = 'block';
                        
                        // 获取域名建议
                        return fetch(`api/domain-search.php?action=suggestions&keyword=${encodeURIComponent(domainName)}&limit=6`);
                    } else {
                        throw new Error(data.message);
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.suggestions.length > 0) {
                        displaySuggestions(data.suggestions);
                        searchSuggestions.style.display = 'block';
                    }
                })
                .catch(error => {
                    searchLoading.style.display = 'none';
                    showAlert('搜索失败：' + error.message, 'danger');
                });
        }

        // 显示搜索结果
        function displaySearchResults(results, searchTerm) {
            const resultsContainer = document.getElementById('searchResultsContainer');
            
            if (!results || results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        没有找到相关域名
                    </div>
                `;
                return;
            }

            let html = `
                <div class="mb-3">
                    <p class="text-muted">为 "<strong>${searchTerm}</strong>" 找到 ${results.length} 个结果</p>
                </div>
                <div class="row">
            `;

            results.forEach(result => {
                const availableClass = result.available ? 'available' : 'unavailable';
                const statusClass = result.available ? 'success' : 'secondary';
                const statusText = result.available ? '可注册' : '不可用';
                const statusIcon = result.available ? 'check-circle' : 'times-circle';
                const price = result.available && result.price ? parseFloat(result.price) : 0;
                const priceText = result.available && price > 0 ? `$${price.toFixed(2)}` : '查询价格';
                
                html += `
                    <div class="col-lg-6 mb-3">
                        <div class="card domain-result-card ${availableClass} h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="mb-0">${result.domain}</h5>
                                    <span class="badge bg-${statusClass}">
                                        <i class="fas fa-${statusIcon} me-1"></i>
                                        ${statusText}
                                    </span>
                                </div>
                                
                                ${result.available ? `
                                    <div class="row align-items-center">
                                        <div class="col-6">
                                            <div class="text-muted small">注册价格</div>
                                            <div class="h5 text-primary mb-0">${priceText}</div>
                                        </div>
                                        <div class="col-6 text-end">
                                            <button class="btn btn-primary" onclick="addToCart('${result.domain}', 'register', 1, ${price})">
                                                <i class="fas fa-cart-plus me-1"></i>
                                                加入购物车
                                            </button>
                                        </div>
                                    </div>
                                ` : `
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        ${result.reason || '域名已被注册'}
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            resultsContainer.innerHTML = html;
        }

        // 显示域名建议
        function displaySuggestions(suggestions) {
            const suggestionsContainer = document.getElementById('suggestionsContainer');
            
            let html = '<div class="row">';
            
            suggestions.forEach(suggestion => {
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6 class="mb-2">${suggestion}</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="searchSpecificDomain('${suggestion}')">
                                    <i class="fas fa-search me-1"></i>
                                    检查可用性
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            suggestionsContainer.innerHTML = html;
        }

        // 搜索特定域名
        function searchSpecificDomain(domain) {
            document.getElementById('domainInput').value = domain;
            searchDomain();
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            const container = document.querySelector('.container');
            if (container) {
                container.insertAdjacentHTML('afterbegin', alertHtml);
                
                setTimeout(() => {
                    const alert = container.querySelector('.alert');
                    if (alert) {
                        alert.remove();
                    }
                }, 3000);
            }
        }

        // 加入购物车
        function addToCart(domain, type, period, price) {
            fetch('api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    domain: domain,
                    type: type,
                    period: period,
                    price: price
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`${domain} 已加入购物车`, 'success');
                    loadCartCount();
                } else {
                    showAlert(data.message || '加入购物车失败', 'danger');
                }
            })
            .catch(error => {
                showAlert('加入购物车失败：' + error.message, 'danger');
            });
        }

        // 加载购物车数量
        function loadCartCount() {
            fetch('api/cart.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        document.getElementById('cartCount').textContent = data.data.count || 0;
                    }
                })
                .catch(error => {
                    console.error('加载购物车数量失败:', error);
                });
        }



        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCartCount();

            // 绑定搜索表单事件
            const searchForm = document.getElementById('domainSearchForm');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    searchDomain();
                });
            }

            // 绑定扩展名点击事件
            document.querySelectorAll('.extension-badge').forEach(badge => {
                badge.addEventListener('click', function() {
                    const ext = this.getAttribute('data-ext');
                    const domainInput = document.getElementById('domainInput');
                    const currentValue = domainInput.value.trim();

                    if (currentValue && !currentValue.includes('.')) {
                        domainInput.value = currentValue + ext;
                        searchDomain();
                    }
                });
            });

            // 检查URL参数，如果有查询参数则自动搜索
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            if (query) {
                document.getElementById('domainInput').value = query;
                searchDomain();
            }
        });
    </script>
</body>
</html>
