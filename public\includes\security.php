<?php
/**
 * 安全工具类
 */

class Security {
    public static function escape($string, $flags = ENT_QUOTES, $encoding = 'UTF-8') {
        return htmlspecialchars($string, $flags, $encoding);
    }
    
    public static function cleanInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'cleanInput'], $input);
        }
        return trim(strip_tags($input));
    }
    
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
}
?>