<?php
// 防止直接访问
if (!defined('ADMIN_PATH')) {
    header('Location: ../../index.php?page=settings');
    exit('Direct access not allowed');
}

// 初始化变量
$message = '';
$error = '';
$usingDatabase = false;

try {
    // 加载必要的函数
    require_once ADMIN_PATH . '/includes/functions.php';
    $db = safeGetDatabase();

    // 检查system_settings表是否存在
    $tablesCheck = $db->query("SHOW TABLES LIKE 'system_settings'")->fetchAll();

    if (empty($tablesCheck)) {
        throw new Exception("系统设置表不存在");
    }

    $usingDatabase = true;

    // 处理设置更新
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';

        switch ($action) {
        case 'update_basic':
            $settings = [
                'site_name' => $_POST['site_name'] ?? '',
                'site_description' => $_POST['site_description'] ?? '',
                'admin_email' => $_POST['admin_email'] ?? '',
                'timezone' => $_POST['timezone'] ?? 'UTC',
                'language' => $_POST['language'] ?? 'zh-CN',
                'maintenance_mode' => isset($_POST['maintenance_mode']) ? '1' : '0',
                'email_verification_required' => isset($_POST['email_verification_required']) ? '1' : '0'
            ];
            
            foreach ($settings as $key => $value) {
                // 确定数据类型
                $type = in_array($key, ['maintenance_mode', 'email_verification_required']) ? 'boolean' : 'string';
                $group = in_array($key, ['email_verification_required']) ? 'auth' : 'general';

                $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`, `group`) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `type` = VALUES(`type`), `group` = VALUES(`group`)");
                $stmt->execute([$key, $value, $type, $group]);
            }
            $message = '基本设置已更新';
            break;
            
        case 'update_email':
            $emailSettings = [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'email_from_name' => $_POST['email_from_name'] ?? '',
                'email_from_address' => $_POST['email_from_address'] ?? ''
            ];
            
            foreach ($emailSettings as $key => $value) {
                $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`, `group`) VALUES (?, ?, 'string', 'email') ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)");
                $stmt->execute([$key, $value]);
            }
            $message = '邮件设置已更新';
            break;
            
        case 'update_security':
            $securitySettings = [
                'max_login_attempts' => $_POST['max_login_attempts'] ?? '5',
                'session_lifetime' => $_POST['session_lifetime'] ?? '120',
                'password_min_length' => $_POST['password_min_length'] ?? '8',
                'enable_two_factor' => isset($_POST['enable_two_factor']) ? '1' : '0',
                'enable_captcha' => isset($_POST['enable_captcha']) ? '1' : '0'
            ];

            foreach ($securitySettings as $key => $value) {
                $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`, `group`) VALUES (?, ?, 'string', 'security') ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)");
                $stmt->execute([$key, $value]);
            }
            $message = '安全设置已更新';
            break;

        case 'update_domain_providers':
            $domainSettings = [
                'namesilo_api_key' => $_POST['namesilo_api_key'] ?? '',
                'namesilo_sandbox' => isset($_POST['namesilo_sandbox']) ? '1' : '0',
                'default_domain_provider' => $_POST['default_domain_provider'] ?? 'namesilo',
                'domain_auto_renew' => isset($_POST['domain_auto_renew']) ? '1' : '0',
                'domain_privacy_protection' => isset($_POST['domain_privacy_protection']) ? '1' : '0'
            ];

            foreach ($domainSettings as $key => $value) {
                $stmt = $db->prepare("INSERT INTO system_settings (`key`, `value`, `type`, `group`) VALUES (?, ?, 'string', 'domain') ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)");
                $stmt->execute([$key, $value]);
            }

            // 同时更新.env文件
            $envPath = ROOT_PATH . '/.env';
            $envContent = '';
            if (file_exists($envPath)) {
                $envContent = file_get_contents($envPath);
            }

            // 更新或添加 NameSilo 配置
            $newConfig = [
                'NAMESILO_API_KEY' => $domainSettings['namesilo_api_key'],
                'NAMESILO_API_URL' => 'https://www.namesilo.com/api',
                'NAMESILO_SANDBOX' => $domainSettings['namesilo_sandbox'] === '1' ? 'true' : 'false'
            ];

            foreach ($newConfig as $key => $value) {
                if (preg_match("/^{$key}=.*$/m", $envContent)) {
                    $envContent = preg_replace("/^{$key}=.*$/m", "{$key}={$value}", $envContent);
                } else {
                    $envContent .= "\n{$key}={$value}";
                }
            }

            file_put_contents($envPath, $envContent);
            $message = '域名商设置已更新';
            break;
        }
    }
} catch (Exception $e) {
    $error = '设置更新失败: ' . $e->getMessage();
}

// 获取当前设置
function getSetting($key, $default = '') {
    global $db, $usingDatabase;

    if (!$usingDatabase) {
        return $default;
    }

    try {
        $stmt = $db->prepare("SELECT `value` FROM system_settings WHERE `key` = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// 基本设置
$basicSettings = [
    'site_name' => getSetting('site_name', 'NameSilo域名销售系统'),
    'site_description' => getSetting('site_description', '专业的域名销售和管理平台'),
    'admin_email' => getSetting('admin_email', '<EMAIL>'),
    'timezone' => getSetting('timezone', 'UTC'),
    'language' => getSetting('language', 'zh-CN'),
    'maintenance_mode' => getSetting('maintenance_mode', '0'),
    'email_verification_required' => getSetting('email_verification_required', '1')
];

// 邮件设置
$emailSettings = [
    'smtp_host' => getSetting('smtp_host'),
    'smtp_port' => getSetting('smtp_port', '587'),
    'smtp_username' => getSetting('smtp_username'),
    'smtp_password' => getSetting('smtp_password'),
    'smtp_encryption' => getSetting('smtp_encryption', 'tls'),
    'email_from_name' => getSetting('email_from_name'),
    'email_from_address' => getSetting('email_from_address')
];

// 安全设置
$securitySettings = [
    'max_login_attempts' => getSetting('max_login_attempts', '5'),
    'session_lifetime' => getSetting('session_lifetime', '120'),
    'password_min_length' => getSetting('password_min_length', '8'),
    'enable_two_factor' => getSetting('enable_two_factor', '0'),
    'enable_captcha' => getSetting('enable_captcha', '0')
];

// 域名商设置
$domainSettings = [
    'namesilo_api_key' => getSetting('namesilo_api_key'),
    'namesilo_sandbox' => getSetting('namesilo_sandbox', '0'),
    'default_domain_provider' => getSetting('default_domain_provider', 'namesilo'),
    'domain_auto_renew' => getSetting('domain_auto_renew', '1'),
    'domain_privacy_protection' => getSetting('domain_privacy_protection', '1')
];
?>

<!-- 消息提示 -->
<?php if ($message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!$usingDatabase): ?>
<div class="alert alert-warning" role="alert">
    <i class="fas fa-database me-2"></i>
    <strong>数据库未配置</strong> - 系统设置表不存在或数据库连接失败。设置将使用默认值。
    <a href="init-database.php" class="alert-link">点击这里初始化数据库</a>
</div>
<?php endif; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">系统设置</h1>
        <p class="text-muted">配置系统参数和功能</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-warning" onclick="backupSettings()">
            <i class="fas fa-download me-2"></i>
            备份设置
        </button>
        <button type="button" class="btn btn-outline-info" onclick="restoreSettings()">
            <i class="fas fa-upload me-2"></i>
            恢复设置
        </button>
    </div>
</div>

<!-- 设置选项卡 -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                    <i class="fas fa-cog me-2"></i>
                    基本设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                    <i class="fas fa-envelope me-2"></i>
                    邮件设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                    <i class="fas fa-shield-alt me-2"></i>
                    安全设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="domain-tab" data-bs-toggle="tab" data-bs-target="#domain" type="button" role="tab">
                    <i class="fas fa-globe me-2"></i>
                    域名商设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                    <i class="fas fa-credit-card me-2"></i>
                    支付设置
                </button>
            </li>
        </ul>
    </div>
    
    <div class="card-body">
        <div class="tab-content" id="settingsTabContent">
            <!-- 基本设置 -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <form method="POST">
                    <input type="hidden" name="action" value="update_basic">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">网站名称</label>
                            <input type="text" class="form-control" name="site_name" value="<?= htmlspecialchars($basicSettings['site_name']) ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">管理员邮箱</label>
                            <input type="email" class="form-control" name="admin_email" value="<?= htmlspecialchars($basicSettings['admin_email']) ?>" required>
                        </div>
                        <div class="col-12">
                            <label class="form-label">网站描述</label>
                            <textarea class="form-control" name="site_description" rows="3"><?= htmlspecialchars($basicSettings['site_description']) ?></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">时区</label>
                            <select class="form-select" name="timezone">
                                <option value="UTC" <?= $basicSettings['timezone'] === 'UTC' ? 'selected' : '' ?>>UTC</option>
                                <option value="Asia/Shanghai" <?= $basicSettings['timezone'] === 'Asia/Shanghai' ? 'selected' : '' ?>>Asia/Shanghai</option>
                                <option value="America/New_York" <?= $basicSettings['timezone'] === 'America/New_York' ? 'selected' : '' ?>>America/New_York</option>
                                <option value="Europe/London" <?= $basicSettings['timezone'] === 'Europe/London' ? 'selected' : '' ?>>Europe/London</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">语言</label>
                            <select class="form-select" name="language">
                                <option value="zh-CN" <?= $basicSettings['language'] === 'zh-CN' ? 'selected' : '' ?>>简体中文</option>
                                <option value="en-US" <?= $basicSettings['language'] === 'en-US' ? 'selected' : '' ?>>English</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="email_verification_required" id="email_verification_required" <?= $basicSettings['email_verification_required'] === '1' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="email_verification_required">
                                    需要邮箱验证
                                    <small class="text-muted d-block">关闭后，用户注册后可以直接登录，无需验证邮箱</small>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenance_mode" <?= $basicSettings['maintenance_mode'] === '1' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="maintenance_mode">
                                    启用维护模式
                                    <small class="text-muted d-block">启用后，普通用户将无法访问网站</small>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存基本设置
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 邮件设置 -->
            <div class="tab-pane fade" id="email" role="tabpanel">
                <form method="POST">
                    <input type="hidden" name="action" value="update_email">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">SMTP服务器</label>
                            <input type="text" class="form-control" name="smtp_host" value="<?= htmlspecialchars($emailSettings['smtp_host']) ?>" placeholder="smtp.gmail.com">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">SMTP端口</label>
                            <input type="number" class="form-control" name="smtp_port" value="<?= htmlspecialchars($emailSettings['smtp_port']) ?>" placeholder="587">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">SMTP用户名</label>
                            <input type="text" class="form-control" name="smtp_username" value="<?= htmlspecialchars($emailSettings['smtp_username']) ?>">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">SMTP密码</label>
                            <input type="password" class="form-control" name="smtp_password" value="<?= htmlspecialchars($emailSettings['smtp_password']) ?>">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">加密方式</label>
                            <select class="form-select" name="smtp_encryption">
                                <option value="tls" <?= $emailSettings['smtp_encryption'] === 'tls' ? 'selected' : '' ?>>TLS</option>
                                <option value="ssl" <?= $emailSettings['smtp_encryption'] === 'ssl' ? 'selected' : '' ?>>SSL</option>
                                <option value="none" <?= $emailSettings['smtp_encryption'] === 'none' ? 'selected' : '' ?>>无加密</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">发件人名称</label>
                            <input type="text" class="form-control" name="email_from_name" value="<?= htmlspecialchars($emailSettings['email_from_name']) ?>" placeholder="NameSilo域名销售">
                        </div>
                        <div class="col-12">
                            <label class="form-label">发件人邮箱</label>
                            <input type="email" class="form-control" name="email_from_address" value="<?= htmlspecialchars($emailSettings['email_from_address']) ?>" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存邮件设置
                        </button>
                        <button type="button" class="btn btn-outline-info ms-2" onclick="testEmail()">
                            <i class="fas fa-paper-plane me-2"></i>
                            测试邮件发送
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 安全设置 -->
            <div class="tab-pane fade" id="security" role="tabpanel">
                <form method="POST">
                    <input type="hidden" name="action" value="update_security">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">最大登录尝试次数</label>
                            <input type="number" class="form-control" name="max_login_attempts" value="<?= htmlspecialchars($securitySettings['max_login_attempts']) ?>" min="1" max="10">
                            <small class="text-muted">超过此次数将锁定账户</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">会话生存时间（分钟）</label>
                            <input type="number" class="form-control" name="session_lifetime" value="<?= htmlspecialchars($securitySettings['session_lifetime']) ?>" min="30" max="1440">
                            <small class="text-muted">用户无操作后自动退出的时间</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">密码最小长度</label>
                            <input type="number" class="form-control" name="password_min_length" value="<?= htmlspecialchars($securitySettings['password_min_length']) ?>" min="6" max="20">
                        </div>
                        <div class="col-12">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="enable_two_factor" id="enable_two_factor" <?= $securitySettings['enable_two_factor'] === '1' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="enable_two_factor">
                                    启用双因素认证
                                    <small class="text-muted d-block">为管理员账户启用2FA验证</small>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_captcha" id="enable_captcha" <?= $securitySettings['enable_captcha'] === '1' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="enable_captcha">
                                    启用验证码
                                    <small class="text-muted d-block">在登录和注册时显示验证码</small>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存安全设置
                        </button>
                    </div>
                </form>
            </div>

            <!-- 域名商设置 -->
            <div class="tab-pane fade" id="domain" role="tabpanel">
                <form method="POST">
                    <input type="hidden" name="action" value="update_domain_providers">

                    <!-- NameSilo 配置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-globe me-2"></i>
                                NameSilo 配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label class="form-label">API密钥</label>
                                    <input type="text" class="form-control" name="namesilo_api_key"
                                           value="<?= htmlspecialchars($domainSettings['namesilo_api_key']) ?>"
                                           placeholder="输入您的NameSilo API密钥">
                                    <small class="text-muted">
                                        在 <a href="https://www.namesilo.com/account/api-manager" target="_blank">NameSilo API管理页面</a> 获取API密钥
                                    </small>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="namesilo_sandbox"
                                               id="namesilo_sandbox" <?= $domainSettings['namesilo_sandbox'] === '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="namesilo_sandbox">
                                            启用沙盒模式
                                            <small class="text-muted d-block">用于测试，不会产生实际费用</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 域名设置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-cog me-2"></i>
                                域名默认设置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">默认域名商</label>
                                    <select class="form-select" name="default_domain_provider">
                                        <option value="namesilo" <?= $domainSettings['default_domain_provider'] === 'namesilo' ? 'selected' : '' ?>>NameSilo</option>
                                        <option value="godaddy" <?= $domainSettings['default_domain_provider'] === 'godaddy' ? 'selected' : '' ?>>GoDaddy (即将支持)</option>
                                        <option value="namecheap" <?= $domainSettings['default_domain_provider'] === 'namecheap' ? 'selected' : '' ?>>Namecheap (即将支持)</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="domain_auto_renew"
                                               id="domain_auto_renew" <?= $domainSettings['domain_auto_renew'] === '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="domain_auto_renew">
                                            默认启用自动续费
                                            <small class="text-muted d-block">新注册的域名将自动启用续费功能</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="domain_privacy_protection"
                                               id="domain_privacy_protection" <?= $domainSettings['domain_privacy_protection'] === '1' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="domain_privacy_protection">
                                            默认启用隐私保护
                                            <small class="text-muted d-block">保护域名注册信息不被公开查询</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存域名商设置
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="testNameSiloConnection()">
                            <i class="fas fa-plug me-2"></i>
                            测试NameSilo连接
                        </button>
                        <a href="test-namesilo-complete.php" target="_blank" class="btn btn-outline-secondary">
                            <i class="fas fa-external-link-alt me-2"></i>
                            完整API测试
                        </a>
                        <a href="test-ssl-fix.php" target="_blank" class="btn btn-outline-warning">
                            <i class="fas fa-shield-alt me-2"></i>
                            SSL修复测试
                        </a>

                        <button type="button" class="btn btn-outline-primary" onclick="syncDomains()">
                            <i class="fas fa-globe me-2"></i>
                            同步销售域名
                        </button>
                        <a href="test-sync-prices.php" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-bug me-2"></i>
                            测试价格同步
                        </a>
                        <a href="quick-test-sync.php" target="_blank" class="btn btn-outline-secondary">
                            <i class="fas fa-flash me-2"></i>
                            快速测试
                        </a>
                        <a href="debug-prices.php" target="_blank" class="btn btn-outline-danger">
                            <i class="fas fa-search me-2"></i>
                            调试API
                        </a>
                        <a href="check-namesilo-api.php" target="_blank" class="btn btn-outline-warning">
                            <i class="fas fa-list me-2"></i>
                            检查API
                        </a>
                        <a href="test-fixed-prices.php" target="_blank" class="btn btn-outline-success">
                            <i class="fas fa-check-circle me-2"></i>
                            测试修复
                        </a>
                        <a href="test-price-api.php" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-database me-2"></i>
                            价格API
                        </a>
                        <a href="import-tld-data.php" target="_blank" class="btn btn-outline-warning">
                            <i class="fas fa-download me-2"></i>
                            导入TLD
                        </a>
                    </div>
                </form>
            </div>

            <!-- 支付设置 -->
            <div class="tab-pane fade" id="payment" role="tabpanel">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    支付设置功能正在开发中，敬请期待...
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fab fa-paypal fa-3x text-primary mb-3"></i>
                                <h5>PayPal</h5>
                                <p class="text-muted">集成PayPal支付网关</p>
                                <button class="btn btn-outline-primary" disabled>配置PayPal</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fab fa-stripe fa-3x text-info mb-3"></i>
                                <h5>Stripe</h5>
                                <p class="text-muted">集成Stripe支付网关</p>
                                <button class="btn btn-outline-info" disabled>配置Stripe</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fab fa-alipay fa-3x text-success mb-3"></i>
                                <h5>支付宝</h5>
                                <p class="text-muted">集成支付宝支付</p>
                                <button class="btn btn-outline-success" disabled>配置支付宝</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fab fa-weixin fa-3x text-warning mb-3"></i>
                                <h5>微信支付</h5>
                                <p class="text-muted">集成微信支付</p>
                                <button class="btn btn-outline-warning" disabled>配置微信支付</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testEmail() {
    // 获取测试邮箱地址
    const email = prompt('请输入测试邮箱地址:', '');
    if (!email) return;

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('邮箱地址格式不正确');
        return;
    }

    // 显示加载状态
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>发送中...';
    button.disabled = true;

    // 发送测试邮件
    fetch('api/test-email.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ 请求失败: ' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function backupSettings() {
    if (!confirm('确定要备份当前设置吗？')) return;

    // 这里可以实现设置备份功能
    fetch('api/backup-settings.php', {
        method: 'POST'
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'settings-backup-' + new Date().toISOString().slice(0, 10) + '.json';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    })
    .catch(error => {
        alert('备份失败: ' + error.message);
    });
}

function restoreSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (!confirm('确定要恢复设置吗？这将覆盖当前所有设置！')) return;

        const formData = new FormData();
        formData.append('backup_file', file);

        fetch('api/restore-settings.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ 设置恢复成功！页面将刷新。');
                location.reload();
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ 恢复失败: ' + error.message);
        });
    };
    input.click();
}

function testNameSiloConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>测试中...';
    button.disabled = true;

    // 测试NameSilo连接
    fetch('ajax/test-namesilo.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.text())
    .then(text => {
        console.log('Response text:', text);
        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert('✅ NameSilo连接测试成功！\n' + data.message);
            } else {
                alert('❌ NameSilo连接测试失败：\n' + data.message);
            }
        } catch (e) {
            alert('❌ JSON解析失败: ' + e.message + '\n响应内容: ' + text);
        }
    })
    .catch(error => {
        alert('❌ 请求失败: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}



function syncDomains() {
    if (!confirm('确定要同步域名吗？这将从NameSilo获取您的域名列表。')) return;

    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>同步中...';
    button.disabled = true;

    fetch('ajax/namesilo-operations.php?action=sync-domains', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ 域名同步成功！\n' +
                  '新增: ' + (data.added || 0) + ' 个域名\n' +
                  '更新: ' + (data.updated || 0) + ' 个域名');
        } else {
            alert('❌ 域名同步失败：\n' + data.message);
        }
    })
    .catch(error => {
        alert('❌ 请求失败: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>
