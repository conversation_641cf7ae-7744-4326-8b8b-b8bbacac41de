<?php
/**
 * 支付页面
 */

session_start();
require_once 'includes/database.php';

$orderNumber = $_GET['order'] ?? '';
$message = '';
$error = '';

if (empty($orderNumber)) {
    header('Location: cart.php');
    exit;
}

try {
    $pdo = getDatabase();
    
    // 获取订单信息
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_number = ?");
    $stmt->execute([$orderNumber]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new Exception('订单不存在');
    }
    
    if ($order['payment_status'] === 'paid') {
        header('Location: order-success.php?order=' . urlencode($orderNumber));
        exit;
    }
    
    // 获取订单项
    $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
    $stmt->execute([$order['id']]);
    $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

// 处理支付
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'process_payment') {
    try {
        $paymentMethod = $_POST['payment_method'] ?? '';
        
        if (empty($paymentMethod)) {
            throw new Exception('请选择支付方式');
        }
        
        // 模拟支付处理
        $paymentId = 'PAY' . date('YmdHis') . rand(1000, 9999);
        
        // 更新订单状态
        $stmt = $pdo->prepare("
            UPDATE orders 
            SET payment_status = 'paid', 
                payment_method = ?, 
                payment_id = ?,
                status = 'processing',
                updated_at = NOW()
            WHERE order_number = ?
        ");
        
        $stmt->execute([$paymentMethod, $paymentId, $orderNumber]);
        
        // 触发自动域名注册
        $webhookData = [
            'order_number' => $orderNumber,
            'payment_status' => 'paid',
            'payment_method' => $paymentMethod,
            'payment_id' => $paymentId
        ];
        
        // 异步调用注册webhook
        $webhookUrl = 'http://127.0.0.1:777/api/auto-registration-webhook.php';
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => json_encode($webhookData),
                'timeout' => 1 // 短超时，不等待响应
            ]
        ]);
        
        // 异步触发，不等待结果
        @file_get_contents($webhookUrl, false, $context);
        
        // 跳转到成功页面
        header('Location: order-success.php?order=' . urlencode($orderNumber));
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$pageTitle = "支付";
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - NameSilo域名销售系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .payment-method.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .payment-method input[type="radio"] {
            display: none;
        }
        .payment-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            position: sticky;
            top: 20px;
        }
        .btn-pay {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            font-weight: bold;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
        }
        .btn-pay:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .security-info {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-globe"></i> NameSilo域名销售系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">首页</a>
                <a class="nav-link" href="domain-search.php">域名搜索</a>
                <a class="nav-link" href="cart.php">购物车</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 消息提示 -->
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($order)): ?>
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-credit-card"></i> 支付</h2>
                <p class="text-muted">订单号: <?= htmlspecialchars($order['order_number']) ?></p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <form method="POST" id="paymentForm">
                    <input type="hidden" name="action" value="process_payment">
                    
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0">选择支付方式</h4>
                        </div>
                        <div class="card-body">
                            <!-- 支付宝 -->
                            <label class="payment-method" for="alipay">
                                <input type="radio" name="payment_method" value="alipay" id="alipay">
                                <div class="row align-items-center">
                                    <div class="col-2 text-center">
                                        <div class="payment-icon text-primary">
                                            <i class="fab fa-alipay"></i>
                                        </div>
                                    </div>
                                    <div class="col-10">
                                        <h5 class="mb-1">支付宝</h5>
                                        <p class="text-muted mb-0">使用支付宝安全快捷支付</p>
                                    </div>
                                </div>
                            </label>

                            <!-- 微信支付 -->
                            <label class="payment-method" for="wechat">
                                <input type="radio" name="payment_method" value="wechat" id="wechat">
                                <div class="row align-items-center">
                                    <div class="col-2 text-center">
                                        <div class="payment-icon text-success">
                                            <i class="fab fa-weixin"></i>
                                        </div>
                                    </div>
                                    <div class="col-10">
                                        <h5 class="mb-1">微信支付</h5>
                                        <p class="text-muted mb-0">使用微信扫码支付</p>
                                    </div>
                                </div>
                            </label>

                            <!-- PayPal -->
                            <label class="payment-method" for="paypal">
                                <input type="radio" name="payment_method" value="paypal" id="paypal">
                                <div class="row align-items-center">
                                    <div class="col-2 text-center">
                                        <div class="payment-icon text-info">
                                            <i class="fab fa-paypal"></i>
                                        </div>
                                    </div>
                                    <div class="col-10">
                                        <h5 class="mb-1">PayPal</h5>
                                        <p class="text-muted mb-0">国际通用的在线支付方式</p>
                                    </div>
                                </div>
                            </label>

                            <!-- 银行卡 -->
                            <label class="payment-method" for="card">
                                <input type="radio" name="payment_method" value="card" id="card">
                                <div class="row align-items-center">
                                    <div class="col-2 text-center">
                                        <div class="payment-icon text-warning">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                    </div>
                                    <div class="col-10">
                                        <h5 class="mb-1">银行卡支付</h5>
                                        <p class="text-muted mb-0">支持各大银行借记卡和信用卡</p>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 安全信息 -->
                    <div class="security-info">
                        <h6><i class="fas fa-shield-alt text-success"></i> 安全保障</h6>
                        <ul class="mb-0">
                            <li>所有支付信息均采用SSL加密传输</li>
                            <li>支持7天无理由退款</li>
                            <li>24小时客服支持</li>
                            <li>域名注册失败全额退款</li>
                        </ul>
                    </div>

                    <!-- 支付按钮 -->
                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-pay" id="payButton" disabled>
                            <i class="fas fa-lock"></i> 立即支付 $<?= number_format($order['total_amount'], 2) ?>
                        </button>
                        <a href="checkout.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回订单确认
                        </a>
                    </div>
                </form>
            </div>

            <!-- 订单摘要 -->
            <div class="col-lg-4">
                <div class="order-summary">
                    <h5 class="mb-3">订单详情</h5>
                    
                    <div class="mb-3">
                        <strong>订单号:</strong><br>
                        <small class="text-muted"><?= htmlspecialchars($order['order_number']) ?></small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>客户信息:</strong><br>
                        <small class="text-muted">
                            <?= htmlspecialchars($order['customer_name']) ?><br>
                            <?= htmlspecialchars($order['customer_email']) ?>
                        </small>
                    </div>
                    
                    <hr>
                    
                    <div class="mb-3">
                        <strong>订单项目:</strong>
                        <?php foreach ($orderItems as $item): ?>
                        <div class="d-flex justify-content-between mt-2">
                            <small><?= htmlspecialchars($item['domain_name']) ?> (<?= $item['years'] ?>年)</small>
                            <small>$<?= number_format($item['total_price'], 2) ?></small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>总计:</strong>
                        <strong class="text-success">$<?= number_format($order['total_amount'], 2) ?></strong>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 支付方式选择
        document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 移除所有选中状态
                document.querySelectorAll('.payment-method').forEach(method => {
                    method.classList.remove('selected');
                });
                
                // 添加选中状态
                this.closest('.payment-method').classList.add('selected');
                
                // 启用支付按钮
                document.getElementById('payButton').disabled = false;
            });
        });

        // 点击支付方式区域选中
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                radio.dispatchEvent(new Event('change'));
            });
        });
    </script>
</body>
</html>
