/**
 * NameSilo域名销售系统 - 前端认证页面JavaScript
 * 基于后台管理系统的设计风格
 */

// 表单验证工具类
class FormValidator {
    constructor() {
        this.rules = {};
        this.errors = {};
    }
    
    addRule(field, validator, message) {
        if (!this.rules[field]) {
            this.rules[field] = [];
        }
        this.rules[field].push({ validator, message });
        return this;
    }
    
    validate(field, value) {
        this.errors[field] = [];
        
        if (this.rules[field]) {
            for (const rule of this.rules[field]) {
                if (!rule.validator(value)) {
                    this.errors[field].push(rule.message);
                }
            }
        }
        
        return this.errors[field].length === 0;
    }
    
    validateAll(formData) {
        let isValid = true;
        for (const [field, value] of Object.entries(formData)) {
            if (!this.validate(field, value)) {
                isValid = false;
            }
        }
        return isValid;
    }
    
    getErrors(field) {
        return this.errors[field] || [];
    }
}

// 通用工具函数
const AuthUtils = {
    // 显示消息
    showMessage(message, type = 'danger', container = 'messageContainer') {
        const messageContainer = document.getElementById(container);
        if (!messageContainer) return;
        
        const alertClass = `alert-${type}`;
        const iconClass = type === 'success' ? 'fa-check-circle' : 
                         type === 'warning' ? 'fa-exclamation-triangle' : 
                         type === 'info' ? 'fa-info-circle' : 'fa-exclamation-circle';
        
        messageContainer.innerHTML = `
            <div class="alert ${alertClass}">
                <i class="fas ${iconClass}"></i>
                ${message}
            </div>
        `;
        
        // 自动滚动到消息位置
        messageContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    },
    
    // 清除消息
    clearMessage(container = 'messageContainer') {
        const messageContainer = document.getElementById(container);
        if (messageContainer) {
            messageContainer.innerHTML = '';
        }
    },
    
    // 显示字段验证状态
    showFieldValidation(fieldName, isValid, errors = []) {
        const field = document.getElementById(fieldName);
        if (!field) return;
        
        const feedback = field.parentNode.querySelector('.invalid-feedback') || 
                        field.parentNode.querySelector('.valid-feedback');
        
        // 移除现有的反馈元素
        if (feedback) {
            feedback.remove();
        }
        
        // 移除现有的验证类
        field.classList.remove('is-valid', 'is-invalid');
        
        if (isValid) {
            field.classList.add('is-valid');
            const validFeedback = document.createElement('div');
            validFeedback.className = 'valid-feedback';
            validFeedback.textContent = '✓ 格式正确';
            field.parentNode.appendChild(validFeedback);
        } else if (errors.length > 0) {
            field.classList.add('is-invalid');
            const invalidFeedback = document.createElement('div');
            invalidFeedback.className = 'invalid-feedback';
            invalidFeedback.textContent = errors[0];
            field.parentNode.appendChild(invalidFeedback);
        }
    },
    
    // 按钮加载状态管理
    setButtonLoading(button, isLoading, text = '') {
        if (!button) return;
        
        const btnText = button.querySelector('.btn-text') || button;
        const btnIcon = button.querySelector('i');
        
        if (isLoading) {
            button.disabled = true;
            if (btnText !== button) {
                btnText.textContent = text;
            } else {
                button.innerHTML = `<div class="loading-spinner"></div> ${text}`;
            }
            if (btnIcon) {
                btnIcon.className = '';
                btnIcon.innerHTML = '<div class="loading-spinner"></div>';
            }
        } else {
            button.disabled = false;
            if (btnText !== button) {
                btnText.textContent = text;
            } else {
                button.innerHTML = text;
            }
            if (btnIcon) {
                btnIcon.className = 'fas fa-sign-in-alt';
                btnIcon.innerHTML = '';
            }
        }
    },
    
    // 实时验证设置
    setupRealTimeValidation(validator, fields) {
        fields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (!field) return;
            
            // 失去焦点时验证
            field.addEventListener('blur', function() {
                const isValid = validator.validate(fieldName, this.value);
                AuthUtils.showFieldValidation(fieldName, isValid, validator.getErrors(fieldName));
            });
            
            // 输入时清除错误状态
            field.addEventListener('input', function() {
                this.classList.remove('is-invalid');
                const feedback = this.parentNode.querySelector('.invalid-feedback');
                if (feedback) feedback.remove();
                AuthUtils.clearMessage();
            });
        });
    },
    
    // 密码显示/隐藏切换
    togglePassword(inputId, iconId) {
        const passwordInput = document.getElementById(inputId);
        const passwordIcon = document.getElementById(iconId);
        
        if (!passwordInput || !passwordIcon) return;
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            passwordIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            passwordIcon.className = 'fas fa-eye';
        }
    },
    
    // 表单提交处理
    async submitForm(formElement, apiUrl, validator = null, onSuccess = null, onError = null) {
        if (!formElement) return;
        
        // 清除之前的消息
        AuthUtils.clearMessage();
        
        // 获取表单数据
        const formData = new FormData(formElement);
        const data = {};
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // 客户端验证
        if (validator) {
            const isValid = validator.validateAll(data);
            if (!isValid) {
                Object.keys(data).forEach(field => {
                    const fieldValid = validator.validate(field, data[field]);
                    AuthUtils.showFieldValidation(field, fieldValid, validator.getErrors(field));
                });
                AuthUtils.showMessage('请检查输入的信息');
                return false;
            }
        }
        
        const submitBtn = formElement.querySelector('button[type="submit"]');
        const originalText = submitBtn ? submitBtn.textContent : '';
        
        try {
            // 显示加载状态
            AuthUtils.setButtonLoading(submitBtn, true, '处理中...');
            
            const response = await fetch(apiUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                AuthUtils.showMessage(result.message || '操作成功', 'success');
                if (onSuccess) {
                    onSuccess(result);
                }
                return true;
            } else {
                AuthUtils.showMessage(result.message || '操作失败，请稍后重试');
                if (onError) {
                    onError(result);
                }
                return false;
            }
        } catch (error) {
            console.error('Form submission error:', error);
            AuthUtils.showMessage('网络连接失败，请检查网络后重试');
            if (onError) {
                onError(error);
            }
            return false;
        } finally {
            // 恢复按钮状态
            AuthUtils.setButtonLoading(submitBtn, false, originalText);
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 键盘导航支持
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Enter 快速提交
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            const form = document.querySelector('form');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
        
        // ESC 清除消息
        if (e.key === 'Escape') {
            AuthUtils.clearMessage();
        }
    });
    
    // 页面可见性变化时的处理
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            // 页面重新可见时，清除可能的错误状态
            const fields = document.querySelectorAll('.form-control.is-invalid');
            fields.forEach(field => {
                field.classList.remove('is-invalid');
                const feedback = field.parentNode.querySelector('.invalid-feedback');
                if (feedback) feedback.remove();
            });
        }
    });
});

// 导出到全局
window.FormValidator = FormValidator;
window.AuthUtils = AuthUtils;
