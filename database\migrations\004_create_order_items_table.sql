-- 创建订单项表
-- Create order items table

CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL COMMENT '订单ID',
    domain_id INT COMMENT '域名ID',
    domain_name VARCHAR(255) NOT NULL COMMENT '域名',
    item_type ENUM('registration', 'renewal', 'transfer', 'privacy') DEFAULT 'registration' COMMENT '项目类型',
    years INT DEFAULT 1 COMMENT '年数',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE SET NULL,
    INDEX idx_order_id (order_id),
    INDEX idx_domain_id (domain_id),
    INDEX idx_item_type (item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';
