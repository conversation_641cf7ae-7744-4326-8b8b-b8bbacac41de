<?php
/**
 * AJAX域名导入处理
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 定义项目根目录
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
}

// 引入统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database.php';

// 引入NameSilo API客户端
require_once ROOT_PATH . '/public/api/NameSiloClient.php';

// 设置JSON响应头
header('Content-Type: application/json');

// 数据库连接函数
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';

        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    return $pdo;
}

// 获取NameSilo客户端
function getNameSiloClient() {
    $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
    $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
    $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';

    if (empty($apiKey)) {
        throw new Exception('NameSilo API密钥未配置，请先在系统设置中配置');
    }

    return new NameSiloClient($apiKey, $apiUrl, $sandbox);
}

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 获取数据库连接
    $db = getDatabase();
    
    // 步骤1: 测试API连接
    $client = getNameSiloClient();
    
    echo json_encode([
        'success' => true,
        'step' => 'connecting',
        'message' => 'API连接成功，正在获取域名列表...',
        'progress' => 20
    ], JSON_UNESCAPED_UNICODE);
    
    // 刷新输出缓冲区
    if (ob_get_level()) {
        ob_end_flush();
    }
    flush();
    
    // 步骤2: 获取域名列表
    try {
        $nameSiloDomains = $client->listDomains();

        if (empty($nameSiloDomains)) {
            echo "\n" . json_encode([
                'success' => false,
                'step' => 'error',
                'message' => '未找到任何域名，请检查NameSilo账户中是否有域名',
                'progress' => 50
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }

        echo "\n" . json_encode([
            'success' => true,
            'step' => 'fetching',
            'message' => '找到 ' . count($nameSiloDomains) . ' 个域名，开始导入...',
            'progress' => 40,
            'total_domains' => count($nameSiloDomains)
        ], JSON_UNESCAPED_UNICODE);

        flush();

    } catch (Exception $e) {
        echo "\n" . json_encode([
            'success' => false,
            'step' => 'error',
            'message' => '获取域名列表失败: ' . $e->getMessage(),
            'progress' => 30,
            'error_details' => [
                'message' => $e->getMessage(),
                'type' => 'api_error'
            ]
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 步骤3: 导入域名
    $imported = 0;
    $skipped = 0;
    $errors = [];
    
    foreach ($nameSiloDomains as $index => $domainData) {
        try {
            // 验证域名数据
            if (!isset($domainData['domain']) || empty(trim($domainData['domain']))) {
                $errors[] = "域名数据无效: 缺少域名字段";
                continue;
            }

            $domainName = trim($domainData['domain']);

            // 验证域名格式
            if (!filter_var($domainName, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
                $errors[] = "域名格式无效: {$domainName}";
                $skipped++;
                continue;
            }

            // 验证域名长度
            if (strlen($domainName) > 255) {
                $errors[] = "域名过长: {$domainName}";
                $skipped++;
                continue;
            }

            // 确定域名状态
            $status = 'registered'; // 默认状态
            if (isset($domainData['status'])) {
                switch (strtolower($domainData['status'])) {
                    case 'active':
                        $status = 'registered';
                        break;
                    case 'expired':
                        $status = 'expired';
                        break;
                    case 'pending':
                        $status = 'pending';
                        break;
                    default:
                        $status = 'registered';
                }
            }

            // 转换到期日期格式
            $expiryDate = null;
            if (!empty($domainData['expires'])) {
                $timestamp = strtotime($domainData['expires']);
                if ($timestamp !== false) {
                    $expiryDate = date('Y-m-d', $timestamp);

                    // 验证日期是否合理（不能是过去太久的日期）
                    if ($timestamp < strtotime('-10 years')) {
                        $errors[] = "域名 {$domainName} 的到期日期异常: {$domainData['expires']}";
                    }
                } else {
                    $errors[] = "域名 {$domainName} 的到期日期格式无效: {$domainData['expires']}";
                }
            }

            // 转换注册日期格式
            $registrationDate = null;
            if (!empty($domainData['created'])) {
                $timestamp = strtotime($domainData['created']);
                if ($timestamp !== false) {
                    $registrationDate = date('Y-m-d', $timestamp);

                    // 验证注册日期是否合理
                    if ($timestamp > time()) {
                        $errors[] = "域名 {$domainName} 的注册日期不能是未来时间: {$domainData['created']}";
                        $registrationDate = null;
                    }
                } else {
                    $errors[] = "域名 {$domainName} 的注册日期格式无效: {$domainData['created']}";
                }
            }

            // 验证自动续费设置
            $autoRenew = 0;
            if (isset($domainData['auto_renew'])) {
                $autoRenew = $domainData['auto_renew'] ? 1 : 0;
            }

            // 提取并验证TLD
            $parts = explode('.', $domainName);
            if (count($parts) < 2) {
                $errors[] = "域名格式无效，无法提取TLD: {$domainName}";
                $skipped++;
                continue;
            }
            $tld = '.' . end($parts);

            // 验证TLD长度
            if (strlen($tld) > 20) {
                $errors[] = "TLD过长: {$tld} (域名: {$domainName})";
                $skipped++;
                continue;
            }
            
            // 检查域名是否已存在
            $stmt = $db->prepare("SELECT id, status, expiry_date FROM domains WHERE domain_name = ?");
            $stmt->execute([$domainName]);
            $existingDomain = $stmt->fetch();

            if ($existingDomain) {
                // 域名已存在，检查是否需要更新信息
                $needUpdate = false;
                $updateFields = [];
                $updateValues = [];

                // 检查状态是否需要更新
                if ($existingDomain['status'] !== $status) {
                    $updateFields[] = "status = ?";
                    $updateValues[] = $status;
                    $needUpdate = true;
                }

                // 检查到期日期是否需要更新
                if ($expiryDate && $existingDomain['expiry_date'] !== $expiryDate) {
                    $updateFields[] = "expiry_date = ?";
                    $updateValues[] = $expiryDate;
                    $needUpdate = true;
                }

                if ($needUpdate) {
                    $updateValues[] = $domainName;
                    $updateSql = "UPDATE domains SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE domain_name = ?";
                    $stmt = $db->prepare($updateSql);
                    $stmt->execute($updateValues);

                    echo "\n" . json_encode([
                        'success' => true,
                        'step' => 'importing',
                        'message' => "更新域名信息: {$domainName}",
                        'progress' => round(40 + (($index + 1) / count($nameSiloDomains)) * 50),
                        'imported' => $imported,
                        'skipped' => $skipped,
                        'current_domain' => $domainName,
                        'action' => 'updated'
                    ], JSON_UNESCAPED_UNICODE);
                }

                $skipped++;
                continue;
            }

            // 获取默认价格
            $stmt = $db->prepare("SELECT registration_price FROM domain_prices WHERE tld = ?");
            $stmt->execute([$tld]);
            $priceData = $stmt->fetch();
            $price = $priceData ? $priceData['registration_price'] : 12.99;

            // 验证价格
            if ($price < 0 || $price > 99999.99) {
                $errors[] = "域名 {$domainName} 的价格异常: {$price}";
                $price = 12.99; // 使用默认价格
            }

            // 插入域名
            $stmt = $db->prepare("
                INSERT INTO domains (domain_name, tld, price, status, expiry_date, auto_renew, registration_date, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            // 如果日期无效，使用NULL
            $expiryDateValue = $expiryDate ?: null;
            $registrationDateValue = $registrationDate ?: null;

            if (!$stmt->execute([$domainName, $tld, $price, $status, $expiryDateValue, $autoRenew, $registrationDateValue])) {
                $errors[] = "插入域名失败: {$domainName} - " . implode(', ', $stmt->errorInfo());
                continue;
            }

            $imported++;
            
            // 发送进度更新
            $progress = 40 + (($index + 1) / count($nameSiloDomains)) * 50;
            echo "\n" . json_encode([
                'success' => true,
                'step' => 'importing',
                'message' => "正在导入: {$domainName}",
                'progress' => round($progress),
                'imported' => $imported,
                'skipped' => $skipped,
                'current_domain' => $domainName,
                'action' => 'imported'
            ], JSON_UNESCAPED_UNICODE);

            flush();

            // 避免请求过快，但不要太慢
            usleep(50000); // 0.05秒

        } catch (Exception $e) {
            $errorMsg = "导入 {$domainName} 失败: " . $e->getMessage();
            $errors[] = $errorMsg;

            // 发送错误信息
            echo "\n" . json_encode([
                'success' => true,
                'step' => 'importing',
                'message' => "处理 {$domainName} 时出错",
                'progress' => round(40 + (($index + 1) / count($nameSiloDomains)) * 50),
                'imported' => $imported,
                'skipped' => $skipped,
                'current_domain' => $domainName,
                'error_message' => $errorMsg
            ], JSON_UNESCAPED_UNICODE);

            flush();
        }
    }
    
    // 步骤4: 完成
    $totalProcessed = $imported + $skipped;
    $errorCount = count($errors);

    $message = "导入完成！";
    if ($imported > 0) {
        $message .= "成功导入 {$imported} 个域名";
    }
    if ($skipped > 0) {
        $message .= ($imported > 0 ? "，" : "") . "跳过 {$skipped} 个已存在的域名";
    }
    if ($errorCount > 0) {
        $message .= ($imported > 0 || $skipped > 0 ? "，" : "") . "处理过程中遇到 {$errorCount} 个错误";
    }

    echo "\n" . json_encode([
        'success' => true,
        'step' => 'completed',
        'message' => $message,
        'progress' => 100,
        'imported' => $imported,
        'skipped' => $skipped,
        'total_domains' => count($nameSiloDomains),
        'total_processed' => $totalProcessed,
        'error_count' => $errorCount,
        'errors' => $errors,
        'summary' => [
            'total' => count($nameSiloDomains),
            'imported' => $imported,
            'skipped' => $skipped,
            'errors' => $errorCount,
            'success_rate' => $totalProcessed > 0 ? round(($imported / $totalProcessed) * 100, 1) : 0
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 记录详细错误信息
    error_log("NameSilo导入失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());

    echo json_encode([
        'success' => false,
        'error' => '导入过程中发生严重错误: ' . $e->getMessage(),
        'step' => 'error',
        'error_details' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
