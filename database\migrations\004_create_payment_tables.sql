-- 创建支付配置表
CREATE TABLE IF NOT EXISTS payment_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_method VARCHAR(50) NOT NULL UNIQUE COMMENT '支付方式: wechat, alipay, epay',
    name VARCHAR(100) NOT NULL COMMENT '支付方式名称',
    enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用',
    config JSON COMMENT '支付配置参数',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建支付记录表
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL COMMENT '订单ID',
    payment_method VARCHAR(50) NOT NULL COMMENT '支付方式',
    payment_number VARCHAR(100) NOT NULL UNIQUE COMMENT '支付单号',
    trade_no VARCHAR(100) COMMENT '第三方交易号',
    amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    status ENUM('pending', 'processing', 'success', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '支付状态',
    notify_data JSON COMMENT '支付通知数据',
    paid_at TIMESTAMP NULL COMMENT '支付完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_payment_number (payment_number),
    INDEX idx_trade_no (trade_no),
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认支付配置
INSERT INTO payment_configs (payment_method, name, enabled, config, sort_order) VALUES
('wechat', '微信支付', 0, JSON_OBJECT(
    'app_id', '',
    'mch_id', '',
    'api_key', '',
    'cert_path', '',
    'key_path', '',
    'notify_url', '/api/payment/notify/wechat'
), 1),
('alipay', '支付宝', 0, JSON_OBJECT(
    'partner_id', '',
    'app_id', '',
    'md5_key', '',
    'rsa_private_key', '',
    'rsa_public_key', '',
    'gateway_url', 'https://mapi.alipay.com/gateway.do',
    'notify_url', '/api/payment/notify/alipay',
    'return_url', '/payment/return/alipay',
    'sign_type', 'MD5',
    'input_charset', 'utf-8',
    'transport', 'https'
), 2),
('epay', '易支付', 0, JSON_OBJECT(
    'pid', '',
    'key', '',
    'api_url', 'https://pay.example.com/',
    'notify_url', '/api/payment/notify/epay',
    'return_url', '/payment/return/epay'
), 3)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    sort_order = VALUES(sort_order);
