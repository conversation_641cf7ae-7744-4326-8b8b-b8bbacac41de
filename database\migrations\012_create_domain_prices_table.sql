-- 创建域名价格表
-- Create domain prices table

CREATE TABLE IF NOT EXISTS domain_prices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tld VARCHAR(20) NOT NULL UNIQUE COMMENT '顶级域名',
    registration_price DECIMAL(10,2) NOT NULL COMMENT '注册价格',
    renewal_price DECIMAL(10,2) NOT NULL COMMENT '续费价格',
    transfer_price DECIMAL(10,2) NOT NULL COMMENT '转移价格',
    min_years INT DEFAULT 1 COMMENT '最小年数',
    max_years INT DEFAULT 10 COMMENT '最大年数',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_tld (tld),
    INDEX idx_enabled (enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名价格表';
