<?php
// DNS设置页面
if (!defined('ADMIN_PATH')) {
    die('Direct access not permitted');
}

// 获取域名ID
$domainId = $_GET['id'] ?? 0;

if (!$domainId) {
    header('Location: ?page=domains');
    exit;
}

// 获取数据库连接
$db = getDatabase();

// 获取域名信息
try {
    $stmt = $db->prepare("
        SELECT d.*, u.username, u.email as user_email 
        FROM domains d 
        LEFT JOIN users u ON d.user_id = u.id 
        WHERE d.id = ?
    ");
    $stmt->execute([$domainId]);
    $domain = $stmt->fetch();
    
    if (!$domain) {
        header('Location: ?page=domains');
        exit;
    }
} catch (Exception $e) {
    $error = "获取域名信息失败：" . $e->getMessage();
}

// 处理DNS记录操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'add_record':
                $type = $_POST['type'] ?? '';
                $name = trim($_POST['name'] ?? '');
                $value = trim($_POST['value'] ?? '');
                $ttl = (int)($_POST['ttl'] ?? 3600);
                $priority = $_POST['priority'] ?? null;

                if (empty($type) || empty($name) || empty($value)) {
                    $error = "请填写完整的DNS记录信息";
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO dns_records (domain_id, type, name, value, ttl, priority, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$domainId, $type, $name, $value, $ttl, $priority]);
                    $success = "DNS记录添加成功！";
                }
                break;

            case 'update_record':
                $recordId = $_POST['record_id'] ?? 0;
                $type = $_POST['type'] ?? '';
                $name = trim($_POST['name'] ?? '');
                $value = trim($_POST['value'] ?? '');
                $ttl = (int)($_POST['ttl'] ?? 3600);
                $priority = $_POST['priority'] ?? null;

                if (empty($type) || empty($name) || empty($value)) {
                    $error = "请填写完整的DNS记录信息";
                } else {
                    $stmt = $db->prepare("
                        UPDATE dns_records
                        SET type = ?, name = ?, value = ?, ttl = ?, priority = ?, updated_at = NOW()
                        WHERE id = ? AND domain_id = ?
                    ");
                    $stmt->execute([$type, $name, $value, $ttl, $priority, $recordId, $domainId]);
                    $success = "DNS记录更新成功！";
                }
                break;

            case 'delete_record':
                $recordId = $_POST['record_id'] ?? 0;
                $stmt = $db->prepare("DELETE FROM dns_records WHERE id = ? AND domain_id = ?");
                $stmt->execute([$recordId, $domainId]);
                $success = "DNS记录删除成功！";
                break;

            case 'update_nameservers':
                $nameservers = [];
                for ($i = 1; $i <= 4; $i++) {
                    $ns = trim($_POST["ns{$i}"] ?? '');
                    if (!empty($ns)) {
                        $nameservers[] = $ns;
                    }
                }

                $nameserversJson = !empty($nameservers) ? json_encode($nameservers) : null;
                $stmt = $db->prepare("UPDATE domains SET nameservers = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$nameserversJson, $domainId]);
                $success = "域名服务器更新成功！";

                // 重新获取域名信息
                $stmt = $db->prepare("
                    SELECT d.*, u.username, u.email as user_email
                    FROM domains d
                    LEFT JOIN users u ON d.user_id = u.id
                    WHERE d.id = ?
                ");
                $stmt->execute([$domainId]);
                $domain = $stmt->fetch();
                break;
        }
    } catch (Exception $e) {
        $error = "操作失败：" . $e->getMessage();
    }
}

// 创建DNS记录表（如果不存在）
try {
    $db->exec("
        CREATE TABLE IF NOT EXISTS dns_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            domain_id INT NOT NULL,
            type ENUM('A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'SRV', 'PTR') NOT NULL,
            name VARCHAR(255) NOT NULL,
            value TEXT NOT NULL,
            ttl INT DEFAULT 3600,
            priority INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_domain_id (domain_id),
            INDEX idx_type (type),
            FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
} catch (Exception $e) {
    // 表已存在或创建失败，记录错误但不中断执行
    error_log("DNS记录表创建失败: " . $e->getMessage());
}

// 获取DNS记录
try {
    $stmt = $db->prepare("SELECT * FROM dns_records WHERE domain_id = ? ORDER BY type, name");
    $stmt->execute([$domainId]);
    $dnsRecords = $stmt->fetchAll();

    // 如果没有DNS记录，创建一些默认记录
    if (empty($dnsRecords)) {
        $defaultRecords = [
            ['type' => 'A', 'name' => '@', 'value' => '***********00', 'ttl' => 3600],
            ['type' => 'CNAME', 'name' => 'www', 'value' => $domain['domain_name'], 'ttl' => 3600],
            ['type' => 'MX', 'name' => '@', 'value' => 'mail.' . $domain['domain_name'], 'ttl' => 3600, 'priority' => 10]
        ];

        foreach ($defaultRecords as $record) {
            try {
                $stmt = $db->prepare("
                    INSERT INTO dns_records (domain_id, type, name, value, ttl, priority, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $domainId,
                    $record['type'],
                    $record['name'],
                    $record['value'],
                    $record['ttl'],
                    $record['priority'] ?? null
                ]);
            } catch (Exception $e) {
                // 忽略插入错误
            }
        }

        // 重新获取记录
        $stmt = $db->prepare("SELECT * FROM dns_records WHERE domain_id = ? ORDER BY type, name");
        $stmt->execute([$domainId]);
        $dnsRecords = $stmt->fetchAll();
    }
} catch (Exception $e) {
    $dnsRecords = [];
    error_log("获取DNS记录失败: " . $e->getMessage());
}

// 解析域名服务器
$nameservers = [];
if (!empty($domain['nameservers'])) {
    $nameservers = json_decode($domain['nameservers'], true) ?: [];
}

ob_start();
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">DNS设置</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="?page=dashboard">首页</a></li>
                    <li class="breadcrumb-item"><a href="?page=domains">域名管理</a></li>
                    <li class="breadcrumb-item active">DNS - <?= htmlspecialchars($domain['domain_name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addRecordModal">
                <i class="fas fa-plus me-2"></i>
                添加记录
            </button>
            <a href="?page=domains&action=manage&id=<?= $domainId ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                返回管理
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- DNS记录列表 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-network-wired me-2"></i>
                        DNS记录 - <?= htmlspecialchars($domain['domain_name']) ?>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($dnsRecords)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-network-wired fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">暂无DNS记录</h6>
                        <p class="text-muted mb-3">还没有配置任何DNS记录</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRecordModal">
                            <i class="fas fa-plus me-2"></i>
                            添加第一条记录
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>类型</th>
                                    <th>名称</th>
                                    <th>值</th>
                                    <th>TTL</th>
                                    <th>优先级</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dnsRecords as $record): ?>
                                <tr>
                                    <td>
                                        <?php
                                        $typeClass = [
                                            'A' => 'primary',
                                            'AAAA' => 'info',
                                            'CNAME' => 'success',
                                            'MX' => 'warning',
                                            'TXT' => 'secondary',
                                            'NS' => 'dark'
                                        ][$record['type']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $typeClass ?>"><?= htmlspecialchars($record['type']) ?></span>
                                    </td>
                                    <td>
                                        <code><?= htmlspecialchars($record['name']) ?></code>
                                    </td>
                                    <td>
                                        <span class="text-break"><?= htmlspecialchars($record['value']) ?></span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= number_format($record['ttl']) ?>s</small>
                                    </td>
                                    <td>
                                        <?= $record['priority'] ? htmlspecialchars($record['priority']) : '-' ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editRecord(<?= htmlspecialchars(json_encode($record)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteRecord(<?= $record['id'] ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="col-lg-3">
            <!-- 域名信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>
                        域名信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">域名</small>
                        <div><?= htmlspecialchars($domain['domain_name']) ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">状态</small>
                        <div>
                            <?php
                            $statusClass = [
                                'available' => 'success',
                                'registered' => 'primary',
                                'transferred' => 'warning',
                                'expired' => 'danger',
                                'pending' => 'info'
                            ][$domain['status']] ?? 'secondary';
                            ?>
                            <span class="badge bg-<?= $statusClass ?>">
                                <?= [
                                    'available' => '可用',
                                    'registered' => '已注册',
                                    'transferred' => '已转移',
                                    'expired' => '已过期',
                                    'pending' => '待处理'
                                ][$domain['status']] ?? $domain['status'] ?>
                            </span>
                        </div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">归属人</small>
                        <div><?= $domain['username'] ? htmlspecialchars($domain['username']) : '未分配' ?></div>
                    </div>
                </div>
            </div>

            <!-- 域名服务器设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>
                        域名服务器
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_nameservers">

                        <?php for ($i = 1; $i <= 4; $i++): ?>
                        <div class="mb-3">
                            <label class="form-label">NS<?= $i ?></label>
                            <input type="text" class="form-control form-control-sm" name="ns<?= $i ?>"
                                   value="<?= htmlspecialchars($nameservers[$i-1] ?? '') ?>"
                                   placeholder="ns<?= $i ?>.namesilo-namesilo-sample.com">
                        </div>
                        <?php endfor; ?>

                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-save me-2"></i>
                            更新域名服务器
                        </button>
                    </form>
                </div>
            </div>

            <!-- DNS记录类型说明 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        记录类型说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge bg-primary me-2">A</span>
                        <small>IPv4地址记录</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-info me-2">AAAA</span>
                        <small>IPv6地址记录</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-success me-2">CNAME</span>
                        <small>别名记录</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning me-2">MX</span>
                        <small>邮件交换记录</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-secondary me-2">TXT</span>
                        <small>文本记录</small>
                    </div>
                    <div>
                        <span class="badge bg-dark me-2">NS</span>
                        <small>域名服务器记录</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加DNS记录模态框 -->
<div class="modal fade" id="addRecordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    添加DNS记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="addRecordForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_record">

                    <div class="mb-3">
                        <label class="form-label">记录类型 *</label>
                        <select class="form-select" name="type" required onchange="updateFormFields(this.value)">
                            <option value="">请选择记录类型</option>
                            <option value="A">A - IPv4地址</option>
                            <option value="AAAA">AAAA - IPv6地址</option>
                            <option value="CNAME">CNAME - 别名</option>
                            <option value="MX">MX - 邮件交换</option>
                            <option value="TXT">TXT - 文本</option>
                            <option value="NS">NS - 域名服务器</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">名称 *</label>
                        <input type="text" class="form-control" name="name" placeholder="@, www, mail等" required>
                        <div class="form-text">使用 @ 表示根域名</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">值 *</label>
                        <input type="text" class="form-control" name="value" placeholder="记录值" required>
                        <div class="form-text" id="valueHelp">请输入对应的记录值</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">TTL (秒)</label>
                                <select class="form-select" name="ttl">
                                    <option value="300">5分钟 (300)</option>
                                    <option value="1800">30分钟 (1800)</option>
                                    <option value="3600" selected>1小时 (3600)</option>
                                    <option value="14400">4小时 (14400)</option>
                                    <option value="86400">1天 (86400)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3" id="priorityField" style="display: none;">
                                <label class="form-label">优先级</label>
                                <input type="number" class="form-control" name="priority" min="0" max="65535">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        添加记录
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑DNS记录模态框 -->
<div class="modal fade" id="editRecordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    编辑DNS记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editRecordForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_record">
                    <input type="hidden" name="record_id" id="edit_record_id">

                    <div class="mb-3">
                        <label class="form-label">记录类型 *</label>
                        <select class="form-select" name="type" id="edit_type" required onchange="updateEditFormFields(this.value)">
                            <option value="A">A - IPv4地址</option>
                            <option value="AAAA">AAAA - IPv6地址</option>
                            <option value="CNAME">CNAME - 别名</option>
                            <option value="MX">MX - 邮件交换</option>
                            <option value="TXT">TXT - 文本</option>
                            <option value="NS">NS - 域名服务器</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">名称 *</label>
                        <input type="text" class="form-control" name="name" id="edit_name" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">值 *</label>
                        <input type="text" class="form-control" name="value" id="edit_value" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">TTL (秒)</label>
                                <select class="form-select" name="ttl" id="edit_ttl">
                                    <option value="300">5分钟 (300)</option>
                                    <option value="1800">30分钟 (1800)</option>
                                    <option value="3600">1小时 (3600)</option>
                                    <option value="14400">4小时 (14400)</option>
                                    <option value="86400">1天 (86400)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3" id="editPriorityField">
                                <label class="form-label">优先级</label>
                                <input type="number" class="form-control" name="priority" id="edit_priority" min="0" max="65535">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 根据记录类型更新表单字段
function updateFormFields(type) {
    const priorityField = document.getElementById('priorityField');
    const valueHelp = document.getElementById('valueHelp');

    if (type === 'MX') {
        priorityField.style.display = 'block';
        valueHelp.textContent = '输入邮件服务器地址，如：mail.namesilo-namesilo-sample.com';
    } else {
        priorityField.style.display = 'none';

        switch (type) {
            case 'A':
                valueHelp.textContent = '输入IPv4地址，如：***********';
                break;
            case 'AAAA':
                valueHelp.textContent = '输入IPv6地址，如：2001:db8::1';
                break;
            case 'CNAME':
                valueHelp.textContent = '输入目标域名，如：namesilo-namesilo-sample.com';
                break;
            case 'TXT':
                valueHelp.textContent = '输入文本内容，如：v=spf1 include:_spf.google.com ~all';
                break;
            case 'NS':
                valueHelp.textContent = '输入域名服务器，如：ns1.namesilo-namesilo-sample.com';
                break;
            default:
                valueHelp.textContent = '请输入对应的记录值';
        }
    }
}

function updateEditFormFields(type) {
    const priorityField = document.getElementById('editPriorityField');

    if (type === 'MX') {
        priorityField.style.display = 'block';
    } else {
        priorityField.style.display = 'none';
    }
}

// 编辑记录
function editRecord(record) {
    document.getElementById('edit_record_id').value = record.id;
    document.getElementById('edit_type').value = record.type;
    document.getElementById('edit_name').value = record.name;
    document.getElementById('edit_value').value = record.value;
    document.getElementById('edit_ttl').value = record.ttl;
    document.getElementById('edit_priority').value = record.priority || '';

    updateEditFormFields(record.type);

    new bootstrap.Modal(document.getElementById('editRecordModal')).show();
}

// 删除记录
function deleteRecord(recordId) {
    if (confirm('确定要删除这条DNS记录吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_record">
            <input type="hidden" name="record_id" value="${recordId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>



