<?php
/**
 * 通知页面
 * Notifications Page
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '通知中心';

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">通知中心</h1>
            <p class="page-subtitle">查看和管理您的系统通知</p>
        </div>
        <div>
            <button class="btn btn-outline-primary me-2" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-2"></i>
                全部已读
            </button>
            <button class="btn btn-outline-secondary" onclick="refreshNotifications()">
                <i class="fas fa-sync-alt me-2"></i>
                刷新
            </button>
        </div>
    </div>
</div>

<!-- 通知统计 -->
<div class="row g-4 mb-4">
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card text-center">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary mx-auto">
                <i class="fas fa-bell"></i>
            </div>
            <h4 class="h6 mb-1">总通知</h4>
            <p class="fw-bold mb-0" id="totalNotifications">-</p>
        </div>
    </div>
    
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card text-center">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning mx-auto">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <h4 class="h6 mb-1">未读</h4>
            <p class="fw-bold mb-0" id="unreadNotifications">-</p>
        </div>
    </div>
    
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card text-center">
            <div class="stats-icon bg-success bg-opacity-10 text-success mx-auto">
                <i class="fas fa-check-circle"></i>
            </div>
            <h4 class="h6 mb-1">已读</h4>
            <p class="fw-bold mb-0" id="readNotifications">-</p>
        </div>
    </div>
    
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="400">
        <div class="stats-card text-center">
            <div class="stats-icon bg-info bg-opacity-10 text-info mx-auto">
                <i class="fas fa-clock"></i>
            </div>
            <h4 class="h6 mb-1">今日新增</h4>
            <p class="fw-bold mb-0" id="todayNotifications">-</p>
        </div>
    </div>
</div>

<!-- 通知列表 -->
<div class="row">
    <div class="col-12" data-aos="fade-up" data-aos-delay="500">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    通知列表
                </h5>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-2"></i>类型筛选
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-filter="all">全部类型</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="system">系统通知</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="order">订单通知</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="domain">域名通知</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="payment">支付通知</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="security">安全通知</a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-eye me-2"></i>状态筛选
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-status="all">全部状态</a></li>
                            <li><a class="dropdown-item" href="#" data-status="unread">未读</a></li>
                            <li><a class="dropdown-item" href="#" data-status="read">已读</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="notificationsContainer">
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-3 mb-0">正在加载通知...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分页 -->
<div class="d-flex justify-content-center mt-4" id="paginationContainer" style="display: none;">
    <!-- 分页将在这里显示 -->
</div>

<script>
let currentPage = 1;
let currentType = 'all';
let currentStatus = 'all';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    loadNotificationStats();
    
    // 绑定筛选事件
    document.querySelectorAll('[data-filter]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            currentType = this.dataset.filter;
            currentPage = 1;
            loadNotifications();
        });
    });
    
    document.querySelectorAll('[data-status]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            currentStatus = this.dataset.status;
            currentPage = 1;
            loadNotifications();
        });
    });
});

// 加载通知列表
function loadNotifications() {
    const params = new URLSearchParams({
        page: currentPage,
        limit: 20
    });
    
    if (currentType !== 'all') {
        params.append('type', currentType);
    }
    
    if (currentStatus !== 'all') {
        params.append('status', currentStatus);
    }
    
    fetch(`../api/notifications.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.data.notifications);
                displayPagination(data.data.pagination);
            } else {
                showError('加载通知失败：' + data.message);
            }
        })
        .catch(error => {
            showError('网络错误，请稍后重试');
        });
}

// 显示通知列表
function displayNotifications(notifications) {
    const container = document.getElementById('notificationsContainer');
    
    if (notifications.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">暂无通知</h4>
                <p class="text-muted mb-4">您还没有收到任何通知</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    notifications.forEach(notification => {
        const isRead = notification.read_at !== null;
        const typeIcon = getNotificationIcon(notification.type);
        const typeColor = getNotificationColor(notification.type);
        const timeAgo = getTimeAgo(notification.created_at);
        
        html += `
            <div class="notification-item ${isRead ? 'read' : 'unread'}" data-id="${notification.id}">
                <div class="d-flex align-items-start p-3">
                    <div class="notification-icon ${typeColor} me-3">
                        <i class="fas fa-${typeIcon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <h6 class="notification-title mb-0">${notification.title}</h6>
                            <div class="d-flex align-items-center gap-2">
                                <small class="text-muted">${timeAgo}</small>
                                ${!isRead ? '<span class="badge bg-primary">新</span>' : ''}
                            </div>
                        </div>
                        <p class="notification-message mb-2">${notification.message}</p>
                        <div class="notification-actions">
                            ${!isRead ? `
                                <button class="btn btn-sm btn-outline-primary" onclick="markAsRead(${notification.id})">
                                    <i class="fas fa-check me-1"></i>
                                    标记已读
                                </button>
                            ` : ''}
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification(${notification.id})">
                                <i class="fas fa-trash me-1"></i>
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 显示分页
function displayPagination(pagination) {
    const container = document.getElementById('paginationContainer');
    
    if (pagination.pages <= 1) {
        container.style.display = 'none';
        return;
    }
    
    let html = '<nav><ul class="pagination">';
    
    // 上一页
    if (pagination.page > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.page - 1})">上一页</a></li>`;
    }
    
    // 页码
    for (let i = 1; i <= pagination.pages; i++) {
        if (i === pagination.page) {
            html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
        }
    }
    
    // 下一页
    if (pagination.page < pagination.pages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.page + 1})">下一页</a></li>`;
    }
    
    html += '</ul></nav>';
    container.innerHTML = html;
    container.style.display = 'block';
}

// 加载通知统计
function loadNotificationStats() {
    // 这里可以添加统计数据的加载逻辑
    // 目前使用模拟数据
    document.getElementById('totalNotifications').textContent = '0';
    document.getElementById('unreadNotifications').textContent = '0';
    document.getElementById('readNotifications').textContent = '0';
    document.getElementById('todayNotifications').textContent = '0';
}

// 获取通知图标
function getNotificationIcon(type) {
    const icons = {
        'system': 'cog',
        'order': 'shopping-cart',
        'domain': 'globe',
        'payment': 'credit-card',
        'security': 'shield-alt'
    };
    return icons[type] || 'bell';
}

// 获取通知颜色
function getNotificationColor(type) {
    const colors = {
        'system': 'text-info',
        'order': 'text-success',
        'domain': 'text-primary',
        'payment': 'text-warning',
        'security': 'text-danger'
    };
    return colors[type] || 'text-secondary';
}

// 获取时间差
function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 30) return `${days}天前`;
    
    return date.toLocaleDateString();
}

// 切换页面
function changePage(page) {
    currentPage = page;
    loadNotifications();
}

// 标记为已读
function markAsRead(notificationId) {
    fetch('../api/notifications.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadNotifications();
            loadNotificationStats();
            showSuccess('通知已标记为已读');
        } else {
            showError('操作失败：' + data.message);
        }
    })
    .catch(error => {
        showError('网络错误，请稍后重试');
    });
}

// 全部标记为已读
function markAllAsRead() {
    if (confirm('确定要将所有通知标记为已读吗？')) {
        fetch('../api/notifications.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'mark_all_read'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
                loadNotificationStats();
                showSuccess('所有通知已标记为已读');
            } else {
                showError('操作失败：' + data.message);
            }
        })
        .catch(error => {
            showError('网络错误，请稍后重试');
        });
    }
}

// 删除通知
function deleteNotification(notificationId) {
    if (confirm('确定要删除这条通知吗？此操作不可撤销！')) {
        fetch('../api/notifications.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: notificationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications();
                loadNotificationStats();
                showSuccess('通知已删除');
            } else {
                showError('删除失败：' + data.message);
            }
        })
        .catch(error => {
            showError('网络错误，请稍后重试');
        });
    }
}

// 刷新通知
function refreshNotifications() {
    loadNotifications();
    loadNotificationStats();
    showSuccess('通知已刷新');
}

// 显示成功消息
function showSuccess(message) {
    showAlert('success', message);
}

// 显示错误消息
function showError(message) {
    showAlert('error', message);
}

// 显示提示消息
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show">
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.content-area');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // 自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            const alert = container.querySelector('.alert-success');
            if (alert) {
                bootstrap.Alert.getInstance(alert)?.close();
            }
        }, 3000);
    }
}
</script>

<style>
.notification-item {
    border-bottom: 1px solid var(--border-light);
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--border-light);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: rgba(79, 70, 229, 0.02);
    border-left: 3px solid var(--primary-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--border-light);
}

.notification-title {
    font-weight: 600;
    color: var(--text-color);
}

.notification-message {
    color: var(--text-muted);
    font-size: 14px;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    gap: 8px;
}

.stats-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    transition: var(--transition);
}

.stats-card:hover {
    box-shadow: var(--shadow-md);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}
</style>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
