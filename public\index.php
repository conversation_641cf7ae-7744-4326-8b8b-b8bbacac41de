<?php
// 加载系统配置
require_once 'config.php';
require_once 'includes/database.php';

// 获取真实的TLD价格
$tldPrices = [];
try {
    $pdo = getDatabase();
    $stmt = $pdo->query("SELECT tld, register_price, renew_price FROM tld_pricing WHERE is_active = 1 ORDER BY is_featured DESC, register_price ASC");
    while ($row = $stmt->fetch()) {
        $tldPrices[$row['tld']] = [
            'register' => $row['register_price'],
            'renew' => $row['renew_price']
        ];
    }
} catch (Exception $e) {
    // 如果数据库查询失败，使用默认价格
    $tldPrices = [
        '.com' => ['register' => 12.99, 'renew' => 14.99],
        '.cn' => ['register' => 8.99, 'renew' => 8.99],
        '.net' => ['register' => 14.99, 'renew' => 16.99],
        '.org' => ['register' => 13.99, 'renew' => 15.99]
    ];
}

// 汇率转换 (USD to CNY)
$usdToCny = 7.2;
function formatPrice($usdPrice, $currency = 'CNY') {
    global $usdToCny;
    if ($currency === 'CNY') {
        return '¥' . number_format($usdPrice * $usdToCny, 0);
    }
    return '$' . number_format($usdPrice, 2);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NameSilo域名销售系统 - 专业域名注册服务</title>
    <meta name="description" content="专业的域名注册服务提供商，提供.com、.cn、.net等多种域名后缀注册">
    <meta name="keywords" content="域名注册,域名购买,NameSilo,域名管理,DNS">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #3730a3;
            --secondary-color: #6b7280;
            --accent-color: #06b6d4;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            line-height: 1.6;
        }

        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--primary-color) !important;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: white;
            padding: 6rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }

        .domain-search {
            background: white;
            border-radius: 16px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }

        .pricing-card {
            background: white;
            border-radius: 16px;
            padding: 2.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card.featured {
            border: 2px solid var(--primary-color);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: '推荐';
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 3rem;
            transform: rotate(45deg);
            font-size: 0.875rem;
            font-weight: 600;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--secondary-color);
            margin-bottom: 3rem;
        }

        .footer {
            background: #1e293b;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer h5 {
            color: white;
            margin-bottom: 1rem;
        }

        .footer a {
            color: #94a3b8;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-globe"></i>
                NameSilo域名销售系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="domain-search.php">
                            <i class="fas fa-search me-1"></i>域名搜索
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特色</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">价格方案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">联系我们</a>
                    </li>
                </ul>
                
                <div class="d-flex gap-2">
                    <a href="cart.php" class="btn btn-outline-secondary position-relative">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cartBadge" style="display: none;">
                            0
                        </span>
                    </a>
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <a href="user/index.php" class="btn btn-outline-primary">用户中心</a>
                    <a href="api/auth.php?action=logout" class="btn btn-outline-secondary">退出</a>
                    <?php else: ?>
                    <a href="login.php" class="btn btn-outline-primary">登录</a>
                    <a href="register.php" class="btn btn-primary">注册</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">专业域名注册服务</h1>
                    <p class="lead mb-4">
                        为您的网站选择完美的域名，享受快速、安全、可靠的域名注册服务。
                        我们提供全球顶级域名注册，助力您的数字化业务发展。
                    </p>
                    <div class="row text-center mb-4">
                        <div class="col-4">
                            <div class="h3 fw-bold">10,000+</div>
                            <small>注册域名</small>
                        </div>
                        <div class="col-4">
                            <div class="h3 fw-bold">5,000+</div>
                            <small>满意客户</small>
                        </div>
                        <div class="col-4">
                            <div class="h3 fw-bold">99.9%</div>
                            <small>服务可用性</small>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="domain-search">
                        <h3 class="text-dark mb-3">搜索您的理想域名</h3>
                        <form class="d-flex gap-2 mb-3" id="domainSearchForm">
                            <input type="text" id="domainInput" class="form-control form-control-lg" placeholder="输入您想要的域名..." required>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                        <div class="text-center">
                            <small class="text-muted">热门后缀：</small>
                            <span class="badge bg-light text-dark me-1">.com</span>
                            <span class="badge bg-light text-dark me-1">.cn</span>
                            <span class="badge bg-light text-dark me-1">.net</span>
                            <span class="badge bg-light text-dark">.org</span>
                        </div>

                        <!-- 搜索结果 -->
                        <div id="searchResults" class="mt-4" style="display: none;">
                            <h5 class="mb-3">搜索结果</h5>
                            <div id="searchResultsContainer">
                                <!-- 搜索结果将在这里显示 -->
                            </div>
                        </div>

                        <!-- 加载状态 -->
                        <div id="searchLoading" class="text-center mt-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">搜索中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在搜索域名...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 统计区域 -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <div class="stat-number">10,000+</div>
                        <div class="stat-label">注册域名</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <div class="stat-number">5,000+</div>
                        <div class="stat-label">满意客户</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">服务可用性</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">技术支持</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-3">为什么选择我们？</h2>
                    <p class="lead text-muted">
                        我们提供专业、可靠的域名注册服务，让您的在线业务更加成功
                    </p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h4 class="feature-title">快速注册</h4>
                        <p class="feature-description">
                            简单几步即可完成域名注册，快速启动您的在线业务
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="feature-title">安全可靠</h4>
                        <p class="feature-description">
                            采用先进的安全技术，保护您的域名和个人信息安全
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="feature-title">专业支持</h4>
                        <p class="feature-description">
                            24/7专业技术支持团队，随时为您解决域名相关问题
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h4 class="feature-title">价格透明</h4>
                        <p class="feature-description">
                            无隐藏费用，价格透明公开，让您明明白白消费
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4 class="feature-title">管理便捷</h4>
                        <p class="feature-description">
                            直观的管理界面，轻松管理您的所有域名和DNS设置
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <h4 class="feature-title">自动续费</h4>
                        <p class="feature-description">
                            智能自动续费功能，确保您的域名永不过期
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格方案 -->
    <section id="pricing" class="pricing-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-3">域名价格方案</h2>
                    <p class="lead text-muted">
                        选择适合您的域名后缀，享受优惠的注册价格
                    </p>
                </div>
            </div>
            
            <div class="row g-4">
                <?php
                $featuredTlds = ['.com', '.cn', '.net', '.org'];
                $tldDescriptions = [
                    '.com' => ['全球通用', '商业首选', 'SEO友好', '免费DNS'],
                    '.cn' => ['中国域名', '本土优势', '价格优惠', '免费DNS'],
                    '.net' => ['网络专用', '技术导向', '专业形象', '免费DNS'],
                    '.org' => ['组织专用', '非营利优选', '信誉度高', '免费DNS']
                ];

                foreach ($featuredTlds as $index => $tld):
                    $price = isset($tldPrices[$tld]) ? $tldPrices[$tld]['register'] : 12.99;
                    $isFeatured = ($tld === '.cn'); // .cn 作为推荐
                ?>
                <div class="col-lg-3 col-md-6">
                    <div class="pricing-card <?php echo $isFeatured ? 'featured' : ''; ?>">
                        <h4 class="pricing-title"><?php echo $tld; ?></h4>
                        <div class="pricing-price"><?php echo formatPrice($price); ?></div>
                        <div class="pricing-period">首年注册</div>
                        <ul class="pricing-features">
                            <?php foreach ($tldDescriptions[$tld] as $feature): ?>
                            <li><i class="fas fa-check"></i> <?php echo $feature; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <a href="domain-search.php?domain=<?php echo urlencode($tld); ?>" class="btn <?php echo $isFeatured ? 'btn-primary' : 'btn-outline-primary'; ?> w-100">立即注册</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-section">
                        <h5 class="footer-title">
                            <i class="fas fa-globe me-2"></i>
                            NameSilo域名销售系统
                        </h5>
                        <p class="text-light opacity-75">
                            专业的域名注册服务提供商，为您的数字化业务提供可靠的基础设施支持。
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-title">服务</h6>
                        <ul class="footer-links">
                            <li><a href="#pricing">域名注册</a></li>
                            <li><a href="#">域名转移</a></li>
                            <li><a href="#">DNS管理</a></li>
                            <li><a href="#">SSL证书</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-title">支持</h6>
                        <ul class="footer-links">
                            <li><a href="#">帮助中心</a></li>
                            <li><a href="#contact">联系我们</a></li>
                            <li><a href="#">技术支持</a></li>
                            <li><a href="#">状态页面</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-title">公司</h6>
                        <ul class="footer-links">
                            <li><a href="#about">关于我们</a></li>
                            <li><a href="#">新闻动态</a></li>
                            <li><a href="#">招聘信息</a></li>
                            <li><a href="#">合作伙伴</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6">
                    <div class="footer-section">
                        <h6 class="footer-title">法律</h6>
                        <ul class="footer-links">
                            <li><a href="#">服务条款</a></li>
                            <li><a href="#">隐私政策</a></li>
                            <li><a href="#">退款政策</a></li>
                            <li><a href="#">域名政策</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> NameSilo域名销售系统. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>


        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 数字动画
        function animateNumbers() {
            const stats = document.querySelectorAll('.stat-number');
            stats.forEach(stat => {
                const target = parseInt(stat.textContent.replace(/[^\d]/g, ''));
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    if (stat.textContent.includes('%')) {
                        stat.textContent = current.toFixed(1) + '%';
                    } else if (stat.textContent.includes('/')) {
                        stat.textContent = '24/7';
                    } else if (target >= 1000) {
                        stat.textContent = Math.floor(current).toLocaleString() + '+';
                    } else {
                        stat.textContent = Math.floor(current);
                    }
                }, 20);
            });
        }

        // 当统计区域进入视口时触发动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.background = 'var(--card-bg)';
                navbar.style.backdropFilter = 'none';
            }
        });

        // 特性卡片悬停效果
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 价格卡片点击效果
        document.querySelectorAll('.pricing-card').forEach(card => {
            card.addEventListener('click', function() {
                const btn = this.querySelector('.btn');
                if (btn) {
                    btn.click();
                }
            });
        });

        // 域名搜索功能
        const domainForm = document.getElementById('domainSearchForm');
        if (domainForm) {
            domainForm.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('搜索表单提交');
                searchDomain();
            });
        } else {
            console.error('找不到域名搜索表单');
        }

        // 加载购物车数量
        loadCartCount();
    });

    // 搜索域名
    function searchDomain() {
        console.log('searchDomain 函数被调用');

        const domainInput = document.getElementById('domainInput');
        if (!domainInput) {
            console.error('找不到域名输入框');
            alert('找不到域名输入框');
            return;
        }

        const domain = domainInput.value.trim();
        console.log('输入的域名:', domain);

        if (!domain) {
            alert('请输入要搜索的域名');
            return;
        }

        // 验证域名格式
        if (!/^[a-zA-Z0-9\-]+$/.test(domain)) {
            alert('域名只能包含字母、数字和连字符');
            return;
        }

        // 检查必要的DOM元素
        const resultsContainer = document.getElementById('searchResultsContainer');
        const searchResults = document.getElementById('searchResults');
        const searchLoading = document.getElementById('searchLoading');

        if (!resultsContainer || !searchResults || !searchLoading) {
            console.error('找不到搜索结果相关的DOM元素');
            // 简单跳转到域名搜索页面
            window.location.href = `domain-search.php?q=${encodeURIComponent(domain)}`;
            return;
        }

        // 显示加载状态
        searchLoading.style.display = 'block';
        searchResults.style.display = 'none';

        // 搜索多个常用后缀
        const extensions = '.com,.net,.org,.cn,.com.cn';

        fetch(`api/test-domain-search.php?domain=${encodeURIComponent(domain)}&extensions=${encodeURIComponent(extensions)}`)
            .then(response => response.json())
            .then(data => {
                searchLoading.style.display = 'none';

                if (data.success) {
                    displaySearchResults(data.results, domain);
                    searchResults.style.display = 'block';
                } else {
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                searchLoading.style.display = 'none';
                resultsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        搜索失败：${error.message}
                    </div>
                `;
                searchResults.style.display = 'block';
            });
    }

    // 显示搜索结果
    function displaySearchResults(results, searchTerm) {
        const resultsContainer = document.getElementById('searchResultsContainer');

        if (!results || results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    没有找到相关域名
                </div>
            `;
            return;
        }

        let html = `
            <div class="row">
                <div class="col-12">
                    <h6 class="mb-3">为 "${searchTerm}" 找到 ${results.length} 个结果</h6>
                </div>
            </div>
            <div class="row">
        `;

        results.forEach(result => {
            const availableClass = result.available ? 'success' : 'secondary';
            const availableText = result.available ? '可注册' : '不可用';
            const availableIcon = result.available ? 'check-circle' : 'times-circle';
            const price = result.available && result.price ? parseFloat(result.price) : 0;
            const priceText = result.available && price > 0 ? `$${price.toFixed(2)}` : '查询价格';

            html += `
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">${result.domain}</h6>
                                <span class="badge bg-${availableClass}">
                                    <i class="fas fa-${availableIcon} me-1"></i>
                                    ${availableText}
                                </span>
                            </div>

                            ${result.available ? `
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="text-muted small">注册价格</div>
                                        <div class="fw-bold text-primary">${priceText}</div>
                                    </div>
                                    <button class="btn btn-primary btn-sm" onclick="addToCart('${result.domain}', 'register', 1, ${price})">
                                        <i class="fas fa-cart-plus me-1"></i>
                                        加入购物车
                                    </button>
                                </div>
                            ` : `
                                <div class="text-muted small">
                                    ${result.reason || '域名已被注册'}
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button class="btn btn-outline-primary" onclick="searchMoreExtensions('${searchTerm}')">
                        <i class="fas fa-search-plus me-1"></i>
                        搜索更多后缀
                    </button>
                </div>
            </div>
        `;

        resultsContainer.innerHTML = html;
    }

    // 搜索更多后缀
    function searchMoreExtensions(domain) {
        const moreExtensions = '.info,.biz,.name,.mobi,.asia,.tel,.pro';

        const searchLoading = document.getElementById('searchLoading');
        const resultsContainer = document.getElementById('searchResultsContainer');

        searchLoading.style.display = 'block';

        fetch(`api/domain-search.php?domain=${encodeURIComponent(domain)}&extensions=${encodeURIComponent(moreExtensions)}`)
            .then(response => response.json())
            .then(data => {
                searchLoading.style.display = 'none';

                if (data.success) {
                    // 将新结果添加到现有结果中
                    const currentResults = resultsContainer.innerHTML;
                    const newResultsHtml = generateAdditionalResults(data.results, domain);
                    resultsContainer.innerHTML = currentResults.replace(
                        '<div class="row mt-3">',
                        newResultsHtml + '<div class="row mt-3">'
                    );
                }
            })
            .catch(error => {
                searchLoading.style.display = 'none';
                showAlert('搜索更多后缀失败: ' + error.message, 'danger');
            });
    }

    // 生成额外结果的HTML
    function generateAdditionalResults(results, searchTerm) {
        let html = `
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="mb-3">更多后缀选择</h6>
                </div>
            </div>
            <div class="row">
        `;

        results.forEach(result => {
            const availableClass = result.available ? 'success' : 'secondary';
            const availableText = result.available ? '可注册' : '不可用';
            const availableIcon = result.available ? 'check-circle' : 'times-circle';
            const price = result.available && result.price ? parseFloat(result.price) : 0;
            const priceText = result.available && price > 0 ? `$${price.toFixed(2)}` : '查询价格';

            html += `
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">${result.domain}</h6>
                                <span class="badge bg-${availableClass}">
                                    <i class="fas fa-${availableIcon} me-1"></i>
                                    ${availableText}
                                </span>
                            </div>

                            ${result.available ? `
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="text-muted small">注册价格</div>
                                        <div class="fw-bold text-primary">${priceText}</div>
                                    </div>
                                    <button class="btn btn-primary btn-sm" onclick="addToCart('${result.domain}', 'register', 1, ${price})">
                                        <i class="fas fa-cart-plus me-1"></i>
                                        加入购物车
                                    </button>
                                </div>
                            ` : `
                                <div class="text-muted small">
                                    ${result.reason || '域名已被注册'}
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    // 显示提示信息
    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在页面顶部显示提示
        const container = document.querySelector('.container');
        if (container) {
            container.insertAdjacentHTML('afterbegin', alertHtml);

            // 3秒后自动关闭
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }
    }

    // 热门后缀点击事件
    document.addEventListener('DOMContentLoaded', function() {
        // 为热门后缀添加点击事件
        document.querySelectorAll('.badge').forEach(badge => {
            if (badge.textContent.startsWith('.')) {
                badge.style.cursor = 'pointer';
                badge.addEventListener('click', function() {
                    const domainInput = document.getElementById('domainInput');
                    const currentValue = domainInput.value.trim();
                    const extension = this.textContent.trim();

                    if (currentValue && !currentValue.includes('.')) {
                        domainInput.value = currentValue + extension;
                        searchDomain();
                    }
                });
            }
        });
    });
                            <div class="fw-bold text-success">$${parseFloat(result.price || 0).toFixed(2)}/年</div>
                        </div>
                        <div class="col-md-3">
        `;

        if (result.available) {
            html += `
                <button class="btn btn-primary" onclick="addToCart('${result.domain}', 'register', 1, ${result.price})">
                    <i class="fas fa-cart-plus me-1"></i>
                    加入购物车
                </button>
            `;
        } else {
            html += `
                <button class="btn btn-secondary" disabled>
                    <i class="fas fa-ban me-1"></i>
                    不可注册
                </button>
            `;
        }

        html += `
                        </div>
                    </div>
                </div>
            </div>
        `;

        resultsContainer.innerHTML = html;
    }

    // 显示域名建议
    function displayDomainSuggestions(suggestions) {
        const resultsContainer = document.getElementById('searchResultsContainer');

        if (suggestions.length === 0) {
            return;
        }

        let html = resultsContainer.innerHTML;
        html += `
            <h6 class="mt-4 mb-3">相关域名推荐</h6>
            <div class="row g-3">
        `;

        suggestions.slice(0, 6).forEach(suggestion => {
            const availableClass = suggestion.available ? 'success' : 'secondary';
            const availableText = suggestion.available ? '可注册' : '不可用';

            html += `
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">${suggestion.domain}</h6>
                                <span class="badge bg-${availableClass}">${availableText}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-success fw-bold">$${parseFloat(suggestion.price || 0).toFixed(2)}/年</div>
            `;

            if (suggestion.available) {
                html += `
                    <button class="btn btn-outline-primary btn-sm" onclick="addToCart('${suggestion.domain}', 'register', 1, parseFloat(suggestion.price || 0))">
                        <i class="fas fa-cart-plus"></i>
                    </button>
                `;
            } else {
                html += `
                    <button class="btn btn-outline-secondary btn-sm" disabled>
                        <i class="fas fa-ban"></i>
                    </button>
                `;
            }

            html += `
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
            </div>
        `;

        resultsContainer.innerHTML = html;
    }

    // 添加到购物车
    function addToCart(domain, type, period, price) {
        fetch('api/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domain,
                type: type,
                period: period
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', '已添加到购物车');
                loadCartCount();
            } else {
                showAlert('error', '添加失败：' + data.message);
            }
        })
        .catch(error => {
            showAlert('error', '网络错误，请稍后重试');
        });
    }

    // 加载购物车数量
    function loadCartCount() {
        fetch('api/cart.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCartBadge(data.data.count);
                }
            })
            .catch(error => {
                console.error('加载购物车数量失败:', error);
            });
    }

    // 更新购物车徽章
    function updateCartBadge(count) {
        const badge = document.getElementById('cartBadge');
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }

    </script>
</body>
</html>
