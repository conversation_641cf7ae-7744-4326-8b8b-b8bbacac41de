-- 插入默认支付配置
INSERT INTO payment_configs (payment_method, name, enabled, config, sort_order) VALUES
('wechat', '微信支付', 0, JSON_OBJECT(
    'app_id', '',
    'mch_id', '',
    'api_key', '',
    'cert_path', '',
    'key_path', '',
    'notify_url', '/api/payment/notify/wechat'
), 1),
('alipay', '支付宝', 0, JSON_OBJECT(
    'app_id', '',
    'private_key', '',
    'public_key', '',
    'notify_url', '/api/payment/notify/alipay',
    'return_url', '/payment/return/alipay'
), 2),
('epay', '易支付', 0, JSON_OBJECT(
    'pid', '',
    'key', '',
    'api_url', 'https://pay.example.com/',
    'notify_url', '/api/payment/notify/epay',
    'return_url', '/payment/return/epay'
), 3)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    sort_order = VALUES(sort_order);
