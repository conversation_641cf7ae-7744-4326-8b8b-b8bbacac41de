<?php
/**
 * 检查邮件模板表状态
 */

header('Content-Type: application/json');

// 引入必要的文件
require_once __DIR__ . '/../includes/functions.php';

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) return;
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

loadEnv(__DIR__ . '/../../../.env');

try {
    $db = safeGetDatabase();
    
    $response = ['success' => true, 'data' => []];
    
    // 检查表是否存在
    $result = $db->query("SHOW TABLES LIKE 'email_templates'");
    $tableExists = $result->rowCount() > 0;
    
    $response['data']['table_exists'] = $tableExists;
    
    if ($tableExists) {
        // 检查表结构
        $result = $db->query("DESCRIBE email_templates");
        $columns = $result->fetchAll(PDO::FETCH_ASSOC);
        $response['data']['columns'] = $columns;
        
        // 检查是否有数据
        $result = $db->query("SELECT COUNT(*) as count FROM email_templates");
        $count = $result->fetch(PDO::FETCH_ASSOC);
        $response['data']['record_count'] = $count['count'];
        
        // 测试插入操作
        try {
            $stmt = $db->prepare("INSERT INTO email_templates (name, subject, content, variables) VALUES (?, ?, ?, ?)");
            $stmt->execute(['测试模板', '测试主题', '测试内容', 'test=value']);
            
            // 立即删除测试记录
            $testId = $db->lastInsertId();
            $db->prepare("DELETE FROM email_templates WHERE id = ?")->execute([$testId]);
            
            $response['data']['insert_test'] = 'success';
        } catch (Exception $e) {
            $response['data']['insert_test'] = 'failed';
            $response['data']['insert_error'] = $e->getMessage();
        }
    } else {
        $response['data']['insert_test'] = 'table_not_exists';
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
