<?php
/**
 * 数据库备份脚本
 * Database Backup Script
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义项目根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception('.env file not found');
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

// 数据库备份类
class DatabaseBackup {
    private $config;
    private $backupPath;
    
    public function __construct() {
        $this->loadConfig();
        $this->backupPath = ROOT_PATH . '/database/backups';
        $this->ensureBackupDirectory();
    }
    
    private function loadConfig() {
        $this->config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'www_bt_cn',
            'username' => $_ENV['DB_USERNAME'] ?? 'www_bt_cn',
            'password' => $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP',
        ];
    }
    
    private function ensureBackupDirectory() {
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }
    }
    
    public function backup($type = 'full') {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "backup_{$type}_{$timestamp}.sql";
        $filepath = $this->backupPath . '/' . $filename;
        
        echo "开始备份数据库...\n";
        echo "备份类型: {$type}\n";
        echo "备份文件: {$filename}\n";
        
        try {
            switch ($type) {
                case 'full':
                    $this->fullBackup($filepath);
                    break;
                case 'structure':
                    $this->structureBackup($filepath);
                    break;
                case 'data':
                    $this->dataBackup($filepath);
                    break;
                default:
                    throw new Exception("不支持的备份类型: {$type}");
            }
            
            $size = $this->formatBytes(filesize($filepath));
            echo "✓ 备份完成！文件大小: {$size}\n";
            echo "备份文件路径: {$filepath}\n";
            
            // 清理旧备份
            $this->cleanOldBackups();
            
            return $filepath;
        } catch (Exception $e) {
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            throw $e;
        }
    }
    
    private function fullBackup($filepath) {
        $command = $this->buildMysqldumpCommand([
            '--single-transaction',
            '--routines',
            '--triggers',
            '--events',
            '--add-drop-table',
            '--create-options',
            '--disable-keys',
            '--extended-insert',
            '--quick',
            '--lock-tables=false'
        ]);
        
        $command .= " > " . escapeshellarg($filepath);
        $this->executeCommand($command);
    }
    
    private function structureBackup($filepath) {
        $command = $this->buildMysqldumpCommand([
            '--no-data',
            '--routines',
            '--triggers',
            '--events',
            '--add-drop-table',
            '--create-options'
        ]);
        
        $command .= " > " . escapeshellarg($filepath);
        $this->executeCommand($command);
    }
    
    private function dataBackup($filepath) {
        $command = $this->buildMysqldumpCommand([
            '--no-create-info',
            '--disable-keys',
            '--extended-insert',
            '--quick',
            '--lock-tables=false'
        ]);
        
        $command .= " > " . escapeshellarg($filepath);
        $this->executeCommand($command);
    }
    
    private function buildMysqldumpCommand($options = []) {
        $command = 'mysqldump';
        $command .= ' --host=' . escapeshellarg($this->config['host']);
        $command .= ' --port=' . escapeshellarg($this->config['port']);
        $command .= ' --user=' . escapeshellarg($this->config['username']);
        
        if (!empty($this->config['password'])) {
            $command .= ' --password=' . escapeshellarg($this->config['password']);
        }
        
        foreach ($options as $option) {
            $command .= ' ' . $option;
        }
        
        $command .= ' ' . escapeshellarg($this->config['database']);
        
        return $command;
    }
    
    private function executeCommand($command) {
        $output = [];
        $returnCode = 0;
        
        exec($command . ' 2>&1', $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("备份命令执行失败: " . implode("\n", $output));
        }
    }
    
    public function restore($backupFile) {
        if (!file_exists($backupFile)) {
            throw new Exception("备份文件不存在: {$backupFile}");
        }
        
        echo "开始恢复数据库...\n";
        echo "备份文件: " . basename($backupFile) . "\n";
        
        $command = 'mysql';
        $command .= ' --host=' . escapeshellarg($this->config['host']);
        $command .= ' --port=' . escapeshellarg($this->config['port']);
        $command .= ' --user=' . escapeshellarg($this->config['username']);
        
        if (!empty($this->config['password'])) {
            $command .= ' --password=' . escapeshellarg($this->config['password']);
        }
        
        $command .= ' ' . escapeshellarg($this->config['database']);
        $command .= ' < ' . escapeshellarg($backupFile);
        
        $this->executeCommand($command);
        
        echo "✓ 数据库恢复完成！\n";
    }
    
    public function listBackups() {
        $backups = glob($this->backupPath . '/backup_*.sql');
        rsort($backups); // 按时间倒序
        
        echo "备份文件列表:\n";
        echo str_repeat('-', 80) . "\n";
        printf("%-30s %-15s %-20s\n", "文件名", "大小", "创建时间");
        echo str_repeat('-', 80) . "\n";
        
        foreach ($backups as $backup) {
            $filename = basename($backup);
            $size = $this->formatBytes(filesize($backup));
            $time = date('Y-m-d H:i:s', filemtime($backup));
            printf("%-30s %-15s %-20s\n", $filename, $size, $time);
        }
        
        if (empty($backups)) {
            echo "没有找到备份文件\n";
        }
    }
    
    private function cleanOldBackups() {
        $retentionDays = (int)($_ENV['BACKUP_RETENTION_DAYS'] ?? 30);
        $cutoffTime = time() - ($retentionDays * 24 * 60 * 60);
        
        $backups = glob($this->backupPath . '/backup_*.sql');
        $deletedCount = 0;
        
        foreach ($backups as $backup) {
            if (filemtime($backup) < $cutoffTime) {
                unlink($backup);
                $deletedCount++;
                echo "删除过期备份: " . basename($backup) . "\n";
            }
        }
        
        if ($deletedCount > 0) {
            echo "清理了 {$deletedCount} 个过期备份文件\n";
        }
    }
    
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// 命令行参数处理
function showUsage() {
    echo "用法: php backup.php [命令] [选项]\n\n";
    echo "命令:\n";
    echo "  backup [type]    创建备份 (type: full|structure|data, 默认: full)\n";
    echo "  restore <file>   恢复备份\n";
    echo "  list            列出所有备份文件\n";
    echo "  clean           清理过期备份\n";
    echo "\n";
    echo "示例:\n";
    echo "  php backup.php backup           # 完整备份\n";
    echo "  php backup.php backup structure # 仅备份结构\n";
    echo "  php backup.php backup data      # 仅备份数据\n";
    echo "  php backup.php restore backup_full_2023-12-01_10-30-00.sql\n";
    echo "  php backup.php list\n";
}

// 主执行逻辑
try {
    // 加载环境变量
    loadEnv(ROOT_PATH . '/.env');
    
    $backup = new DatabaseBackup();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'backup':
            $type = $argv[2] ?? 'full';
            $backup->backup($type);
            break;
            
        case 'restore':
            if (!isset($argv[2])) {
                echo "错误: 请指定备份文件\n";
                showUsage();
                exit(1);
            }
            $backupFile = $argv[2];
            if (!str_starts_with($backupFile, '/')) {
                $backupFile = ROOT_PATH . '/database/backups/' . $backupFile;
            }
            $backup->restore($backupFile);
            break;
            
        case 'list':
            $backup->listBackups();
            break;
            
        case 'clean':
            $backup->cleanOldBackups();
            break;
            
        case 'help':
        default:
            showUsage();
            break;
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
