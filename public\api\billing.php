<?php
/**
 * 账单和充值API
 * Billing and Recharge API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once '../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    // 获取数据库连接
    $db = getDatabase();
    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    // 检查是否是支付回调通知
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($requestUri, '/notify/') !== false && $method === 'POST') {
        // 处理支付回调通知
        $path = parse_url($requestUri, PHP_URL_PATH);
        $notifyMethod = str_replace('/api/billing.php/notify/', '', $path);

        $notifyData = $_POST;
        if (empty($notifyData)) {
            $notifyData = json_decode(file_get_contents('php://input'), true);
        }

        handlePaymentNotify($db, $notifyMethod, $notifyData);
        return;
    }

    // 检查是否是支付返回回调 (GET请求)
    if (strpos($requestUri, '/return/') !== false && $method === 'GET') {
        // 处理支付返回回调
        $path = parse_url($requestUri, PHP_URL_PATH);
        $returnMethod = str_replace('/api/billing.php/return/', '', $path);

        handlePaymentReturn($db, $returnMethod, $_GET);
        return;
    }

    // 获取用户ID，如果未登录则使用测试用户
    $userId = $_SESSION['user_id'] ?? 1; // 使用测试用户ID

    switch ($method) {
        case 'GET':
            handleGetRequest($db, $userId);
            break;

        case 'POST':
            handlePostRequest($db, $userId, $input);
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误：' . $e->getMessage()]);
}

/**
 * 处理GET请求
 */
function handleGetRequest($db, $userId) {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'balance':
            getUserBalance($db, $userId);
            break;

        case 'transactions':
            getTransactions($db, $userId);
            break;

        case 'recharge_methods':
            getRechargeMethods();
            break;

        case 'statistics':
            getBillingStatistics($db, $userId);
            break;

        case 'stats':
            getTransactionStats($db, $userId);
            break;

        default:
            // 默认返回账单概览
            echo json_encode([
                'success' => true,
                'message' => '账单API工作正常',
                'data' => [
                    'user_id' => $userId,
                    'balance' => getUserBalanceFromDB($db, $userId),
                    'currency' => 'CNY',
                    'last_transaction' => date('Y-m-d H:i:s')
                ]
            ]);
            break;
    }
}

/**
 * 处理POST请求
 */
function handlePostRequest($db, $userId, $input) {
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'recharge':
            createRechargeOrder($db, $userId, $input);
            break;

        case 'confirm_payment':
            confirmPayment($db, $userId, $input);
            break;

        case 'clear_transactions':
            clearTransactions($db, $userId, $input);
            break;

        default:
            echo json_encode([
                'success' => true,
                'message' => '账单POST请求处理成功',
                'data' => [
                    'action' => $action,
                    'user_id' => $userId,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            break;
    }
}

/**
 * 获取用户余额
 */
function getUserBalance($db, $userId) {
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }

    try {
        $stmt = $db->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            // 用户不存在，返回默认余额
            echo json_encode([
                'success' => true,
                'data' => [
                    'user_id' => $userId,
                    'balance' => 0.00,
                    'currency' => 'CNY',
                    'last_updated' => date('Y-m-d H:i:s'),
                    'note' => '新用户，余额为0'
                ]
            ]);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'user_id' => $userId,
                'balance' => floatval($user['balance']),
                'currency' => 'CNY',
                'last_updated' => date('Y-m-d H:i:s')
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '获取余额失败: ' . $e->getMessage()]);
    }
}

/**
 * 从数据库获取用户余额（内部函数）
 */
function getUserBalanceFromDB($db, $userId) {
    if ($db === null || !$userId) {
        return 0.00;
    }

    try {
        $stmt = $db->prepare("SELECT balance FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        return $user ? floatval($user['balance']) : 0.00;
    } catch (Exception $e) {
        return 0.00;
    }
}

/**
 * 获取交易记录
 */
function getTransactions($db, $userId) {
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }

    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        $type = $_GET['type'] ?? ''; // recharge, payment, refund

        // 构建查询条件
        $sql = "SELECT * FROM billing_transactions WHERE user_id = ?";
        $params = [$userId];

        if ($type) {
            $sql .= " AND type = ?";
            $params[] = $type;
        }

        $sql .= " ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = (int)$offset;
        $params[] = (int)$limit;

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $transactions = $stmt->fetchAll();

        // 获取总数
        $countSql = "SELECT COUNT(*) FROM billing_transactions WHERE user_id = ?";
        $countParams = [$userId];

        if ($type) {
            $countSql .= " AND type = ?";
            $countParams[] = $type;
        }

        $stmt = $db->prepare($countSql);
        $stmt->execute($countParams);
        $total = $stmt->fetchColumn();

        echo json_encode([
            'success' => true,
            'data' => [
                'transactions' => $transactions,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '获取交易记录失败: ' . $e->getMessage()]);
    }
}

/**
 * 获取充值方式
 */
function getRechargeMethods() {
    $methods = [
        [
            'id' => 'alipay',
            'name' => '支付宝',
            'icon' => 'fab fa-alipay',
            'color' => 'primary',
            'enabled' => true,
            'min_amount' => 10,
            'max_amount' => 10000,
            'fee_rate' => 0
        ],
        [
            'id' => 'wechat',
            'name' => '微信支付',
            'icon' => 'fab fa-weixin',
            'color' => 'success',
            'enabled' => true,
            'min_amount' => 10,
            'max_amount' => 10000,
            'fee_rate' => 0
        ],
        [
            'id' => 'bank',
            'name' => '银行转账',
            'icon' => 'fas fa-university',
            'color' => 'info',
            'enabled' => false,
            'min_amount' => 100,
            'max_amount' => 50000,
            'fee_rate' => 0
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $methods
    ]);
}

/**
 * 获取账单统计
 */
function getBillingStatistics($db, $userId) {
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }

    try {
        // 总充值金额
        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) FROM billing_transactions WHERE user_id = ? AND type = 'recharge' AND status = 'completed'");
        $stmt->execute([$userId]);
        $totalRecharge = $stmt->fetchColumn();

        // 总消费金额
        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) FROM billing_transactions WHERE user_id = ? AND type = 'payment' AND status = 'completed'");
        $stmt->execute([$userId]);
        $totalSpent = $stmt->fetchColumn();

        // 本月充值
        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) FROM billing_transactions WHERE user_id = ? AND type = 'recharge' AND status = 'completed' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
        $stmt->execute([$userId]);
        $monthlyRecharge = $stmt->fetchColumn();

        // 本月消费
        $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) FROM billing_transactions WHERE user_id = ? AND type = 'payment' AND status = 'completed' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
        $stmt->execute([$userId]);
        $monthlySpent = $stmt->fetchColumn();

        echo json_encode([
            'success' => true,
            'data' => [
                'total_recharge' => floatval($totalRecharge),
                'total_spent' => floatval($totalSpent),
                'monthly_recharge' => floatval($monthlyRecharge),
                'monthly_spent' => floatval($monthlySpent)
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '获取统计数据失败: ' . $e->getMessage()]);
    }
}

/**
 * 创建充值订单
 */
function createRechargeOrder($db, $userId, $input) {
    $amount = floatval($input['amount'] ?? 0);
    $method = $input['method'] ?? '';

    if ($amount <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '充值金额必须大于0']);
        return;
    }

    if (!in_array($method, ['alipay', 'wechat', 'bank'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '不支持的支付方式']);
        return;
    }

    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }
    
    try {
        $db->beginTransaction();
        
        // 创建充值订单
        $orderNumber = 'RCH' . date('YmdHis') . rand(1000, 9999);
        $stmt = $db->prepare("
            INSERT INTO billing_transactions (user_id, type, order_number, amount, payment_method, status, created_at) 
            VALUES (?, 'recharge', ?, ?, ?, 'pending', NOW())
        ");
        $stmt->execute([$userId, $orderNumber, $amount, $method]);
        $transactionId = $db->lastInsertId();
        
        $db->commit();
        
        // 调用支付API创建真实支付订单
        $paymentInfo = createRealPayment($transactionId, $method, $amount, $orderNumber);
        
        echo json_encode([
            'success' => true,
            'message' => '充值订单创建成功',
            'data' => [
                'transaction_id' => $transactionId,
                'order_number' => $orderNumber,
                'amount' => $amount,
                'method' => $method,
                'payment_info' => $paymentInfo
            ]
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '创建充值订单失败：' . $e->getMessage()]);
    }
}

/**
 * 检查支付状态（不直接确认支付）
 */
function confirmPayment($db, $userId, $input) {
    $transactionId = intval($input['transaction_id'] ?? 0);
    $orderNumber = $input['order_number'] ?? '';

    if ($transactionId <= 0 && empty($orderNumber)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '交易ID或订单号无效']);
        return;
    }

    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => '请先登录']);
        return;
    }

    try {
        // 获取交易信息
        if ($transactionId > 0) {
            $stmt = $db->prepare("SELECT * FROM billing_transactions WHERE id = ? AND user_id = ? AND type = 'recharge'");
            $stmt->execute([$transactionId, $userId]);
        } else {
            $stmt = $db->prepare("SELECT * FROM billing_transactions WHERE order_number = ? AND user_id = ? AND type = 'recharge'");
            $stmt->execute([$orderNumber, $userId]);
        }
        $transaction = $stmt->fetch();

        if (!$transaction) {
            echo json_encode(['success' => false, 'message' => '交易不存在']);
            return;
        }

        // 检查交易状态，不直接确认支付
        if ($transaction['status'] === 'completed') {
            echo json_encode([
                'success' => true,
                'message' => '支付已完成',
                'data' => [
                    'status' => 'completed',
                    'amount' => floatval($transaction['amount'])
                ]
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '支付尚未完成，请确认支付后再试',
                'data' => [
                    'status' => $transaction['status'],
                    'amount' => floatval($transaction['amount'])
                ]
            ]);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '检查支付状态失败：' . $e->getMessage()]);
    }
}

/**
 * 创建真实支付订单
 */
function createRealPayment($transactionId, $method, $amount, $orderNumber) {
    // 获取支付配置
    $config = getPaymentConfig($method);
    if (!$config) {
        throw new Exception('支付方式未配置或未启用');
    }

    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'];
    $notifyUrl = $baseUrl . '/api/billing.php/notify/' . $method;
    $returnUrl = $baseUrl . '/api/billing.php/return/' . $method;

    switch ($method) {
        case 'alipay':
            return createAlipayPayment($config, $amount, $orderNumber, $notifyUrl, $returnUrl);

        case 'wechat':
            return createWechatPayment($config, $amount, $orderNumber, $notifyUrl, $returnUrl);

        case 'epay':
            return createEpayPayment($config, $amount, $orderNumber, $notifyUrl, $returnUrl);

        default:
            throw new Exception('不支持的支付方式');
    }
}

/**
 * 获取支付配置
 */
function getPaymentConfig($method) {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT enabled, config FROM payment_configs WHERE payment_method = ? AND enabled = 1");
    $stmt->execute([$method]);
    $result = $stmt->fetch();

    if (!$result) {
        return null;
    }

    return json_decode($result['config'], true);
}

/**
 * 创建支付宝支付
 */
function createAlipayPayment($config, $amount, $orderNumber, $notifyUrl, $returnUrl) {
    // 检查必要的配置参数
    if (empty($config['partner_id']) || empty($config['md5_key'])) {
        throw new Exception('支付宝配置不完整，请联系管理员');
    }

    // 构建支付宝支付参数 - 使用即时到账接口
    $params = [
        'service' => 'create_direct_pay_by_user',
        'partner' => $config['partner_id'],
        'payment_type' => '1',
        'notify_url' => $notifyUrl,
        'return_url' => $returnUrl,
        'out_trade_no' => $orderNumber,
        'subject' => '账户充值',
        'body' => "充值金额：¥{$amount}",
        'total_fee' => $amount,
        '_input_charset' => 'utf-8'
    ];

    // 添加卖家信息 - 优先使用seller_id，如果没有则使用seller_email
    if (!empty($config['partner_id'])) {
        $params['seller_id'] = $config['partner_id'];
    } elseif (!empty($config['seller_email'])) {
        $params['seller_email'] = $config['seller_email'];
    }

    // 生成签名
    $sign = generateAlipaySign($params, $config['md5_key']);
    $params['sign'] = $sign;
    $params['sign_type'] = 'MD5';

    // 调试信息
    error_log("支付宝支付参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));
    error_log("支付宝签名字符串: " . generateAlipaySignString($params, $config['md5_key']));

    // 构建支付URL
    $gatewayUrl = $config['gateway_url'] ?? 'https://mapi.alipay.com/gateway.do';
    $paymentUrl = $gatewayUrl . '?' . http_build_query($params);

    error_log("支付宝支付URL: " . $paymentUrl);

    return [
        'type' => 'redirect',
        'payment_url' => $paymentUrl,
        'instructions' => '点击下方按钮跳转到支付宝完成支付',
        'order_number' => $orderNumber,
        'amount' => $amount
    ];
}

/**
 * 创建微信支付
 */
function createWechatPayment($config, $amount, $orderNumber, $notifyUrl, $returnUrl) {
    // 这里应该调用微信支付API创建订单
    // 目前返回二维码支付方式
    return [
        'type' => 'qrcode',
        'qr_code' => generateWechatQRCode($config, $amount, $orderNumber, $notifyUrl),
        'instructions' => '请使用微信扫描二维码完成支付',
        'order_number' => $orderNumber,
        'amount' => $amount
    ];
}

/**
 * 创建易支付
 */
function createEpayPayment($config, $amount, $orderNumber, $notifyUrl, $returnUrl) {
    $params = [
        'pid' => $config['pid'],
        'type' => 'alipay',
        'out_trade_no' => $orderNumber,
        'notify_url' => $notifyUrl,
        'return_url' => $returnUrl,
        'name' => '账户充值',
        'money' => $amount,
        'sitename' => '域名销售系统'
    ];

    // 生成签名
    $sign = generateEpaySign($params, $config['key']);
    $params['sign'] = $sign;
    $params['sign_type'] = 'MD5';

    // 构建支付URL
    $apiUrl = rtrim($config['api_url'], '/') . '/submit.php';
    $paymentUrl = $apiUrl . '?' . http_build_query($params);

    return [
        'type' => 'redirect',
        'payment_url' => $paymentUrl,
        'instructions' => '点击下方按钮跳转到易支付完成支付',
        'order_number' => $orderNumber,
        'amount' => $amount
    ];
}

/**
 * 生成支付信息（模拟/备用）
 */
function generatePaymentInfo($method, $amount, $orderNumber) {
    switch ($method) {
        case 'alipay':
            return [
                'type' => 'redirect',
                'payment_url' => "/test-alipay.php?amount={$amount}&order={$orderNumber}",
                'instructions' => '点击下方按钮跳转到支付宝支付页面'
            ];

        case 'wechat':
            return [
                'type' => 'qrcode',
                'qr_code' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                'instructions' => '请使用微信扫描二维码完成支付'
            ];

        case 'bank':
            return [
                'type' => 'bank_info',
                'bank_name' => '中国工商银行',
                'account_name' => 'NameSilo域名销售系统',
                'account_number' => '6222 0000 0000 0000',
                'instructions' => '请转账到指定银行账户，并在备注中填写订单号：' . $orderNumber
            ];

        default:
            return null;
    }
}

/**
 * 生成支付宝签名字符串（用于调试）
 */
function generateAlipaySignString($params, $key) {
    // 移除空值和sign相关参数
    $filteredParams = [];
    foreach ($params as $k => $v) {
        if ($v !== '' && $v !== null && $k !== 'sign' && $k !== 'sign_type') {
            // 确保参数值是字符串类型
            $filteredParams[$k] = (string)$v;
        }
    }

    // 按键名ASCII码排序
    ksort($filteredParams);

    // 构建签名字符串 - 注意：签名时不进行URL编码
    $signString = '';
    foreach ($filteredParams as $k => $v) {
        if ($signString !== '') {
            $signString .= '&';
        }
        $signString .= $k . '=' . $v;
    }

    // 添加密钥
    $signString .= $key;

    return $signString;
}

/**
 * 生成支付宝签名
 */
function generateAlipaySign($params, $key) {
    $signString = generateAlipaySignString($params, $key);
    return md5($signString);
}

/**
 * 生成易支付签名
 */
function generateEpaySign($params, $key) {
    // 移除空值和sign参数
    $params = array_filter($params, function($value) {
        return $value !== '' && $value !== null;
    });
    unset($params['sign'], $params['sign_type']);

    // 按键名排序
    ksort($params);

    // 构建签名字符串
    $signString = '';
    foreach ($params as $k => $v) {
        $signString .= $k . '=' . $v . '&';
    }
    $signString = rtrim($signString, '&') . $key;

    return md5($signString);
}

/**
 * 生成微信支付二维码
 */
function generateWechatQRCode($config, $amount, $orderNumber, $notifyUrl) {
    // 这里应该调用微信支付API生成二维码
    // 目前返回占位符
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
}

/**
 * 处理支付返回回调 (用户从支付页面返回)
 */
function handlePaymentReturn($db, $method, $data) {
    try {
        error_log("支付返回回调: method={$method}, data=" . json_encode($data));

        $orderNumber = '';
        $tradeNo = '';
        $status = 'failed';

        switch ($method) {
            case 'alipay':
                // 处理支付宝返回
                $orderNumber = $data['out_trade_no'] ?? '';
                $tradeNo = $data['trade_no'] ?? '';
                $tradeStatus = $data['trade_status'] ?? '';

                // 验证支付宝返回签名
                if (verifyAlipayReturnSign($data)) {
                    if (in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                        $status = 'completed';
                    }
                } else {
                    error_log("支付宝返回签名验证失败");
                }
                break;

            case 'wechat':
                // 处理微信支付返回
                $orderNumber = $data['out_trade_no'] ?? '';
                $tradeNo = $data['transaction_id'] ?? '';
                // 微信支付返回通常不包含支付状态，需要主动查询
                break;

            default:
                error_log("不支持的支付返回方式: {$method}");
                break;
        }

        if (!empty($orderNumber)) {
            // 处理订单状态更新
            $result = processPaymentCompletion($db, $orderNumber, $tradeNo, $status, 'return_callback');

            if ($result['success']) {
                // 重定向到成功页面
                $redirectUrl = '/user/billing.php?status=success&order=' . urlencode($orderNumber) . '&amount=' . urlencode($result['amount']);
            } else {
                // 重定向到失败页面
                $redirectUrl = '/user/billing.php?status=failed&order=' . urlencode($orderNumber) . '&error=' . urlencode($result['message']);
            }
        } else {
            $redirectUrl = '/user/billing.php?status=failed&error=' . urlencode('订单号缺失');
        }

        // 执行重定向
        header('Location: ' . $redirectUrl);
        exit;

    } catch (Exception $e) {
        error_log("支付返回回调处理失败: " . $e->getMessage());
        header('Location: /user/billing.php?status=error&error=' . urlencode($e->getMessage()));
        exit;
    }
}

/**
 * 验证支付宝返回签名
 */
function verifyAlipayReturnSign($data) {
    try {
        $config = getPaymentConfig('alipay');
        if (!$config) {
            return false;
        }

        $sign = $data['sign'] ?? '';
        if (empty($sign)) {
            return false;
        }

        // 移除签名参数
        $verifyData = $data;
        unset($verifyData['sign']);
        unset($verifyData['sign_type']);

        // 生成验证签名
        $signString = generateAlipaySignString($verifyData, $config['md5_key']);
        $expectedSign = md5($signString);

        return $sign === $expectedSign;
    } catch (Exception $e) {
        error_log("支付宝签名验证异常: " . $e->getMessage());
        return false;
    }
}

/**
 * 处理支付完成 (统一处理函数)
 */
function processPaymentCompletion($db, $orderNumber, $tradeNo, $status, $source = 'notify') {
    try {
        if (empty($orderNumber)) {
            throw new Exception('订单号为空');
        }

        // 查找对应的充值交易
        $stmt = $db->prepare("SELECT * FROM billing_transactions WHERE order_number = ? AND type = 'recharge'");
        $stmt->execute([$orderNumber]);
        $transaction = $stmt->fetch();

        if (!$transaction) {
            throw new Exception('交易不存在');
        }

        // 如果已经完成，直接返回成功
        if ($transaction['status'] === 'completed') {
            return [
                'success' => true,
                'message' => '订单已完成',
                'amount' => $transaction['amount'],
                'already_completed' => true
            ];
        }

        if ($status === 'completed') {
            $db->beginTransaction();

            // 更新交易状态
            $stmt = $db->prepare("UPDATE billing_transactions SET status = 'completed', trade_no = ?, completed_at = NOW(), callback_source = ? WHERE id = ?");
            $stmt->execute([$tradeNo, $source, $transaction['id']]);

            // 增加用户余额
            $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
            $stmt->execute([$transaction['amount'], $transaction['user_id']]);

            $db->commit();

            // 记录日志
            error_log("充值成功({$source}): 订单号={$orderNumber}, 金额={$transaction['amount']}, 用户ID={$transaction['user_id']}");

            return [
                'success' => true,
                'message' => '充值成功',
                'amount' => $transaction['amount']
            ];
        } else {
            return [
                'success' => false,
                'message' => '支付未成功',
                'amount' => $transaction['amount']
            ];
        }

    } catch (Exception $e) {
        error_log("支付完成处理失败: " . $e->getMessage());
        return [
            'success' => false,
            'message' => $e->getMessage(),
            'amount' => 0
        ];
    }
}

/**
 * 处理支付回调通知
 */
function handlePaymentNotify($db, $method, $data) {
    try {
        error_log("支付异步回调: method={$method}, data=" . json_encode($data));

        $orderNumber = '';
        $tradeNo = '';
        $status = 'failed';

        switch ($method) {
            case 'alipay':
                // 处理支付宝回调
                $orderNumber = $data['out_trade_no'] ?? '';
                $tradeNo = $data['trade_no'] ?? '';
                $tradeStatus = $data['trade_status'] ?? '';

                // 验证支付宝回调签名
                if (verifyAlipayNotifySign($data)) {
                    if (in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                        $status = 'completed';
                    }
                } else {
                    error_log("支付宝回调签名验证失败");
                }
                break;

            case 'wechat':
                // 处理微信支付回调
                $orderNumber = $data['out_trade_no'] ?? '';
                $tradeNo = $data['transaction_id'] ?? '';
                $resultCode = $data['result_code'] ?? '';

                if ($resultCode === 'SUCCESS') {
                    $status = 'completed';
                }
                break;

            case 'epay':
                // 处理易支付回调
                $orderNumber = $data['out_trade_no'] ?? '';
                $tradeNo = $data['trade_no'] ?? '';
                $tradeStatus = $data['trade_status'] ?? '';

                if ($tradeStatus === 'TRADE_SUCCESS') {
                    $status = 'completed';
                }
                break;

            default:
                throw new Exception('不支持的支付方式');
        }

        // 使用统一的支付完成处理函数
        $result = processPaymentCompletion($db, $orderNumber, $tradeNo, $status, 'async_notify');

        // 返回成功响应
        switch ($method) {
            case 'alipay':
                echo $result['success'] ? 'success' : 'fail';
                break;
            case 'wechat':
                echo $result['success'] ? 'SUCCESS' : 'FAIL';
                break;
            case 'epay':
                echo $result['success'] ? 'success' : 'fail';
                break;
            default:
                echo $result['success'] ? 'OK' : 'ERROR';
        }

    } catch (Exception $e) {
        error_log("支付回调处理失败: " . $e->getMessage());

        // 返回失败响应
        switch ($method) {
            case 'alipay':
                echo 'fail';
                break;
            case 'wechat':
                echo 'FAIL';
                break;
            case 'epay':
                echo 'fail';
                break;
            default:
                echo 'ERROR';
        }
    }
}

/**
 * 验证支付宝异步回调签名
 */
function verifyAlipayNotifySign($data) {
    try {
        $config = getPaymentConfig('alipay');
        if (!$config) {
            return false;
        }

        $sign = $data['sign'] ?? '';
        if (empty($sign)) {
            return false;
        }

        // 移除签名参数
        $verifyData = $data;
        unset($verifyData['sign']);
        unset($verifyData['sign_type']);

        // 生成验证签名
        $signString = generateAlipaySignString($verifyData, $config['md5_key']);
        $expectedSign = md5($signString);

        return $sign === $expectedSign;
    } catch (Exception $e) {
        error_log("支付宝回调签名验证异常: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取交易记录统计信息
 */
function getTransactionStats($db, $userId) {
    try {
        $type = $_GET['type'] ?? 'all';

        if ($type === 'pending') {
            // 获取未支付记录统计
            $stmt = $db->prepare("
                SELECT
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) as amount
                FROM billing_transactions
                WHERE user_id = ? AND status = 'pending'
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'data' => [
                    'count' => (int)$result['count'],
                    'amount' => number_format($result['amount'], 2)
                ]
            ]);

        } else if ($type === 'all') {
            // 获取全部记录统计
            $stmt = $db->prepare("
                SELECT
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) as amount,
                    SUM(CASE WHEN type = 'recharge' THEN 1 ELSE 0 END) as recharge_count,
                    SUM(CASE WHEN type = 'payment' THEN 1 ELSE 0 END) as payment_count,
                    SUM(CASE WHEN type = 'refund' THEN 1 ELSE 0 END) as refund_count
                FROM billing_transactions
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'data' => [
                    'count' => (int)$result['count'],
                    'amount' => number_format($result['amount'], 2),
                    'recharge_count' => (int)$result['recharge_count'],
                    'payment_count' => (int)$result['payment_count'],
                    'refund_count' => (int)$result['refund_count']
                ]
            ]);
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取统计信息失败：' . $e->getMessage()
        ]);
    }
}

/**
 * 清空交易记录
 */
function clearTransactions($db, $userId, $input) {
    try {
        $type = $input['type'] ?? 'all';

        if ($type === 'pending') {
            // 清空未支付记录
            $stmt = $db->prepare("DELETE FROM billing_transactions WHERE user_id = ? AND status = 'pending'");
            $stmt->execute([$userId]);
            $deletedCount = $stmt->rowCount();

            echo json_encode([
                'success' => true,
                'message' => "成功清空 {$deletedCount} 条未支付记录",
                'data' => [
                    'deleted_count' => $deletedCount,
                    'type' => 'pending'
                ]
            ]);

        } else if ($type === 'all') {
            // 清空全部记录
            $stmt = $db->prepare("DELETE FROM billing_transactions WHERE user_id = ?");
            $stmt->execute([$userId]);
            $deletedCount = $stmt->rowCount();

            echo json_encode([
                'success' => true,
                'message' => "成功清空 {$deletedCount} 条交易记录",
                'data' => [
                    'deleted_count' => $deletedCount,
                    'type' => 'all'
                ]
            ]);

        } else {
            echo json_encode([
                'success' => false,
                'message' => '无效的清空类型'
            ]);
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '清空交易记录失败：' . $e->getMessage()
        ]);
    }
}
?>
