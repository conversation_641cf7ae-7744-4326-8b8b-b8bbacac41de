<?php
/**
 * 邮件错误处理器
 * Email Error Handler
 */

class EmailErrorHandler {
    
    /**
     * 分析SMTP错误并提供解决方案
     */
    public static function analyzeError($errorMessage, $smtpHost = '') {
        $errorMessage = strtolower($errorMessage);
        $host = strtolower($smtpHost);
        
        // QQ邮箱特定错误
        if (strpos($host, 'qq.com') !== false) {
            return self::handleQQErrors($errorMessage);
        }
        
        // Gmail特定错误
        if (strpos($host, 'gmail.com') !== false) {
            return self::handleGmailErrors($errorMessage);
        }
        
        // 163邮箱特定错误
        if (strpos($host, '163.com') !== false) {
            return self::handle163Errors($errorMessage);
        }
        
        // Outlook特定错误
        if (strpos($host, 'outlook.com') !== false || strpos($host, 'hotmail.com') !== false) {
            return self::handleOutlookErrors($errorMessage);
        }
        
        // 通用错误处理
        return self::handleGenericErrors($errorMessage);
    }
    
    /**
     * 处理QQ邮箱错误
     */
    private static function handleQQErrors($errorMessage) {
        if (strpos($errorMessage, '535') !== false && strpos($errorMessage, 'login fail') !== false) {
            return [
                'type' => 'auth_error',
                'title' => 'QQ邮箱认证失败',
                'message' => '请检查以下设置：',
                'solutions' => [
                    '确认已在QQ邮箱网页版开启SMTP服务',
                    '使用授权码作为密码，不是QQ密码',
                    '用户名必须是完整的邮箱地址（如：<EMAIL>）',
                    '检查是否有登录频率限制，稍后再试'
                ],
                'help_link' => 'qq-email-setup.php',
                'help_text' => '查看QQ邮箱详细配置指南'
            ];
        }
        
        if (strpos($errorMessage, 'starttls') !== false) {
            return [
                'type' => 'tls_error',
                'title' => 'TLS连接失败',
                'message' => 'QQ邮箱TLS连接问题：',
                'solutions' => [
                    '确认使用端口587和TLS加密',
                    '检查服务器是否支持TLS 1.2',
                    '尝试使用SSL加密和端口465'
                ]
            ];
        }
        
        return self::handleGenericErrors($errorMessage);
    }
    
    /**
     * 处理Gmail错误
     */
    private static function handleGmailErrors($errorMessage) {
        if (strpos($errorMessage, '535') !== false) {
            return [
                'type' => 'auth_error',
                'title' => 'Gmail认证失败',
                'message' => 'Gmail需要特殊配置：',
                'solutions' => [
                    '开启两步验证',
                    '生成应用专用密码',
                    '使用应用专用密码而不是Gmail密码',
                    '确认"允许不够安全的应用"已开启（如果未使用两步验证）'
                ]
            ];
        }
        
        return self::handleGenericErrors($errorMessage);
    }
    
    /**
     * 处理163邮箱错误
     */
    private static function handle163Errors($errorMessage) {
        if (strpos($errorMessage, '535') !== false) {
            return [
                'type' => 'auth_error',
                'title' => '163邮箱认证失败',
                'message' => '163邮箱配置要求：',
                'solutions' => [
                    '在163邮箱设置中开启SMTP服务',
                    '使用授权码作为密码',
                    '用户名使用完整邮箱地址',
                    '确认授权码输入正确'
                ]
            ];
        }
        
        return self::handleGenericErrors($errorMessage);
    }
    
    /**
     * 处理Outlook错误
     */
    private static function handleOutlookErrors($errorMessage) {
        if (strpos($errorMessage, '535') !== false) {
            return [
                'type' => 'auth_error',
                'title' => 'Outlook认证失败',
                'message' => 'Outlook邮箱配置：',
                'solutions' => [
                    '使用完整的邮箱地址作为用户名',
                    '使用Microsoft账户密码',
                    '如果开启了两步验证，需要使用应用密码',
                    '确认账户没有被锁定'
                ]
            ];
        }
        
        return self::handleGenericErrors($errorMessage);
    }
    
    /**
     * 处理通用错误
     */
    private static function handleGenericErrors($errorMessage) {
        // 连接错误
        if (strpos($errorMessage, 'connection') !== false || strpos($errorMessage, '连接') !== false) {
            return [
                'type' => 'connection_error',
                'title' => '连接失败',
                'message' => '无法连接到SMTP服务器：',
                'solutions' => [
                    '检查SMTP服务器地址是否正确',
                    '检查端口号是否正确',
                    '检查网络连接',
                    '确认防火墙没有阻止连接'
                ]
            ];
        }
        
        // TLS/SSL错误
        if (strpos($errorMessage, 'tls') !== false || strpos($errorMessage, 'ssl') !== false) {
            return [
                'type' => 'encryption_error',
                'title' => '加密连接失败',
                'message' => 'TLS/SSL连接问题：',
                'solutions' => [
                    '检查加密方式设置（TLS/SSL）',
                    '尝试不同的端口（587用TLS，465用SSL）',
                    '确认服务器支持所选的加密方式',
                    '检查PHP的OpenSSL扩展是否启用'
                ]
            ];
        }
        
        // 认证错误
        if (strpos($errorMessage, '535') !== false || strpos($errorMessage, 'auth') !== false) {
            return [
                'type' => 'auth_error',
                'title' => '认证失败',
                'message' => '用户名或密码错误：',
                'solutions' => [
                    '检查用户名是否正确（通常是完整邮箱地址）',
                    '检查密码是否正确',
                    '某些邮箱需要使用授权码而不是登录密码',
                    '检查账户是否被锁定或限制'
                ]
            ];
        }
        
        // 默认错误
        return [
            'type' => 'unknown_error',
            'title' => '未知错误',
            'message' => '发生了未知的SMTP错误：',
            'solutions' => [
                '检查所有SMTP配置是否正确',
                '尝试使用邮件客户端测试相同配置',
                '联系邮件服务商确认服务状态',
                '查看服务器错误日志获取更多信息'
            ]
        ];
    }
    
    /**
     * 格式化错误信息为HTML
     */
    public static function formatErrorHtml($errorAnalysis) {
        $html = '<div class="alert alert-danger">';
        $html .= '<h6><i class="fas fa-exclamation-triangle me-2"></i>' . $errorAnalysis['title'] . '</h6>';
        $html .= '<p>' . $errorAnalysis['message'] . '</p>';
        
        if (!empty($errorAnalysis['solutions'])) {
            $html .= '<ul class="mb-2">';
            foreach ($errorAnalysis['solutions'] as $solution) {
                $html .= '<li>' . htmlspecialchars($solution) . '</li>';
            }
            $html .= '</ul>';
        }
        
        if (!empty($errorAnalysis['help_link'])) {
            $html .= '<a href="' . $errorAnalysis['help_link'] . '" class="btn btn-sm btn-outline-primary" target="_blank">';
            $html .= '<i class="fas fa-external-link-alt me-1"></i>' . $errorAnalysis['help_text'] . '</a>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
?>
