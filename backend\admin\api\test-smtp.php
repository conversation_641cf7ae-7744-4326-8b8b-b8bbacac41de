<?php
/**
 * SMTP连接测试API
 * SMTP Connection Test API
 */

// 关闭错误显示，避免影响JSON输出
error_reporting(0);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
define('ADMIN_PATH', dirname(__DIR__));

// 引入通用函数
require_once ADMIN_PATH . '/includes/functions.php';
require_once ADMIN_PATH . '/includes/email-error-handler.php';

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) return;
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value, '"\'');
        }
    }
}

loadEnv(ROOT_PATH . '/.env');

// 简单的SMTP连接测试函数
function testSMTPConnection($config) {
    $host = $config['smtp_host'];
    $port = $config['smtp_port'] ?? 587;
    $encryption = $config['smtp_encryption'] ?? 'tls';
    
    try {
        // 创建连接
        $context = stream_context_create();
        
        if ($encryption === 'ssl') {
            $socket = stream_socket_client(
                "ssl://{$host}:{$port}",
                $errno, $errstr, 10, STREAM_CLIENT_CONNECT, $context
            );
        } else {
            $socket = stream_socket_client(
                "{$host}:{$port}",
                $errno, $errstr, 10, STREAM_CLIENT_CONNECT, $context
            );
        }
        
        if (!$socket) {
            return [
                'success' => false, 
                'message' => "无法连接到SMTP服务器 {$host}:{$port} - {$errstr} ({$errno})"
            ];
        }
        
        // 读取服务器欢迎消息
        $response = fgets($socket, 512);
        if (substr($response, 0, 3) !== '220') {
            fclose($socket);
            return [
                'success' => false, 
                'message' => "SMTP服务器响应异常: " . trim($response)
            ];
        }
        
        // 发送EHLO命令
        $hostname = $_SERVER['HTTP_HOST'] ?? 'localhost';
        fwrite($socket, "EHLO {$hostname}\r\n");

        // 读取完整的EHLO响应（通常有多行）
        $response = '';
        $startTime = time();
        while (time() - $startTime < 5) { // 5秒超时
            $line = fgets($socket, 512);
            if ($line === false) break;

            $response .= $line;

            // 检查是否是最后一行（没有连字符）
            if (strlen($line) >= 4 && $line[3] !== '-') {
                break;
            }
        }

        // 检查响应码（取第一行的前3个字符）
        $firstLine = strtok($response, "\r\n");
        if (substr($firstLine, 0, 3) !== '250') {
            fclose($socket);
            return [
                'success' => false,
                'message' => "EHLO命令失败: " . trim($firstLine)
            ];
        }
        
        // 如果是TLS，测试STARTTLS
        if ($encryption === 'tls') {
            fwrite($socket, "STARTTLS\r\n");

            // 读取完整的STARTTLS响应（可能有多行）
            $response = '';
            $startTime = time();
            while (time() - $startTime < 5) { // 5秒超时
                $line = fgets($socket, 512);
                if ($line === false) break;

                $response .= $line;

                // 检查是否是最后一行（没有连字符）
                if (strlen($line) >= 4 && $line[3] !== '-') {
                    break;
                }
            }

            // 检查响应码（取第一行的前3个字符）
            $firstLine = strtok($response, "\r\n");
            if (substr($firstLine, 0, 3) !== '220') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "STARTTLS失败: " . trim($firstLine)
                ];
            }
            
            // 尝试启用TLS
            $cryptoMethod = STREAM_CRYPTO_METHOD_TLS_CLIENT;
            if (defined('STREAM_CRYPTO_METHOD_TLSv1_2_CLIENT')) {
                $cryptoMethod = STREAM_CRYPTO_METHOD_TLSv1_2_CLIENT;
            }
            
            if (!stream_socket_enable_crypto($socket, true, $cryptoMethod)) {
                fclose($socket);
                return [
                    'success' => false, 
                    'message' => "TLS加密启用失败，请检查服务器TLS支持"
                ];
            }
            
            // TLS后重新EHLO
            fwrite($socket, "EHLO {$hostname}\r\n");

            // 读取完整的EHLO响应
            $response = '';
            $startTime = time();
            while (time() - $startTime < 5) {
                $line = fgets($socket, 512);
                if ($line === false) break;

                $response .= $line;

                if (strlen($line) >= 4 && $line[3] !== '-') {
                    break;
                }
            }

            $firstLine = strtok($response, "\r\n");
            if (substr($firstLine, 0, 3) !== '250') {
                fclose($socket);
                return [
                    'success' => false,
                    'message' => "TLS后EHLO失败: " . trim($firstLine)
                ];
            }
        }
        
        // 测试认证（如果提供了用户名和密码）
        if (!empty($config['smtp_username']) && !empty($config['smtp_password'])) {
            fwrite($socket, "AUTH LOGIN\r\n");
            $response = fgets($socket, 512);
            
            if (substr($response, 0, 3) !== '334') {
                fclose($socket);
                return [
                    'success' => false, 
                    'message' => "AUTH LOGIN失败: " . trim($response)
                ];
            }
            
            // 发送用户名
            fwrite($socket, base64_encode($config['smtp_username']) . "\r\n");
            $response = fgets($socket, 512);
            
            if (substr($response, 0, 3) !== '334') {
                fclose($socket);
                return [
                    'success' => false, 
                    'message' => "用户名认证失败: " . trim($response)
                ];
            }
            
            // 发送密码
            fwrite($socket, base64_encode($config['smtp_password']) . "\r\n");
            $response = fgets($socket, 512);
            
            if (substr($response, 0, 3) !== '235') {
                fclose($socket);
                return [
                    'success' => false, 
                    'message' => "密码认证失败，请检查用户名和密码: " . trim($response)
                ];
            }
        }
        
        // 退出连接
        fwrite($socket, "QUIT\r\n");
        fclose($socket);
        
        return [
            'success' => true, 
            'message' => 'SMTP连接测试成功！服务器支持' . ($encryption === 'tls' ? 'TLS加密' : ($encryption === 'ssl' ? 'SSL加密' : '无加密'))
        ];
        
    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
        $errorAnalysis = EmailErrorHandler::analyzeError($errorMessage, $host);

        return [
            'success' => false,
            'message' => $errorMessage,
            'error_analysis' => $errorAnalysis,
            'formatted_error' => EmailErrorHandler::formatErrorHtml($errorAnalysis)
        ];
    }
}

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            // 尝试从表单数据获取
            $input = $_POST;
        }
        
        // 从数据库获取当前SMTP配置
        $db = safeGetDatabase();
        $stmt = $db->query("SELECT `key`, `value` FROM system_settings WHERE `group` = 'email'");
        $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // 如果POST中有新的配置，使用新配置
        $smtpConfig = [
            'smtp_host' => $input['smtp_host'] ?? $settings['smtp_host'] ?? '',
            'smtp_port' => $input['smtp_port'] ?? $settings['smtp_port'] ?? '587',
            'smtp_username' => $input['smtp_username'] ?? $settings['smtp_username'] ?? '',
            'smtp_password' => $input['smtp_password'] ?? $settings['smtp_password'] ?? '',
            'smtp_encryption' => $input['smtp_encryption'] ?? $settings['smtp_encryption'] ?? 'tls'
        ];
        
        // 验证必要的配置
        if (empty($smtpConfig['smtp_host'])) {
            echo json_encode([
                'success' => false,
                'message' => '请先配置SMTP服务器地址'
            ]);
            exit;
        }
        
        // 执行SMTP连接测试
        $result = testSMTPConnection($smtpConfig);
        echo json_encode($result);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '测试失败: ' . $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => '仅支持POST请求'
    ]);
}
?>
