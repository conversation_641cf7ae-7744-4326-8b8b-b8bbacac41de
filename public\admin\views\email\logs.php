<?php
/**
 * 邮件日志页面
 * Email Logs Page
 */

// 获取数据库连接
$db = getDatabase();

// 分页参数
$currentPage = max(1, intval($_GET['p'] ?? 1));
$limit = 20;
$offset = ($currentPage - 1) * $limit;

// 搜索参数
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// 构建查询条件
$conditions = [];
$params = [];

if ($search) {
    $conditions[] = "(recipient LIKE ? OR subject LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if ($status) {
    $conditions[] = "status = ?";
    $params[] = $status;
}

if ($dateFrom) {
    $conditions[] = "DATE(sent_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $conditions[] = "DATE(sent_at) <= ?";
    $params[] = $dateTo;
}

$whereClause = $conditions ? "WHERE " . implode(" AND ", $conditions) : "";

// 获取邮件日志
$logs = [];
$totalCount = 0;

try {
    // 确保邮件日志表存在
    $db->exec("
        CREATE TABLE IF NOT EXISTS email_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            recipient VARCHAR(255) NOT NULL,
            subject VARCHAR(500) NOT NULL,
            content TEXT,
            status ENUM('sent', 'failed') NOT NULL DEFAULT 'sent',
            error_message TEXT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_recipient (recipient),
            INDEX idx_status (status),
            INDEX idx_sent_at (sent_at)
        )
    ");
    
    // 获取总数
    $countSql = "SELECT COUNT(*) FROM email_logs {$whereClause}";
    $stmt = $db->prepare($countSql);
    $stmt->execute($params);
    $totalCount = $stmt->fetchColumn();
    
    // 获取日志列表
    $sql = "SELECT * FROM email_logs {$whereClause} ORDER BY sent_at DESC LIMIT {$limit} OFFSET {$offset}";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = '获取邮件日志失败: ' . $e->getMessage();
}

// 计算分页
$totalPages = ceil($totalCount / $limit);

// 获取统计信息
$stats = ['total' => 0, 'sent' => 0, 'failed' => 0];
try {
    $stmt = $db->prepare("SELECT status, COUNT(*) as count FROM email_logs GROUP BY status");
    $stmt->execute();
    $statusCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($statusCounts as $statusCount) {
        $stats[$statusCount['status']] = $statusCount['count'];
        $stats['total'] += $statusCount['count'];
    }
} catch (Exception $e) {
    // 忽略错误
}

// 处理删除操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'delete_log':
                $logId = $_POST['log_id'] ?? '';
                if ($logId) {
                    $stmt = $db->prepare("DELETE FROM email_logs WHERE id = ?");
                    $stmt->execute([$logId]);
                    $success = '日志删除成功';
                }
                break;
                
            case 'clear_logs':
                $clearType = $_POST['clear_type'] ?? '';
                if ($clearType === 'all') {
                    $stmt = $db->prepare("DELETE FROM email_logs");
                    $stmt->execute();
                    $success = '所有日志已清空';
                } elseif ($clearType === 'failed') {
                    $stmt = $db->prepare("DELETE FROM email_logs WHERE status = 'failed'");
                    $stmt->execute();
                    $success = '失败日志已清空';
                } elseif ($clearType === 'old') {
                    $stmt = $db->prepare("DELETE FROM email_logs WHERE sent_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
                    $stmt->execute();
                    $success = '30天前的日志已清空';
                }
                break;
        }
        
        // 刷新页面
        header("Location: " . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">邮件日志</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="#">邮件管理</a></li>
                    <li class="breadcrumb-item active">邮件日志</li>
                </ol>
            </div>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 统计卡片 -->
    <div class="row mb-3">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary"><?= $stats['total'] ?></h3>
                    <p class="text-muted mb-0">总邮件数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success"><?= $stats['sent'] ?></h3>
                    <p class="text-muted mb-0">发送成功</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-danger"><?= $stats['failed'] ?></h3>
                    <p class="text-muted mb-0">发送失败</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info"><?= $stats['total'] > 0 ? round($stats['sent'] / $stats['total'] * 100, 1) : 0 ?>%</h3>
                    <p class="text-muted mb-0">成功率</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>邮件发送日志
                    </h5>
                </div>
                <div class="col-auto">
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="showClearModal()">
                            <i class="fas fa-trash me-1"></i>清空日志
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportLogs()">
                            <i class="fas fa-download me-1"></i>导出日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索表单 -->
        <div class="card-body border-bottom">
            <form method="GET" class="row g-3">
                <input type="hidden" name="page" value="email-logs">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" 
                           placeholder="搜索收件人或主题">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">所有状态</option>
                        <option value="sent" <?= $status === 'sent' ? 'selected' : '' ?>>发送成功</option>
                        <option value="failed" <?= $status === 'failed' ? 'selected' : '' ?>>发送失败</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>" 
                           placeholder="开始日期">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($dateTo) ?>" 
                           placeholder="结束日期">
                </div>
                <div class="col-md-3">
                    <div class="btn-group w-100">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <a href="index.php?page=email-logs" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <div class="card-body p-0">
            <?php if (empty($logs)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无邮件日志</h5>
                <p class="text-muted">还没有发送过邮件</p>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>收件人</th>
                            <th>主题</th>
                            <th>状态</th>
                            <th>发送时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-envelope me-2 text-muted"></i>
                                    <?= htmlspecialchars($log['recipient']) ?>
                                </div>
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 300px;" title="<?= htmlspecialchars($log['subject']) ?>">
                                    <?= htmlspecialchars($log['subject']) ?>
                                </div>
                            </td>
                            <td>
                                <?php if ($log['status'] === 'sent'): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>发送成功
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>发送失败
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?= date('Y-m-d H:i:s', strtotime($log['sent_at'])) ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info" onclick="viewLog(<?= $log['id'] ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteLog(<?= $log['id'] ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <div class="card-footer">
                <div class="row align-items-center">
                    <div class="col">
                        <small class="text-muted">
                            显示第 <?= $offset + 1 ?> - <?= min($offset + $limit, $totalCount) ?> 条，共 <?= $totalCount ?> 条记录
                        </small>
                    </div>
                    <div class="col-auto">
                        <nav>
                            <ul class="pagination pagination-sm mb-0">
                                <?php if ($currentPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=email-logs&p=<?= $currentPage - 1 ?>&<?= http_build_query(array_diff_key($_GET, ['page' => ''])) ?>">上一页</a>
                                </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=email-logs&p=<?= $i ?>&<?= http_build_query(array_diff_key($_GET, ['page' => ''])) ?>"><?= $i ?></a>
                                </li>
                                <?php endfor; ?>

                                <?php if ($currentPage < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=email-logs&p=<?= $currentPage + 1 ?>&<?= http_build_query(array_diff_key($_GET, ['page' => ''])) ?>">下一页</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 查看日志模态框 -->
<div class="modal fade" id="viewLogModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">邮件详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="logDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 清空日志模态框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">清空日志</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="clear_logs">
                    <div class="mb-3">
                        <label class="form-label">清空类型</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="clear_type" value="failed" id="clearFailed">
                            <label class="form-check-label" for="clearFailed">
                                只清空失败日志
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="clear_type" value="old" id="clearOld">
                            <label class="form-check-label" for="clearOld">
                                清空30天前的日志
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="clear_type" value="all" id="clearAll">
                            <label class="form-check-label" for="clearAll">
                                清空所有日志
                            </label>
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        此操作不可恢复，请谨慎操作！
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认清空</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewLog(id) {
    // 这里应该通过AJAX获取日志详情
    const log = <?= json_encode($logs) ?>.find(l => l.id == id);
    if (log) {
        let html = `
            <div class="row">
                <div class="col-md-6">
                    <strong>收件人：</strong><br>
                    ${log.recipient}
                </div>
                <div class="col-md-6">
                    <strong>状态：</strong><br>
                    <span class="badge bg-${log.status === 'sent' ? 'success' : 'danger'}">
                        ${log.status === 'sent' ? '发送成功' : '发送失败'}
                    </span>
                </div>
            </div>
            <hr>
            <div class="mb-3">
                <strong>主题：</strong><br>
                ${log.subject}
            </div>
            <div class="mb-3">
                <strong>发送时间：</strong><br>
                ${log.sent_at}
            </div>
        `;
        
        if (log.error_message) {
            html += `
                <div class="mb-3">
                    <strong>错误信息：</strong><br>
                    <div class="alert alert-danger">${log.error_message}</div>
                </div>
            `;
        }
        
        if (log.content) {
            html += `
                <div class="mb-3">
                    <strong>邮件内容：</strong><br>
                    <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                        ${log.content}
                    </div>
                </div>
            `;
        }
        
        document.getElementById('logDetails').innerHTML = html;
        const modal = new bootstrap.Modal(document.getElementById('viewLogModal'));
        modal.show();
    }
}

function deleteLog(id) {
    if (confirm('确定要删除这条日志吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_log">
            <input type="hidden" name="log_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function showClearModal() {
    const modal = new bootstrap.Modal(document.getElementById('clearLogsModal'));
    modal.show();
}

function exportLogs() {
    // 这里可以实现导出功能
    alert('导出功能开发中...');
}
</script>
