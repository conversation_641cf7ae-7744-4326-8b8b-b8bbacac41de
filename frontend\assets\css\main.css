/**
 * 域名管理系统 - 主样式文件
 * Domain Management System - Main Stylesheet
 * Version: 2.0
 */

/* ============================================================================
   CSS变量定义
   ============================================================================ */
:root {
  /* 主色调 */
  --primary-color: #007bff;
  --primary-hover: #0056b3;
  --primary-light: #e3f2fd;
  
  /* 辅助色 */
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  /* 中性色 */
  --white: #ffffff;
  --light: #f8f9fa;
  --dark: #343a40;
  --muted: #6c757d;
  
  /* 边框和阴影 */
  --border-color: #dee2e6;
  --border-radius: 0.375rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  
  /* 字体 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-mono: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* 过渡动画 */
  --transition: all 0.15s ease-in-out;
}

/* ============================================================================
   基础样式重置
   ============================================================================ */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--dark);
  background-color: var(--light);
  margin: 0;
  padding: 0;
}

/* ============================================================================
   布局组件
   ============================================================================ */

/* 主容器 */
.main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--dark);
  margin: 0;
}

.page-subtitle {
  color: var(--muted);
  margin: var(--spacing-xs) 0 0 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 0 var(--spacing-md);
}

/* 卡片组件 */
.card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: var(--spacing-lg);
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--light);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: var(--spacing-md);
}

.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: var(--light);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* ============================================================================
   表单组件
   ============================================================================ */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--dark);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--dark);
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
}

.form-control.is-valid {
  border-color: var(--success-color);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--danger-color);
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--success-color);
}

/* ============================================================================
   按钮组件
   ============================================================================ */
.btn {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  transition: var(--transition);
  user-select: none;
}

.btn:hover {
  text-decoration: none;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.btn-primary {
  color: var(--white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-warning {
  color: var(--dark);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-danger {
  color: var(--white);
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  color: var(--white);
  background-color: var(--primary-color);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

/* ============================================================================
   表格组件
   ============================================================================ */
.table {
  width: 100%;
  margin-bottom: var(--spacing-md);
  background-color: transparent;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--border-color);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  background-color: var(--light);
}

.table tbody + tbody {
  border-top: 2px solid var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* ============================================================================
   状态标签
   ============================================================================ */
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge-primary {
  color: var(--white);
  background-color: var(--primary-color);
}

.badge-success {
  color: var(--white);
  background-color: var(--success-color);
}

.badge-warning {
  color: var(--dark);
  background-color: var(--warning-color);
}

.badge-danger {
  color: var(--white);
  background-color: var(--danger-color);
}

.badge-info {
  color: var(--white);
  background-color: var(--info-color);
}

.badge-secondary {
  color: var(--white);
  background-color: var(--secondary-color);
}

/* ============================================================================
   工具类
   ============================================================================ */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-muted { color: var(--muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-light { background-color: var(--light) !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }

.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }

.m-0 { margin: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }

.p-0 { padding: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }

/* ============================================================================
   响应式设计
   ============================================================================ */
@media (max-width: 768px) {
  .page-content {
    padding: 0 var(--spacing-sm);
  }
  
  .card-body {
    padding: var(--spacing-sm);
  }
  
  .table-responsive {
    font-size: 0.75rem;
  }
  
  .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* ============================================================================
   加载动画
   ============================================================================ */
.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 0.125rem solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ============================================================================
   自定义组件
   ============================================================================ */

/* 域名状态指示器 */
.domain-status {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.domain-status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.domain-status-dot.active { background-color: var(--success-color); }
.domain-status-dot.expired { background-color: var(--danger-color); }
.domain-status-dot.expiring { background-color: var(--warning-color); }
.domain-status-dot.pending { background-color: var(--info-color); }

/* 搜索框 */
.search-box {
  position: relative;
}

.search-box .form-control {
  padding-left: 2.5rem;
}

.search-box .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted);
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.action-buttons .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
