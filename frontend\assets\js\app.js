/**
 * 域名管理系统 - 主JavaScript文件
 * Domain Management System - Main JavaScript
 * Version: 2.0
 */

// 全局应用对象
window.DomainApp = {
    config: {
        apiUrl: '/api/v1',
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        locale: 'zh_CN',
        debug: false
    },
    
    // 工具函数
    utils: {},
    
    // API客户端
    api: {},
    
    // UI组件
    ui: {},
    
    // 初始化
    init: function() {
        this.setupCSRF();
        this.setupAjaxDefaults();
        this.setupGlobalErrorHandler();
        this.initializeComponents();
        
        console.log('Domain Management System initialized');
    },
    
    // 设置CSRF令牌
    setupCSRF: function() {
        if (this.config.csrfToken) {
            // 为所有AJAX请求添加CSRF令牌
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': this.config.csrfToken
                }
            });
        }
    },
    
    // 设置AJAX默认配置
    setupAjaxDefaults: function() {
        $.ajaxSetup({
            timeout: 30000,
            cache: false,
            beforeSend: function(xhr, settings) {
                // 显示加载指示器
                DomainApp.ui.showLoading();
            },
            complete: function(xhr, status) {
                // 隐藏加载指示器
                DomainApp.ui.hideLoading();
            },
            error: function(xhr, status, error) {
                DomainApp.handleAjaxError(xhr, status, error);
            }
        });
    },
    
    // 设置全局错误处理
    setupGlobalErrorHandler: function() {
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            if (DomainApp.config.debug) {
                DomainApp.ui.showAlert('发生了一个错误: ' + event.error.message, 'danger');
            }
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            if (DomainApp.config.debug) {
                DomainApp.ui.showAlert('Promise错误: ' + event.reason, 'danger');
            }
        });
    },
    
    // 初始化组件
    initializeComponents: function() {
        // 初始化工具提示
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
        
        // 初始化弹出框
        if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }
        
        // 初始化表单验证
        this.initFormValidation();
        
        // 初始化搜索功能
        this.initSearchFunctionality();
    },
    
    // 初始化表单验证
    initFormValidation: function() {
        // 为所有需要验证的表单添加验证
        var forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    },
    
    // 初始化搜索功能
    initSearchFunctionality: function() {
        var searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(function(input) {
            var debounceTimer;
            input.addEventListener('input', function() {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(function() {
                    DomainApp.handleSearch(input.value, input.dataset.target);
                }, 300);
            });
        });
    },
    
    // 处理搜索
    handleSearch: function(query, target) {
        if (target && window[target] && typeof window[target].search === 'function') {
            window[target].search(query);
        }
    },
    
    // 处理AJAX错误
    handleAjaxError: function(xhr, status, error) {
        var message = '请求失败';
        
        if (xhr.responseJSON && xhr.responseJSON.message) {
            message = xhr.responseJSON.message;
        } else if (xhr.status === 0) {
            message = '网络连接失败，请检查网络连接';
        } else if (xhr.status === 401) {
            message = '登录已过期，请重新登录';
            setTimeout(function() {
                window.location.href = '/login';
            }, 2000);
        } else if (xhr.status === 403) {
            message = '权限不足';
        } else if (xhr.status === 404) {
            message = '请求的资源不存在';
        } else if (xhr.status === 500) {
            message = '服务器内部错误';
        } else if (status === 'timeout') {
            message = '请求超时，请稍后重试';
        }
        
        this.ui.showAlert(message, 'danger');
    }
};

// 工具函数
DomainApp.utils = {
    // 格式化日期
    formatDate: function(date, format) {
        if (!date) return '';
        
        var d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        format = format || 'YYYY-MM-DD';
        
        var year = d.getFullYear();
        var month = String(d.getMonth() + 1).padStart(2, '0');
        var day = String(d.getDate()).padStart(2, '0');
        var hours = String(d.getHours()).padStart(2, '0');
        var minutes = String(d.getMinutes()).padStart(2, '0');
        var seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    // 格式化货币
    formatCurrency: function(amount, currency) {
        if (isNaN(amount)) return '';
        
        currency = currency || 'CNY';
        var symbols = {
            'CNY': '¥',
            'USD': '$',
            'EUR': '€',
            'GBP': '£'
        };
        
        var symbol = symbols[currency] || currency;
        return symbol + parseFloat(amount).toFixed(2);
    },
    
    // 防抖函数
    debounce: function(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() { inThrottle = false; }, limit);
            }
        };
    },
    
    // 生成随机字符串
    randomString: function(length) {
        length = length || 8;
        var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        var result = '';
        for (var i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },
    
    // 复制到剪贴板
    copyToClipboard: function(text) {
        if (navigator.clipboard && window.isSecureContext) {
            return navigator.clipboard.writeText(text);
        } else {
            // 降级方案
            var textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            return new Promise(function(resolve, reject) {
                if (document.execCommand('copy')) {
                    resolve();
                } else {
                    reject();
                }
                document.body.removeChild(textArea);
            });
        }
    },
    
    // 验证邮箱
    validateEmail: function(email) {
        var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // 验证域名
    validateDomain: function(domain) {
        var re = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
        return re.test(domain);
    },
    
    // 获取URL参数
    getUrlParameter: function(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    },
    
    // 设置URL参数
    setUrlParameter: function(name, value) {
        var url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    }
};

// API客户端
DomainApp.api = {
    // 基础请求方法
    request: function(method, endpoint, data, options) {
        options = options || {};
        
        var config = {
            url: DomainApp.config.apiUrl + endpoint,
            method: method,
            dataType: 'json',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };
        
        if (data) {
            if (method === 'GET') {
                config.data = data;
            } else {
                config.data = JSON.stringify(data);
            }
        }
        
        // 合并自定义选项
        $.extend(config, options);
        
        return $.ajax(config);
    },
    
    // GET请求
    get: function(endpoint, params, options) {
        return this.request('GET', endpoint, params, options);
    },
    
    // POST请求
    post: function(endpoint, data, options) {
        return this.request('POST', endpoint, data, options);
    },
    
    // PUT请求
    put: function(endpoint, data, options) {
        return this.request('PUT', endpoint, data, options);
    },
    
    // DELETE请求
    delete: function(endpoint, options) {
        return this.request('DELETE', endpoint, null, options);
    },
    
    // 上传文件
    upload: function(endpoint, formData, options) {
        options = options || {};
        
        var config = {
            url: DomainApp.config.apiUrl + endpoint,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json'
        };
        
        $.extend(config, options);
        
        return $.ajax(config);
    }
};

// UI组件
DomainApp.ui = {
    // 显示警告消息
    showAlert: function(message, type, duration) {
        type = type || 'info';
        duration = duration || 5000;
        
        var alertId = 'alert-' + DomainApp.utils.randomString(8);
        var alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        var container = document.getElementById('alert-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'alert-container';
            container.style.position = 'fixed';
            container.style.top = '20px';
            container.style.right = '20px';
            container.style.zIndex = '9999';
            container.style.maxWidth = '400px';
            document.body.appendChild(container);
        }
        
        container.insertAdjacentHTML('beforeend', alertHtml);
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(function() {
                var alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, duration);
        }
    },
    
    // 显示确认对话框
    confirm: function(message, callback) {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            // 使用Bootstrap模态框
            var modalHtml = `
                <div class="modal fade" id="confirmModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">确认</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${message}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="confirmBtn">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            var modal = new bootstrap.Modal(document.getElementById('confirmModal'));
            
            document.getElementById('confirmBtn').addEventListener('click', function() {
                modal.hide();
                if (callback) callback(true);
            });
            
            document.getElementById('confirmModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
            
            modal.show();
        } else {
            // 降级到原生confirm
            var result = confirm(message);
            if (callback) callback(result);
        }
    },
    
    // 显示加载指示器
    showLoading: function(target) {
        var loadingHtml = '<div class="text-center"><div class="spinner"></div> 加载中...</div>';
        
        if (target) {
            document.querySelector(target).innerHTML = loadingHtml;
        } else {
            // 全局加载指示器
            var overlay = document.getElementById('loading-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.id = 'loading-overlay';
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9998;
                `;
                overlay.innerHTML = '<div class="spinner" style="width: 3rem; height: 3rem;"></div>';
                document.body.appendChild(overlay);
            }
            overlay.style.display = 'flex';
        }
    },
    
    // 隐藏加载指示器
    hideLoading: function() {
        var overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    DomainApp.init();
});

// 导出到全局
window.App = DomainApp;
