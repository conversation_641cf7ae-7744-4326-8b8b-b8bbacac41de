<?php
/**
 * 邮件模板管理页面
 * Email Templates Management Page
 */

// 获取数据库连接
$db = getDatabase();

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'init_default_templates':
                // 初始化默认模板
                $defaultTemplates = [
                    [
                        'name' => '用户注册',
                        'subject' => '欢迎注册 {{site_name}}',
                        'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #28a745; margin-top: 0;">欢迎注册</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">欢迎您注册 {{site_name}}！您的账户已成功创建。</p>
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #28a745;">
            <p style="margin: 5px 0; color: #155724;"><strong>账户信息：</strong></p>
            <p style="margin: 5px 0; color: #155724;">用户名：{{user_name}}</p>
            <p style="margin: 5px 0; color: #155724;">邮箱：{{user_email}}</p>
            <p style="margin: 5px 0; color: #155724;">注册时间：{{register_time}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">感谢您选择我们的服务！</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                        'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
user_email=<EMAIL>
register_time=2024-01-01 12:00:00'
                    ],
                    [
                        'name' => '邮箱验证',
                        'subject' => '验证您的邮箱地址 - {{site_name}}',
                        'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #333; margin-top: 0;">邮箱验证</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您好，{{user_name}}！</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的邮箱验证码是：</p>
        <div style="background: #fff; padding: 20px; text-align: center; margin: 20px 0; border-radius: 4px; border: 2px dashed #007bff;">
            <span style="font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 4px;">{{verification_code}}</span>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">验证码有效期为10分钟，请及时使用。</p>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果您没有请求此验证码，请忽略此邮件。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                        'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
verification_code=123456'
                    ],
                    [
                        'name' => '密码找回',
                        'subject' => '重置您的密码 - {{site_name}}',
                        'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #333; margin-top: 0;">重置密码请求</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您好，{{user_name}}！</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">我们收到了重置您账户密码的请求。请点击下面的按钮重置密码：</p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{reset_link}}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block;">重置密码</a>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果按钮无法点击，请复制以下链接到浏览器：</p>
        <p style="color: #007bff; font-size: 14px; word-break: break-all;">{{reset_link}}</p>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">此链接将在1小时后失效。</p>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果您没有请求重置密码，请忽略此邮件。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                        'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
reset_link=https://yourdomain.com/reset-password?token=abc123'
                    ],
                    [
                        'name' => '登录提醒',
                        'subject' => '账户登录提醒 - {{site_name}}',
                        'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #ffc107; margin-top: 0;">登录提醒</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的账户于 {{login_time}} 成功登录。</p>
        <div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <p style="margin: 5px 0; color: #856404;"><strong>登录信息：</strong></p>
            <p style="margin: 5px 0; color: #856404;">登录时间：{{login_time}}</p>
            <p style="margin: 5px 0; color: #856404;">登录IP：{{login_ip}}</p>
            <p style="margin: 5px 0; color: #856404;">设备信息：{{user_agent}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果这不是您本人的操作，请立即修改密码并联系我们。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                        'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
login_time=2024-01-01 12:00:00
login_ip=***********
user_agent=Chrome浏览器'
                    ]
                ];

                $insertCount = 0;
                foreach ($defaultTemplates as $template) {
                    $stmt = $db->prepare("INSERT INTO email_templates (name, subject, content, variables) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$template['name'], $template['subject'], $template['content'], $template['variables']]);
                    $insertCount++;
                }

                $success = "成功创建 {$insertCount} 个默认邮件模板！";
                break;
            case 'save_template':
                $templateId = $_POST['template_id'] ?? '';
                $templateName = $_POST['template_name'] ?? '';
                $subject = $_POST['subject'] ?? '';
                $content = $_POST['content'] ?? '';
                $variables = $_POST['variables'] ?? '';
                
                if (empty($templateName) || empty($subject) || empty($content)) {
                    throw new Exception('模板名称、主题和内容不能为空');
                }
                
                if ($templateId) {
                    // 更新模板
                    $stmt = $db->prepare("UPDATE email_templates SET name = ?, subject = ?, content = ?, variables = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$templateName, $subject, $content, $variables, $templateId]);
                    $success = '模板更新成功';
                } else {
                    // 创建新模板
                    $stmt = $db->prepare("INSERT INTO email_templates (name, subject, content, variables, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
                    $stmt->execute([$templateName, $subject, $content, $variables]);
                    $success = '模板创建成功';
                }
                break;
                
            case 'delete_template':
                $templateId = $_POST['template_id'] ?? '';
                if ($templateId) {
                    $stmt = $db->prepare("DELETE FROM email_templates WHERE id = ?");
                    $stmt->execute([$templateId]);
                    $success = '模板删除成功';
                }
                break;
                
            case 'preview_template':
                $content = $_POST['content'] ?? '';
                $variables = $_POST['variables'] ?? '';
                
                // 解析变量
                $vars = [];
                if ($variables) {
                    $lines = explode("\n", $variables);
                    foreach ($lines as $line) {
                        if (strpos($line, '=') !== false) {
                            list($key, $value) = explode('=', $line, 2);
                            $vars[trim($key)] = trim($value);
                        }
                    }
                }
                
                // 替换变量
                $previewContent = $content;
                foreach ($vars as $key => $value) {
                    $previewContent = str_replace('{{' . $key . '}}', $value, $previewContent);
                }
                
                echo json_encode(['success' => true, 'content' => $previewContent]);
                exit;
        }
    } catch (Exception $e) {
        if ($_POST['action'] === 'preview_template') {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
        $error = $e->getMessage();
    }
}

// 获取模板列表
$templates = [];
try {
    $stmt = $db->prepare("SELECT * FROM email_templates ORDER BY created_at DESC");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // 如果表不存在，创建表
    try {
        $db->exec("
            CREATE TABLE IF NOT EXISTS email_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                subject VARCHAR(500) NOT NULL,
                content TEXT NOT NULL,
                variables TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // 插入默认模板
        $defaultTemplates = [
            [
                'name' => '邮箱验证',
                'subject' => '验证您的邮箱地址 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #333; margin-top: 0;">邮箱验证</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您好，{{user_name}}！</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的邮箱验证码是：</p>
        <div style="background: #fff; padding: 20px; text-align: center; margin: 20px 0; border-radius: 4px; border: 2px dashed #007bff;">
            <span style="font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 4px;">{{verification_code}}</span>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">验证码有效期为10分钟，请及时使用。</p>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果您没有请求此验证码，请忽略此邮件。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
verification_code=123456'
            ],
            [
                'name' => '密码重置',
                'subject' => '重置您的密码 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #333; margin-top: 0;">重置密码请求</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您好，{{user_name}}！</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">我们收到了重置您账户密码的请求。请点击下面的按钮重置密码：</p>
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{reset_link}}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block;">重置密码</a>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果按钮无法点击，请复制以下链接到浏览器：</p>
        <p style="color: #007bff; font-size: 14px; word-break: break-all;">{{reset_link}}</p>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">此链接将在1小时后失效。</p>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果您没有请求重置密码，请忽略此邮件。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
reset_link=https://yourdomain.com/reset-password?token=abc123'
            ],
            [
                'name' => '用户注册',
                'subject' => '欢迎注册 {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #28a745; margin-top: 0;">欢迎注册</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">欢迎您注册 {{site_name}}！您的账户已成功创建。</p>
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #28a745;">
            <p style="margin: 5px 0; color: #155724;"><strong>账户信息：</strong></p>
            <p style="margin: 5px 0; color: #155724;">用户名：{{user_name}}</p>
            <p style="margin: 5px 0; color: #155724;">邮箱：{{user_email}}</p>
            <p style="margin: 5px 0; color: #155724;">注册时间：{{register_time}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">感谢您选择我们的服务！</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
user_email=<EMAIL>
register_time=2024-01-01 12:00:00'
            ],
            [
                'name' => '登录提醒',
                'subject' => '账户登录提醒 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #ffc107; margin-top: 0;">登录提醒</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的账户于 {{login_time}} 成功登录。</p>
        <div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <p style="margin: 5px 0; color: #856404;"><strong>登录信息：</strong></p>
            <p style="margin: 5px 0; color: #856404;">登录时间：{{login_time}}</p>
            <p style="margin: 5px 0; color: #856404;">登录IP：{{login_ip}}</p>
            <p style="margin: 5px 0; color: #856404;">设备信息：{{user_agent}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">如果这不是您本人的操作，请立即修改密码并联系我们。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
login_time=2024-01-01 12:00:00
login_ip=***********
user_agent=Chrome浏览器'
            ],
            [
                'name' => '余额充值成功',
                'subject' => '余额充值成功通知 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #28a745; margin-top: 0;">充值成功</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的账户余额充值已成功完成！</p>
        <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #28a745;">
            <p style="margin: 5px 0; color: #155724;"><strong>充值详情：</strong></p>
            <p style="margin: 5px 0; color: #155724;">充值金额：¥{{recharge_amount}}</p>
            <p style="margin: 5px 0; color: #155724;">充值方式：{{payment_method}}</p>
            <p style="margin: 5px 0; color: #155724;">交易单号：{{transaction_id}}</p>
            <p style="margin: 5px 0; color: #155724;">充值时间：{{recharge_time}}</p>
            <p style="margin: 5px 0; color: #155724;">当前余额：¥{{current_balance}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">感谢您对我们的信任和支持！</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
recharge_amount=100.00
payment_method=支付宝
transaction_id=202401011200001
recharge_time=2024-01-01 12:00:00
current_balance=500.00'
            ],
            [
                'name' => '域名购买成功',
                'subject' => '域名购买成功通知 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #007bff; margin-top: 0;">域名购买成功</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">恭喜您！域名购买已成功完成。</p>
        <div style="background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #007bff;">
            <p style="margin: 5px 0; color: #0c5460;"><strong>域名信息：</strong></p>
            <p style="margin: 5px 0; color: #0c5460;">域名：{{domain_name}}</p>
            <p style="margin: 5px 0; color: #0c5460;">注册年限：{{register_years}}年</p>
            <p style="margin: 5px 0; color: #0c5460;">购买金额：¥{{purchase_amount}}</p>
            <p style="margin: 5px 0; color: #0c5460;">注册时间：{{register_time}}</p>
            <p style="margin: 5px 0; color: #0c5460;">到期时间：{{expire_time}}</p>
            <p style="margin: 5px 0; color: #0c5460;">订单号：{{order_id}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">您可以登录管理后台查看和管理您的域名。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
domain_name=yourdomain.com
register_years=1
purchase_amount=68.00
register_time=2024-01-01 12:00:00
expire_time=2025-01-01 12:00:00
order_id=ORD202401011200001'
            ],
            [
                'name' => '工单回复提醒',
                'subject' => '工单回复通知 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #6f42c1; margin-top: 0;">工单回复</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的工单有新的回复，请及时查看。</p>
        <div style="background: #e2e3f0; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #6f42c1;">
            <p style="margin: 5px 0; color: #493057;"><strong>工单信息：</strong></p>
            <p style="margin: 5px 0; color: #493057;">工单编号：{{ticket_id}}</p>
            <p style="margin: 5px 0; color: #493057;">工单标题：{{ticket_title}}</p>
            <p style="margin: 5px 0; color: #493057;">回复时间：{{reply_time}}</p>
            <p style="margin: 5px 0; color: #493057;">回复人员：{{reply_staff}}</p>
        </div>
        <div style="background: #fff; padding: 15px; border-radius: 4px; margin: 20px 0; border: 1px solid #dee2e6;">
            <p style="margin: 5px 0; color: #333;"><strong>回复内容：</strong></p>
            <p style="margin: 10px 0; color: #666; line-height: 1.5;">{{reply_content}}</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">请登录系统查看完整工单详情。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
ticket_id=TK202401011200001
ticket_title=域名解析问题
reply_time=2024-01-01 12:00:00
reply_staff=客服小王
reply_content=您好，关于您的域名解析问题，我们已经为您处理完成。'
            ],
            [
                'name' => '域名到期提醒',
                'subject' => '域名即将到期提醒 - {{site_name}}',
                'content' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #007bff; margin: 0;">{{site_name}}</h1>
    </div>
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #fd7e14; margin-top: 0;">域名到期提醒</h2>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">亲爱的 {{user_name}}，</p>
        <p style="color: #666; font-size: 16px; line-height: 1.5;">您的域名即将到期，请及时续费以避免域名被删除。</p>
        <div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #fd7e14;">
            <p style="margin: 5px 0; color: #856404;"><strong>域名信息：</strong></p>
            <p style="margin: 5px 0; color: #856404;">域名：{{domain_name}}</p>
            <p style="margin: 5px 0; color: #856404;">到期时间：{{expire_time}}</p>
            <p style="margin: 5px 0; color: #856404;">剩余天数：{{days_left}}天</p>
            <p style="margin: 5px 0; color: #856404;">续费价格：¥{{renewal_price}}/年</p>
        </div>
        <div style="background: #f8d7da; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #dc3545;">
            <p style="margin: 5px 0; color: #721c24;"><strong>重要提醒：</strong></p>
            <p style="margin: 5px 0; color: #721c24;">• 域名到期后将进入赎回期，续费价格会大幅上涨</p>
            <p style="margin: 5px 0; color: #721c24;">• 赎回期结束后域名将被删除，任何人都可以重新注册</p>
            <p style="margin: 5px 0; color: #721c24;">• 建议您尽快续费以确保域名正常使用</p>
        </div>
        <p style="color: #666; font-size: 14px; line-height: 1.5;">请登录系统进行域名续费操作。</p>
    </div>
    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #999; font-size: 12px; margin: 0;">此邮件由系统自动发送，请勿回复。</p>
    </div>
</div>',
                'variables' => 'site_name=NameSilo域名销售系统
user_name=用户名
domain_name=yourdomain.com
expire_time=2024-12-31 23:59:59
days_left=30
renewal_price=68.00'
            ]
        ];

        foreach ($defaultTemplates as $template) {
            $stmt = $db->prepare("INSERT INTO email_templates (name, subject, content, variables) VALUES (?, ?, ?, ?)");
            $stmt->execute([$template['name'], $template['subject'], $template['content'], $template['variables']]);
        }
        
        // 重新获取模板列表
        $stmt = $db->prepare("SELECT * FROM email_templates ORDER BY created_at DESC");
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $createError) {
        $error = '无法创建邮件模板表: ' . $createError->getMessage();
    }
}

// 获取编辑的模板
$editTemplate = null;
if (isset($_GET['edit']) && $_GET['edit']) {
    foreach ($templates as $template) {
        if ($template['id'] == $_GET['edit']) {
            $editTemplate = $template;
            break;
        }
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="page-title">邮件模板</h4>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                        <li class="breadcrumb-item"><a href="#">邮件管理</a></li>
                        <li class="breadcrumb-item active">邮件模板</li>
                    </ol>
                </div>
                <?php if (empty($templates)): ?>
                <div>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="init_default_templates">
                        <button type="submit" class="btn btn-primary" onclick="return confirm('确定要创建默认邮件模板吗？')">
                            <i class="fas fa-plus me-1"></i>创建默认模板
                        </button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>模板列表
                    </h5>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" onclick="newTemplate()">
                            <i class="fas fa-plus me-1"></i>新建模板
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php foreach ($templates as $template): ?>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><?= htmlspecialchars($template['name']) ?></h6>
                                <small class="text-muted"><?= htmlspecialchars($template['subject']) ?></small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" onclick="editTemplate(<?= $template['id'] ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(<?= $template['id'] ?>, '<?= htmlspecialchars($template['name']) ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($templates)): ?>
                        <div class="list-group-item text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p class="mb-0">暂无邮件模板</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i><?= $editTemplate ? '编辑模板' : '新建模板' ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="templateForm">
                        <input type="hidden" name="action" value="save_template">
                        <input type="hidden" name="template_id" value="<?= $editTemplate ? $editTemplate['id'] : '' ?>">
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="template_type" class="form-label">模板类型</label>
                                    <select class="form-select" id="template_type" name="template_type" onchange="loadTemplatePreset()">
                                        <option value="">选择模板类型</option>
                                        <option value="user_register">用户注册</option>
                                        <option value="email_verification">邮箱验证</option>
                                        <option value="password_reset">密码找回</option>
                                        <option value="login_notification">登录提醒</option>
                                        <option value="balance_recharge">余额充值成功</option>
                                        <option value="domain_purchase">域名购买成功</option>
                                        <option value="ticket_reply">工单回复提醒</option>
                                        <option value="domain_expiry">域名到期提醒</option>
                                        <option value="custom">自定义模板</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="template_name" class="form-label">模板名称</label>
                                    <input type="text" class="form-control" id="template_name" name="template_name"
                                           value="<?= $editTemplate ? htmlspecialchars($editTemplate['name']) : '' ?>"
                                           placeholder="邮箱验证" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">邮件主题</label>
                                    <input type="text" class="form-control" id="subject" name="subject"
                                           value="<?= $editTemplate ? htmlspecialchars($editTemplate['subject']) : '' ?>"
                                           placeholder="验证您的邮箱地址 - {{site_name}}" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">邮件内容</label>
                            <textarea class="form-control" id="content" name="content" rows="15" required><?= $editTemplate ? htmlspecialchars($editTemplate['content']) : '' ?></textarea>
                            <div class="form-text">支持HTML格式，可使用变量如 {{user_name}}, {{site_name}} 等</div>
                        </div>

                        <div class="mb-3">
                            <label for="variables" class="form-label">模板变量</label>
                            <textarea class="form-control" id="variables" name="variables" rows="5" 
                                      placeholder="user_name=用户名&#10;site_name=网站名称&#10;verification_code=验证码"><?= $editTemplate ? htmlspecialchars($editTemplate['variables']) : '' ?></textarea>
                            <div class="form-text">每行一个变量，格式：变量名=示例值</div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存模板
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewTemplate()">
                                <i class="fas fa-eye me-1"></i>预览模板
                            </button>
                            <?php if ($editTemplate): ?>
                            <a href="index.php?page=email-templates" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>取消编辑
                            </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模板预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
function newTemplate() {
    window.location.href = 'index.php?page=email-templates';
}

function editTemplate(id) {
    window.location.href = 'index.php?page=email-templates&edit=' + id;
}

function deleteTemplate(id, name) {
    if (confirm('确定要删除模板 "' + name + '" 吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_template">
            <input type="hidden" name="template_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function previewTemplate() {
    const content = document.getElementById('content').value;
    const variables = document.getElementById('variables').value;
    
    if (!content) {
        alert('请先输入邮件内容');
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'preview_template');
    formData.append('content', content);
    formData.append('variables', variables);
    
    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewContent').innerHTML = data.content;
            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            modal.show();
        } else {
            alert('预览失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('预览失败: ' + error.message);
    });
}

// 加载模板预设
function loadTemplatePreset() {
    const templateType = document.getElementById('template_type').value;
    if (!templateType || templateType === 'custom') return;

    const presets = {
        'user_register': {
            name: '用户注册',
            subject: '欢迎注册 {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\nuser_email=<EMAIL>\nregister_time=2024-01-01 12:00:00'
        },
        'email_verification': {
            name: '邮箱验证',
            subject: '验证您的邮箱地址 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\nverification_code=123456'
        },
        'password_reset': {
            name: '密码找回',
            subject: '重置您的密码 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\nreset_link=https://yourdomain.com/reset-password?token=abc123'
        },
        'login_notification': {
            name: '登录提醒',
            subject: '账户登录提醒 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\nlogin_time=2024-01-01 12:00:00\nlogin_ip=***********\nuser_agent=Chrome浏览器'
        },
        'balance_recharge': {
            name: '余额充值成功',
            subject: '余额充值成功通知 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\nrecharge_amount=100.00\npayment_method=支付宝\ntransaction_id=202401011200001\nrecharge_time=2024-01-01 12:00:00\ncurrent_balance=500.00'
        },
        'domain_purchase': {
            name: '域名购买成功',
            subject: '域名购买成功通知 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\ndomain_name=yourdomain.com\nregister_years=1\npurchase_amount=68.00\nregister_time=2024-01-01 12:00:00\nexpire_time=2025-01-01 12:00:00\norder_id=ORD202401011200001'
        },
        'ticket_reply': {
            name: '工单回复提醒',
            subject: '工单回复通知 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\nticket_id=TK202401011200001\nticket_title=域名解析问题\nreply_time=2024-01-01 12:00:00\nreply_staff=客服小王\nreply_content=您好，关于您的域名解析问题，我们已经为您处理完成。'
        },
        'domain_expiry': {
            name: '域名到期提醒',
            subject: '域名即将到期提醒 - {{site_name}}',
            variables: 'site_name=NameSilo域名销售系统\nuser_name=用户名\ndomain_name=yourdomain.com\nexpire_time=2024-12-31 23:59:59\ndays_left=30\nrenewal_price=68.00'
        }
    };

    const preset = presets[templateType];
    if (preset) {
        document.getElementById('template_name').value = preset.name;
        document.getElementById('subject').value = preset.subject;
        document.getElementById('variables').value = preset.variables;
    }
}
</script>
