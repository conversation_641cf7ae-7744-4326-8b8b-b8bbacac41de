<?php
/**
 * 用户认证API接口
 * User Authentication API Endpoints
 */

// 启用错误报告（开发环境）
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));

// 引入统一的数据库连接
require_once dirname(__DIR__) . '/includes/database.php';

// 记录日志
function logActivity($action, $userId = null, $data = null) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("INSERT INTO logs (user_id, action, resource_type, ip_address, user_agent, request_data, created_at) VALUES (?, ?, 'auth', ?, ?, ?, NOW())");
        $stmt->execute([
            $userId,
            $action,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $data ? json_encode($data) : null
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主要功能
    }
}

// 发送JSON响应
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];

    if ($data !== null) {
        $response = array_merge($response, $data);
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取系统设置
function getSetting($key, $default = null) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("SELECT value, type FROM system_settings WHERE `key` = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        if (!$result) {
            return $default;
        }

        // 根据类型转换值
        switch ($result['type']) {
            case 'boolean':
                return (bool)$result['value'];
            case 'integer':
                return (int)$result['value'];
            case 'json':
                return json_decode($result['value'], true);
            default:
                return $result['value'];
        }
    } catch (Exception $e) {
        return $default;
    }
}

// 验证邮箱格式
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// 验证密码强度
function validatePassword($password) {
    if (strlen($password) < 8) {
        return '密码长度至少为8个字符';
    }
    
    if (!preg_match('/[A-Za-z]/', $password)) {
        return '密码必须包含字母';
    }
    
    if (!preg_match('/\d/', $password)) {
        return '密码必须包含数字';
    }
    
    return true;
}

// 生成随机令牌
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// 发送邮件（模拟）
function sendEmail($to, $subject, $message) {
    // 这里应该集成真实的邮件服务
    // 目前返回成功以便测试
    logActivity('email_sent', null, [
        'to' => $to,
        'subject' => $subject,
        'message_length' => strlen($message)
    ]);
    return true;
}

// 发送验证邮件
function sendVerificationEmail($email, $code) {
    try {
        $db = getDatabase();

        // 获取邮件设置
        $smtpHost = getSetting('smtp_host');
        $smtpPort = getSetting('smtp_port', '587');
        $smtpUsername = getSetting('smtp_username');
        $smtpPassword = getSetting('smtp_password');
        $smtpEncryption = getSetting('smtp_encryption', 'tls');
        $fromEmail = getSetting('from_email');
        $fromName = getSetting('from_name', 'NameSilo域名销售系统');

        if (empty($smtpHost) || empty($smtpUsername) || empty($smtpPassword)) {
            error_log('邮件服务未配置完整');
            return false;
        }

        // 检查是否有PHPMailer
        $composerAutoload = dirname(dirname(__DIR__)) . '/vendor/autoload.php';
        if (!file_exists($composerAutoload)) {
            error_log('PHPMailer未安装，请运行: composer require phpmailer/phpmailer');
            return false;
        }

        require_once $composerAutoload;

        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $smtpHost;
        $mail->SMTPAuth = true;
        $mail->Username = $smtpUsername;
        $mail->Password = $smtpPassword;
        $mail->SMTPSecure = $smtpEncryption;
        $mail->Port = $smtpPort;
        $mail->CharSet = 'UTF-8';

        // 发件人和收件人
        $mail->setFrom($fromEmail, $fromName);
        $mail->addAddress($email);

        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = '邮箱验证码 - ' . $fromName;
        $mail->Body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='text-align: center; margin-bottom: 30px;'>
                <h1 style='color: #007bff; margin: 0;'>{$fromName}</h1>
            </div>
            <div style='background: #f8f9fa; padding: 30px; border-radius: 8px;'>
                <h2 style='color: #333; margin-top: 0;'>邮箱验证</h2>
                <p style='color: #666; font-size: 16px; line-height: 1.5;'>您好！</p>
                <p style='color: #666; font-size: 16px; line-height: 1.5;'>您的邮箱验证码是：</p>
                <div style='background: #fff; padding: 20px; text-align: center; margin: 20px 0; border-radius: 4px; border: 2px dashed #007bff;'>
                    <span style='font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 4px;'>{$code}</span>
                </div>
                <p style='color: #666; font-size: 14px; line-height: 1.5;'>验证码有效期为10分钟，请及时使用。</p>
                <p style='color: #666; font-size: 14px; line-height: 1.5;'>如果您没有请求此验证码，请忽略此邮件。</p>
            </div>
            <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                <p style='color: #999; font-size: 12px; margin: 0;'>此邮件由系统自动发送，请勿回复。</p>
            </div>
        </div>";

        $mail->send();
        return true;

    } catch (Exception $e) {
        error_log('邮件发送失败: ' . $e->getMessage());
        return false;
    }
}

// 处理API请求
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'login':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }
            
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $remember = isset($_POST['remember']);
            
            if (!$email || !$password) {
                throw new Exception('邮箱和密码不能为空', 400);
            }
            
            if (!validateEmail($email)) {
                throw new Exception('邮箱格式不正确', 400);
            }
            
            try {
                $db = getDatabase();

                // 查找用户
                $stmt = $db->prepare("SELECT id, email, password_hash, first_name, last_name, status, email_verified FROM users WHERE email = ?");
                $stmt->execute([$email]);
                $user = $stmt->fetch();

                if (!$user || !password_verify($password, $user['password_hash'])) {
                    logActivity('login_failed', null, ['email' => $email]);
                    throw new Exception('邮箱或密码错误', 401);
                }

                if ($user['status'] !== 'active') {
                    throw new Exception('账户已被禁用，请联系客服', 403);
                }

                // 检查是否需要邮箱验证 - 暂时禁用以便测试
                $emailVerificationRequired = getSetting('email_verification_required', false);
                if ($emailVerificationRequired && !$user['email_verified']) {
                    throw new Exception('请先验证您的邮箱地址', 403);
                }

            } catch (Exception $dbError) {
                // 如果数据库连接失败，使用临时验证（仅用于测试）
                if (strpos($dbError->getMessage(), '数据库连接失败') !== false) {
                    // 临时测试账户
                    if ($email === '<EMAIL>' && $password === 'admin123') {
                        $user = [
                            'id' => 1,
                            'email' => $email,
                            'first_name' => 'Test',
                            'last_name' => 'Admin',
                            'status' => 'active',
                            'email_verified' => true
                        ];
                    } else {
                        throw new Exception('邮箱或密码错误（数据库未初始化，请使用测试账户：<EMAIL> / admin123）', 401);
                    }
                } else {
                    throw $dbError;
                }
            }
            
            // 设置会话
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];

            // 更新最后登录时间（如果数据库可用）
            if (isset($db)) {
                try {
                    $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                    $stmt->execute([$user['id']]);
                } catch (Exception $e) {
                    // 忽略更新失败，不影响登录
                    error_log("更新登录时间失败: " . $e->getMessage());
                }
            }

            logActivity('login_success', $user['id']);
            
            sendResponse(true, '登录成功', [
                'redirect' => 'dashboard.php',
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'name' => $user['first_name'] . ' ' . $user['last_name']
                ]
            ]);
            break;
            
        case 'register':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }
            
            $firstName = trim($_POST['firstName'] ?? '');
            $lastName = trim($_POST['lastName'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $password = $_POST['password'] ?? '';
            $confirmPassword = $_POST['confirmPassword'] ?? '';
            $terms = isset($_POST['terms']);
            $newsletter = isset($_POST['newsletter']);
            
            // 验证必填字段
            if (!$email || !$password) {
                throw new Exception('请填写邮箱和密码', 400);
            }
            
            if (!validateEmail($email)) {
                throw new Exception('邮箱格式不正确', 400);
            }
            
            if ($password !== $confirmPassword) {
                throw new Exception('两次输入的密码不一致', 400);
            }
            
            $passwordValidation = validatePassword($password);
            if ($passwordValidation !== true) {
                throw new Exception($passwordValidation, 400);
            }
            
            if (!$terms) {
                throw new Exception('请同意服务条款和隐私政策', 400);
            }
            
            $db = getDatabase();
            
            // 检查邮箱是否已存在
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                throw new Exception('该邮箱已被注册', 409);
            }
            
            // 生成验证令牌和用户名
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $username = strtolower($firstName . '_' . $lastName . '_' . substr(md5($email), 0, 6));

            // 检查是否需要邮箱验证
            $emailVerificationRequired = getSetting('email_verification_required', true);
            $verificationToken = $emailVerificationRequired ? generateToken() : null;
            $emailVerified = $emailVerificationRequired ? 0 : 1;

            // 创建用户
            $stmt = $db->prepare("
                INSERT INTO users (username, first_name, last_name, email, phone, password_hash, email_verification_token, email_verified, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $username,
                $firstName,
                $lastName,
                $email,
                $phone,
                $hashedPassword,
                $verificationToken,
                $emailVerified
            ]);
            
            $userId = $db->lastInsertId();
            
            // 根据设置决定是否发送验证邮件
            if ($emailVerificationRequired && $verificationToken) {
                // 发送验证邮件
                $verificationLink = "http://" . $_SERVER['HTTP_HOST'] . "/verify-email.php?token=" . $verificationToken;
                $emailSubject = "验证您的邮箱地址 - NameSilo域名销售";
                $emailMessage = "
                    <h2>欢迎注册 NameSilo域名销售系统！</h2>
                    <p>亲爱的 {$firstName} {$lastName}，</p>
                    <p>感谢您注册我们的服务。请点击下面的链接验证您的邮箱地址：</p>
                    <p><a href='{$verificationLink}'>验证邮箱地址</a></p>
                    <p>如果您无法点击链接，请复制以下地址到浏览器：</p>
                    <p>{$verificationLink}</p>
                    <p>此链接将在24小时后失效。</p>
                    <br>
                    <p>NameSilo域名销售团队</p>
                ";

                sendEmail($email, $emailSubject, $emailMessage);
                $message = '注册成功！请检查您的邮箱进行验证';
            } else {
                $message = '注册成功！您现在可以直接登录';
            }

            logActivity('register_success', $userId, [
                'email' => $email,
                'name' => $firstName . ' ' . $lastName
            ]);

            sendResponse(true, $message);
            break;
            
        case 'forgot-password':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }
            
            $email = trim($_POST['email'] ?? '');
            
            if (!$email) {
                throw new Exception('请输入邮箱地址', 400);
            }
            
            if (!validateEmail($email)) {
                throw new Exception('邮箱格式不正确', 400);
            }
            
            $db = getDatabase();
            
            // 查找用户
            $stmt = $db->prepare("SELECT id, first_name, last_name FROM users WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if (!$user) {
                // 为了安全，即使用户不存在也返回成功
                sendResponse(true, '如果该邮箱地址存在，我们已发送重置密码的邮件');
            }
            
            // 生成重置令牌
            $resetToken = generateToken();
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // 保存重置令牌到用户表
            $stmt = $db->prepare("
                UPDATE users
                SET password_reset_token = ?, password_reset_expires = ?
                WHERE id = ?
            ");
            $stmt->execute([$resetToken, $expiresAt, $user['id']]);
            
            // 发送重置邮件
            $resetLink = "http://" . $_SERVER['HTTP_HOST'] . "/reset-password.php?token=" . $resetToken;
            $emailSubject = "重置您的密码 - NameSilo域名销售";
            $emailMessage = "
                <h2>重置密码请求</h2>
                <p>亲爱的 {$user['first_name']} {$user['last_name']}，</p>
                <p>我们收到了重置您账户密码的请求。请点击下面的链接重置密码：</p>
                <p><a href='{$resetLink}'>重置密码</a></p>
                <p>如果您无法点击链接，请复制以下地址到浏览器：</p>
                <p>{$resetLink}</p>
                <p>此链接将在1小时后失效。</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <br>
                <p>NameSilo域名销售团队</p>
            ";
            
            sendEmail($email, $emailSubject, $emailMessage);
            
            logActivity('password_reset_requested', $user['id'], ['email' => $email]);
            
            sendResponse(true, '重置密码的邮件已发送，请检查您的邮箱');
            break;

        case 'logout':
            $userId = $_SESSION['user_id'] ?? null;
            session_destroy();
            logActivity('logout', $userId);

            // 如果是AJAX请求，返回JSON响应
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                sendResponse(true, '已成功退出登录');
            } else {
                // 如果是直接访问，重定向到退出页面
                header('Location: ../logout.php');
                exit;
            }
            break;

        case 'verify-email':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            $token = trim($_POST['token'] ?? '');

            if (!$token) {
                throw new Exception('验证令牌不能为空', 400);
            }

            $db = getDatabase();

            // 查找用户
            $stmt = $db->prepare("SELECT id, email, first_name, last_name FROM users WHERE email_verification_token = ? AND email_verified = 0");
            $stmt->execute([$token]);
            $user = $stmt->fetch();

            if (!$user) {
                throw new Exception('验证令牌无效或已过期', 400);
            }

            // 更新用户状态
            $stmt = $db->prepare("UPDATE users SET email_verified = 1, email_verification_token = NULL WHERE id = ?");
            $stmt->execute([$user['id']]);

            logActivity('email_verified', $user['id'], ['email' => $user['email']]);

            sendResponse(true, '邮箱验证成功！您现在可以登录了');
            break;

        case 'check':
        case 'check-session':
            if (isset($_SESSION['user_id'])) {
                sendResponse(true, '用户已登录', [
                    'user' => [
                        'id' => $_SESSION['user_id'],
                        'email' => $_SESSION['user_email'],
                        'name' => $_SESSION['user_name']
                    ]
                ]);
            } else {
                sendResponse(false, '用户未登录');
            }
            break;

        case 'send-verification':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            if (!isset($_SESSION['user_id'])) {
                throw new Exception('用户未登录', 401);
            }

            $userId = $_SESSION['user_id'];
            $db = getDatabase();

            // 获取用户邮箱
            $stmt = $db->prepare("SELECT email, email_verified FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                throw new Exception('用户不存在', 404);
            }

            if ($user['email_verified']) {
                throw new Exception('邮箱已经验证过了', 400);
            }

            // 生成6位验证码
            $verificationCode = sprintf('%06d', mt_rand(0, 999999));

            // 保存验证码到数据库（10分钟有效期）
            $expiresAt = date('Y-m-d H:i:s', time() + 600);
            $stmt = $db->prepare("INSERT INTO email_verifications (user_id, code, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE code = VALUES(code), expires_at = VALUES(expires_at)");
            $stmt->execute([$userId, $verificationCode, $expiresAt]);

            // 发送邮件
            $emailSent = sendVerificationEmail($user['email'], $verificationCode);

            if ($emailSent) {
                logActivity('verification_code_sent', $userId, ['email' => $user['email']]);
                sendResponse(true, '验证码已发送到您的邮箱');
            } else {
                throw new Exception('邮件发送失败，请稍后重试', 500);
            }
            break;

        case 'verify-code':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed', 405);
            }

            if (!isset($_SESSION['user_id'])) {
                throw new Exception('用户未登录', 401);
            }

            $userId = $_SESSION['user_id'];
            $code = trim($_POST['code'] ?? '');

            if (empty($code)) {
                throw new Exception('请输入验证码', 400);
            }

            $db = getDatabase();

            // 验证验证码
            $stmt = $db->prepare("SELECT id FROM email_verifications WHERE user_id = ? AND code = ? AND expires_at > NOW()");
            $stmt->execute([$userId, $code]);
            $verification = $stmt->fetch();

            if (!$verification) {
                throw new Exception('验证码无效或已过期', 400);
            }

            // 更新用户邮箱验证状态
            $stmt = $db->prepare("UPDATE users SET email_verified = 1 WHERE id = ?");
            $stmt->execute([$userId]);

            // 删除已使用的验证码
            $stmt = $db->prepare("DELETE FROM email_verifications WHERE user_id = ?");
            $stmt->execute([$userId]);

            logActivity('email_verified', $userId);
            sendResponse(true, '邮箱验证成功');
            break;

        case '':
        case 'status':
            // 默认返回认证状态
            if (isset($_SESSION['user_id'])) {
                sendResponse(true, '用户已登录', [
                    'authenticated' => true,
                    'user' => [
                        'id' => $_SESSION['user_id'],
                        'email' => $_SESSION['user_email'] ?? '',
                        'name' => $_SESSION['user_name'] ?? ''
                    ]
                ]);
            } else {
                sendResponse(true, '用户未登录', [
                    'authenticated' => false,
                    'user' => null
                ]);
            }
            break;

        default:
            throw new Exception('未知的API操作: ' . $action, 400);
    }

} catch (Exception $e) {
    $code = $e->getCode() ?: 500;
    http_response_code($code);

    sendResponse(false, $e->getMessage());
}
?>
