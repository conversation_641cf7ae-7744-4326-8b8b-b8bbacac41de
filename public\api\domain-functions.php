<?php
/**
 * 域名相关共享函数
 * Shared Domain Functions
 */

// 包含数据库连接
require_once '../includes/database.php';

// 获取真实的TLD价格数据
function getRealTldPrices() {
    static $tldPrices = null;

    if ($tldPrices === null) {
        $tldPrices = [];
        try {
            $pdo = getDatabase();
            $stmt = $pdo->query("SELECT tld, register_price, renew_price FROM tld_pricing WHERE is_active = 1");
            while ($row = $stmt->fetch()) {
                $tldPrices[$row['tld']] = [
                    'register' => $row['register_price'],
                    'renew' => $row['renew_price']
                ];
            }
        } catch (Exception $e) {
            // 如果数据库查询失败，使用默认价格
            $tldPrices = [
                '.com' => ['register' => 12.99, 'renew' => 14.99],
                '.cn' => ['register' => 8.99, 'renew' => 8.99],
                '.net' => ['register' => 14.99, 'renew' => 16.99],
                '.org' => ['register' => 13.99, 'renew' => 15.99],
                '.info' => ['register' => 8.99, 'renew' => 18.99],
                '.biz' => ['register' => 9.99, 'renew' => 19.99],
                '.io' => ['register' => 39.99, 'renew' => 59.99],
                '.ai' => ['register' => 89.99, 'renew' => 89.99]
            ];
        }
    }

    return $tldPrices;
}

// 防止重复包含
if (!function_exists('checkDomainAvailability')) {

    /**
     * 检查域名可用性（模拟实现）
     */
    function checkDomainAvailability($domain) {
        // 模拟域名检查结果
        $unavailableDomains = [
            'google.com', 'facebook.com', 'amazon.com', 'microsoft.com',
            'apple.com', 'twitter.com', 'youtube.com', 'instagram.com',
            'linkedin.com', 'github.com', 'stackoverflow.com', 'reddit.com'
        ];
        
        $domain = strtolower($domain);
        $available = !in_array($domain, $unavailableDomains);
        
        // 随机化一些结果以模拟真实情况
        if (!in_array($domain, $unavailableDomains)) {
            $available = (rand(1, 100) > 30); // 70%的概率可用
        }
        
        return [
            'domain' => $domain,
            'available' => $available,
            'price' => $available ? (rand(8, 25) + 0.99) : null,
            'currency' => 'USD',
            'period' => 1
        ];
    }

    /**
     * 获取域名价格信息
     */
    function getDomainPrice($tld) {
        $tldPrices = getRealTldPrices();

        if (isset($tldPrices[$tld])) {
            $priceData = $tldPrices[$tld];
            return [
                'register' => $priceData['register'],
                'renew' => $priceData['renew'],
                'transfer' => $priceData['register'], // 使用注册价格作为转移价格
                'min_years' => 1,
                'max_years' => 10,
                'currency' => 'USD',
                'available_for_registration' => true,
                'is_featured' => false
            ];
        }

        // 默认价格
        return [
            'register' => 19.99,
            'renew' => 21.99,
            'transfer' => 19.99,
            'min_years' => 1,
            'max_years' => 10,
            'currency' => 'USD',
            'available_for_registration' => false,
            'note' => 'TLD not supported'
        ];
    }

    /**
     * 获取域名建议
     */
    function getDomainSuggestions($keyword, $limit = 10) {
        $suggestions = [];
        $tldPrices = getRealTldPrices();
        $extensions = array_keys($tldPrices);
        
        // 基础建议
        foreach ($extensions as $ext) {
            if (count($suggestions) >= $limit) break;
            $domain = $keyword . $ext;
            $suggestions[] = [
                'domain' => $domain,
                'available' => (rand(1, 100) > 40), // 60%概率可用
                'price' => getDomainPrice($ext),
                'currency' => 'USD'
            ];
        }
        
        // 添加一些变体建议
        $prefixes = ['my', 'get', 'the', 'best'];
        $suffixes = ['app', 'pro', 'online', 'site', 'web'];
        
        foreach ($prefixes as $prefix) {
            if (count($suggestions) >= $limit) break;
            $domain = $prefix . $keyword . '.com';
            $suggestions[] = [
                'domain' => $domain,
                'available' => (rand(1, 100) > 50),
                'price' => getDomainPrice('.com'),
                'currency' => 'USD'
            ];
        }
        
        foreach ($suffixes as $suffix) {
            if (count($suggestions) >= $limit) break;
            $domain = $keyword . $suffix . '.com';
            $suggestions[] = [
                'domain' => $domain,
                'available' => (rand(1, 100) > 50),
                'price' => getDomainPrice('.com'),
                'currency' => 'USD'
            ];
        }
        
        return array_slice($suggestions, 0, $limit);
    }

    /**
     * 验证域名格式
     */
    function validateDomainName($domain) {
        // 基本格式检查
        if (empty($domain) || strlen($domain) > 253) {
            return false;
        }
        
        // 检查是否包含有效字符
        if (!preg_match('/^[a-z0-9\-\.]+$/i', $domain)) {
            return false;
        }
        
        // 检查是否以点开头或结尾
        if (str_starts_with($domain, '.') || str_ends_with($domain, '.')) {
            return false;
        }
        
        // 检查是否包含连续的点
        if (strpos($domain, '..') !== false) {
            return false;
        }
        
        return true;
    }

    /**
     * 清理域名输入
     */
    function sanitizeDomainName($domain) {
        $domain = strtolower(trim($domain));
        $domain = preg_replace('/[^a-z0-9\-\.]/', '', $domain);
        return $domain;
    }

    /**
     * 解析域名和TLD
     */
    function parseDomainName($domain) {
        $parts = explode('.', $domain);
        if (count($parts) < 2) {
            return ['name' => $domain, 'tld' => ''];
        }

        $tld = '.' . array_pop($parts);
        $name = implode('.', $parts);

        return ['name' => $name, 'tld' => $tld];
    }

    /**
     * 记录活动日志（简化版本）
     */
    function logActivity($action, $userId = null, $data = null) {
        // 简化的日志记录，写入文件
        $logFile = dirname(__DIR__) . '/logs/api_activity.log';
        $logDir = dirname($logFile);

        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'user_id' => $userId,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $data
        ];

        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
}
?>
