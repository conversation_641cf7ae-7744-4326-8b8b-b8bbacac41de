-- 创建邮件队列表
-- Create email queue table

CREATE TABLE IF NOT EXISTS email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    to_email VARCHAR(255) NOT NULL COMMENT '收件人邮箱',
    to_name VA<PERSON>HAR(255) COMMENT '收件人姓名',
    subject VARCHAR(255) NOT NULL COMMENT '邮件主题',
    body TEXT NOT NULL COMMENT '邮件内容',
    template VARCHAR(100) COMMENT '邮件模板',
    template_data JSON COMMENT '模板数据',
    priority INT DEFAULT 5 COMMENT '优先级',
    attempts INT DEFAULT 0 COMMENT '尝试次数',
    max_attempts INT DEFAULT 3 COMMENT '最大尝试次数',
    status ENUM('pending', 'processing', 'sent', 'failed') DEFAULT 'pending' COMMENT '状态',
    error_message TEXT COMMENT '错误信息',
    scheduled_at TIMESTAMP NULL COMMENT '计划发送时间',
    sent_at TIMESTAMP NULL COMMENT '发送时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件队列表';
