<?php
/**
 * 统一数据库连接文件
 */

// 如果配置文件存在，则引入
if (file_exists(__DIR__ . '/config.php')) {
    require_once __DIR__ . '/config.php';
}
if (file_exists(__DIR__ . '/logger.php')) {
    require_once __DIR__ . '/logger.php';
}
if (file_exists(__DIR__ . '/security.php')) {
    require_once __DIR__ . '/security.php';
}

/**
 * 获取数据库连接
 */
if (!function_exists('getDatabase')) {
    function getDatabase() {
        static $pdo = null;
        
        if ($pdo === null) {
            try {
                if (class_exists('Config')) {
                    $config = Config::database();
                    $host = $config['host'];
                    $port = $config['port'];
                    $database = $config['database'];
                    $username = $config['username'];
                    $password = $config['password'];
                } else {
                    $host = 'localhost';
                    $port = '3306';
                    $database = 'www_bt_cn';
                    $username = 'www_bt_cn';
                    $password = 'YAfxfrB8nr6F84LP';
                }
                
                $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
                ]);
                
                if (class_exists('Logger')) {
                    Logger::info('数据库连接成功');
                }
            } catch (PDOException $e) {
                if (class_exists('Logger')) {
                    Logger::error('数据库连接失败', ['error' => $e->getMessage()]);
                }
                throw new Exception('数据库连接失败');
            }
        }
        
        return $pdo;
    }
}

/**
 * 安全的数据库连接
 */
if (!function_exists('safeGetDatabase')) {
    function safeGetDatabase() {
        return getDatabase();
    }
}

// 便捷函数
if (!function_exists('e')) {
    function e($string) {
        if (class_exists('Security')) {
            return Security::escape($string);
        }
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('clean')) {
    function clean($input) {
        if (class_exists('Security')) {
            return Security::cleanInput($input);
        }
        return trim(strip_tags($input));
    }
}
?>