<?php
/**
 * 测试域名导入功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置JSON响应头
header('Content-Type: application/json');

try {
    // 定义项目根目录
    if (!defined('ROOT_PATH')) {
        define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
    }
    
    // 加载环境变量
    function loadEnv($path) {
        if (!file_exists($path)) return;
        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) continue;
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $_ENV[trim($name)] = trim($value, '"\'');
            }
        }
    }
    
    loadEnv(ROOT_PATH . '/.env');
    
    // 测试环境变量
    echo json_encode([
        'success' => true,
        'message' => '测试成功',
        'env_check' => [
            'ROOT_PATH' => ROOT_PATH,
            'NAMESILO_API_KEY' => isset($_ENV['NAMESILO_API_KEY']) ? 'configured' : 'missing',
            'DB_HOST' => $_ENV['DB_HOST'] ?? 'missing',
            'DB_DATABASE' => $_ENV['DB_DATABASE'] ?? 'missing'
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
