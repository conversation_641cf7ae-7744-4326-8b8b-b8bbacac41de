<?php
/**
 * NameSilo Marketplace 网页抓取器
 * 由于API限制，通过网页抓取获取市场域名信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置JSON响应头
header('Content-Type: application/json');

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'scrape-marketplace':
            $page = (int)($_GET['page'] ?? 1);
            $keyword = $_GET['keyword'] ?? '';
            $category = $_GET['category'] ?? '';
            
            $result = scrapeMarketplacePage($page, $keyword, $category);
            
            echo json_encode([
                'success' => true,
                'data' => $result
            ]);
            break;
            
        case 'get-marketplace-stats':
            $stats = getMarketplaceStats();
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
            break;
            
        default:
            throw new Exception('不支持的操作', 400);
    }
    
} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * 抓取NameSilo市场页面
 */
function scrapeMarketplacePage($page = 1, $keyword = '', $category = '') {
    $url = 'https://www.namesilo.com/Marketplace';
    
    // 构建查询参数
    $params = [];
    if ($page > 1) $params['page'] = $page;
    if ($keyword) $params['search'] = $keyword;
    if ($category) $params['category'] = $category;
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    // 发送HTTP请求
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    $html = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200 || !$html) {
        throw new Exception('无法访问NameSilo市场页面');
    }
    
    // 解析HTML获取域名信息
    $domains = parseMarketplaceHTML($html);
    
    return [
        'page' => $page,
        'keyword' => $keyword,
        'category' => $category,
        'domains' => $domains,
        'total_found' => count($domains),
        'url' => $url,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

/**
 * 解析市场页面HTML
 */
function parseMarketplaceHTML($html) {
    $domains = [];
    
    // 使用DOMDocument解析HTML
    $dom = new DOMDocument();
    @$dom->loadHTML($html);
    $xpath = new DOMXPath($dom);
    
    // 查找域名列表（这需要根据实际的HTML结构调整）
    // 注意：这是一个示例，实际的选择器需要根据NameSilo的页面结构来调整
    
    // 尝试多种可能的选择器
    $selectors = [
        '//div[contains(@class, "domain-item")]',
        '//tr[contains(@class, "domain-row")]',
        '//div[contains(@class, "marketplace-item")]',
        '//div[contains(@class, "listing")]'
    ];
    
    foreach ($selectors as $selector) {
        $nodes = $xpath->query($selector);
        if ($nodes->length > 0) {
            foreach ($nodes as $node) {
                $domain = extractDomainInfo($node, $xpath);
                if ($domain) {
                    $domains[] = $domain;
                }
            }
            break; // 找到有效的选择器就停止
        }
    }
    
    // 如果没有找到结构化数据，尝试正则表达式
    if (empty($domains)) {
        $domains = extractDomainsWithRegex($html);
    }
    
    return $domains;
}

/**
 * 从DOM节点提取域名信息
 */
function extractDomainInfo($node, $xpath) {
    $domain = [];
    
    try {
        // 提取域名名称
        $nameNodes = $xpath->query('.//a[contains(@href, ".")]', $node);
        if ($nameNodes->length > 0) {
            $domain['name'] = trim($nameNodes->item(0)->textContent);
        }
        
        // 提取价格
        $priceNodes = $xpath->query('.//*[contains(text(), "$") or contains(text(), "USD")]', $node);
        if ($priceNodes->length > 0) {
            $priceText = $priceNodes->item(0)->textContent;
            preg_match('/\$?([0-9,]+\.?[0-9]*)/', $priceText, $matches);
            if (isset($matches[1])) {
                $domain['price'] = (float)str_replace(',', '', $matches[1]);
            }
        }
        
        // 提取其他信息
        $domain['type'] = 'buy_now'; // 默认类型
        $domain['source'] = 'namesilo_marketplace';
        $domain['scraped_at'] = date('Y-m-d H:i:s');
        
        return isset($domain['name']) ? $domain : null;
        
    } catch (Exception $e) {
        return null;
    }
}

/**
 * 使用正则表达式提取域名
 */
function extractDomainsWithRegex($html) {
    $domains = [];
    
    // 查找域名模式
    preg_match_all('/([a-zA-Z0-9-]+\.[a-zA-Z]{2,})/i', $html, $matches);
    
    if (!empty($matches[1])) {
        $foundDomains = array_unique($matches[1]);
        
        foreach ($foundDomains as $domainName) {
            // 过滤掉明显不是销售域名的域名
            if (isValidMarketplaceDomain($domainName)) {
                $domains[] = [
                    'name' => $domainName,
                    'price' => null,
                    'type' => 'unknown',
                    'source' => 'regex_extraction',
                    'scraped_at' => date('Y-m-d H:i:s')
                ];
            }
        }
    }
    
    return $domains;
}

/**
 * 验证是否为有效的市场域名
 */
function isValidMarketplaceDomain($domain) {
    // 排除常见的系统域名
    $excludePatterns = [
        'namesilo.com',
        'google.com',
        'facebook.com',
        'twitter.com',
        'linkedin.com',
        'youtube.com',
        'cdn.',
        'api.',
        'www.',
        'static.',
        'assets.'
    ];
    
    foreach ($excludePatterns as $pattern) {
        if (strpos($domain, $pattern) !== false) {
            return false;
        }
    }
    
    // 检查域名长度和格式
    if (strlen($domain) < 4 || strlen($domain) > 63) {
        return false;
    }
    
    return true;
}

/**
 * 获取市场统计信息
 */
function getMarketplaceStats() {
    return [
        'note' => 'NameSilo市场统计',
        'limitation' => 'API限制，无法获取完整统计',
        'alternatives' => [
            '访问NameSilo官方市场页面',
            '使用第三方域名市场数据',
            '联系NameSilo获取API访问权限'
        ],
        'marketplace_url' => 'https://www.namesilo.com/Marketplace',
        'timestamp' => date('Y-m-d H:i:s')
    ];
}
?>
