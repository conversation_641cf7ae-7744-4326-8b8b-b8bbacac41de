<?php
/**
 * 结算页面
 */

session_start();
require_once 'includes/database.php';
require_once 'api/domain-functions.php';

// 检查购物车是否为空
if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
    header('Location: cart.php');
    exit;
}

$message = '';
$error = '';

// 处理订单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_order') {
    try {
        $pdo = getDatabase();
        
        // 获取用户信息
        $customerInfo = [
            'name' => trim($_POST['customer_name'] ?? ''),
            'email' => trim($_POST['customer_email'] ?? ''),
            'phone' => trim($_POST['customer_phone'] ?? ''),
            'company' => trim($_POST['customer_company'] ?? ''),
            'address' => trim($_POST['customer_address'] ?? ''),
            'city' => trim($_POST['customer_city'] ?? ''),
            'country' => trim($_POST['customer_country'] ?? 'CN')
        ];
        
        // 验证必填字段
        if (empty($customerInfo['name']) || empty($customerInfo['email'])) {
            throw new Exception('请填写姓名和邮箱');
        }
        
        if (!filter_var($customerInfo['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('邮箱格式不正确');
        }
        
        // 计算订单总价
        $orderTotal = 0;
        foreach ($_SESSION['cart'] as $item) {
            $orderTotal += $item['total_price'];
        }
        
        // 生成订单号
        $orderNumber = 'ORD' . date('Ymd') . sprintf('%06d', rand(1, 999999));
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 创建订单
        $stmt = $pdo->prepare("
            INSERT INTO orders (
                order_number, customer_name, customer_email, customer_phone, 
                customer_company, customer_address, customer_city, customer_country,
                total_amount, currency, status, payment_status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $orderNumber,
            $customerInfo['name'],
            $customerInfo['email'],
            $customerInfo['phone'],
            $customerInfo['company'],
            $customerInfo['address'],
            $customerInfo['city'],
            $customerInfo['country'],
            $orderTotal,
            'USD',
            'pending',
            'pending'
        ]);
        
        $orderId = $pdo->lastInsertId();
        
        // 添加订单项
        $stmt = $pdo->prepare("
            INSERT INTO order_items (
                order_id, domain_name, service_type, years, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($_SESSION['cart'] as $item) {
            $stmt->execute([
                $orderId,
                $item['domain'],
                'register',
                $item['years'],
                $item['price_per_year'],
                $item['total_price']
            ]);
        }
        
        // 提交事务
        $pdo->commit();
        
        // 清空购物车
        $_SESSION['cart'] = [];
        
        // 跳转到支付页面
        header("Location: payment.php?order=" . urlencode($orderNumber));
        exit;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// 计算购物车总价
$cartTotal = 0;
$cartCount = count($_SESSION['cart']);

foreach ($_SESSION['cart'] as $item) {
    $cartTotal += $item['total_price'];
}

$pageTitle = "结算";
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - NameSilo域名销售系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .checkout-step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .order-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            position: sticky;
            top: 20px;
        }
        .domain-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .btn-checkout {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-weight: bold;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
        }
        .btn-checkout:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-globe"></i> NameSilo域名销售系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">首页</a>
                <a class="nav-link" href="domain-search.php">域名搜索</a>
                <a class="nav-link" href="cart.php">购物车</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 消息提示 -->
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fas fa-credit-card"></i> 结算</h2>
                <p class="text-muted">请填写您的信息以完成订单</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <form method="POST" id="checkoutForm">
                    <input type="hidden" name="action" value="create_order">
                    
                    <!-- 步骤1：客户信息 -->
                    <div class="checkout-step">
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <h4 class="mb-0">客户信息</h4>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">姓名 *</label>
                                <input type="text" class="form-control" name="customer_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">邮箱 *</label>
                                <input type="email" class="form-control" name="customer_email" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">电话</label>
                                <input type="tel" class="form-control" name="customer_phone">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">公司</label>
                                <input type="text" class="form-control" name="customer_company">
                            </div>
                            <div class="col-12">
                                <label class="form-label">地址</label>
                                <input type="text" class="form-control" name="customer_address">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">城市</label>
                                <input type="text" class="form-control" name="customer_city">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">国家</label>
                                <select class="form-select" name="customer_country">
                                    <option value="CN" selected>中国</option>
                                    <option value="US">美国</option>
                                    <option value="UK">英国</option>
                                    <option value="JP">日本</option>
                                    <option value="KR">韩国</option>
                                    <option value="SG">新加坡</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2：确认订单 -->
                    <div class="checkout-step">
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <h4 class="mb-0">确认订单</h4>
                        </div>
                        
                        <?php foreach ($_SESSION['cart'] as $item): ?>
                        <div class="domain-item">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1"><?= htmlspecialchars($item['domain']) ?></h6>
                                    <small class="text-muted">域名注册</small>
                                </div>
                                <div class="col-md-3">
                                    <span class="badge bg-info"><?= $item['years'] ?> 年</span>
                                </div>
                                <div class="col-md-3 text-end">
                                    <strong>$<?= number_format($item['total_price'], 2) ?></strong>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-checkout">
                            <i class="fas fa-lock"></i> 创建订单并继续支付
                        </button>
                        <a href="cart.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回购物车
                        </a>
                    </div>
                </form>
            </div>

            <!-- 订单摘要 -->
            <div class="col-lg-4">
                <div class="order-summary">
                    <h5 class="mb-3">订单摘要</h5>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>域名数量:</span>
                        <span><?= $cartCount ?> 个</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>小计:</span>
                        <span>$<?= number_format($cartTotal, 2) ?></span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <strong>总计:</strong>
                        <strong class="text-success">$<?= number_format($cartTotal, 2) ?></strong>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt"></i> 安全支付保障<br>
                            <i class="fas fa-clock"></i> 即时域名激活<br>
                            <i class="fas fa-headset"></i> 24/7 客户支持
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
