<?php
/**
 * 域名管理系统 - 主入口文件
 * Domain Management System - Main Entry Point
 * Version: 2.0
 */

// 启动会话
session_start();

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_VERSION', '2.0.0');
define('APP_NAME', 'NameSilo域名管理系统');

// 直接使用硬编码数据库连接
function getDatabase() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=localhost;port=3306;dbname=www_bt_cn;charset=utf8mb4";
            $pdo = new PDO($dsn, 'www_bt_cn', 'YAfxfrB8nr6F84LP', [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]);
        } catch (PDOException $e) {
            die('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    return $pdo;
}

// 获取系统设置
function getSystemSetting($key, $default = '') {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        try {
            $db = getDatabase();
            $stmt = $db->prepare("SELECT setting_key, setting_value FROM system_settings WHERE is_active = 1");
            $stmt->execute();
            $result = $stmt->fetchAll();
            
            foreach ($result as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            // 如果表不存在，使用默认设置
            $settings = [
                'site_name' => APP_NAME,
                'site_description' => '专业的域名注册和管理平台',
                'maintenance_mode' => '0'
            ];
        }
    }
    
    return $settings[$key] ?? $default;
}

// 检查维护模式
if (getSystemSetting('maintenance_mode', '0') === '1') {
    $maintenanceMessage = getSystemSetting('maintenance_message', '系统维护中，请稍后访问');
    
    http_response_code(503);
    header('Content-Type: text/html; charset=utf-8');
    
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统维护中 - ' . APP_NAME . '</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            text-align: center; 
            padding: 50px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            max-width: 600px; 
            background: rgba(255, 255, 255, 0.1);
            padding: 40px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 { 
            font-size: 2.5rem;
            margin-bottom: 20px; 
            font-weight: 300;
        }
        p { 
            font-size: 1.1rem; 
            line-height: 1.6; 
            opacity: 0.9;
        }
        .icon { 
            font-size: 4rem; 
            margin-bottom: 20px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔧</div>
        <h1>系统维护中</h1>
        <p>' . htmlspecialchars($maintenanceMessage) . '</p>
        <p>我们正在进行系统升级，预计很快就会恢复正常。感谢您的耐心等待！</p>
    </div>
</body>
</html>';
    exit;
}

// 路由处理
$requestUri = $_SERVER['REQUEST_URI'];
$requestPath = parse_url($requestUri, PHP_URL_PATH);

// 移除查询参数
$path = trim($requestPath, '/');

// 路由规则
switch ($path) {
    case '':
    case 'index':
    case 'home':
        include 'frontend/public/index.php';
        break;
        
    case 'search':
    case 'domain-search':
        include 'frontend/public/domain-search.php';
        break;
        
    case 'cart':
        include 'frontend/public/cart.php';
        break;
        
    case 'checkout':
        include 'frontend/public/checkout.php';
        break;
        
    case 'login':
        include 'frontend/auth/login.php';
        break;
        
    case 'register':
        include 'frontend/auth/register.php';
        break;
        
    case 'forgot-password':
        include 'frontend/auth/forgot-password.php';
        break;
        
    case 'verify-email':
        include 'frontend/auth/verify-email.php';
        break;
        
    case 'dashboard':
    case 'user':
        include 'frontend/user/index.php';
        break;
        
    case 'user/domains':
        include 'frontend/user/domains.php';
        break;
        
    case 'user/orders':
        include 'frontend/user/orders.php';
        break;
        
    case 'user/profile':
        include 'frontend/user/profile.php';
        break;
        
    case 'admin':
        include 'backend/admin/index.php';
        break;
        
    case 'admin/login':
        include 'backend/auth/login.php';
        break;
        
    default:
        // 检查是否是API请求
        if (strpos($path, 'api/') === 0) {
            include 'api/router.php';
        } else {
            // 404页面
            http_response_code(404);
            echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - ' . APP_NAME . '</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            text-align: center; 
            padding: 50px; 
            background: #f8f9fa;
            color: #333;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            max-width: 600px; 
            background: white;
            padding: 40px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { 
            font-size: 3rem;
            margin-bottom: 20px; 
            color: #dc3545;
        }
        p { 
            font-size: 1.1rem; 
            line-height: 1.6; 
            margin-bottom: 30px;
        }
        a {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>404</h1>
        <p>抱歉，您访问的页面不存在。</p>
        <a href="/">返回首页</a>
    </div>
</body>
</html>';
        }
        break;
}
?>
