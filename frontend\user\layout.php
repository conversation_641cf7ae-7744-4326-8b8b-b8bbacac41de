<?php
/**
 * 用户中心统一布局
 * User Center Layout
 */

// 确保用户已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// 获取用户信息
function getUserInfo($userId) {
    try {
        require_once '../config.php';
        $db = getDatabase();

        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return null;
    }
}

$userInfo = getUserInfo($_SESSION['user_id']);
$userName = $userInfo ? trim($userInfo['first_name'] . ' ' . $userInfo['last_name']) : '用户';
$userEmail = $userInfo['email'] ?? $_SESSION['user_email'] ?? '';
$userBalance = $userInfo['balance'] ?? 0;

// 获取当前页面
$currentPage = $_GET['page'] ?? 'dashboard';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? '用户中心' ?> - NameSilo域名销售系统</title>
    <meta name="description" content="NameSilo域名销售系统用户中心">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <link href="assets/css/user.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #3730a3;
            --secondary-color: #6b7280;
            --accent-color: #06b6d4;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #2563eb;

            --sidebar-width: 180px;
            --sidebar-collapsed-width: 55px;
            --navbar-height: 64px;

            --sidebar-bg: #ffffff;
            --sidebar-text: #6b7280;
            --sidebar-active: #4f46e5;
            --sidebar-hover: #f3f4f6;
            --sidebar-border: #e5e7eb;

            --content-bg: #f9fafb;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --border-light: #f3f4f6;
            --text-color: #111827;
            --text-muted: #6b7280;
            --text-light: #9ca3af;

            --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

            --border-radius: 8px;
            --border-radius-sm: 6px;
            --border-radius-lg: 12px;

            --transition-fast: all 0.15s ease-out;
            --transition: all 0.2s ease-out;
            --transition-slow: all 0.3s ease-out;

            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-color);
            line-height: 1.5;
            font-size: 14px;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--content-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* 布局容器 */
        .wrapper {
            display: flex;
            width: 100%;
            min-height: 100vh;
        }

        /* 侧边栏样式 - 采用后台风格 */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            color: var(--sidebar-text);
            transition: var(--transition);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            overflow-x: hidden;
            border-right: 1px solid var(--sidebar-border);
            box-shadow: var(--shadow-sm);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
            overflow: visible; /* 允许工具提示显示 */
        }

        /* 折叠状态下隐藏文字元素 */
        .sidebar.collapsed .sidebar-header .brand-text,
        .sidebar.collapsed .nav-section,
        .sidebar.collapsed .nav-text {
            opacity: 0;
            visibility: hidden;
            transform: translateX(-10px);
            transition: var(--transition);
        }

        /* 折叠状态下的品牌图标居中 */
        .sidebar.collapsed .sidebar-header {
            justify-content: center;
            padding: var(--spacing-lg) 0;
        }

        .sidebar.collapsed .sidebar-brand {
            justify-content: center;
            position: relative;
        }

        .sidebar.collapsed .sidebar-brand i {
            font-size: 1.8rem;
            color: var(--primary-color);
            transition: var(--transition);
        }

        .sidebar.collapsed .sidebar-brand:hover i {
            transform: scale(1.1);
            color: var(--primary-light);
        }



        .sidebar.collapsed .user-avatar {
            margin: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .sidebar.collapsed .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        .sidebar.collapsed .user-avatar i {
            color: white;
            font-size: 1.2rem;
        }



        /* 自定义滚动条 */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--content-bg);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: var(--text-light);
        }

        .sidebar-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--sidebar-border);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            text-decoration: none;
            color: var(--text-color);
        }

        .sidebar-brand i {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .brand-text h5 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
        }

        .brand-text span {
            font-size: 10px;
            color: var(--text-muted);
        }



        /* 导航菜单 */
        /* 侧边栏菜单 - 后台风格 */
        .sidebar .components {
            padding: 0;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            list-style: none;
            margin: 0;
        }

        /* 导航分组 */
        .nav-section {
            padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-sm);
            margin-top: var(--spacing-xl);
            position: relative;
        }

        .nav-section:first-child {
            margin-top: var(--spacing-md);
        }

        .nav-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: var(--spacing-lg);
            right: var(--spacing-lg);
            height: 1px;
            background: var(--border-light);
        }

        .nav-section:first-child::before {
            display: none;
        }

        .nav-section span {
            color: var(--text-muted);
            font-size: 0.6875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: var(--transition);
        }

        /* 导航项 */
        .nav-item {
            position: relative;
            margin: 0 var(--spacing-md);
        }

        .nav-link {
            padding: 8px 12px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            color: var(--sidebar-text);
            text-decoration: none;
            transition: var(--transition-fast);
            position: relative;
            border-radius: var(--border-radius);
            margin-bottom: 2px;
            overflow: hidden;
        }

        /* 悬停效果 - 后台风格 */
        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            transition: var(--transition-fast);
            z-index: 0;
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: white;
            background: transparent;
            transform: translateX(3px);
        }

        .nav-link:hover .nav-icon i,
        .nav-link:hover .nav-text {
            color: white;
            position: relative;
            z-index: 1;
        }

        .nav-item.active > .nav-link::before {
            width: 100%;
        }

        .nav-item.active > .nav-link {
            color: white;
            background: transparent;
            font-weight: 600;
        }

        .nav-item.active > .nav-link .nav-icon i,
        .nav-item.active > .nav-link .nav-text {
            color: white;
            position: relative;
            z-index: 1;
        }

        /* 导航图标 */
        .nav-icon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
            position: relative;
        }

        .nav-icon i {
            font-size: 0.7rem;
            transition: var(--transition-fast);
        }

        .nav-item.active .nav-icon i {
            transform: scale(1.1);
        }

        /* 导航文字 */
        .nav-text {
            flex: 1;
            transition: var(--transition);
            white-space: nowrap;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* 通知徽章 */
        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--sidebar-bg);
        }

        /* 折叠状态下的样式 */
        .sidebar.collapsed .nav-section span,
        .sidebar.collapsed .nav-text {
            opacity: 0;
            visibility: hidden;
            transform: translateX(-10px);
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: var(--spacing-md);
            position: relative;
            border-radius: var(--border-radius);
            margin: 2px var(--spacing-xs);
        }

        .sidebar.collapsed .nav-link:hover {
            background: var(--sidebar-hover);
            transform: translateX(2px);
        }

        .sidebar.collapsed .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .sidebar.collapsed .nav-link.active .nav-icon i {
            color: white;
        }

        .sidebar.collapsed .nav-icon {
            margin: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar.collapsed .nav-icon i {
            font-size: 1.1rem;
            transition: var(--transition);
        }

        .sidebar.collapsed .nav-link:hover .nav-icon i {
            transform: scale(1.1);
        }

        /* 工具提示样式 */
        .sidebar.collapsed .nav-link::after {
            content: attr(data-tooltip);
            position: absolute;
            left: calc(100% + 10px);
            top: 50%;
            transform: translateY(-50%);
            background: var(--text-color);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 1000;
            pointer-events: none;
        }

        .sidebar.collapsed .nav-link::before {
            content: '';
            position: absolute;
            left: calc(100% + 5px);
            top: 50%;
            transform: translateY(-50%);
            border: 5px solid transparent;
            border-right-color: var(--text-color);
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 1000;
        }

        .sidebar.collapsed .nav-link:hover::after,
        .sidebar.collapsed .nav-link:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            width: calc(100% - var(--sidebar-width));
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            transition: var(--transition);
            background: var(--content-bg);
        }

        .main-content.expanded {
            margin-left: var(--sidebar-collapsed-width);
            width: calc(100% - var(--sidebar-collapsed-width));
        }

        .top-navbar {
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
            padding: 0 var(--spacing-xl);
            height: var(--navbar-height);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-xs);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .navbar-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        /* 侧边栏切换按钮 - 后台风格 */
        .sidebar-toggle {
            width: 36px;
            height: 36px;
            border: 1px solid var(--border-color);
            background: transparent;
            border-radius: var(--border-radius);
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
            margin-right: var(--spacing-md);
            font-size: 0.875rem;
            outline: none;
            position: relative;
            z-index: 1;
        }

        .sidebar-toggle:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .sidebar-toggle:active {
            transform: scale(0.95);
        }

        .sidebar-toggle:focus {
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }

        /* 确保按钮可点击 */
        .sidebar-toggle {
            pointer-events: auto !important;
            user-select: none;
        }

        .sidebar-toggle i {
            pointer-events: none;
        }

        .content-area {
            flex: 1;
            padding: var(--spacing-xl);
            background: var(--content-bg);
        }

        /* 页面标题 */
        .page-header {
            margin-bottom: var(--spacing-xl);
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: var(--spacing-xs);
        }

        .page-subtitle {
            color: var(--text-muted);
            font-size: 14px;
            margin: 0;
        }

        /* 卡片样式 */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
        }

        .card-header {
            background: transparent;
            border-bottom: 1px solid var(--border-color);
            padding: var(--spacing-lg);
            font-weight: 600;
        }

        .card-body {
            padding: var(--spacing-lg);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }

        /* 统计卡片 */
        .stats-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            transition: var(--transition);
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: var(--spacing-md);
        }

        /* 按钮样式 */
        .btn {
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: var(--transition);
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }



        /* 表格样式 */
        .table {
            font-size: 14px;
        }

        .table th {
            font-weight: 600;
            color: var(--text-muted);
            border-bottom: 1px solid var(--border-color);
            padding: 12px;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-light);
        }

        /* 徽章样式 */
        .badge {
            font-size: 11px;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: var(--border-radius-sm);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 170px;
            }
        }

        @media (max-width: 992px) {
            :root {
                --sidebar-width: 160px;
            }



            .nav-link {
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .nav-text {
                font-size: 13px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 240px;
                z-index: 1050;
                transition: transform 0.3s ease-out;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .content-area {
                padding: var(--spacing-md);
            }

            .top-navbar {
                padding: 0 var(--spacing-md);
            }

            .navbar-title {
                font-size: 16px;
            }

            /* 移动端隐藏工具提示 */
            .sidebar .nav-link::after,
            .sidebar .nav-link::before {
                display: none !important;
            }

            /* 添加遮罩层 */
            .sidebar.show::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: -1;
                animation: fadeIn 0.3s ease-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        }

        @media (max-width: 576px) {
            .content-area {
                padding: var(--spacing-sm);
            }

            .sidebar {
                width: 100vw;
            }

            .sidebar-header {
                padding: var(--spacing-md);
            }

            /* 小屏幕下的菜单项优化 */
            .nav-link {
                padding: var(--spacing-md) var(--spacing-lg);
                font-size: 15px;
            }

            .nav-icon {
                width: 20px;
                margin-right: var(--spacing-md);
            }

            .nav-icon i {
                font-size: 1rem;
            }

            /* 用户信息区域优化 */
            .sidebar-user {
                padding: var(--spacing-lg);
            }

            .user-avatar {
                width: 50px;
                height: 50px;
            }

            .user-info h6 {
                font-size: 16px;
            }


        }



            .balance-card {
                margin: var(--spacing-sm) var(--spacing-md);
                padding: var(--spacing-sm);
            }



            .nav-link {
                padding: var(--spacing-md);
            }

            .nav-text {
                font-size: 14px;
            }

            .top-navbar {
                padding: 0 var(--spacing-sm);
            }

            .navbar-title {
                font-size: 14px;
            }

            .navbar-actions .text-muted {
                display: none !important;
            }

            .stats-card {
                text-align: center;
                padding: var(--spacing-sm);
            }

            .page-title {
                font-size: 20px;
            }

            .page-header h1 {
                font-size: 20px;
            }

            .page-subtitle {
                font-size: 13px;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .btn {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: 13px;
            }

            .table-responsive {
                font-size: 12px;
            }

            .card {
                margin-bottom: var(--spacing-md);
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- 侧边栏 -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <i class="fas fa-globe"></i>
                    <div class="brand-text">
                        <h5>NameSilo</h5>
                        <span>用户中心</span>
                    </div>
                </div>
            </div>






            <ul class="components">
                <li class="nav-section">
                    <span>主要功能</span>
                </li>
                <li class="nav-item <?= $currentPage === 'dashboard' ? 'active' : '' ?>">
                    <a href="index.php" class="nav-link" data-tooltip="仪表盘">
                        <div class="nav-icon"><i class="fas fa-tachometer-alt"></i></div>
                        <span class="nav-text">仪表盘</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'profile' ? 'active' : '' ?>">
                    <a href="profile.php" class="nav-link" data-tooltip="个人信息">
                        <div class="nav-icon"><i class="fas fa-user-edit"></i></div>
                        <span class="nav-text">个人信息</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'domains' ? 'active' : '' ?>">
                    <a href="domains.php" class="nav-link" data-tooltip="域名管理">
                        <div class="nav-icon"><i class="fas fa-globe"></i></div>
                        <span class="nav-text">域名管理</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'dns' ? 'active' : '' ?>">
                    <a href="#" class="nav-link" data-tooltip="DNS管理" onclick="showDNSQuickAccess()">
                        <div class="nav-icon"><i class="fas fa-dns"></i></div>
                        <span class="nav-text">DNS管理</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'notifications' ? 'active' : '' ?>">
                    <a href="notifications.php" class="nav-link" data-tooltip="通知中心">
                        <div class="nav-icon">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge" id="notificationBadge" style="display: none;"></span>
                        </div>
                        <span class="nav-text">通知中心</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'orders' ? 'active' : '' ?>">
                    <a href="orders.php" class="nav-link" data-tooltip="订单管理">
                        <div class="nav-icon"><i class="fas fa-shopping-cart"></i></div>
                        <span class="nav-text">订单管理</span>
                    </a>
                </li>

                <li class="nav-section">
                    <span>财务管理</span>
                </li>
                <li class="nav-item <?= $currentPage === 'billing' ? 'active' : '' ?>">
                    <a href="billing.php" class="nav-link" data-tooltip="财务管理">
                        <div class="nav-icon"><i class="fas fa-credit-card"></i></div>
                        <span class="nav-text">财务管理</span>
                    </a>
                </li>

                <li class="nav-section">
                    <span>账户设置</span>
                </li>
                <li class="nav-item <?= $currentPage === 'security' ? 'active' : '' ?>">
                    <a href="security.php" class="nav-link" data-tooltip="账户安全">
                        <div class="nav-icon"><i class="fas fa-shield-alt"></i></div>
                        <span class="nav-text">账户安全</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'support' ? 'active' : '' ?>">
                    <a href="support.php" class="nav-link" data-tooltip="支持中心">
                        <div class="nav-icon"><i class="fas fa-life-ring"></i></div>
                        <span class="nav-text">支持中心</span>
                    </a>
                </li>

                <li class="nav-section">
                    <span>其他</span>
                </li>
                <li class="nav-item">
                    <a href="../index.php" class="nav-link" data-tooltip="返回首页">
                        <div class="nav-icon"><i class="fas fa-home"></i></div>
                        <span class="nav-text">返回首页</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="../api/auth.php?action=logout" class="nav-link" data-tooltip="退出登录">
                        <div class="nav-icon"><i class="fas fa-sign-out-alt"></i></div>
                        <span class="nav-text">退出登录</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <div class="top-navbar">
                <div class="navbar-brand">
                    <button class="sidebar-toggle" id="sidebarCollapse" type="button">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="btn btn-link d-lg-none me-3 p-2" id="sidebarToggle" style="color: var(--text-color); border: none; background: none;">
                        <i class="fas fa-bars" style="font-size: 18px;"></i>
                    </button>
                    <h1 class="navbar-title"><?= $pageTitle ?? '用户中心' ?></h1>
                </div>
                <div class="navbar-actions">
                    <span class="text-muted me-3 d-none d-md-inline">欢迎回来，<?= htmlspecialchars($userName) ?></span>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>个人信息</a></li>
                            <li><a class="dropdown-item" href="security.php"><i class="fas fa-shield-alt me-2"></i>账户安全</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../api/auth.php?action=logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <?php if (isset($pageContent)) echo $pageContent; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 600,
            easing: 'ease-out-cubic',
            once: true
        });

        // 恢复侧边栏状态
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.querySelector('.sidebar');
            const content = document.querySelector('.main-content');
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';

            if (isCollapsed && window.innerWidth > 768) {
                sidebar.classList.add('collapsed');
                content.classList.add('expanded');
            }


        });



        // 桌面端侧边栏切换（折叠/展开）
        document.getElementById('sidebarCollapse')?.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const sidebar = document.querySelector('.sidebar');
            const content = document.querySelector('.main-content');

            sidebar.classList.toggle('collapsed');
            content.classList.toggle('expanded');

            // 保存侧边栏状态到本地存储
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebar_collapsed', isCollapsed);

            // 添加动画效果
            sidebar.style.transition = 'width 0.3s ease-out';
            content.style.transition = 'margin-left 0.3s ease-out, width 0.3s ease-out';
        });

        // 移动端侧边栏切换
        document.getElementById('sidebarToggle')?.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');

            // 防止页面滚动
            if (sidebar.classList.contains('show')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });

        // 点击外部关闭侧边栏
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.sidebar');
            const toggle = document.getElementById('sidebarToggle');

            if (window.innerWidth <= 768 &&
                sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                !toggle?.contains(e.target)) {
                sidebar.classList.remove('show');
                document.body.style.overflow = '';
            }
        });

        // 窗口大小改变时的处理
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
                document.body.style.overflow = '';
            }
        });

        // 侧边栏链接点击后自动关闭（移动端）
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    const sidebar = document.querySelector('.sidebar');
                    sidebar.classList.remove('show');
                    document.body.style.overflow = '';
                }
            });
        });

        // 页面加载完成后显示
        document.addEventListener('DOMContentLoaded', function() {
            document.body.classList.add('loaded');
        });

        // DNS快速访问
        function showDNSQuickAccess() {
            // 这里可以显示域名选择模态框
            window.location.href = 'domains.php';
        }

        // 加载通知数量
        function loadNotificationCount() {
            fetch('api/notifications.php?action=unread_count')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationBadge(data.data.unread_count);
                    }
                })
                .catch(error => {
                    console.error('加载通知数量失败:', error);
                });
        }

        // 更新通知徽章
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notificationBadge');
            if (badge) {
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count;
                    badge.style.display = 'flex';
                } else {
                    badge.style.display = 'none';
                }
            }
        }

        // 定期检查通知
        setInterval(loadNotificationCount, 30000); // 每30秒检查一次

        // 页面加载时检查通知
        loadNotificationCount();
    </script>
</body>
</html>
