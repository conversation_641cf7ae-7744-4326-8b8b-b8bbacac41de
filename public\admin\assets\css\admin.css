/**
 * NameSilo域名销售系统 - 管理后台样式
 * NameSilo Domain Sales System - Admin Panel CSS
 */

/* 全局样式 */
:root {
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --secondary-color: #6b7280;
    --accent-color: #06b6d4;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #2563eb;

    --sidebar-width: 260px;
    --sidebar-collapsed-width: 64px;
    --navbar-height: 64px;

    --sidebar-bg: #ffffff;
    --sidebar-text: #6b7280;
    --sidebar-active: #4f46e5;
    --sidebar-hover: #f3f4f6;
    --sidebar-border: #e5e7eb;

    --content-bg: #f9fafb;
    --card-bg: #ffffff;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --text-color: #111827;
    --text-muted: #6b7280;
    --text-light: #9ca3af;

    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    --border-radius: 8px;
    --border-radius-sm: 6px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    --transition-fast: all 0.15s ease-out;
    --transition: all 0.2s ease-out;
    --transition-slow: all 0.3s ease-out;

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--content-bg);
    color: var(--text-color);
    line-height: 1.5;
    font-size: 14px;
    font-weight: 400;
    opacity: 0;
    transition: opacity 0.3s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body.loaded {
    opacity: 1;
}

/* 性能优化 */
* {
    will-change: auto;
}

.transition-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--content-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 布局容器 */
.wrapper {
    display: flex;
    width: 100%;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    transition: var(--transition);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    overflow-y: auto;
    overflow-x: hidden;
    border-right: 1px solid var(--sidebar-border);
    box-shadow: var(--shadow-sm);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .sidebar-header .brand-text,
.sidebar.collapsed .components span,
.sidebar.collapsed .nav-section span,
.sidebar.collapsed .sidebar-footer span {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
}

.sidebar.collapsed .sidebar-header {
    padding: var(--spacing-lg) var(--spacing-md);
    justify-content: center;
}

.sidebar.collapsed .sidebar-brand {
    justify-content: center;
}



.sidebar.collapsed .components li a {
    justify-content: center;
    padding: var(--spacing-md);
}

.sidebar.collapsed .sidebar-footer {
    padding: var(--spacing-md);
}

.sidebar.collapsed .sidebar-footer .footer-link {
    justify-content: center;
    padding: var(--spacing-sm);
}

/* 侧边栏头部 */
.sidebar-header {
    padding: var(--spacing-lg) var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sidebar-brand i {
    font-size: 1rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    width: 28px;
    height: 28px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xs);
    flex-shrink: 0;
}

.brand-text h5 {
    color: var(--text-color);
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

.brand-text span {
    color: var(--text-muted);
    font-size: 0.6875rem;
    font-weight: 500;
    transition: var(--transition);
}





/* 侧边栏菜单 */
.sidebar .components {
    padding: 0;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 导航分组 */
.nav-section {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-sm);
    margin-top: var(--spacing-xl);
    position: relative;
}

.nav-section:first-child {
    margin-top: var(--spacing-md);
}

.nav-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: var(--spacing-lg);
    right: var(--spacing-lg);
    height: 1px;
    background: var(--border-light);
}

.nav-section:first-child::before {
    display: none;
}

.nav-section span {
    color: var(--text-muted);
    font-size: 0.6875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-section span i {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* 导航项 */
.nav-item {
    position: relative;
    margin: 0 var(--spacing-md);
}

.nav-link {
    padding: var(--spacing-md);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    gap: var(--spacing-md);
}

.nav-link:hover {
    color: var(--primary-color);
    background: var(--sidebar-hover);
    transform: translateX(3px);
}

.nav-item.active > .nav-link {
    color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
    font-weight: 600;
    box-shadow: inset 3px 0 0 var(--primary-color);
}

/* 导航图标 */
.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.nav-icon i {
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.nav-item.active .nav-icon i {
    transform: scale(1.1);
}

/* 导航文字 */
.nav-text {
    flex: 1;
    transition: var(--transition);
    white-space: nowrap;
}

/* 导航箭头 */
.nav-arrow {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    opacity: 0.6;
}

.nav-arrow i {
    font-size: 0.75rem;
    transition: var(--transition-fast);
}

.nav-dropdown.expanded .nav-arrow i {
    transform: rotate(90deg);
}

/* 导航徽章 */
.nav-badge {
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-dot {
    width: 6px;
    height: 6px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.badge-count {
    background: var(--danger-color);
    color: white;
    font-size: 0.6875rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* 子菜单 */
.nav-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-xs);
}

.nav-dropdown.expanded + .nav-submenu,
.nav-item.active .nav-submenu {
    max-height: 300px;
    opacity: 1;
}

.nav-submenu li {
    margin: 0;
}

.nav-submenu a {
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) var(--spacing-xl);
    font-size: 0.8125rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    margin: var(--spacing-xs);
    transition: var(--transition-fast);
    position: relative;
}

.nav-submenu a:hover {
    color: var(--primary-color);
    background: rgba(79, 70, 229, 0.05);
    transform: translateX(2px);
}

.nav-submenu li.active a {
    color: var(--primary-color);
    background: rgba(79, 70, 229, 0.08);
    font-weight: 600;
}

.nav-submenu a i {
    font-size: 0.75rem;
    width: 14px;
    text-align: center;
    opacity: 0.7;
}

.nav-submenu a span {
    flex: 1;
}

/* 折叠状态下的样式 */
.sidebar.collapsed .nav-section span,
.sidebar.collapsed .nav-text,
.sidebar.collapsed .nav-arrow,
.sidebar.collapsed .nav-badge {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: var(--spacing-md);
}

.sidebar.collapsed .nav-submenu {
    display: none;
}

.sidebar.collapsed .nav-icon {
    margin: 0;
}

/* 工具提示 */
.sidebar.collapsed [data-tooltip] {
    position: relative;
}

.sidebar.collapsed [data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: calc(100% + 10px);
    top: 50%;
    transform: translateY(-50%);
    background: var(--text-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed [data-tooltip]:hover::before {
    content: '';
    position: absolute;
    left: calc(100% + 4px);
    top: 50%;
    transform: translateY(-50%);
    border: 3px solid transparent;
    border-right-color: var(--text-color);
    z-index: 1000;
}

/* 菜单搜索 */
.nav-search {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    margin-bottom: var(--spacing-md);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-wrapper i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-muted);
    font-size: 0.875rem;
    z-index: 1;
}

.search-input-wrapper input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-xl);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    background: var(--card-bg);
    transition: var(--transition-fast);
}

.search-input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-clear {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.search-clear:hover {
    background: var(--border-light);
    color: var(--text-color);
}

/* 收藏菜单 */
.nav-link.favorited::after {
    content: '';
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    width: 6px;
    height: 6px;
    background: var(--warning-color);
    border-radius: 50%;
}

/* 上下文菜单 */
.nav-context-menu {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-xs);
    min-width: 160px;
    z-index: 9999;
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
    color: var(--text-color);
}

.context-menu-item:hover {
    background: var(--border-light);
    color: var(--primary-color);
}

.context-menu-item i {
    width: 16px;
    text-align: center;
    font-size: 0.75rem;
}

/* 菜单动画增强 */
.nav-item {
    animation: slideInLeft 0.3s ease-out;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.15s; }
.nav-item:nth-child(3) { animation-delay: 0.2s; }
.nav-item:nth-child(4) { animation-delay: 0.25s; }
.nav-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 菜单项悬停效果增强 */
.nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius);
    transition: width 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link:hover {
    color: white;
    background: transparent;
}

.nav-link:hover .nav-icon i,
.nav-link:hover .nav-arrow i {
    color: white;
}

.nav-item.active > .nav-link::before {
    width: 100%;
}

.nav-item.active > .nav-link {
    color: white;
    background: transparent;
    box-shadow: none;
}

.nav-item.active > .nav-link .nav-icon i,
.nav-item.active > .nav-link .nav-arrow i {
    color: white;
}

/* 菜单加载状态 */
.nav-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.nav-loading .loading {
    margin-right: var(--spacing-sm);
}

/* 菜单空状态 */
.nav-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.nav-empty i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* 菜单分隔线 */
.nav-divider {
    height: 1px;
    background: var(--border-light);
    margin: var(--spacing-md) var(--spacing-lg);
}

/* 菜单快捷键提示 */
.nav-shortcut {
    font-size: 0.6875rem;
    color: var(--text-light);
    background: var(--border-light);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    margin-left: auto;
}

/* 响应式菜单优化 */
@media (max-width: 992px) {
    .nav-search {
        display: none;
    }

    .nav-submenu {
        position: static;
        box-shadow: none;
        border: none;
        background: rgba(0, 0, 0, 0.05);
    }

    .nav-link::before {
        display: none;
    }

    .nav-link:hover {
        background: var(--sidebar-hover);
        color: var(--primary-color);
    }

    .nav-item.active > .nav-link {
        background: rgba(79, 70, 229, 0.1);
        color: var(--primary-color);
        box-shadow: inset 3px 0 0 var(--primary-color);
    }
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.footer-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    color: var(--sidebar-text);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    margin-bottom: 0.5rem;
}

.footer-link:hover {
    color: white;
    background: var(--sidebar-hover);
}

.footer-link.logout:hover {
    background: var(--danger-color);
}

.footer-link i {
    width: 20px;
    text-align: center;
}

.footer-link span {
    transition: var(--transition);
}

/* 主内容区域 */
.content {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    transition: var(--transition);
    background: var(--content-bg);
}

.content.expanded {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
}

/* 顶部导航栏 */
.top-navbar {
    height: var(--navbar-height);
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-xs);
    position: sticky;
    top: 0;
    z-index: 999;
    backdrop-filter: blur(8px);
}

.navbar-content {
    height: 100%;
    padding: 0 var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.sidebar-toggle {
    width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: var(--border-radius);
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    position: relative;
    z-index: 9999 !important;
    outline: none;
    pointer-events: auto !important;
    touch-action: manipulation;
}

.sidebar-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.sidebar-toggle:active {
    transform: scale(0.95);
}

.sidebar-toggle:focus {
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* 确保按钮可点击 */
.sidebar-toggle {
    pointer-events: auto !important;
    user-select: none;
}

.sidebar-toggle i {
    pointer-events: none;
}

.breadcrumb-container {
    margin: 0;
}

.breadcrumb {
    margin: 0;
    background: transparent;
    padding: 0;
}

.breadcrumb-item {
    font-size: 0.9rem;
}

.breadcrumb-item a {
    color: var(--text-muted);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-color);
    font-weight: 500;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* 搜索框 */
.search-container {
    position: relative;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.9rem;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: 25px;
    background: var(--content-bg);
    font-size: 0.9rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 通知菜单 */
.notification-dropdown {
    position: relative;
}

.notification-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: 50%;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-btn:hover {
    background: var(--content-bg);
}

.notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 18px;
    height: 18px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.notification-menu {
    width: 350px;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    padding: 0;
    margin-top: 0.5rem;
}

.notification-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: between;
    align-items: center;
}

.notification-header h6 {
    margin: 0;
    font-weight: 600;
    color: var(--text-color);
}

.mark-read {
    color: var(--primary-color);
    font-size: 0.8rem;
    cursor: pointer;
    font-weight: 500;
}

.mark-read:hover {
    text-decoration: underline;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--content-bg);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--content-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-content p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
}

.notification-content span {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.notification-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.notification-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.notification-footer a:hover {
    text-decoration: underline;
}

/* 用户菜单 */
.user-dropdown {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
}

.user-btn:hover {
    background: var(--content-bg);
}

.user-btn .user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.user-btn .user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.user-btn .user-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    line-height: 1.2;
}

.user-btn .user-role {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.2;
}

.user-btn i.fa-chevron-down {
    font-size: 0.8rem;
    color: var(--text-muted);
    transition: var(--transition);
}

.user-dropdown.show .user-btn i.fa-chevron-down {
    transform: rotate(180deg);
}

.user-menu {
    width: 280px;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    padding: 0;
    margin-top: 0.5rem;
}

.user-menu-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.user-details h6 {
    margin: 0;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.user-details p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
}

.user-menu-body {
    padding: 0.5rem 0;
}

.user-menu-footer {
    border-top: 1px solid var(--border-color);
    padding: 0.5rem 0;
}

.user-menu .dropdown-item {
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: var(--transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.user-menu .dropdown-item:hover {
    background: var(--content-bg);
    color: var(--text-color);
}

.user-menu .dropdown-item.logout:hover {
    background: var(--danger-color);
    color: white;
}

.user-menu .dropdown-item i {
    width: 20px;
    text-align: center;
    font-size: 0.9rem;
}

/* 主内容区域 */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - var(--navbar-height));
}

/* 卡片样式 */
.card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xs);
    margin-bottom: var(--spacing-xl);
    background: var(--card-bg);
    transition: var(--transition-fast);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--border-light);
}

.card-header {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
    padding: var(--spacing-lg) var(--spacing-xl);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-xl);
    right: var(--spacing-xl);
    height: 1px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    opacity: 0.1;
}

.card-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    line-height: 1.4;
}

.card-title i {
    color: var(--primary-color);
    font-size: 0.875rem;
}

.card-body {
    padding: var(--spacing-xl);
}

/* 统计卡片 */
.stats-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-xs);
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--primary-color);
    opacity: 0.6;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.stats-card:hover::before {
    opacity: 1;
    width: 4px;
}

.stats-card .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin-bottom: var(--spacing-lg);
    position: relative;
    box-shadow: var(--shadow-sm);
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1;
    font-feature-settings: 'tnum';
}

.stats-card .stats-label {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
    line-height: 1.4;
}

.stats-card .stats-trend {
    font-size: 0.75rem;
    margin-top: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background: var(--border-light);
    width: fit-content;
}

.stats-trend.up {
    color: var(--success-color);
    background: rgba(5, 150, 105, 0.1);
}

.stats-trend.down {
    color: var(--danger-color);
    background: rgba(220, 38, 38, 0.1);
}

.stats-trend i {
    font-size: 0.75rem;
}

/* 表格样式 */
.table {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-xs);
    border: 1px solid var(--border-color);
    margin-bottom: 0;
}

.table thead th {
    background: var(--border-light);
    border: none;
    font-weight: 600;
    color: var(--text-color);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    position: relative;
}

.table thead th:first-child {
    border-radius: var(--border-radius) 0 0 0;
}

.table thead th:last-child {
    border-radius: 0 var(--border-radius) 0 0;
}

.table tbody td {
    border: none;
    padding: var(--spacing-lg);
    vertical-align: middle;
    color: var(--text-color);
    font-size: 0.875rem;
}

.table tbody tr {
    border-bottom: 1px solid var(--border-light);
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: rgba(79, 70, 229, 0.02);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table tbody tr:last-child td:first-child {
    border-radius: 0 0 0 var(--border-radius);
}

.table tbody tr:last-child td:last-child {
    border-radius: 0 0 var(--border-radius) 0;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition-fast);
    border: 1px solid transparent;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #047857;
    border-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #b45309;
    border-color: #b45309;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-outline-secondary {
    border-color: var(--border-color);
    color: var(--text-muted);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--border-light);
    color: var(--text-color);
    border-color: var(--text-muted);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    gap: var(--spacing-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    gap: var(--spacing-sm);
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.875rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-secondary {
    background: var(--text-muted);
    color: white;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    background: var(--card-bg);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    background: var(--card-bg);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.input-group {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.input-group-text {
    background: var(--content-bg);
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    font-weight: 500;
}

/* 分页样式 */
.pagination {
    margin: 0;
    gap: 0.25rem;
}

.page-link {
    border: 1px solid var(--border-color);
    color: var(--text-color);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    text-decoration: none;
}

.page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-item.disabled .page-link {
    color: var(--text-muted);
    background: var(--content-bg);
    border-color: var(--border-color);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 350px;
    margin: 1rem 0;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
    color: var(--text-light);
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    background: var(--text-color);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 0.75rem;
}

/* 模态框 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
}

/* 警告框 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border-left-color: var(--info-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .search-box {
        width: 250px;
    }

    .user-btn .user-info {
        display: none;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        position: fixed;
        z-index: 1050;
        transition: transform 0.3s ease;
    }

    .sidebar.collapsed {
        transform: translateX(0);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
        width: 100%;
    }

    .content.expanded {
        margin-left: 0;
        width: 100%;
    }

    .navbar-content {
        padding: 0 1rem;
    }

    .search-container {
        display: none;
    }

    .main-content {
        padding: 1.5rem;
    }

    .stats-card {
        margin-bottom: 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
    }
}

@media (max-width: 768px) {
    .navbar-content {
        padding: 0 1rem;
    }

    .navbar-right {
        gap: 1rem;
    }

    .notification-menu,
    .user-menu {
        width: 280px;
        margin-left: -200px;
    }

    .main-content {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem 1.5rem;
    }

    .card-body {
        padding: 1rem 1.5rem;
    }

    .stats-card {
        padding: 1.5rem;
    }

    .stats-card .stats-number {
        font-size: 2rem;
    }

    .table thead th,
    .table tbody td {
        padding: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 0.75rem;
    }

    .card-header {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .stats-card {
        padding: 1rem;
    }

    .stats-card .stats-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stats-card .stats-number {
        font-size: 1.75rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .top-navbar {
        display: none !important;
    }

    .content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .main-content {
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .btn {
        display: none !important;
    }
}

/* 页面头部样式 */
.page-header {
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    line-height: 1.3;
}

.page-title i {
    font-size: 1.5rem;
}

.page-subtitle {
    color: var(--text-muted);
    margin: 0;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.4;
}

.page-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

/* 用户头像样式 */
.user-avatar-wrapper {
    position: relative;
    display: inline-block;
}

.user-avatar-large {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.bg-primary {
    background: var(--primary-color) !important;
}

.bg-secondary {
    background: var(--text-muted) !important;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.online {
    background: var(--success-color);
}

.status-indicator.offline {
    background: var(--text-muted);
}

/* 用户信息单元格 */
.user-info-cell {
    min-width: 200px;
}

.user-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.user-name strong {
    color: var(--text-color);
    font-size: 0.95rem;
}

.user-id {
    font-size: 0.75rem;
    color: var(--text-muted);
    background: var(--content-bg);
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
}

.user-fullname {
    color: var(--text-muted);
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
}

.user-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.meta-item {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 联系信息 */
.contact-info {
    min-width: 200px;
}

.email-info,
.phone-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

.email-info i,
.phone-info i {
    width: 14px;
    color: var(--text-muted);
}

/* 角色信息 */
.role-info {
    min-width: 120px;
}

.permission-info {
    margin-top: 0.25rem;
}

/* 状态信息 */
.status-info {
    min-width: 120px;
}

.status-detail {
    margin-top: 0.25rem;
}

/* 活动信息 */
.activity-info {
    min-width: 140px;
}

.last-login,
.login-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

.last-login i,
.login-count i {
    width: 14px;
    color: var(--text-muted);
}

/* 表格增强样式 */
.table th {
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background: rgba(99, 102, 241, 0.02);
}

/* 复选框样式 */
.form-check-input {
    border-radius: 4px;
    border: 2px solid var(--border-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 快速操作卡片 */
.quick-actions {
    margin-bottom: var(--spacing-xl);
}

.quick-action-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition-fast);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-color);
    border-color: var(--primary-color);
}

.quick-action-card:hover::before {
    transform: scaleX(1);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.quick-action-content h6 {
    margin: 0 0 var(--spacing-xs) 0;
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.875rem;
    line-height: 1.4;
}

.quick-action-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.75rem;
    line-height: 1.4;
}

/* 页面头部增强 */
.page-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.current-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--content-bg);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
}

/* 搜索结果样式 */
.search-results {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.search-result-item:hover {
    background: var(--content-bg);
    color: var(--primary-color);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item i {
    width: 20px;
    text-align: center;
    color: var(--text-muted);
}

.search-result-item:hover i {
    color: var(--primary-color);
}

/* 实用样式类 */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), var(--primary-light)) 1;
}

.shadow-soft {
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.08);
}

.rounded-soft {
    border-radius: var(--border-radius);
}

.rounded-large {
    border-radius: var(--border-radius-lg);
}

/* 动画增强 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeIn {
    animation: fadeIn 0.4s ease-out;
}

.animate-slideInDown {
    animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

/* 延迟动画 */
.animate-delay-100 {
    animation-delay: 0.1s;
}

.animate-delay-200 {
    animation-delay: 0.2s;
}

.animate-delay-300 {
    animation-delay: 0.3s;
}

.animate-delay-500 {
    animation-delay: 0.5s;
}

/* 动画持续时间 */
.animate-fast {
    animation-duration: 0.3s;
}

.animate-slow {
    animation-duration: 1s;
}

/* 动画填充模式 */
.animate-fill-both {
    animation-fill-mode: both;
}

/* 加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, var(--border-light) 25%, var(--border-color) 50%, var(--border-light) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-sm);
}

.skeleton-text {
    height: 1rem;
    margin-bottom: var(--spacing-sm);
}

.skeleton-title {
    height: 1.5rem;
    width: 60%;
    margin-bottom: var(--spacing-md);
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* 状态指示器 */
.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: var(--spacing-xs);
}

.status-dot.online {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.2);
}

.status-dot.offline {
    background: var(--text-muted);
}

.status-dot.busy {
    background: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(217, 119, 6, 0.2);
}

/* 进度条 */
.progress-modern {
    height: 6px;
    background: var(--border-light);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-modern .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 3px;
    transition: width 0.6s ease;
    position: relative;
}

.progress-modern .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --content-bg: #0f172a;
        --card-bg: #1e293b;
        --text-color: #f1f5f9;
        --text-muted: #94a3b8;
        --border-color: #334155;
        --sidebar-bg: #1e293b;
        --sidebar-text: #94a3b8;
        --sidebar-hover: #334155;
    }
}
