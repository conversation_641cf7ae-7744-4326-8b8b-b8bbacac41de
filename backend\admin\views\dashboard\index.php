<?php
$title = '仪表盘';
$page = 'dashboard';

// 引入模拟数据生成器
require_once __DIR__ . '/../../data/mock-data.php';
$mockGenerator = new MockDataGenerator();

// 初始化变量
$userStats = null;
$domainStats = null;
$orderStats = null;
$recentOrders = [];
$recentUsers = [];
$usingMockData = false;

try {
    // 尝试获取数据库数据
    $db = getDatabase();

    // 检查表是否存在
    $tables = $db->query("SHOW TABLES LIKE 'users'")->fetchAll();

    if (empty($tables)) {
        throw new Exception("数据库表不存在，使用模拟数据");
    }

    // 获取用户统计
    $userStats = $db->query("SELECT
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_users
        FROM users")->fetch();

    // 获取域名统计
    $domainStats = $db->query("SELECT
        COUNT(*) as total_domains,
        SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_domains,
        SUM(CASE WHEN status = 'sold' THEN 1 ELSE 0 END) as sold_domains,
        SUM(CASE WHEN featured = 1 THEN 1 ELSE 0 END) as featured_domains
        FROM domains")->fetch();

    // 获取订单统计
    $orderStats = $db->query("SELECT
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_orders,
        SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue
        FROM orders")->fetch();

    // 获取最近订单
    $recentOrders = $db->query("SELECT o.*, u.username
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        ORDER BY o.created_at DESC
        LIMIT 10")->fetchAll();

    // 获取最近用户
    $recentUsers = $db->query("SELECT * FROM users
        WHERE role = 'user'
        ORDER BY created_at DESC
        LIMIT 10")->fetchAll();

} catch (Exception $e) {
    // 如果数据库查询失败，使用模拟数据
    $usingMockData = true;
    $allMockData = $mockGenerator->getAllMockData();
    $dashboardStats = $allMockData['dashboard_stats'];

    $userStats = [
        'total_users' => $dashboardStats['total_users'],
        'admin_users' => rand(3, 10),
        'active_users' => $dashboardStats['active_users'],
        'today_users' => $dashboardStats['today_users']
    ];

    $domainStats = [
        'total_domains' => $dashboardStats['total_domains'],
        'available_domains' => $dashboardStats['available_domains'],
        'sold_domains' => $dashboardStats['sold_domains'],
        'featured_domains' => rand(50, 200)
    ];

    $orderStats = [
        'total_orders' => $dashboardStats['total_orders'],
        'completed_orders' => $dashboardStats['completed_orders'],
        'pending_orders' => $dashboardStats['pending_orders'],
        'today_orders' => $dashboardStats['today_orders'],
        'total_revenue' => $dashboardStats['total_revenue']
    ];

    // 使用模拟数据
    $recentOrders = array_slice($allMockData['orders'], 0, 10);
    $recentUsers = array_slice($allMockData['users'], 0, 10);

    // 为模拟订单添加用户名
    foreach ($recentOrders as &$order) {
        $user = $allMockData['users'][$order['user_id'] - 1] ?? null;
        $order['username'] = $user ? $user['username'] : 'unknown_user';
    }
}

ob_start();
?>

<?php if ($usingMockData): ?>
<div class="alert alert-warning alert-dismissible fade show" data-aos="fade-down">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
        <div class="flex-grow-1">
            <h5 class="alert-heading mb-1">系统提示</h5>
            <p class="mb-2">当前使用模拟数据显示，数据库可能未正确初始化。</p>
            <div class="d-flex gap-2">
                <a href="database-manager.php" class="btn btn-warning btn-sm">
                    <i class="fas fa-database"></i> 初始化数据库
                </a>
                <a href="index.php?page=database-status" class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-info-circle"></i> 查看状态
                </a>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
<?php endif; ?>

<!-- 页面头部 -->
<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-tachometer-alt text-primary"></i>
                仪表盘
            </h1>
            <p class="page-subtitle">系统概览和关键指标</p>
        </div>
        <div class="page-actions">
            <div class="current-time">
                <i class="fas fa-calendar text-muted"></i>
                <span class="text-muted"><?= date('Y年m月d日 H:i') ?></span>
            </div>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i>
                打印报告
            </button>
            <button class="btn btn-outline-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i>
                刷新数据
            </button>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i>
                    导出数据
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportData('dashboard', 'pdf')">
                        <i class="fas fa-file-pdf text-danger"></i> PDF报告
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('dashboard', 'excel')">
                        <i class="fas fa-file-excel text-success"></i> Excel表格
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportData('dashboard', 'csv')">
                        <i class="fas fa-file-csv text-info"></i> CSV数据
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作栏 -->
<div class="quick-actions mb-4" data-aos="fade-up" data-aos-delay="100">
    <div class="row g-3">
        <div class="col-lg-3 col-md-6">
            <a href="index.php?page=users&action=add" class="quick-action-card">
                <div class="quick-action-icon bg-primary">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="quick-action-content">
                    <h6>添加用户</h6>
                    <p>创建新的用户账户</p>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-6">
            <a href="index.php?page=domains&action=pricing" class="quick-action-card">
                <div class="quick-action-icon bg-success">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="quick-action-content">
                    <h6>域名价格设置</h6>
                    <p>管理域名后缀价格</p>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-6">
            <a href="index.php?page=orders" class="quick-action-card">
                <div class="quick-action-icon bg-warning">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="quick-action-content">
                    <h6>查看订单</h6>
                    <p>管理所有订单信息</p>
                </div>
            </a>
        </div>
        <div class="col-lg-3 col-md-6">
            <a href="index.php?page=settings" class="quick-action-card">
                <div class="quick-action-icon bg-info">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="quick-action-content">
                    <h6>系统设置</h6>
                    <p>配置系统参数</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" data-aos="fade-up" data-aos-delay="200">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-delay-100">
            <div class="stats-icon" style="background: linear-gradient(135deg, #4f46e5, #6366f1);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number" data-stat="users"><?= number_format($userStats['total_users'] ?? 0) ?></div>
            <p class="stats-label">总用户数</p>
            <div class="stats-trend up">
                <i class="fas fa-trending-up"></i>
                今日新增: <?= $userStats['today_users'] ?? 0 ?>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-delay-200">
            <div class="stats-icon" style="background: linear-gradient(135deg, #059669, #10b981);">
                <i class="fas fa-globe"></i>
            </div>
            <div class="stats-number" data-stat="domains"><?= number_format($domainStats['total_domains'] ?? 0) ?></div>
            <p class="stats-label">域名总数</p>
            <div class="stats-trend up">
                <i class="fas fa-check-circle"></i>
                可用: <?= $domainStats['available_domains'] ?? 0 ?>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-delay-300">
            <div class="stats-icon" style="background: linear-gradient(135deg, #d97706, #f59e0b);">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-number" data-stat="orders"><?= number_format($orderStats['total_orders'] ?? 0) ?></div>
            <p class="stats-label">订单总数</p>
            <div class="stats-trend up">
                <i class="fas fa-trending-up"></i>
                今日: <?= $orderStats['today_orders'] ?? 0 ?>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-delay-500">
            <div class="stats-icon" style="background: linear-gradient(135deg, #2563eb, #3b82f6);">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stats-number" data-stat="revenue">¥<?= number_format($orderStats['total_revenue'] ?? 0, 2) ?></div>
            <p class="stats-label">总收入</p>
            <div class="stats-trend up">
                <i class="fas fa-check-circle"></i>
                已完成: <?= $orderStats['completed_orders'] ?? 0 ?>
            </div>
        </div>
    </div>
</div>

<!-- 图表和数据 -->
<div class="row">
    <!-- 订单趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-chart-line"></i>
                    订单趋势
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="ordersChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 域名状态分布 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-chart-pie"></i>
                    域名状态分布
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="domainsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row mt-4">
    <!-- 最近订单 -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">
                    <i class="fas fa-shopping-cart"></i>
                    最近订单
                </h5>
                <a href="index.php?page=orders" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recentOrders)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>用户</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($recentOrders, 0, 5) as $order): ?>
                                    <tr>
                                        <td><code><?= htmlspecialchars($order['order_number']) ?></code></td>
                                        <td><?= htmlspecialchars($order['username'] ?? '未知') ?></td>
                                        <td>¥<?= number_format($order['total_amount'], 2) ?></td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'pending' => 'warning',
                                                'completed' => 'success',
                                                'cancelled' => 'danger',
                                                'processing' => 'info'
                                            ];
                                            $statusText = [
                                                'pending' => '待处理',
                                                'completed' => '已完成',
                                                'cancelled' => '已取消',
                                                'processing' => '处理中'
                                            ];
                                            ?>
                                            <span class="badge badge-<?= $statusClass[$order['status']] ?? 'secondary' ?>">
                                                <?= $statusText[$order['status']] ?? $order['status'] ?>
                                            </span>
                                        </td>
                                        <td><?= date('m-d H:i', strtotime($order['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-shopping-cart"></i>
                        <p>暂无订单数据</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 最近用户 -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title">
                    <i class="fas fa-users"></i>
                    最近用户
                </h5>
                <a href="index.php?page=users" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recentUsers)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($recentUsers, 0, 5) as $user): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($user['username']) ?></td>
                                        <td><?= htmlspecialchars($user['email']) ?></td>
                                        <td>
                                            <span class="badge badge-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                                                <?= $user['status'] === 'active' ? '活跃' : '非活跃' ?>
                                            </span>
                                        </td>
                                        <td><?= date('m-d H:i', strtotime($user['created_at'])) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <p>暂无用户数据</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// 等待页面和Chart.js加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 检查Chart.js是否加载
    if (typeof Chart === 'undefined') {
        console.error('Chart.js 未加载');
        return;
    }

    // 订单趋势图
    const ordersCtx = document.getElementById('ordersChart');
    if (ordersCtx) {
        new Chart(ordersCtx, {
    type: 'line',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
            label: '订单数量',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

    // 域名状态分布图
    const domainsCtx = document.getElementById('domainsChart');
    if (domainsCtx) {
        new Chart(domainsCtx, {
    type: 'doughnut',
    data: {
        labels: ['可用', '已注册', '已过期'],
        datasets: [{
            data: [<?= $domainStats['available_domains'] ?? 0 ?>, <?= $domainStats['registered_domains'] ?? 0 ?>, 5],
            backgroundColor: ['#4ade80', '#667eea', '#f59e0b']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
            }
        }
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
