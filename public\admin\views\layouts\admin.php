<?php
// 确保session已启动
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}



// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 获取当前页面
$currentPage = $_GET['page'] ?? 'dashboard';

// 获取用户权限（这里可以从数据库获取）
$userPermissions = [
    'dashboard.view',
    'users.view',
    'roles.view',
    'permissions.view',
    'security.view',
    'domains.view',
    'domains.categories',
    'domains.pricing',
    'domains.providers',  // 添加域名商管理权限
    'orders.view',
    'orders.reports',
    'payments.config',
    'payments.records',
    'billing.view',
    'finance.reports',
    'notifications.view',
    'support.view',
    'system.logs',
    'settings.basic',
    'settings.email',
    'settings.security'
];

// 尝试引入菜单渲染器
$menuRenderer = null;
try {
    require_once __DIR__ . '/../../classes/MenuRenderer.php';
    $menuRenderer = new MenuRenderer($currentPage, $userPermissions);
} catch (Exception $e) {
    // 如果菜单渲染器失败，使用简单的菜单
    $menuRenderer = null;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? '管理后台' ?> - NameSilo域名销售系统</title>
    <meta name="description" content="NameSilo域名销售系统管理后台">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">


</head>
<body>
    <div class="wrapper">
        <!-- 侧边栏 -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <i class="fas fa-globe"></i>
                    <div class="brand-text">
                        <h5>NameSilo</h5>
                        <span>管理后台</span>
                    </div>
                </div>
            </div>



            <ul class="components">
                <li class="nav-section">
                    <span>主要功能</span>
                </li>
                <li class="nav-item <?= $currentPage === 'dashboard' ? 'active' : '' ?>">
                    <a href="index.php?page=dashboard" class="nav-link">
                        <div class="nav-icon"><i class="fas fa-tachometer-alt"></i></div>
                        <span class="nav-text">仪表盘</span>
                    </a>
                </li>
                <li class="nav-item <?= in_array($currentPage, ['users', 'security']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#usersSubmenu">
                        <div class="nav-icon"><i class="fas fa-users"></i></div>
                        <span class="nav-text">用户管理</span>
                        <i class="fas fa-chevron-down nav-arrow"></i>
                    </a>
                    <div class="collapse <?= in_array($currentPage, ['users', 'security']) ? 'show' : '' ?>" id="usersSubmenu">
                        <ul class="nav-submenu">
                            <li><a href="index.php?page=users" class="<?= $currentPage === 'users' ? 'active' : '' ?>">用户列表</a></li>
                            <li><a href="index.php?page=security" class="<?= $currentPage === 'security' ? 'active' : '' ?>">安全管理</a></li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item <?= in_array($currentPage, ['domains', 'domain-categories', 'domain-pricing', 'domain-providers']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#domainsSubmenu">
                        <div class="nav-icon"><i class="fas fa-globe"></i></div>
                        <span class="nav-text">域名管理</span>
                        <i class="fas fa-chevron-down nav-arrow"></i>
                    </a>
                    <div class="collapse <?= in_array($currentPage, ['domains', 'domain-categories', 'domain-pricing', 'domain-providers']) ? 'show' : '' ?>" id="domainsSubmenu">
                        <ul class="nav-submenu">
                            <li><a href="index.php?page=domains" class="<?= $currentPage === 'domains' ? 'active' : '' ?>">域名列表</a></li>
                            <li><a href="index.php?page=domain-categories" class="<?= $currentPage === 'domain-categories' ? 'active' : '' ?>">域名分类</a></li>
                            <li><a href="index.php?page=domain-pricing" class="<?= $currentPage === 'domain-pricing' ? 'active' : '' ?>">价格设置</a></li>
                            <li><a href="domain-providers.php" class="<?= basename($_SERVER['PHP_SELF']) === 'domain-providers.php' ? 'active' : '' ?>">
                                <i class="fas fa-building me-2"></i>域名商管理
                            </a></li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item <?= in_array($currentPage, ['orders', 'order-reports']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#ordersSubmenu">
                        <div class="nav-icon"><i class="fas fa-shopping-cart"></i></div>
                        <span class="nav-text">订单管理</span>
                        <i class="fas fa-chevron-down nav-arrow"></i>
                    </a>
                    <div class="collapse <?= in_array($currentPage, ['orders', 'order-reports']) ? 'show' : '' ?>" id="ordersSubmenu">
                        <ul class="nav-submenu">
                            <li><a href="index.php?page=orders" class="<?= $currentPage === 'orders' ? 'active' : '' ?>">订单列表</a></li>
                            <li><a href="index.php?page=order-reports" class="<?= $currentPage === 'order-reports' ? 'active' : '' ?>">订单报表</a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-section">
                    <span>系统管理</span>
                </li>
                <li class="nav-item <?= in_array($currentPage, ['payments', 'payment-records', 'billing']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#paymentsSubmenu">
                        <div class="nav-icon"><i class="fas fa-credit-card"></i></div>
                        <span class="nav-text">财务管理</span>
                        <i class="fas fa-chevron-down nav-arrow"></i>
                    </a>
                    <div class="collapse <?= in_array($currentPage, ['payments', 'payment-records', 'billing']) ? 'show' : '' ?>" id="paymentsSubmenu">
                        <ul class="nav-submenu">
                            <li><a href="index.php?page=payments" class="<?= $currentPage === 'payments' ? 'active' : '' ?>">支付配置</a></li>
                            <li><a href="index.php?page=payment-records" class="<?= $currentPage === 'payment-records' ? 'active' : '' ?>">支付记录</a></li>
                            <li><a href="index.php?page=billing" class="<?= $currentPage === 'billing' ? 'active' : '' ?>">账单管理</a></li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item <?= $currentPage === 'notifications' ? 'active' : '' ?>">
                    <a href="index.php?page=notifications" class="nav-link">
                        <div class="nav-icon"><i class="fas fa-bell"></i></div>
                        <span class="nav-text">通知管理</span>
                    </a>
                </li>
                <li class="nav-item has-submenu <?= in_array($currentPage, ['email-config', 'email-templates', 'email-send', 'email-logs']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link">
                        <div class="nav-icon"><i class="fas fa-envelope"></i></div>
                        <span class="nav-text">邮件管理</span>
                        <div class="nav-arrow"><i class="fas fa-chevron-right"></i></div>
                    </a>
                    <div class="nav-submenu-wrapper">
                        <ul class="nav-submenu">
                            <li><a href="index.php?page=email-config" class="<?= $currentPage === 'email-config' ? 'active' : '' ?>">邮件配置</a></li>
                            <li><a href="index.php?page=email-templates" class="<?= $currentPage === 'email-templates' ? 'active' : '' ?>">邮件模板</a></li>
                            <li><a href="index.php?page=email-send" class="<?= $currentPage === 'email-send' ? 'active' : '' ?>">邮件发送</a></li>
                            <li><a href="index.php?page=email-logs" class="<?= $currentPage === 'email-logs' ? 'active' : '' ?>">邮件日志</a></li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item <?= $currentPage === 'support' ? 'active' : '' ?>">
                    <a href="index.php?page=support" class="nav-link">
                        <div class="nav-icon"><i class="fas fa-life-ring"></i></div>
                        <span class="nav-text">支持中心</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'logs' ? 'active' : '' ?>">
                    <a href="index.php?page=logs" class="nav-link">
                        <div class="nav-icon"><i class="fas fa-file-alt"></i></div>
                        <span class="nav-text">系统日志</span>
                    </a>
                </li>
                <li class="nav-item <?= $currentPage === 'settings' ? 'active' : '' ?>">
                    <a href="index.php?page=settings" class="nav-link">
                        <div class="nav-icon"><i class="fas fa-cog"></i></div>
                        <span class="nav-text">系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 主内容区域 -->
        <div id="content" class="content">
            <!-- 顶部导航栏 -->
            <nav class="top-navbar">
                <div class="navbar-content">
                    <div class="navbar-left">
                        <button id="sidebarCollapse" class="sidebar-toggle" type="button">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div class="breadcrumb-container">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="index.php?page=dashboard">
                                            <i class="fas fa-home"></i>
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active"><?= $title ?? '管理后台' ?></li>
                                </ol>
                            </nav>
                        </div>
                    </div>

                    <div class="navbar-right">
                        <!-- 快速搜索 -->
                        <div class="search-container">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="搜索用户、域名、订单..." id="quickSearch">
                            </div>
                        </div>

                        <!-- 通知 -->
                        <div class="notification-dropdown">
                            <button class="notification-btn" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">3</span>
                            </button>
                            <div class="dropdown-menu notification-menu">
                                <div class="notification-header">
                                    <h6>通知</h6>
                                    <span class="mark-read">全部已读</span>
                                </div>
                                <div class="notification-list">
                                    <div class="notification-item">
                                        <div class="notification-icon">
                                            <i class="fas fa-user-plus text-success"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p>新用户注册</p>
                                            <span>5分钟前</span>
                                        </div>
                                    </div>
                                    <div class="notification-item">
                                        <div class="notification-icon">
                                            <i class="fas fa-shopping-cart text-primary"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p>新订单创建</p>
                                            <span>10分钟前</span>
                                        </div>
                                    </div>
                                    <div class="notification-item">
                                        <div class="notification-icon">
                                            <i class="fas fa-credit-card text-warning"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p>支付完成</p>
                                            <span>15分钟前</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="notification-footer">
                                    <a href="index.php?page=logs">查看全部通知</a>
                                </div>
                            </div>
                        </div>

                        <!-- 用户菜单 -->
                        <div class="user-dropdown">
                            <button class="user-btn" data-bs-toggle="dropdown">
                                <div class="user-avatar">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                <div class="user-info">
                                    <span class="user-name"><?= $_SESSION['admin_name'] ?? '管理员' ?></span>
                                    <span class="user-role">超级管理员</span>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu user-menu">
                                <div class="user-menu-header">
                                    <div class="user-avatar-large">
                                        <i class="fas fa-user-circle"></i>
                                    </div>
                                    <div class="user-details">
                                        <h6><?= $_SESSION['admin_name'] ?? '管理员' ?></h6>
                                        <p><?= $_SESSION['admin_username'] ?? 'admin' ?></p>
                                    </div>
                                </div>
                                <div class="user-menu-body">
                                    <a href="index.php?page=profile" class="dropdown-item">
                                        <i class="fas fa-user"></i>
                                        个人资料
                                    </a>
                                    <a href="index.php?page=settings" class="dropdown-item">
                                        <i class="fas fa-cog"></i>
                                        系统设置
                                    </a>
                                    <a href="../" class="dropdown-item" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                        访问前台
                                    </a>
                                </div>
                                <div class="user-menu-footer">
                                    <a href="index.php?page=logout" class="dropdown-item logout">
                                        <i class="fas fa-sign-out-alt"></i>
                                        退出登录
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- 页面内容 -->
            <div class="main-content">
                <?php if (isset($contentFile) && file_exists($contentFile)): ?>
                    <?php include $contentFile; ?>
                <?php elseif (isset($content)): ?>
                    <?= $content ?>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        页面内容未找到
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script src="assets/js/admin.js"></script>

    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 600,
            easing: 'ease-in-out',
            once: true
        });

        // 快速搜索功能
        document.addEventListener('DOMContentLoaded', function() {
            const quickSearch = document.getElementById('quickSearch');
            if (quickSearch) {
                quickSearch.addEventListener('input', function(e) {
                    const query = e.target.value.trim();
                    if (query.length > 2) {
                        console.log('搜索:', query);
                    }
                });
            }

            // 通知标记为已读
            const markRead = document.querySelector('.mark-read');
            if (markRead) {
                markRead.addEventListener('click', function() {
                    const badge = document.querySelector('.notification-badge');
                    if (badge) {
                        badge.style.display = 'none';
                    }
                });
            }
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });
    </script>


</body>
</html>
