<?php
/**
 * 账单和充值页面
 * Billing and Recharge Page
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '账单管理';

// 处理支付结果
$paymentStatus = $_GET['status'] ?? '';
$orderNumber = $_GET['order'] ?? '';
$paymentAmount = $_GET['amount'] ?? '';
$paymentError = $_GET['error'] ?? '';

// 获取用户信息
try {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $userInfo = $stmt->fetch();
    
    if (!$userInfo) {
        header('Location: ../login.php');
        exit;
    }

    // 获取交易记录
    $stmt = $db->prepare("SELECT * FROM billing_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 20");
    $stmt->execute([$userId]);
    $transactions = $stmt->fetchAll();

    // 获取统计数据
    $stmt = $db->prepare("SELECT
        SUM(CASE WHEN type = 'recharge' AND status = 'completed' THEN amount ELSE 0 END) as total_recharge,
        SUM(CASE WHEN type = 'payment' AND status = 'completed' THEN amount ELSE 0 END) as total_spent,
        SUM(CASE WHEN type = 'recharge' AND status = 'completed' AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN amount ELSE 0 END) as monthly_recharge,
        SUM(CASE WHEN type = 'payment' AND status = 'completed' AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN amount ELSE 0 END) as monthly_spent
        FROM billing_transactions WHERE user_id = ?");
    $stmt->execute([$userId]);
    $stats = $stmt->fetch();

} catch (Exception $e) {
    $error = '获取用户信息失败：' . $e->getMessage();
    $transactions = [];
    $stats = ['total_recharge' => 0, 'total_spent' => 0, 'monthly_recharge' => 0, 'monthly_spent' => 0];
}

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">账单管理</h1>
            <p class="page-subtitle">管理您的余额、充值和交易记录</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="showRechargeModal()">
                <i class="fas fa-plus me-2"></i>
                充值
            </button>
        </div>
    </div>
</div>

<!-- 支付结果提示 -->
<?php if ($paymentStatus === 'success'): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-up">
    <i class="fas fa-check-circle me-2"></i>
    <strong>充值成功！</strong>
    <?php if ($orderNumber && $paymentAmount): ?>
        订单号：<?= htmlspecialchars($orderNumber) ?>，充值金额：¥<?= htmlspecialchars($paymentAmount) ?>
    <?php endif; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php elseif ($paymentStatus === 'failed'): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-up">
    <i class="fas fa-exclamation-circle me-2"></i>
    <strong>充值失败！</strong>
    <?php if ($paymentError): ?>
        错误信息：<?= htmlspecialchars($paymentError) ?>
    <?php endif; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php elseif ($paymentStatus === 'error'): ?>
<div class="alert alert-warning alert-dismissible fade show" role="alert" data-aos="fade-up">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>系统错误！</strong>
    <?php if ($paymentError): ?>
        <?= htmlspecialchars($paymentError) ?>
    <?php endif; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 余额和统计 -->
<div class="row g-4 mb-4">
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card text-center">
            <div class="stats-icon bg-success bg-opacity-10 text-success mx-auto">
                <i class="fas fa-wallet"></i>
            </div>
            <h4 class="h6 mb-1">当前余额</h4>
            <p class="fw-bold mb-0 text-success" id="currentBalance">¥<?= number_format($userInfo['balance'] ?? 0, 2) ?></p>
        </div>
    </div>
    
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="200">
        <div class="stats-card text-center">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary mx-auto">
                <i class="fas fa-arrow-up"></i>
            </div>
            <h4 class="h6 mb-1">总充值</h4>
            <p class="fw-bold mb-0" id="totalRecharge">¥<?= number_format($stats['total_recharge'] ?? 0, 2) ?></p>
        </div>
    </div>
    
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="300">
        <div class="stats-card text-center">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning mx-auto">
                <i class="fas fa-arrow-down"></i>
            </div>
            <h4 class="h6 mb-1">总消费</h4>
            <p class="fw-bold mb-0" id="totalSpent">¥<?= number_format($stats['total_spent'] ?? 0, 2) ?></p>
        </div>
    </div>
    
    <div class="col-md-3" data-aos="fade-up" data-aos-delay="400">
        <div class="stats-card text-center">
            <div class="stats-icon bg-info bg-opacity-10 text-info mx-auto">
                <i class="fas fa-calendar-month"></i>
            </div>
            <h4 class="h6 mb-1">本月消费</h4>
            <p class="fw-bold mb-0" id="monthlySpent">¥<?= number_format($stats['monthly_spent'] ?? 0, 2) ?></p>
        </div>
    </div>
</div>

<!-- 交易记录 -->
<div class="row">
    <div class="col-12" data-aos="fade-up" data-aos-delay="500">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    交易记录
                </h5>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-danger dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-trash me-1"></i>
                            清空记录
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showClearModal('pending')">
                                <i class="fas fa-clock me-2 text-warning"></i>
                                清空未支付记录
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showClearModal('all')">
                                <i class="fas fa-trash me-2 text-danger"></i>
                                清空全部记录
                            </a></li>
                        </ul>
                    </div>
                    <button class="btn btn-outline-secondary" onclick="refreshTransactions()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="transactionsContainer">
                    <?php if (empty($transactions)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-4x text-muted mb-4"></i>
                            <h4 class="text-muted">暂无交易记录</h4>
                            <p class="text-muted mb-4">您还没有任何交易记录</p>
                            <button class="btn btn-primary" onclick="showRechargeModal()">
                                <i class="fas fa-plus me-2"></i>
                                立即充值
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>交易类型</th>
                                        <th>订单号</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>回调来源</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transactions as $transaction): ?>
                                        <tr>
                                            <td>
                                                <?php
                                                $typeIcons = [
                                                    'recharge' => '<i class="fas fa-arrow-up text-success me-2"></i>充值',
                                                    'payment' => '<i class="fas fa-arrow-down text-warning me-2"></i>消费',
                                                    'refund' => '<i class="fas fa-undo text-info me-2"></i>退款'
                                                ];
                                                echo $typeIcons[$transaction['type']] ?? $transaction['type'];
                                                ?>
                                            </td>
                                            <td>
                                                <code class="small"><?= htmlspecialchars($transaction['order_number']) ?></code>
                                            </td>
                                            <td>
                                                <span class="fw-bold <?= $transaction['type'] === 'recharge' ? 'text-success' : 'text-warning' ?>">
                                                    <?= $transaction['type'] === 'recharge' ? '+' : '-' ?>¥<?= number_format($transaction['amount'] ?? 0, 2) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusBadges = [
                                                    'pending' => '<span class="badge bg-warning">待处理</span>',
                                                    'completed' => '<span class="badge bg-success">已完成</span>',
                                                    'failed' => '<span class="badge bg-danger">失败</span>',
                                                    'cancelled' => '<span class="badge bg-secondary">已取消</span>'
                                                ];
                                                echo $statusBadges[$transaction['status']] ?? $transaction['status'];
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $sources = [
                                                    'async_notify' => '<span class="badge bg-primary">异步回调</span>',
                                                    'return_callback' => '<span class="badge bg-info">返回回调</span>',
                                                    'manual' => '<span class="badge bg-secondary">手动处理</span>',
                                                    'test' => '<span class="badge bg-warning">测试</span>'
                                                ];
                                                echo $sources[$transaction['callback_source']] ?? '<span class="badge bg-light text-dark">未知</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><?= date('Y-m-d H:i:s', strtotime($transaction['created_at'])) ?></div>
                                                    <?php if ($transaction['completed_at']): ?>
                                                        <div class="text-muted">完成: <?= date('H:i:s', strtotime($transaction['completed_at'])) ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 充值模态框 -->
<div class="modal fade" id="rechargeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    账户充值
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 充值金额选择 -->
                <div class="mb-4">
                    <label class="form-label">选择充值金额</label>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="amount-option" data-amount="50">
                                <div class="amount-value">¥50</div>
                                <div class="amount-label">基础充值</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="amount-option" data-amount="100">
                                <div class="amount-value">¥100</div>
                                <div class="amount-label">推荐金额</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="amount-option" data-amount="200">
                                <div class="amount-value">¥200</div>
                                <div class="amount-label">优惠充值</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="amount-option" data-amount="500">
                                <div class="amount-value">¥500</div>
                                <div class="amount-label">超值充值</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="amount-option" data-amount="1000">
                                <div class="amount-value">¥1000</div>
                                <div class="amount-label">企业充值</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="amount-option custom" data-amount="custom">
                                <div class="amount-value">自定义</div>
                                <div class="amount-label">其他金额</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 自定义金额输入 -->
                <div class="mb-4" id="customAmountInput" style="display: none;">
                    <label class="form-label">自定义充值金额</label>
                    <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input type="number" class="form-control" id="customAmount" placeholder="请输入充值金额" min="0.01" step="0.01">
                    </div>
                    <small class="text-muted">请输入大于0的金额，支持小数点后两位</small>
                </div>

                <!-- 支付方式选择 -->
                <div class="mb-4">
                    <label class="form-label">选择支付方式</label>
                    <div id="paymentMethods">
                        <div class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <span class="ms-2">正在加载支付方式...</span>
                        </div>
                    </div>
                </div>
                
                <!-- 充值说明 -->
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>
                        充值说明
                    </h6>
                    <ul class="mb-0 small">
                        <li>支持任意金额充值，无最低和最高限制</li>
                        <li>充值金额将立即到账，可用于购买域名等服务</li>
                        <li>支持支付宝、微信支付等多种支付方式</li>
                        <li>充值过程中如遇问题，请联系客服</li>
                        <li>充值记录可在交易记录中查看</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="proceedToPayment()" id="rechargeBtn" disabled>
                    <i class="fas fa-credit-card me-2"></i>
                    立即充值
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 清空交易记录确认模态框 -->
<div class="modal fade" id="clearTransactionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    确认清空交易记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>警告：</strong>此操作不可撤销！
                </div>
                <p id="clearModalMessage">您确定要清空交易记录吗？</p>
                <div class="bg-light p-3 rounded">
                    <h6 class="mb-2">将要清空的记录：</h6>
                    <div id="clearModalStats">
                        <!-- 统计信息将通过JavaScript填充 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    取消
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmClearTransactions()" id="confirmClearBtn">
                    <i class="fas fa-trash me-2"></i>
                    确认清空
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedAmount = 0;
let selectedPaymentMethod = '';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 绑定金额选择事件
    document.querySelectorAll('.amount-option').forEach(option => {
        option.addEventListener('click', function() {
            selectAmount(this);
        });
    });
    
    // 监听自定义金额输入
    document.getElementById('customAmount').addEventListener('input', function() {
        // 只有在自定义选项被选中时才更新
        const customOption = document.querySelector('.amount-option[data-amount="custom"]');
        if (customOption && customOption.classList.contains('selected')) {
            selectedAmount = parseFloat(this.value) || 0;
            updateRechargeButton();
        }
    });

    // 监听自定义金额输入框的键盘事件
    document.getElementById('customAmount').addEventListener('keyup', function() {
        const customOption = document.querySelector('.amount-option[data-amount="custom"]');
        if (customOption && customOption.classList.contains('selected')) {
            selectedAmount = parseFloat(this.value) || 0;
            updateRechargeButton();
        }
    });
});

// 显示充值模态框
function showRechargeModal() {
    // 重置状态
    selectedAmount = 0;
    selectedPaymentMethod = '';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('rechargeModal'));
    modal.show();

    // 加载支付方式
    loadPaymentMethods();
}

// 加载支付方式
function loadPaymentMethods() {
    console.log('开始加载支付方式...');

    fetch('../api/payment.php?action=get_methods')
        .then(response => {
            console.log('支付方式API响应状态:', response.status);
            console.log('支付方式API响应头:', response.headers.get('content-type'));

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.text().then(text => {
                console.log('支付方式API原始响应:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    throw new Error(`无效的JSON响应: ${text.substring(0, 100)}...`);
                }
            });
        })
        .then(data => {
            console.log('支付方式API响应数据:', data);
            const container = document.getElementById('paymentMethods');

            if (data.success && data.data && data.data.length > 0) {
                // 只显示启用的支付方式
                const enabledMethods = data.data.filter(method => method.enabled == 1 || method.enabled === true);
                console.log('启用的支付方式:', enabledMethods);

                if (enabledMethods.length > 0) {
                    let html = '<div class="row g-3">';

                    enabledMethods.forEach(method => {
                        const icon = getPaymentIcon(method.payment_method);

                        html += `
                            <div class="col-md-6">
                                <div class="payment-method-option" data-method="${method.payment_method}" onclick="selectPaymentMethod('${method.payment_method}')">
                                    <div class="d-flex align-items-center">
                                        <div class="payment-icon me-3">
                                            ${icon}
                                        </div>
                                        <div>
                                            <div class="payment-name">${method.name}</div>
                                            <div class="payment-desc text-muted small">
                                                ${getPaymentDescription(method.payment_method)}
                                            </div>
                                        </div>
                                        <div class="ms-auto">
                                            <i class="fas fa-check-circle payment-check" style="display: none;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                    container.innerHTML = html;

                    // 默认选择第一个启用的支付方式
                    selectPaymentMethod(enabledMethods[0].payment_method);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            暂无可用的支付方式，请联系管理员启用支付方式
                        </div>
                    `;
                }
            } else {
                container.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        暂无可用的支付方式，请联系管理员配置支付方式
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载支付方式失败:', error);
            const container = document.getElementById('paymentMethods');
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        支付方式加载失败: ${error.message}<br>
                        <small>请检查网络连接或联系管理员</small>
                    </div>
                `;
            }
        });
}

// 选择充值金额
function selectAmount(element) {
    // 移除其他选中状态
    document.querySelectorAll('.amount-option').forEach(option => {
        option.classList.remove('selected');
    });

    // 添加选中状态
    element.classList.add('selected');

    const amount = element.dataset.amount;
    if (amount === 'custom') {
        document.getElementById('customAmountInput').style.display = 'block';
        // 获取当前自定义输入框的值
        const customInput = document.getElementById('customAmount');
        selectedAmount = parseFloat(customInput.value) || 0;
        // 聚焦到输入框
        setTimeout(() => customInput.focus(), 100);
    } else {
        document.getElementById('customAmountInput').style.display = 'none';
        selectedAmount = parseFloat(amount);
    }

    updateRechargeButton();
}

// 选择支付方式
function selectPaymentMethod(method) {
    // 移除其他选中状态
    document.querySelectorAll('.payment-method-option').forEach(option => {
        option.classList.remove('selected');
        const checkIcon = option.querySelector('.payment-check');
        if (checkIcon) {
            checkIcon.style.display = 'none';
        }
    });

    // 添加选中状态
    const selectedOption = document.querySelector(`[data-method="${method}"]`);
    if (selectedOption) {
        selectedOption.classList.add('selected');
        const checkIcon = selectedOption.querySelector('.payment-check');
        if (checkIcon) {
            checkIcon.style.display = 'block';
        }
        selectedPaymentMethod = method;
        updateRechargeButton();
    }
}

// 获取支付方式图标
function getPaymentIcon(method) {
    const icons = {
        'alipay': '<i class="fab fa-alipay text-primary" style="font-size: 24px;"></i>',
        'wechat': '<i class="fab fa-weixin text-success" style="font-size: 24px;"></i>',
        'epay': '<i class="fas fa-credit-card text-info" style="font-size: 24px;"></i>',
        'paypal': '<i class="fab fa-paypal text-primary" style="font-size: 24px;"></i>',
        'stripe': '<i class="fab fa-stripe text-primary" style="font-size: 24px;"></i>'
    };
    return icons[method] || '<i class="fas fa-credit-card text-secondary" style="font-size: 24px;"></i>';
}

// 获取支付方式描述
function getPaymentDescription(method) {
    const descriptions = {
        'alipay': '支持支付宝扫码支付',
        'wechat': '支持微信扫码支付',
        'epay': '第三方聚合支付',
        'paypal': '国际PayPal支付',
        'stripe': '国际信用卡支付'
    };
    return descriptions[method] || '在线支付';
}

// 更新充值按钮状态
function updateRechargeButton() {
    const btn = document.getElementById('rechargeBtn');
    const canRecharge = selectedAmount > 0 && selectedPaymentMethod;

    btn.disabled = !canRecharge;

    if (canRecharge) {
        const methodName = getPaymentMethodName(selectedPaymentMethod);
        btn.innerHTML = `<i class="fas fa-credit-card me-2"></i>使用${methodName}充值 ¥${selectedAmount.toFixed(2)}`;
    } else if (selectedAmount > 0) {
        btn.innerHTML = `<i class="fas fa-credit-card me-2"></i>请选择支付方式`;
    } else {
        btn.innerHTML = `<i class="fas fa-credit-card me-2"></i>立即充值`;
    }
}

// 获取支付方式名称
function getPaymentMethodName(method) {
    const names = {
        'alipay': '支付宝',
        'wechat': '微信支付',
        'epay': '易支付',
        'paypal': 'PayPal',
        'stripe': 'Stripe'
    };
    return names[method] || '在线支付';
}

// 进行支付
function proceedToPayment() {
    if (selectedAmount <= 0) {
        showAlert('warning', '请输入有效的充值金额');
        return;
    }

    if (!selectedPaymentMethod) {
        showAlert('warning', '请选择支付方式');
        return;
    }

    // 禁用按钮，防止重复提交
    const btn = document.getElementById('rechargeBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在创建订单...';

    // 创建充值订单
    fetch('../api/billing.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'recharge',
            amount: selectedAmount,
            method: selectedPaymentMethod
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('充值订单创建响应:', data); // 调试信息

        if (data.success) {
            // 关闭充值模态框
            bootstrap.Modal.getInstance(document.getElementById('rechargeModal')).hide();

            // 显示支付页面
            showPaymentModal(data.data);
        } else {
            showAlert('danger', '创建订单失败: ' + data.message);
            resetRechargeButton();
        }
    })
    .catch(error => {
        console.error('创建订单失败:', error);
        showAlert('danger', '网络错误，请重试');
        resetRechargeButton();
    });
}

// 重置充值按钮
function resetRechargeButton() {
    const btn = document.getElementById('rechargeBtn');
    btn.disabled = false;
    btn.innerHTML = `<i class="fas fa-credit-card me-2"></i>充值 ¥${selectedAmount.toFixed(2)}`;
}

// 显示支付模态框
function showPaymentModal(orderData) {
    console.log('显示支付模态框，订单数据:', orderData); // 调试信息

    // 创建支付模态框
    const paymentModal = document.createElement('div');
    paymentModal.className = 'modal fade';
    paymentModal.id = 'paymentModal';
    paymentModal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        ${getPaymentIcon(orderData.method)}
                        <span class="ms-2">${getPaymentMethodName(orderData.method)}支付</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-4">
                        <h4>充值金额: ¥${orderData.amount}</h4>
                        <p class="text-muted">订单号: ${orderData.order_number}</p>
                    </div>

                    <div id="paymentContent">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">正在生成支付信息...</span>
                        </div>
                        <p class="mt-3">正在跳转到支付宝...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消支付</button>
                    <button type="button" class="btn btn-primary" onclick="retryPayment('${orderData.order_number}')" id="retryBtn" style="display: none;">
                        <i class="fas fa-redo me-2"></i>重新支付
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(paymentModal);
    const modal = new bootstrap.Modal(paymentModal);
    modal.show();

    // 创建支付
    createPayment(orderData);

    // 模态框关闭时清理
    paymentModal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(paymentModal);
    });
}

// 创建支付
function createPayment(orderData) {
    // 直接使用充值订单返回的支付信息
    const paymentInfo = orderData.payment_info;
    const paymentContent = document.getElementById('paymentContent');

    if (!paymentInfo) {
        paymentContent.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                支付信息获取失败
            </div>
            <button class="btn btn-primary" onclick="retryPayment('${orderData.order_number}')">
                <i class="fas fa-redo me-2"></i>重新尝试
            </button>
        `;
        return;
    }

    if (paymentInfo.type === 'redirect' && paymentInfo.payment_url) {
        // 跳转类型支付
        window.open(paymentInfo.payment_url, '_blank');

        paymentContent.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                支付页面已在新窗口打开
            </div>
            <p>${paymentInfo.instructions || '请在新窗口完成支付，支付完成后点击下方按钮确认'}</p>
            <button class="btn btn-success" onclick="checkPaymentStatus('${orderData.order_number}')">
                <i class="fas fa-check me-2"></i>我已完成支付
            </button>
        `;
    } else if (paymentInfo.type === 'qrcode' && paymentInfo.qr_code) {
        // 二维码类型支付
        paymentContent.innerHTML = `
            <div class="mb-3">
                <img src="${paymentInfo.qr_code}" alt="支付二维码" class="img-fluid" style="max-width: 200px;">
            </div>
            <p>${paymentInfo.instructions || '请扫描二维码完成支付'}</p>
            <button class="btn btn-success" onclick="checkPaymentStatus('${orderData.order_number}')">
                <i class="fas fa-check me-2"></i>我已完成支付
            </button>
        `;
    } else if (paymentInfo.type === 'bank_info') {
        // 银行转账类型支付
        paymentContent.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <h6>银行转账信息</h6>
                    <p><strong>银行名称:</strong> ${paymentInfo.bank_name}</p>
                    <p><strong>账户名称:</strong> ${paymentInfo.account_name}</p>
                    <p><strong>账户号码:</strong> ${paymentInfo.account_number}</p>
                    <p class="text-muted small">${paymentInfo.instructions}</p>
                </div>
            </div>
            <button class="btn btn-success mt-3" onclick="checkPaymentStatus('${orderData.order_number}')">
                <i class="fas fa-check me-2"></i>我已完成转账
            </button>
        `;
    } else {
        paymentContent.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                不支持的支付方式
            </div>
            <button class="btn btn-primary" onclick="retryPayment('${orderData.order_number}')">
                <i class="fas fa-redo me-2"></i>重新尝试
            </button>
        `;
    }

    document.getElementById('retryBtn').style.display = 'inline-block';
}

// 重新支付
function retryPayment(orderNumber) {
    // 重新创建支付
    location.reload();
}

// 检查支付状态
function checkPaymentStatus(orderNumber) {
    // 显示检查状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>检查支付状态...';

    fetch(`../api/billing.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'confirm_payment',
            order_number: orderNumber
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('支付状态检查响应:', data);

        if (data.success) {
            showAlert('success', '支付成功！余额已到账');
            // 关闭支付模态框
            const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
            if (paymentModal) {
                paymentModal.hide();
            }
            // 刷新页面余额
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('warning', data.message || '支付尚未完成，请稍后再试');
            // 恢复按钮
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('检查支付状态失败:', error);
        showAlert('danger', '检查支付状态失败，请重试');
        // 恢复按钮
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
}

// 刷新交易记录
function refreshTransactions() {
    // 刷新页面以获取最新数据
    window.location.reload();
}

// 显示清空交易记录模态框
function showClearModal(type) {
    const modal = new bootstrap.Modal(document.getElementById('clearTransactionsModal'));
    const messageEl = document.getElementById('clearModalMessage');
    const statsEl = document.getElementById('clearModalStats');
    const confirmBtn = document.getElementById('confirmClearBtn');

    // 设置清空类型
    confirmBtn.setAttribute('data-clear-type', type);

    if (type === 'pending') {
        messageEl.textContent = '您确定要清空所有未支付的交易记录吗？';
        confirmBtn.innerHTML = '<i class="fas fa-clock me-2"></i>清空未支付记录';
        confirmBtn.className = 'btn btn-warning';

        // 获取未支付记录统计
        fetch('../api/billing.php?action=stats&type=pending')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statsEl.innerHTML = `
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">未支付记录数：</small>
                                <div class="fw-bold">${data.data.count} 条</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">涉及金额：</small>
                                <div class="fw-bold text-warning">¥${data.data.amount}</div>
                            </div>
                        </div>
                    `;
                }
            })
            .catch(() => {
                statsEl.innerHTML = '<small class="text-muted">无法获取统计信息</small>';
            });

    } else if (type === 'all') {
        messageEl.textContent = '您确定要清空所有交易记录吗？这将删除所有充值、消费和退款记录！';
        confirmBtn.innerHTML = '<i class="fas fa-trash me-2"></i>清空全部记录';
        confirmBtn.className = 'btn btn-danger';

        // 获取全部记录统计
        fetch('../api/billing.php?action=stats&type=all')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statsEl.innerHTML = `
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">总记录数：</small>
                                <div class="fw-bold">${data.data.count} 条</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">总金额：</small>
                                <div class="fw-bold text-danger">¥${data.data.amount}</div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-4">
                                <small class="text-muted">充值：</small>
                                <div class="small text-success">${data.data.recharge_count} 条</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">消费：</small>
                                <div class="small text-warning">${data.data.payment_count} 条</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">退款：</small>
                                <div class="small text-info">${data.data.refund_count} 条</div>
                            </div>
                        </div>
                    `;
                }
            })
            .catch(() => {
                statsEl.innerHTML = '<small class="text-muted">无法获取统计信息</small>';
            });
    }

    modal.show();
}

// 确认清空交易记录
function confirmClearTransactions() {
    const confirmBtn = document.getElementById('confirmClearBtn');
    const clearType = confirmBtn.getAttribute('data-clear-type');
    const originalText = confirmBtn.innerHTML;

    // 显示加载状态
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>清空中...';

    // 发送清空请求
    fetch('../api/billing.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'clear_transactions',
            type: clearType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('clearTransactionsModal')).hide();

            // 显示成功消息
            showAlert('success', data.message || '交易记录清空成功');

            // 延迟刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message || '清空失败，请重试');
            // 恢复按钮
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('清空交易记录失败:', error);
        showAlert('danger', '清空失败，请重试');
        // 恢复按钮
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;
    });
}

// 显示提示消息
function showAlert(type, message) {
    let alertClass, icon;

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'check-circle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'exclamation-triangle';
            break;
        case 'danger':
        default:
            alertClass = 'alert-danger';
            icon = 'exclamation-circle';
            break;
    }

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show">
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.content-area');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 自动隐藏成功和警告消息
    if (type === 'success' || type === 'warning') {
        setTimeout(() => {
            const alert = container.querySelector(`.${alertClass}`);
            if (alert) {
                bootstrap.Alert.getInstance(alert)?.close();
            }
        }, 3000);
    }
}
</script>

<style>
.stats-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    transition: var(--transition);
}

.stats-card:hover {
    box-shadow: var(--shadow-md);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}

.amount-option {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.amount-option:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.amount-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.payment-method-option {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.payment-method-option:hover:not(.disabled) {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.payment-method-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(108, 117, 255, 0.1);
}

.payment-method-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #f8f9fa;
}

.payment-check {
    color: var(--success-color);
    font-size: 18px;
}

.payment-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.amount-value {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 4px;
}

.amount-label {
    font-size: 12px;
    opacity: 0.8;
}
</style>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
