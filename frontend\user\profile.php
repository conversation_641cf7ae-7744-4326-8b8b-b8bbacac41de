<?php
/**
 * 个人信息管理页面
 * User Profile Management
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '个人信息';
$message = '';
$error = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDatabase();
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_profile':
                $firstName = trim($_POST['first_name'] ?? '');
                $lastName = trim($_POST['last_name'] ?? '');
                $phone = trim($_POST['phone'] ?? '');
                $address = trim($_POST['address'] ?? '');
                $city = trim($_POST['city'] ?? '');
                $country = trim($_POST['country'] ?? '');
                $zip_code = trim($_POST['postal_code'] ?? '');

                if (empty($firstName) || empty($lastName)) {
                    throw new Exception('姓名不能为空');
                }

                $stmt = $db->prepare("UPDATE users SET first_name = ?, last_name = ?, phone = ?, address = ?, city = ?, country = ?, zip_code = ? WHERE id = ?");
                $stmt->execute([$firstName, $lastName, $phone, $address, $city, $country, $zip_code, $userId]);
                
                $message = '个人信息更新成功！';
                $_SESSION['user_name'] = trim($firstName . ' ' . $lastName);
                break;
                
            case 'change_password':
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    throw new Exception('所有密码字段都不能为空');
                }
                
                if ($newPassword !== $confirmPassword) {
                    throw new Exception('新密码和确认密码不一致');
                }
                
                if (strlen($newPassword) < 8) {
                    throw new Exception('新密码长度至少为8个字符');
                }
                
                // 验证当前密码
                $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                
                if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
                    throw new Exception('当前密码不正确');
                }
                
                // 更新密码
                $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
                $stmt->execute([$newPasswordHash, $userId]);
                
                $message = '密码修改成功！';
                break;

            case 'send_verification_email':
                // 检查是否已经验证
                if ($userInfo['email_verified']) {
                    throw new Exception('邮箱已经验证过了');
                }

                // 生成6位数验证码
                $verificationCode = sprintf('%06d', mt_rand(0, 999999));
                $expiresAt = date('Y-m-d H:i:s', time() + 600); // 10分钟有效期

                // 保存验证码到数据库
                $stmt = $db->prepare("UPDATE users SET email_verification_token = ?, password_reset_expires = ? WHERE id = ?");
                $stmt->execute([$verificationCode, $expiresAt, $userId]);

                // 发送验证邮件
                require_once '../admin/includes/smtp-mailer.php';

                $emailSubject = '邮箱验证码 - NameSilo域名销售系统';
                $emailBody = "
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;'>
                        <h1 style='color: white; margin: 0;'>邮箱验证</h1>
                    </div>
                    <div style='padding: 30px; background: #f8f9fa;'>
                        <h2 style='color: #333;'>您好，{$userInfo['username']}！</h2>
                        <p style='font-size: 16px; line-height: 1.6; color: #555;'>
                            您正在验证您的邮箱地址。请使用以下验证码完成验证：
                        </p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <div style='display: inline-block; background: #007bff; color: white; padding: 15px 30px; font-size: 24px; font-weight: bold; letter-spacing: 3px; border-radius: 8px;'>
                                {$verificationCode}
                            </div>
                        </div>
                        <p style='color: #666; font-size: 14px;'>
                            <strong>注意：</strong>
                            <br>• 验证码有效期为10分钟
                            <br>• 请勿将验证码告诉他人
                            <br>• 如果您没有请求此验证码，请忽略此邮件
                        </p>
                    </div>
                    <div style='padding: 20px; text-align: center; background: #e9ecef; color: #6c757d; font-size: 12px;'>
                        此邮件由系统自动发送，请勿回复。
                    </div>
                </div>
                ";

                try {
                    $emailSent = sendEmail($userInfo['email'], $emailSubject, $emailBody);
                    if ($emailSent) {
                        $message = '验证码已发送到您的邮箱，请查收！验证码10分钟内有效。';
                    } else {
                        throw new Exception('邮件发送失败，请联系管理员检查邮件服务配置');
                    }
                } catch (Exception $e) {
                    // 如果是配置问题，给出更友好的提示
                    if (strpos($e->getMessage(), '邮件服务未配置') !== false) {
                        throw new Exception('邮件服务暂未配置，请联系管理员启用邮箱验证功能');
                    } else {
                        throw new Exception('邮件发送失败：' . $e->getMessage());
                    }
                }
                break;

            case 'verify_email':
                $verificationCode = trim($_POST['verification_code'] ?? '');

                if (empty($verificationCode)) {
                    throw new Exception('请输入验证码');
                }

                // 验证码格式检查
                if (!preg_match('/^\d{6}$/', $verificationCode)) {
                    throw new Exception('验证码格式错误，应为6位数字');
                }

                // 检查验证码
                $stmt = $db->prepare("SELECT email_verification_token, password_reset_expires FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();

                if (!$user || !$user['email_verification_token']) {
                    throw new Exception('请先发送验证码');
                }

                if ($user['password_reset_expires'] && strtotime($user['password_reset_expires']) < time()) {
                    throw new Exception('验证码已过期，请重新发送');
                }

                if ($user['email_verification_token'] !== $verificationCode) {
                    throw new Exception('验证码错误');
                }

                // 验证成功，更新邮箱验证状态
                $stmt = $db->prepare("UPDATE users SET email_verified = 1, email_verification_token = NULL, password_reset_expires = NULL WHERE id = ?");
                $stmt->execute([$userId]);

                $message = '邮箱验证成功！';

                // 重新获取用户信息以更新显示
                $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $userInfo = $stmt->fetch();
                break;

            default:
                throw new Exception('无效的操作');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取用户信息
try {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $userInfo = $stmt->fetch();
    
    if (!$userInfo) {
        throw new Exception('用户信息不存在');
    }
} catch (Exception $e) {
    $error = '获取用户信息失败：' . $e->getMessage();
    $userInfo = [];
}

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header" data-aos="fade-up">
    <h1 class="page-title">个人信息</h1>
    <p class="page-subtitle">管理您的个人资料和账户设置</p>
</div>

<!-- 消息提示 -->
<?php if ($message): ?>
<div class="alert alert-success alert-dismissible fade show">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row g-4">
    <!-- 基本信息 -->
    <div class="col-lg-8" data-aos="fade-up" data-aos-delay="100">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    基本信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">名字 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="first_name" 
                                   value="<?= htmlspecialchars($userInfo['first_name'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">姓氏 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="last_name" 
                                   value="<?= htmlspecialchars($userInfo['last_name'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" value="<?= htmlspecialchars($userInfo['email'] ?? '') ?>" disabled>
                            <small class="text-muted">邮箱地址不可修改</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">手机号码</label>
                            <input type="tel" class="form-control" name="phone" 
                                   value="<?= htmlspecialchars($userInfo['phone'] ?? '') ?>">
                        </div>
                        <div class="col-12">
                            <label class="form-label">地址</label>
                            <input type="text" class="form-control" name="address" 
                                   value="<?= htmlspecialchars($userInfo['address'] ?? '') ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">城市</label>
                            <input type="text" class="form-control" name="city" 
                                   value="<?= htmlspecialchars($userInfo['city'] ?? '') ?>">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">国家/地区</label>
                            <select class="form-select" name="country">
                                <option value="">请选择</option>
                                <option value="CN" <?= ($userInfo['country'] ?? '') === 'CN' ? 'selected' : '' ?>>中国</option>
                                <option value="US" <?= ($userInfo['country'] ?? '') === 'US' ? 'selected' : '' ?>>美国</option>
                                <option value="UK" <?= ($userInfo['country'] ?? '') === 'UK' ? 'selected' : '' ?>>英国</option>
                                <option value="CA" <?= ($userInfo['country'] ?? '') === 'CA' ? 'selected' : '' ?>>加拿大</option>
                                <option value="AU" <?= ($userInfo['country'] ?? '') === 'AU' ? 'selected' : '' ?>>澳大利亚</option>
                                <option value="JP" <?= ($userInfo['country'] ?? '') === 'JP' ? 'selected' : '' ?>>日本</option>
                                <option value="KR" <?= ($userInfo['country'] ?? '') === 'KR' ? 'selected' : '' ?>>韩国</option>
                                <option value="SG" <?= ($userInfo['country'] ?? '') === 'SG' ? 'selected' : '' ?>>新加坡</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">邮政编码</label>
                            <input type="text" class="form-control" name="postal_code"
                                   value="<?= htmlspecialchars($userInfo['zip_code'] ?? '') ?>">
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            保存更改
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 修改密码 -->
        <div class="card mt-4" data-aos="fade-up" data-aos-delay="200">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lock me-2"></i>
                    修改密码
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">当前密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" name="current_password" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">新密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" name="new_password" 
                                   minlength="8" required>
                            <small class="text-muted">至少8个字符</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">确认新密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" name="confirm_password" 
                                   minlength="8" required>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>
                            修改密码
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏信息 -->
    <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
        <!-- 账户状态 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    账户状态
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">账户状态</small>
                    <div>
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>
                            正常
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">邮箱验证</small>
                    <div class="d-flex align-items-center gap-2">
                        <?php if ($userInfo['email_verified'] ?? false): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>
                                已验证
                            </span>
                        <?php else: ?>
                            <span class="badge bg-warning">
                                <i class="fas fa-exclamation me-1"></i>
                                未验证
                            </span>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="showEmailVerification()">
                                <i class="fas fa-envelope me-1"></i>
                                验证邮箱
                            </button>
                            <small class="text-muted ms-2">点击发送验证码到您的邮箱</small>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="mb-3">
                    <small class="text-muted">注册时间</small>
                    <div class="fw-medium">
                        <?= date('Y年m月d日', strtotime($userInfo['created_at'] ?? 'now')) ?>
                    </div>
                </div>
                <div class="mb-0">
                    <small class="text-muted">最后登录</small>
                    <div class="fw-medium">
                        <?= date('Y-m-d H:i', strtotime($userInfo['last_login'] ?? 'now')) ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 安全提示 -->
        <div class="card mt-4" data-aos="fade-up" data-aos-delay="400">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    安全提示
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">保护您的账户</h6>
                    <ul class="mb-0 small">
                        <li>定期更换密码</li>
                        <li>使用强密码</li>
                        <li>不要在公共场所登录</li>
                        <li>及时更新联系信息</li>
                    </ul>
                </div>
                <a href="security.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cog me-2"></i>
                    安全设置
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 邮箱验证模态框 -->
<div class="modal fade" id="emailVerificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-envelope me-2"></i>
                    邮箱验证
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 发送验证码步骤 -->
                <div id="sendCodeStep">
                    <div class="text-center mb-4">
                        <i class="fas fa-envelope-open fa-3x text-primary mb-3"></i>
                        <h6>验证您的邮箱地址</h6>
                        <p class="text-muted">我们将向您的邮箱发送一个6位数验证码</p>
                        <p class="fw-medium"><?= htmlspecialchars($userInfo['email']) ?></p>
                    </div>

                    <form method="POST" id="sendCodeForm">
                        <input type="hidden" name="action" value="send_verification_email">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="sendCodeBtn">
                                <i class="fas fa-paper-plane me-2"></i>
                                发送验证码
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 输入验证码步骤 -->
                <div id="verifyCodeStep" style="display: none;">
                    <div class="text-center mb-4">
                        <i class="fas fa-key fa-3x text-success mb-3"></i>
                        <h6>输入验证码</h6>
                        <p class="text-muted">请输入发送到您邮箱的6位数验证码</p>
                        <p class="fw-medium"><?= htmlspecialchars($userInfo['email']) ?></p>
                    </div>

                    <form method="POST" id="verifyCodeForm">
                        <input type="hidden" name="action" value="verify_email">
                        <div class="mb-3">
                            <label class="form-label">验证码</label>
                            <input type="text" class="form-control text-center" name="verification_code"
                                   placeholder="请输入6位数验证码" maxlength="6" pattern="[0-9]{6}"
                                   style="font-size: 1.2rem; letter-spacing: 0.2rem;" required>
                            <div class="form-text">验证码有效期为10分钟</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success" id="verifyCodeBtn">
                                <i class="fas fa-check me-2"></i>
                                验证邮箱
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="resendCode()">
                                <i class="fas fa-redo me-2"></i>
                                重新发送验证码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 密码强度检查
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordInput = document.querySelector('input[name="new_password"]');
    const confirmPasswordInput = document.querySelector('input[name="confirm_password"]');

    if (newPasswordInput && confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value && this.value !== newPasswordInput.value) {
                this.setCustomValidity('密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });

        newPasswordInput.addEventListener('input', function() {
            if (confirmPasswordInput.value && confirmPasswordInput.value !== this.value) {
                confirmPasswordInput.setCustomValidity('密码不一致');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        });
    }

    // 邮箱验证相关功能
    window.showEmailVerification = function() {
        const modal = new bootstrap.Modal(document.getElementById('emailVerificationModal'));
        modal.show();
    };

    window.resendCode = function() {
        document.getElementById('verifyCodeStep').style.display = 'none';
        document.getElementById('sendCodeStep').style.display = 'block';
    };

    // 发送验证码表单处理
    const sendCodeForm = document.getElementById('sendCodeForm');
    if (sendCodeForm) {
        sendCodeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const btn = document.getElementById('sendCodeBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>发送中...';

            fetch(window.location.href, {
                method: 'POST',
                body: new FormData(this)
            })
            .then(response => response.text())
            .then(html => {
                // 检查是否发送成功
                if (html.includes('验证码已发送')) {
                    document.getElementById('sendCodeStep').style.display = 'none';
                    document.getElementById('verifyCodeStep').style.display = 'block';

                    // 显示成功消息
                    showAlert('success', '验证码已发送到您的邮箱，请查收！');
                } else {
                    // 提取错误消息
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const errorAlert = doc.querySelector('.alert-danger');
                    const errorMsg = errorAlert ? errorAlert.textContent.trim() : '发送失败，请重试';
                    showAlert('error', errorMsg);
                }
            })
            .catch(error => {
                showAlert('error', '网络错误，请重试');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
            });
        });
    }

    // 验证码表单处理
    const verifyCodeForm = document.getElementById('verifyCodeForm');
    if (verifyCodeForm) {
        verifyCodeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const btn = document.getElementById('verifyCodeBtn');
            const originalText = btn.innerHTML;

            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>验证中...';

            fetch(window.location.href, {
                method: 'POST',
                body: new FormData(this)
            })
            .then(response => response.text())
            .then(html => {
                if (html.includes('邮箱验证成功')) {
                    showAlert('success', '邮箱验证成功！');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const errorAlert = doc.querySelector('.alert-danger');
                    const errorMsg = errorAlert ? errorAlert.textContent.trim() : '验证失败，请重试';
                    showAlert('error', errorMsg);
                }
            })
            .catch(error => {
                showAlert('error', '网络错误，请重试');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = originalText;
            });
        });
    }

    // 验证码输入框自动格式化
    const codeInput = document.querySelector('input[name="verification_code"]');
    if (codeInput) {
        codeInput.addEventListener('input', function(e) {
            // 只允许数字
            this.value = this.value.replace(/[^0-9]/g, '');

            // 限制长度
            if (this.value.length > 6) {
                this.value = this.value.slice(0, 6);
            }
        });

        codeInput.addEventListener('paste', function(e) {
            e.preventDefault();
            const paste = (e.clipboardData || window.clipboardData).getData('text');
            const numbers = paste.replace(/[^0-9]/g, '').slice(0, 6);
            this.value = numbers;
        });
    }

    // 通用提示函数
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas ${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在模态框中显示
        const modalBody = document.querySelector('#emailVerificationModal .modal-body');
        const existingAlert = modalBody.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        modalBody.insertAdjacentHTML('afterbegin', alertHtml);
    }
});
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
