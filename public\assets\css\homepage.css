/**
 * NameSilo域名销售系统 - 首页样式
 * 基于后台管理系统的设计风格
 */

/* 首页特定样式 - 继承auth.css中的CSS变量 */
body {
    background: var(--content-bg);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 导航栏 */
.navbar {
    background: var(--card-bg);
    box-shadow: var(--shadow-sm);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand:hover {
    color: var(--primary-dark);
    transform: scale(1.02);
}

.navbar-brand i {
    font-size: 1.75rem;
}

.navbar-nav .nav-link {
    color: var(--text-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
    background: rgba(79, 70, 229, 0.1);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 英雄区域 */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="white" stop-opacity="0.1"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="120" fill="url(%23a)"/><circle cx="800" cy="300" r="180" fill="url(%23a)"/><circle cx="400" cy="700" r="150" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.domain-search {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-xl);
    margin-top: 3rem;
}

.search-input {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    padding: 1rem 1.5rem;
    font-size: 1.125rem;
    border-right: 0;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: none;
}

.search-btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 1rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
}

/* 特性区域 */
.features-section {
    padding: 5rem 0;
    background: var(--card-bg);
}

.feature-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.feature-description {
    color: var(--text-muted);
    line-height: 1.6;
}

/* 统计区域 */
.stats-section {
    background: var(--content-bg);
    padding: 4rem 0;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-muted);
    font-weight: 500;
}

/* 价格区域 */
.pricing-section {
    padding: 5rem 0;
    background: var(--card-bg);
}

.pricing-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    height: 100%;
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: '推荐';
    position: absolute;
    top: -1rem;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius-full);
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.pricing-card.featured:hover {
    transform: translateY(-5px) scale(1.05);
}

.pricing-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.pricing-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.pricing-period {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
}

.pricing-features li {
    padding: 0.5rem 0;
    color: var(--text-muted);
}

.pricing-features li i {
    color: var(--success-color);
    margin-right: 0.5rem;
}

/* 页脚 */
.footer {
    background: var(--text-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-section {
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .domain-search {
        padding: 1rem;
    }
    
    .search-input,
    .search-btn {
        font-size: 1rem;
        padding: 0.875rem 1rem;
    }
    
    .pricing-card.featured {
        transform: none;
        margin-top: 1rem;
    }
    
    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2rem;
    }
}
