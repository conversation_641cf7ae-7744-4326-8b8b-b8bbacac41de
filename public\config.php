<?php
/**
 * 系统配置文件
 * System Configuration File
 */

// 防止直接访问
if (!defined('CONFIG_LOADED')) {
    define('CONFIG_LOADED', true);
}

// 加载环境变量
function loadEnv($path) {
    if (!file_exists($path)) {
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') === false) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_ENV)) {
            $_ENV[$name] = $value;
        }
    }
}

// 定义根目录（如果未定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__FILE__));
}

// 定义public目录路径
if (!defined('PUBLIC_PATH')) {
    define('PUBLIC_PATH', dirname(__FILE__));
}

// 引入统一的数据库连接
require_once PUBLIC_PATH . '/includes/database.php';

// 加载环境变量
loadEnv(ROOT_PATH . '/../.env');

/**
 * 获取系统设置
 */
function getSystemSetting($key, $default = '') {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        try {
            $db = getDatabase();
            if ($db) {
                // 尝试从 system_settings 表获取
                try {
                    $stmt = $db->query("SELECT `key`, `value` FROM system_settings WHERE is_public = 1");
                    $result = $stmt->fetchAll();
                    foreach ($result as $row) {
                        $settings[$row['key']] = $row['value'];
                    }
                } catch (PDOException $e) {
                    // 如果 system_settings 表不存在，尝试 settings 表
                    try {
                        $stmt = $db->query("SELECT setting_key as `key`, setting_value as `value` FROM settings");
                        $result = $stmt->fetchAll();
                        foreach ($result as $row) {
                            $settings[$row['key']] = $row['value'];
                        }
                    } catch (PDOException $e) {
                        // 两个表都不存在，使用默认值
                        error_log("无法获取系统设置: " . $e->getMessage());
                    }
                }
            }
        } catch (Exception $e) {
            error_log("获取系统设置时发生错误: " . $e->getMessage());
        }
    }
    
    return $settings[$key] ?? $default;
}

/**
 * 获取所有系统设置
 */
function getAllSystemSettings() {
    static $allSettings = null;
    
    if ($allSettings === null) {
        $allSettings = [
            'site_name' => getSystemSetting('site_name', 'NameSilo域名销售系统'),
            'site_description' => getSystemSetting('site_description', '专业的域名注册和管理平台，提供快速、安全、可靠的域名服务'),
            'site_keywords' => getSystemSetting('site_keywords', '域名注册,域名购买,NameSilo,域名管理,网站域名'),
            'admin_email' => getSystemSetting('admin_email', '<EMAIL>'),
            'currency' => getSystemSetting('currency', 'USD'),
            'currency_symbol' => getSystemSetting('currency_symbol', '$'),
            'timezone' => getSystemSetting('timezone', 'UTC'),
            'language' => getSystemSetting('language', 'zh-CN'),
            'maintenance_mode' => getSystemSetting('maintenance_mode', '0'),
        ];
    }
    
    return $allSettings;
}

/**
 * 检查维护模式
 */
function isMaintenanceMode() {
    return getSystemSetting('maintenance_mode', '0') === '1';
}

/**
 * 获取网站基本信息
 */
function getSiteInfo() {
    $settings = getAllSystemSettings();
    return [
        'name' => $settings['site_name'],
        'description' => $settings['site_description'],
        'keywords' => $settings['site_keywords'],
        'admin_email' => $settings['admin_email'],
        'currency' => $settings['currency'],
        'currency_symbol' => $settings['currency_symbol'],
        'timezone' => $settings['timezone'],
        'language' => $settings['language'],
    ];
}

// 获取网站信息（全局变量）
$SITE_INFO = getSiteInfo();

// 常用的网站信息变量
$siteName = $SITE_INFO['name'];
$siteDescription = $SITE_INFO['description'];
$siteKeywords = $SITE_INFO['keywords'];
$adminEmail = $SITE_INFO['admin_email'];
$currency = $SITE_INFO['currency'];
$currencySymbol = $SITE_INFO['currency_symbol'];

// NameSilo API配置
define('NAMESILO_API_KEY', $_ENV['NAMESILO_API_KEY'] ?? 'your_namesilo_api_key_here');
define('NAMESILO_API_URL', 'https://www.namesilo.com/api/');

// 其他域名商API配置
define('GODADDY_API_KEY', $_ENV['GODADDY_API_KEY'] ?? '');
define('GODADDY_API_SECRET', $_ENV['GODADDY_API_SECRET'] ?? '');

// 检查维护模式
if (isMaintenanceMode() && !defined('ADMIN_AREA')) {
    // 如果不是管理员区域且处于维护模式，显示维护页面
    $maintenanceMessage = getSystemSetting('maintenance_message', '系统维护中，请稍后访问');
    
    http_response_code(503);
    header('Content-Type: text/html; charset=utf-8');
    
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统维护中 - ' . htmlspecialchars($siteName) . '</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; margin-bottom: 20px; }
        p { color: #666; font-size: 16px; line-height: 1.6; }
        .icon { font-size: 48px; color: #667eea; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔧</div>
        <h1>系统维护中</h1>
        <p>' . htmlspecialchars($maintenanceMessage) . '</p>
        <p>我们正在进行系统升级，预计很快就会恢复正常。感谢您的耐心等待！</p>
    </div>
</body>
</html>';
    exit;
}
?>
