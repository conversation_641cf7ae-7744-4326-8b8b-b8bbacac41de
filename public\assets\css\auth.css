/**
 * NameSilo域名销售系统 - 前端认证页面样式
 * 基于后台管理系统的设计风格
 */

/* CSS变量 - 与后台保持一致 */
:root {
    --primary-color: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #3730a3;
    --secondary-color: #6b7280;
    --accent-color: #06b6d4;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #2563eb;

    --sidebar-bg: #ffffff;
    --sidebar-text: #6b7280;
    --sidebar-active: #4f46e5;
    --sidebar-hover: #f3f4f6;
    --sidebar-border: #e5e7eb;

    --content-bg: #f9fafb;
    --card-bg: #ffffff;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --text-color: #111827;
    --text-muted: #6b7280;
    --text-light: #9ca3af;

    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    --border-radius: 8px;
    --border-radius-sm: 6px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;

    --transition-fast: all 0.15s ease-out;
    --transition: all 0.2s ease-out;
    --transition-slow: all 0.3s ease-out;

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 页面基础样式 */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    line-height: 1.5;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 认证容器 */
.auth-container {
    background: var(--card-bg);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    max-width: 400px;
    width: 100%;
    margin: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

/* 认证头部 */
.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-header .icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.auth-header h1 {
    color: var(--text-color);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    font-size: 1.5rem;
}

.auth-header p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
    background: var(--card-bg);
    color: var(--text-color);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.25);
}

.form-control::placeholder {
    color: var(--text-light);
}

/* 输入组 */
.input-group {
    position: relative;
    display: flex;
    margin-bottom: var(--spacing-md);
}

.input-group-text {
    background: var(--border-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.input-group .form-control {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    border-left: 0;
    margin-bottom: 0;
}

.input-group .form-control:focus {
    border-left: 1px solid var(--primary-color);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    width: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 反馈消息 */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    border: none;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.alert-success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background: rgba(37, 99, 235, 0.1);
    color: var(--info-color);
}

/* 表单验证反馈 */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--danger-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--success-color);
}

/* 链接样式 */
.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.auth-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* 页脚 */
.auth-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
}

.auth-footer p {
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

/* 加载动画 */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-container {
        margin: var(--spacing-sm);
        padding: var(--spacing-xl);
    }
    
    .auth-header h1 {
        font-size: 1.25rem;
    }
    
    .auth-header .icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .auth-container {
        margin: var(--spacing-xs);
        padding: var(--spacing-lg);
    }
    
    .form-control,
    .input-group-text,
    .btn {
        padding: 0.625rem 0.875rem;
        font-size: 0.8125rem;
    }
}

/* 可访问性 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 焦点可见性 */
.btn:focus-visible,
.form-control:focus-visible,
.auth-link:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
