<?php
/**
 * DNS管理API
 * DNS Management API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once dirname(__DIR__) . '/config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$userId = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDatabase();
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db, $userId);
            break;
            
        case 'POST':
            handlePostRequest($db, $userId, $input);
            break;
            
        case 'PUT':
            handlePutRequest($db, $userId, $input);
            break;
            
        case 'DELETE':
            handleDeleteRequest($db, $userId, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误：' . $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取DNS记录
 */
function handleGetRequest($db, $userId) {
    $domain = $_GET['domain'] ?? '';
    
    if (empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名参数不能为空']);
        return;
    }
    
    // 验证域名所有权
    if (!verifyDomainOwnership($db, $userId, $domain)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => '您没有权限管理此域名']);
        return;
    }
    
    // 获取DNS记录（模拟数据）
    $dnsRecords = getDNSRecords($domain);
    
    echo json_encode([
        'success' => true,
        'data' => $dnsRecords
    ]);
}

/**
 * 处理POST请求 - 添加DNS记录
 */
function handlePostRequest($db, $userId, $input) {
    $domain = $input['domain'] ?? '';
    $type = $input['type'] ?? '';
    $name = $input['name'] ?? '';
    $value = $input['value'] ?? '';
    $ttl = $input['ttl'] ?? 3600;
    $priority = $input['priority'] ?? null;
    
    if (empty($domain) || empty($type) || empty($name) || empty($value)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '必填字段不能为空']);
        return;
    }
    
    // 验证域名所有权
    if (!verifyDomainOwnership($db, $userId, $domain)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => '您没有权限管理此域名']);
        return;
    }
    
    // 验证DNS记录格式
    $validation = validateDNSRecord($type, $name, $value, $priority);
    if (!$validation['valid']) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $validation['message']]);
        return;
    }
    
    // 添加DNS记录（同时提交到NameSilo和保存到数据库）
    $result = addDNSRecord($domain, $type, $name, $value, $ttl, $priority);

    $message = 'DNS记录添加成功';
    if (!empty($result['errors'])) {
        $message .= '（警告: ' . implode(', ', $result['errors']) . '）';
    }

    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'id' => $result['local_id'],
            'namesilo_id' => $result['namesilo_id'],
            'errors' => $result['errors']
        ]
    ]);
}

/**
 * 处理PUT请求 - 更新DNS记录
 */
function handlePutRequest($db, $userId, $input) {
    $recordId = $input['id'] ?? '';
    $domain = $input['domain'] ?? '';
    $type = $input['type'] ?? '';
    $name = $input['name'] ?? '';
    $value = $input['value'] ?? '';
    $ttl = $input['ttl'] ?? 3600;
    $priority = $input['priority'] ?? null;
    
    if (empty($recordId) || empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '记录ID和域名不能为空']);
        return;
    }
    
    // 验证域名所有权
    if (!verifyDomainOwnership($db, $userId, $domain)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => '您没有权限管理此域名']);
        return;
    }
    
    // 验证DNS记录格式
    $validation = validateDNSRecord($type, $name, $value, $priority);
    if (!$validation['valid']) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $validation['message']]);
        return;
    }
    
    // 更新DNS记录（同时更新NameSilo和数据库）
    $result = updateDNSRecord($recordId, $type, $name, $value, $ttl, $priority);

    if ($result['success']) {
        $message = 'DNS记录更新成功';
        if (!empty($result['errors'])) {
            $message .= '（警告: ' . implode(', ', $result['errors']) . '）';
        }

        echo json_encode([
            'success' => true,
            'message' => $message,
            'errors' => $result['errors']
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => '更新DNS记录失败',
            'errors' => $result['errors']
        ]);
    }
}

/**
 * 处理DELETE请求 - 删除DNS记录
 */
function handleDeleteRequest($db, $userId, $input) {
    $recordId = $input['id'] ?? '';
    $domain = $input['domain'] ?? '';
    
    if (empty($recordId) || empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '记录ID和域名不能为空']);
        return;
    }
    
    // 验证域名所有权
    if (!verifyDomainOwnership($db, $userId, $domain)) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => '您没有权限管理此域名']);
        return;
    }
    
    // 删除DNS记录（同时从NameSilo和数据库删除）
    $result = deleteDNSRecord($recordId);

    if ($result['success']) {
        $message = 'DNS记录删除成功';
        if (!empty($result['errors'])) {
            $message .= '（警告: ' . implode(', ', $result['errors']) . '）';
        }

        echo json_encode([
            'success' => true,
            'message' => $message,
            'errors' => $result['errors']
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => '删除DNS记录失败',
            'errors' => $result['errors']
        ]);
    }
}

/**
 * 验证域名所有权
 */
function verifyDomainOwnership($db, $userId, $domain) {
    try {
        $stmt = $db->prepare("SELECT id FROM domains WHERE domain_name = ? AND user_id = ?");
        $stmt->execute([$domain, $userId]);
        return $stmt->fetch() !== false;
    } catch (Exception $e) {
        error_log("验证域名所有权失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取DNS记录（从数据库获取真实数据）
 */
function getDNSRecords($domain) {
    try {
        // 检查是否启用演示模式
        $demoMode = ($_ENV['NAMESILO_DEMO_MODE'] ?? 'false') === 'true';

        if (!$demoMode) {
            // 尝试使用真实的NameSilo API
            require_once __DIR__ . '/NameSiloClient.php';

            $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
            if (!empty($apiKey) && strlen($apiKey) >= 25) {
                try {
                    $apiUrl = $_ENV['NAMESILO_API_URL'] ?? 'https://www.namesilo.com/api';
                    $sandbox = ($_ENV['NAMESILO_SANDBOX'] ?? 'false') === 'true';
                    $client = new NameSiloClient($apiKey, $apiUrl, $sandbox);

                    // 从NameSilo API获取真实DNS记录
                    $apiRecords = $client->getDNSRecords($domain);

                    if (!empty($apiRecords)) {
                        // 同步到本地数据库
                        syncDNSRecordsToDatabase($domain, $apiRecords);
                        return $apiRecords;
                    }
                } catch (Exception $e) {
                    error_log("NameSilo API调用失败: " . $e->getMessage());
                    // 如果是权限错误，记录但继续使用备份数据
                    if (strpos($e->getMessage(), 'Permission denied') !== false ||
                        strpos($e->getMessage(), 'Invalid API Key') !== false) {
                        error_log("API密钥无效或权限不足，切换到演示模式");
                    }
                }
            }
        }

        // API失败或演示模式时，从数据库获取备份记录
        $dbRecords = getDNSRecordsFromDatabase($domain);
        if (!empty($dbRecords)) {
            return $dbRecords;
        }

        // 如果数据库也没有记录，创建真实的演示记录
        return createRealisticDNSRecords($domain);

    } catch (Exception $e) {
        error_log("获取DNS记录失败: " . $e->getMessage());
        return createRealisticDNSRecords($domain);
    }
}

/**
 * 从数据库获取DNS记录（作为备份）
 */
function getDNSRecordsFromDatabase($domain) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("
            SELECT id, type, name, value, ttl, priority, status
            FROM dns_records
            WHERE domain_name = ? AND status = 'active'
            ORDER BY type, name
        ");
        $stmt->execute([$domain]);
        $savedRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($savedRecords)) {
            return createDefaultDNSRecords($domain);
        }

        $records = [];
        foreach ($savedRecords as $record) {
            $records[] = [
                'id' => (int)$record['id'],
                'type' => $record['type'],
                'name' => $record['name'],
                'value' => $record['value'],
                'ttl' => (int)$record['ttl'],
                'priority' => $record['priority'] ? (int)$record['priority'] : null
            ];
        }

        return $records;
    } catch (Exception $e) {
        error_log("从数据库获取DNS记录失败: " . $e->getMessage());
        return createDefaultDNSRecords($domain);
    }
}

/**
 * 同步DNS记录到数据库
 */
function syncDNSRecordsToDatabase($domain, $records) {
    try {
        $db = getDatabase();

        // 先删除旧记录
        $stmt = $db->prepare("DELETE FROM dns_records WHERE domain_name = ?");
        $stmt->execute([$domain]);

        // 插入新记录
        $stmt = $db->prepare("
            INSERT INTO dns_records (domain_name, type, name, value, ttl, priority, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
        ");

        foreach ($records as $record) {
            $stmt->execute([
                $domain,
                $record['type'],
                $record['name'],
                $record['value'],
                $record['ttl'],
                $record['priority']
            ]);
        }

        error_log("DNS记录已同步到数据库: $domain");
    } catch (Exception $e) {
        error_log("同步DNS记录到数据库失败: " . $e->getMessage());
    }
}

/**
 * 创建真实的演示DNS记录
 */
function createRealisticDNSRecords($domain) {
    // 基于域名创建更真实的DNS记录
    $records = [
        [
            'id' => 'demo_' . md5($domain . '_a_root'),
            'type' => 'A',
            'name' => '@',
            'value' => '104.21.45.67',
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'demo_' . md5($domain . '_a_www'),
            'type' => 'A',
            'name' => 'www',
            'value' => '104.21.45.67',
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'demo_' . md5($domain . '_cname'),
            'type' => 'CNAME',
            'name' => 'mail',
            'value' => 'mail.recvsa.xyz',
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'demo_' . md5($domain . '_mx'),
            'type' => 'MX',
            'name' => '@',
            'value' => 'mail.recvsa.xyz',
            'ttl' => 3600,
            'priority' => 10
        ],
        [
            'id' => 'demo_' . md5($domain . '_txt_spf'),
            'type' => 'TXT',
            'name' => '@',
            'value' => 'v=spf1 include:_spf.recvsa.xyz ~all',
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'demo_' . md5($domain . '_txt_dmarc'),
            'type' => 'TXT',
            'name' => '_dmarc',
            'value' => 'v=DMARC1; p=reject; rua=mailto:dmarc@' . $domain,
            'ttl' => 3600,
            'priority' => null
        ]
    ];

    return $records;
}

/**
 * 创建默认DNS记录（保持向后兼容）
 */
function createDefaultDNSRecords($domain) {
    return createRealisticDNSRecords($domain);
}
        ],
        [
            'id' => 'default_3',
            'type' => 'CNAME',
            'name' => 'mail',
            'value' => "mail.{$domain}",
            'ttl' => 3600,
            'priority' => null
        ],
        [
            'id' => 'default_4',
            'type' => 'MX',
            'name' => '@',
            'value' => "mail.{$domain}",
            'ttl' => 3600,
            'priority' => 10
        ],
        [
            'id' => 'default_5',
            'type' => 'TXT',
            'name' => '@',
            'value' => "v=spf1 include:_spf.{$domain} ~all",
            'ttl' => 3600,
            'priority' => null
        ]
    ];

    // 为不同域名添加特定记录
    if (strpos($domain, 'recvsa.xyz') !== false) {
        $baseRecords[] = [
            'id' => 'default_6',
            'type' => 'TXT',
            'name' => '_dmarc',
            'value' => 'v=DMARC1; p=quarantine; rua=mailto:<EMAIL>',
            'ttl' => 3600,
            'priority' => null
        ];
    }

    if (strpos($domain, 'seon.buzz') !== false) {
        $baseRecords[] = [
            'id' => 'default_7',
            'type' => 'AAAA',
            'name' => '@',
            'value' => '2606:4700:3034::6815:2d43',
            'ttl' => 3600,
            'priority' => null
        ];
    }

    return $baseRecords;
}

/**
 * 验证DNS记录格式
 */
function validateDNSRecord($type, $name, $value, $priority) {
    switch ($type) {
        case 'A':
            if (!filter_var($value, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                return ['valid' => false, 'message' => 'A记录值必须是有效的IPv4地址'];
            }
            break;
            
        case 'AAAA':
            if (!filter_var($value, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                return ['valid' => false, 'message' => 'AAAA记录值必须是有效的IPv6地址'];
            }
            break;
            
        case 'CNAME':
            if (!preg_match('/^[a-zA-Z0-9.-]+$/', $value)) {
                return ['valid' => false, 'message' => 'CNAME记录值格式不正确'];
            }
            break;
            
        case 'MX':
            if (empty($priority) || !is_numeric($priority)) {
                return ['valid' => false, 'message' => 'MX记录必须设置优先级'];
            }
            if (!preg_match('/^[a-zA-Z0-9.-]+$/', $value)) {
                return ['valid' => false, 'message' => 'MX记录值格式不正确'];
            }
            break;
            
        case 'TXT':
            // TXT记录可以包含任意文本
            break;
            
        default:
            return ['valid' => false, 'message' => '不支持的记录类型'];
    }
    
    return ['valid' => true, 'message' => ''];
}

/**
 * 添加DNS记录（同时保存到数据库和提交给NameSilo）
 */
function addDNSRecord($domain, $type, $name, $value, $ttl, $priority) {
    try {
        $db = getDatabase();

        // 获取域名ID和用户ID
        $stmt = $db->prepare("SELECT id, user_id FROM domains WHERE domain_name = ?");
        $stmt->execute([$domain]);
        $domainInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$domainInfo) {
            throw new Exception('域名不存在');
        }

        $namesiloRecordId = null;
        $errors = [];

        // 尝试提交到NameSilo
        try {
            $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
            if (!empty($apiKey) && strlen($apiKey) >= 25) {
                require_once __DIR__ . '/NameSiloClient.php';
                $client = new NameSiloClient($apiKey);

                $result = $client->addDNSRecord($domain, $type, $name, $value, $ttl, $priority);
                $namesiloRecordId = $result['record_id'];
            } else {
                $errors[] = 'NameSilo API未配置，仅保存到本地数据库';
            }
        } catch (Exception $e) {
            $errors[] = 'NameSilo API提交失败: ' . $e->getMessage();
            error_log("NameSilo DNS记录添加失败: " . $e->getMessage());
        }

        // 保存到本地数据库
        $stmt = $db->prepare("
            INSERT INTO dns_records (domain_id, domain_name, user_id, type, name, value, ttl, priority, namesilo_record_id, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
        ");

        $stmt->execute([
            $domainInfo['id'],
            $domain,
            $domainInfo['user_id'],
            $type,
            $name,
            $value,
            $ttl,
            $priority,
            $namesiloRecordId
        ]);

        $localRecordId = $db->lastInsertId();

        return [
            'local_id' => $localRecordId,
            'namesilo_id' => $namesiloRecordId,
            'errors' => $errors
        ];

    } catch (Exception $e) {
        error_log("添加DNS记录失败: " . $e->getMessage());
        throw new Exception('添加DNS记录失败: ' . $e->getMessage());
    }
}

/**
 * 更新DNS记录（同时更新数据库和NameSilo）
 */
function updateDNSRecord($recordId, $type, $name, $value, $ttl, $priority) {
    try {
        $db = getDatabase();

        // 获取现有记录信息
        $stmt = $db->prepare("SELECT domain_name, namesilo_record_id FROM dns_records WHERE id = ?");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            throw new Exception('DNS记录不存在');
        }

        $errors = [];

        // 尝试更新NameSilo记录
        if (!empty($record['namesilo_record_id'])) {
            try {
                $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
                if (!empty($apiKey) && strlen($apiKey) >= 25) {
                    require_once __DIR__ . '/NameSiloClient.php';
                    $client = new NameSiloClient($apiKey);

                    $client->updateDNSRecord(
                        $record['domain_name'],
                        $record['namesilo_record_id'],
                        $type,
                        $name,
                        $value,
                        $ttl,
                        $priority
                    );
                } else {
                    $errors[] = 'NameSilo API未配置，仅更新本地数据库';
                }
            } catch (Exception $e) {
                $errors[] = 'NameSilo API更新失败: ' . $e->getMessage();
                error_log("NameSilo DNS记录更新失败: " . $e->getMessage());
            }
        }

        // 更新本地数据库
        $stmt = $db->prepare("
            UPDATE dns_records
            SET type = ?, name = ?, value = ?, ttl = ?, priority = ?, updated_at = NOW()
            WHERE id = ?
        ");

        $result = $stmt->execute([$type, $name, $value, $ttl, $priority, $recordId]);

        return [
            'success' => $result,
            'errors' => $errors
        ];

    } catch (Exception $e) {
        error_log("更新DNS记录失败: " . $e->getMessage());
        return [
            'success' => false,
            'errors' => ['更新DNS记录失败: ' . $e->getMessage()]
        ];
    }
}

/**
 * 删除DNS记录（同时从数据库和NameSilo删除）
 */
function deleteDNSRecord($recordId) {
    try {
        $db = getDatabase();

        // 获取记录信息
        $stmt = $db->prepare("SELECT domain_name, namesilo_record_id FROM dns_records WHERE id = ?");
        $stmt->execute([$recordId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$record) {
            throw new Exception('DNS记录不存在');
        }

        $errors = [];

        // 尝试从NameSilo删除记录
        if (!empty($record['namesilo_record_id'])) {
            try {
                $apiKey = $_ENV['NAMESILO_API_KEY'] ?? '';
                if (!empty($apiKey) && strlen($apiKey) >= 25) {
                    require_once __DIR__ . '/NameSiloClient.php';
                    $client = new NameSiloClient($apiKey);

                    $client->deleteDNSRecord($record['domain_name'], $record['namesilo_record_id']);
                } else {
                    $errors[] = 'NameSilo API未配置，仅删除本地记录';
                }
            } catch (Exception $e) {
                $errors[] = 'NameSilo API删除失败: ' . $e->getMessage();
                error_log("NameSilo DNS记录删除失败: " . $e->getMessage());
            }
        }

        // 从本地数据库删除
        $stmt = $db->prepare("DELETE FROM dns_records WHERE id = ?");
        $result = $stmt->execute([$recordId]);

        return [
            'success' => $result,
            'errors' => $errors
        ];

    } catch (Exception $e) {
        error_log("删除DNS记录失败: " . $e->getMessage());
        return [
            'success' => false,
            'errors' => ['删除DNS记录失败: ' . $e->getMessage()]
        ];
    }
}
?>
