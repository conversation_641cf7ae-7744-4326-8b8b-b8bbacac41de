<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - NameSilo域名销售系统</title>
    <meta name="description" content="注册新账户开始管理您的域名">
    <meta name="keywords" content="域名注册,NameSilo,域名管理,新用户注册">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/auth.css" rel="stylesheet">
    
    <style>
        /* 注册页面特定样式 */
        .auth-container {
            max-width: 450px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            opacity: 0;
            transform: translateY(-10px);
            transition: var(--transition);
        }
        
        .password-strength.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .strength-bar {
            height: 6px;
            background: var(--border-light);
            border-radius: var(--border-radius-sm);
            margin-bottom: 0.5rem;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: var(--transition-slow);
            border-radius: var(--border-radius-sm);
        }
        
        .strength-weak .strength-fill {
            width: 25%;
            background: var(--danger-color);
        }
        
        .strength-fair .strength-fill {
            width: 50%;
            background: var(--warning-color);
        }
        
        .strength-good .strength-fill {
            width: 75%;
            background: var(--info-color);
        }
        
        .strength-strong .strength-fill {
            width: 100%;
            background: var(--success-color);
        }
        
        .strength-text {
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <i class="fas fa-user-plus icon"></i>
            <h1>创建账户</h1>
            <p>注册新账户开始管理您的域名</p>
        </div>
        
        <!-- 错误/成功消息 -->
        <div id="messageContainer"></div>
        
        <form id="registerForm">
            <div class="form-row">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" id="firstName" name="firstName" class="form-control" placeholder="名字（可选）">
                </div>

                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" id="lastName" name="lastName" class="form-control" placeholder="请输入您的姓氏" required>
                </div>
            </div>
            
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-envelope"></i>
                </span>
                <input type="email" id="email" name="email" class="form-control" placeholder="请输入您的邮箱地址" required>
            </div>
            
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-phone"></i>
                </span>
                <input type="tel" id="phone" name="phone" class="form-control" placeholder="请输入您的手机号码" required>
            </div>
            
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                </span>
                <input type="password" id="password" name="password" class="form-control" placeholder="请设置您的密码" required>
            </div>
            
            <div class="password-strength" id="passwordStrength">
                <div class="strength-bar">
                    <div class="strength-fill"></div>
                </div>
                <div class="strength-text"></div>
            </div>
            
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-lock"></i>
                </span>
                <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" placeholder="请再次输入密码" required>
            </div>
            
            <div class="form-check mb-3">
                <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                <label class="form-check-label" for="terms">
                    我已阅读并同意 <a href="#" target="_blank" class="auth-link">服务条款</a> 和 <a href="#" target="_blank" class="auth-link">隐私政策</a>
                </label>
            </div>
            
            <div class="form-check mb-3">
                <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                <label class="form-check-label" for="newsletter">
                    订阅我们的新闻通讯，获取最新域名优惠信息
                </label>
            </div>
            
            <button type="submit" class="btn btn-primary" id="registerBtn">
                <i class="fas fa-user-plus"></i>
                <span class="btn-text">创建账户</span>
            </button>
        </form>
        
        <div class="auth-footer">
            <p>已有账户？</p>
            <a href="login.php" class="auth-link">立即登录</a>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简单的消息显示函数
        function showMessage(message, type = 'error') {
            // 移除现有消息
            const existingMessage = document.querySelector('.auth-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // 创建新消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `auth-message ${type === 'success' ? 'success' : 'error'}`;
            messageDiv.style.cssText = `
                padding: 12px 16px;
                margin-bottom: 20px;
                border-radius: 8px;
                font-size: 14px;
                text-align: center;
                ${type === 'success' ?
                    'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' :
                    'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'
                }
            `;
            messageDiv.textContent = message;

            // 插入到表单前面
            const form = document.getElementById('registerForm');
            form.parentNode.insertBefore(messageDiv, form);

            // 自动滚动到消息位置
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // 验证函数
        function validateEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        function validatePhone(phone) {
            return /^1[3-9]\d{9}$/.test(phone);
        }

        function validatePassword(password) {
            return password.length >= 6;
        }

        // 密码强度检测
        function checkPasswordStrength(password) {
            const strengthContainer = document.getElementById('passwordStrength');
            const strengthText = strengthContainer.querySelector('.strength-text');

            if (password.length === 0) {
                strengthContainer.classList.remove('show');
                return;
            }

            strengthContainer.classList.add('show');

            // 计算强度分数
            let score = 0;
            if (password.length >= 8) score++;
            if (/[a-zA-Z]/.test(password)) score++;
            if (/\d/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            // 移除所有强度类
            strengthContainer.classList.remove('strength-weak', 'strength-fair', 'strength-good', 'strength-strong');

            // 设置强度等级和文本
            if (score <= 1) {
                strengthContainer.classList.add('strength-weak');
                strengthText.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 密码强度：很弱';
            } else if (score === 2) {
                strengthContainer.classList.add('strength-fair');
                strengthText.innerHTML = '<i class="fas fa-minus-circle"></i> 密码强度：一般';
            } else if (score === 3) {
                strengthContainer.classList.add('strength-good');
                strengthText.innerHTML = '<i class="fas fa-check-circle"></i> 密码强度：良好';
            } else {
                strengthContainer.classList.add('strength-strong');
                strengthText.innerHTML = '<i class="fas fa-shield-alt"></i> 密码强度：很强';
            }
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 密码强度实时检测
            document.getElementById('password').addEventListener('input', function() {
                checkPasswordStrength(this.value);
            });

            // 确认密码实时检查
            document.getElementById('confirmPassword').addEventListener('input', function() {
                const password = document.getElementById('password').value;
                const confirmField = this;

                if (this.value && this.value !== password) {
                    confirmField.style.borderColor = '#dc3545';
                } else if (this.value === password && password) {
                    confirmField.style.borderColor = '#28a745';
                } else {
                    confirmField.style.borderColor = '';
                }
            });

            // 自动聚焦到第一个输入框
            const firstNameField = document.getElementById('firstName');
            if (firstNameField) {
                firstNameField.focus();
            }
        });
        
        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);
            const data = {};
            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }

            // 基本验证
            if (!data.email || !validateEmail(data.email)) {
                showMessage('请输入有效的邮箱地址');
                return;
            }

            if (!data.password || !validatePassword(data.password)) {
                showMessage('密码长度至少6位');
                return;
            }

            if (data.password !== data.confirmPassword) {
                showMessage('两次输入的密码不一致');
                return;
            }

            if (!document.getElementById('terms').checked) {
                showMessage('请同意服务条款和隐私政策');
                return;
            }

            const registerBtn = document.getElementById('registerBtn');
            const originalText = registerBtn.innerHTML;

            try {
                // 显示加载状态
                registerBtn.disabled = true;
                registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';

                const response = await fetch('api/auth.php?action=register', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message || '注册成功！', 'success');
                    registerBtn.style.background = '#28a745';
                    registerBtn.innerHTML = '<i class="fas fa-check"></i> 注册成功';

                    // 禁用表单
                    const inputs = document.querySelectorAll('input');
                    inputs.forEach(input => input.disabled = true);

                    setTimeout(() => {
                        window.location.href = 'login.php';
                    }, 2000);
                } else {
                    showMessage(result.message || '注册失败，请稍后重试');
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = originalText;
                }
            } catch (error) {
                console.error('Registration error:', error);
                showMessage('网络连接失败，请检查网络后重试');
                registerBtn.disabled = false;
                registerBtn.innerHTML = originalText;
            }
        });

    </script>
</body>
</html>
