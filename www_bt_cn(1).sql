-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version *********
-- https://www.phpmyadmin.net
--
-- Host: localhost:3306
-- Generation Time: 2025-07-22 17:29:57
-- 服务器版本： 5.7.38-log
-- PHP Version: 7.0.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `www_bt_cn`
--

DELIMITER $$
--
-- 存储过程
--
CREATE DEFINER=`www_bt_cn`@`localhost` PROCEDURE `GenerateOrderNo`(OUT order_no VARCHAR(32))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE temp_no VARCHAR(32);
    
    REPEAT
        SET temp_no = CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(FLOOR(RAND() * 999999), 6, '0'));
        
        SELECT COUNT(*) INTO done FROM orders WHERE order_no = temp_no;
        
    UNTIL done = 0 END REPEAT;
    
    SET order_no = temp_no;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- 表的结构 `activity_logs`
--

CREATE TABLE IF NOT EXISTS `activity_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `additional_data` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `admins`
--

CREATE TABLE IF NOT EXISTS `admins` (
  `id` int(11) NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员用户名',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `role` enum('super_admin','admin','moderator') COLLATE utf8mb4_unicode_ci DEFAULT 'admin' COMMENT '管理员角色',
  `permissions` json DEFAULT NULL COMMENT '权限设置',
  `first_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名',
  `last_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话',
  `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_attempts` int(11) DEFAULT '0' COMMENT '登录尝试次数',
  `locked_until` datetime DEFAULT NULL COMMENT '锁定到期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

--
-- 转存表中的数据 `admins`
--

INSERT INTO `admins` (`id`, `username`, `email`, `password_hash`, `role`, `permissions`, `first_name`, `last_name`, `phone`, `status`, `last_login`, `login_attempts`, `locked_until`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$zNy20J5RrN6e.1dpGq7vtOTrMbjO5/PriE.IGVEHmGnezYjOpNXTq', 'super_admin', '["all"]', 'Admin', 'User', NULL, 'active', NULL, 0, NULL, '2025-07-20 05:23:12', '2025-07-20 05:23:12');

-- --------------------------------------------------------

--
-- 表的结构 `api_keys`
--

CREATE TABLE IF NOT EXISTS `api_keys` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API密钥名称',
  `key_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API密钥哈希',
  `permissions` json DEFAULT NULL COMMENT '权限设置',
  `rate_limit` int(11) DEFAULT '1000' COMMENT '速率限制（每小时）',
  `last_used` datetime DEFAULT NULL COMMENT '最后使用时间',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `status` enum('active','inactive','revoked') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API密钥表';

-- --------------------------------------------------------

--
-- 表的结构 `backups`
--

CREATE TABLE IF NOT EXISTS `backups` (
  `id` int(11) NOT NULL,
  `filename` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备份文件名',
  `type` enum('database','files','full') COLLATE utf8mb4_unicode_ci DEFAULT 'database' COMMENT '备份类型',
  `size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件路径',
  `status` enum('pending','running','completed','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '状态',
  `progress` int(11) DEFAULT '0' COMMENT '进度百分比',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `started_at` datetime DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份记录表';

-- --------------------------------------------------------

--
-- 表的结构 `billing_transactions`
--

CREATE TABLE IF NOT EXISTS `billing_transactions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('recharge','payment','refund','adjustment') NOT NULL,
  `order_number` varchar(50) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` varchar(20) DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled') DEFAULT 'pending',
  `callback_source` varchar(20) DEFAULT NULL COMMENT '回调来源: async_notify, return_callback, manual',
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `trade_no` varchar(100) DEFAULT NULL COMMENT '第三方交易号'
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8;

--
-- 转存表中的数据 `billing_transactions`
--

INSERT INTO `billing_transactions` (`id`, `user_id`, `type`, `order_number`, `amount`, `payment_method`, `status`, `callback_source`, `description`, `created_at`, `completed_at`, `trade_no`) VALUES
(1, 1, 'recharge', NULL, '500.00', NULL, 'completed', NULL, '支付宝充值', '2025-07-20 08:16:44', NULL, NULL),
(2, 1, 'payment', NULL, '-99.99', NULL, 'completed', NULL, '域名注册费用', '2025-07-20 08:16:44', NULL, NULL),
(3, 1, 'recharge', NULL, '1000.00', NULL, 'completed', NULL, '微信充值', '2025-07-20 08:16:44', NULL, NULL),
(20, 1, 'recharge', 'RCH202507202036257911', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:36:25', NULL, NULL),
(21, 1, 'recharge', 'RCH202507202036559304', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:36:55', NULL, NULL),
(22, 1, 'recharge', 'RCH202507202037256986', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:37:25', NULL, NULL),
(23, 1, 'recharge', 'RCH202507202037563326', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:37:56', NULL, NULL),
(24, 1, 'recharge', 'RCH202507202038261260', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:38:26', NULL, NULL),
(25, 1, 'recharge', 'RCH202507202038568611', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:38:56', NULL, NULL),
(26, 1, 'recharge', 'RCH202507202039271518', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:39:27', NULL, NULL),
(27, 1, 'recharge', 'RCH202507202039576271', '0.01', NULL, 'pending', NULL, NULL, '2025-07-20 12:39:57', NULL, NULL),
(32, 15, 'recharge', 'RCH202507202050169812', '0.01', 'alipay', 'completed', 'return_callback', NULL, '2025-07-20 12:50:16', '2025-07-20 12:50:41', '2025072022001466311433801730'),
(34, 15, 'recharge', 'RCH202507202246597430', '0.01', 'alipay', 'completed', 'return_callback', NULL, '2025-07-20 14:46:59', '2025-07-20 14:47:39', '2025072022001466311434806578'),
(35, 15, 'recharge', 'RCH202507220301157052', '0.01', 'alipay', 'completed', 'return_callback', NULL, '2025-07-21 19:01:15', '2025-07-21 19:02:02', '2025072222001466311440289946');

-- --------------------------------------------------------

--
-- 表的结构 `cart`
--

CREATE TABLE IF NOT EXISTS `cart` (
  `id` int(11) NOT NULL,
  `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名',
  `type` enum('register','renew','transfer') COLLATE utf8mb4_unicode_ci DEFAULT 'register' COMMENT '类型',
  `period` int(11) DEFAULT '1' COMMENT '年限',
  `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `total_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总价',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- --------------------------------------------------------

--
-- 表的结构 `cart_items`
--

CREATE TABLE IF NOT EXISTS `cart_items` (
  `id` int(11) NOT NULL,
  `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名',
  `type` enum('register','renew','transfer') COLLATE utf8mb4_unicode_ci DEFAULT 'register' COMMENT '类型',
  `period` int(11) DEFAULT '1' COMMENT '年限',
  `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `total_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总价',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车项目表';

-- --------------------------------------------------------

--
-- 表的结构 `coupons`
--

CREATE TABLE IF NOT EXISTS `coupons` (
  `id` int(11) NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠券代码',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠券名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
  `type` enum('percentage','fixed_amount') COLLATE utf8mb4_unicode_ci DEFAULT 'percentage' COMMENT '类型',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `minimum_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低消费金额',
  `usage_limit` int(11) DEFAULT NULL COMMENT '使用次数限制',
  `used_count` int(11) DEFAULT '0' COMMENT '已使用次数',
  `valid_from` datetime NOT NULL COMMENT '有效期开始',
  `valid_until` datetime NOT NULL COMMENT '有效期结束',
  `status` enum('active','inactive','expired') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券表';

-- --------------------------------------------------------

--
-- 表的结构 `coupon_usage`
--

CREATE TABLE IF NOT EXISTS `coupon_usage` (
  `id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL COMMENT '优惠券ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `discount_amount` decimal(10,2) NOT NULL COMMENT '折扣金额',
  `used_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券使用记录表';

-- --------------------------------------------------------

--
-- 表的结构 `dns_records`
--

CREATE TABLE IF NOT EXISTS `dns_records` (
  `id` int(11) NOT NULL,
  `domain_id` int(11) NOT NULL COMMENT '域名ID',
  `type` enum('A','AAAA','CNAME','MX','TXT','NS','PTR','SRV') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录类型',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录名称',
  `value` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录值',
  `ttl` int(11) DEFAULT '3600' COMMENT 'TTL值',
  `priority` int(11) DEFAULT NULL COMMENT '优先级(MX记录使用)',
  `namesilo_record_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NameSilo记录ID',
  `weight` int(11) DEFAULT NULL COMMENT '权重(SRV记录使用)',
  `port` int(11) DEFAULT NULL COMMENT '端口(SRV记录使用)',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='DNS记录表';

--
-- 转存表中的数据 `dns_records`
--

INSERT INTO `dns_records` (`id`, `domain_id`, `type`, `name`, `value`, `ttl`, `priority`, `namesilo_record_id`, `weight`, `port`, `created_at`, `updated_at`) VALUES
(1, 4, 'A', '@', '192.168.1.100', 3600, NULL, NULL, NULL, NULL, '2025-07-20 15:47:23', '2025-07-20 15:47:23'),
(2, 4, 'CNAME', 'www', 'cloudservices.org', 3600, NULL, NULL, NULL, NULL, '2025-07-20 15:47:23', '2025-07-20 15:47:23'),
(3, 4, 'MX', '@', 'mail.cloudservices.org', 3600, 10, NULL, NULL, NULL, '2025-07-20 15:47:23', '2025-07-20 15:47:23'),
(4, 3, 'A', '@', '192.168.1.100', 3600, NULL, NULL, NULL, NULL, '2025-07-20 15:47:23', '2025-07-20 15:47:23'),
(5, 3, 'CNAME', 'www', 'digitalmarketing.net', 3600, NULL, NULL, NULL, NULL, '2025-07-20 15:47:23', '2025-07-20 15:47:23'),
(6, 3, 'MX', '@', 'mail.digitalmarketing.net', 3600, 10, NULL, NULL, NULL, '2025-07-20 15:47:23', '2025-07-20 15:47:23');

-- --------------------------------------------------------

--
-- 表的结构 `domains`
--

CREATE TABLE IF NOT EXISTS `domains` (
  `id` int(11) NOT NULL,
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名',
  `tld` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '顶级域名',
  `user_id` int(11) DEFAULT NULL COMMENT '所有者用户ID',
  `status` enum('available','registered','expired','pending','transferred') COLLATE utf8mb4_unicode_ci DEFAULT 'available' COMMENT '域名状态',
  `registration_date` date DEFAULT NULL COMMENT '注册日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期',
  `auto_renew` tinyint(1) DEFAULT '0' COMMENT '自动续费',
  `privacy_protection` tinyint(1) DEFAULT '1' COMMENT '隐私保护',
  `nameservers` json DEFAULT NULL COMMENT 'DNS服务器',
  `registrar_order_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册商订单ID',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `renewal_price` decimal(10,2) DEFAULT NULL COMMENT '续费价格',
  `transfer_price` decimal(10,2) DEFAULT NULL COMMENT '转移价格',
  `featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类',
  `keywords` text COLLATE utf8mb4_unicode_ci COMMENT '关键词',
  `views` int(11) DEFAULT '0' COMMENT '浏览次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名表';

--
-- 转存表中的数据 `domains`
--

INSERT INTO `domains` (`id`, `domain_name`, `tld`, `user_id`, `status`, `registration_date`, `expiry_date`, `auto_renew`, `privacy_protection`, `nameservers`, `registrar_order_id`, `price`, `renewal_price`, `transfer_price`, `featured`, `description`, `category`, `keywords`, `views`, `created_at`, `updated_at`) VALUES
(30, 'recvsa.xyz', 'xyz', 1, 'registered', NULL, '2026-07-22', 0, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-21 18:33:51', '2025-07-22 07:23:13'),
(31, '20cxcc.lol', '.lol', NULL, 'registered', '2024-07-30', '2025-07-30', 0, 1, NULL, NULL, '12.99', NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-22 07:30:41', '2025-07-22 07:30:41'),
(32, 'dnse.lol', '.lol', NULL, 'registered', '2024-04-16', '2025-04-16', 0, 1, NULL, NULL, '12.99', NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-22 07:30:41', '2025-07-22 07:30:41'),
(33, 'eoes.top', '.top', NULL, 'registered', '2024-05-13', '2025-05-13', 0, 1, NULL, NULL, '12.99', NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-22 07:30:41', '2025-07-22 07:30:41'),
(34, 'mtlaoqian.top', '.top', NULL, 'registered', '2024-08-09', '2025-08-09', 0, 1, NULL, NULL, '12.99', NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-22 07:30:41', '2025-07-22 07:30:41'),
(35, 'recvse.xyz', '.xyz', 15, 'registered', '2025-06-30', '2026-06-30', 0, 1, NULL, NULL, '12.99', NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-22 07:30:41', '2025-07-22 07:33:48'),
(36, 'seoe.buzz', '.buzz', NULL, 'registered', '2024-05-13', '2025-05-13', 0, 1, NULL, NULL, '12.99', NULL, NULL, 0, NULL, NULL, NULL, 0, '2025-07-22 07:30:41', '2025-07-22 07:30:41');

-- --------------------------------------------------------

--
-- 表的结构 `domain_categories`
--

CREATE TABLE IF NOT EXISTS `domain_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `domain_logs`
--

CREATE TABLE IF NOT EXISTS `domain_logs` (
  `id` int(11) NOT NULL,
  `domain_id` int(11) NOT NULL COMMENT '域名ID',
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名名称',
  `action` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '操作描述',
  `old_value` text COLLATE utf8mb4_unicode_ci COMMENT '原值',
  `new_value` text COLLATE utf8mb4_unicode_ci COMMENT '新值',
  `user_id` int(11) DEFAULT NULL COMMENT '操作用户ID',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名操作日志表';

-- --------------------------------------------------------

--
-- 表的结构 `domain_prices`
--

CREATE TABLE IF NOT EXISTS `domain_prices` (
  `id` int(11) NOT NULL,
  `tld` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '顶级域名',
  `registration_price` decimal(10,2) NOT NULL COMMENT '注册价格',
  `renewal_price` decimal(10,2) NOT NULL COMMENT '续费价格',
  `transfer_price` decimal(10,2) NOT NULL COMMENT '转移价格',
  `min_years` int(11) DEFAULT '1' COMMENT '最小年数',
  `max_years` int(11) DEFAULT '10' COMMENT '最大年数',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名价格表';

-- --------------------------------------------------------

--
-- 表的结构 `domain_providers`
--

CREATE TABLE IF NOT EXISTS `domain_providers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `api_url` varchar(255) DEFAULT NULL,
  `description` text,
  `status` enum('active','inactive') DEFAULT 'inactive',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

--
-- 转存表中的数据 `domain_providers`
--

INSERT INTO `domain_providers` (`id`, `name`, `code`, `api_url`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, 'NameSilo', 'namesilo', 'https://www.namesilo.com/api', 'NameSilo是一家知名的域名注册商，提供竞争力的价格和优质的服务。', 'active', '2025-07-20 10:00:48', '2025-07-20 10:00:48');

-- --------------------------------------------------------

--
-- 表的结构 `domain_registration_logs`
--

CREATE TABLE IF NOT EXISTS `domain_registration_logs` (
  `id` int(11) NOT NULL,
  `order_item_id` int(11) NOT NULL COMMENT '订单项ID',
  `success` tinyint(1) DEFAULT '0' COMMENT '是否成功',
  `namesilo_order_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'NameSilo订单ID',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `api_response` text COLLATE utf8mb4_unicode_ci COMMENT 'API响应',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名注册日志表';

--
-- 转存表中的数据 `domain_registration_logs`
--

INSERT INTO `domain_registration_logs` (`id`, `order_item_id`, `success`, `namesilo_order_id`, `error_message`, `api_response`, `retry_count`, `created_at`) VALUES
(1, 1, 1, 'TEST123456', NULL, NULL, 0, '2025-07-21 15:57:59');

-- --------------------------------------------------------

--
-- 表的结构 `domain_renewals`
--

CREATE TABLE IF NOT EXISTS `domain_renewals` (
  `id` int(11) NOT NULL,
  `domain_id` int(11) NOT NULL COMMENT '域名ID',
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名名称',
  `years` int(11) NOT NULL COMMENT '续费年数',
  `old_expiry_date` date DEFAULT NULL COMMENT '原到期日期',
  `new_expiry_date` date DEFAULT NULL COMMENT '新到期日期',
  `renewal_fee` decimal(10,2) NOT NULL COMMENT '续费费用',
  `auto_renew` tinyint(1) DEFAULT '0' COMMENT '是否自动续费',
  `payment_status` enum('pending','paid','failed','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '支付状态',
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式',
  `transaction_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易ID',
  `renewed_by` int(11) DEFAULT NULL COMMENT '续费操作人',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名续费记录表';

-- --------------------------------------------------------

--
-- 表的结构 `domain_templates`
--

CREATE TABLE IF NOT EXISTS `domain_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
  `dns_records` json DEFAULT NULL COMMENT 'DNS记录配置',
  `nameservers` json DEFAULT NULL COMMENT '名称服务器',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名模板表';

-- --------------------------------------------------------

--
-- 表的结构 `domain_tlds`
--

CREATE TABLE IF NOT EXISTS `domain_tlds` (
  `id` int(11) NOT NULL,
  `tld` varchar(50) NOT NULL COMMENT 'TLD后缀，如.com, .net',
  `registration_price` decimal(10,2) DEFAULT NULL COMMENT '注册价格',
  `renewal_price` decimal(10,2) DEFAULT NULL COMMENT '续费价格',
  `transfer_price` decimal(10,2) DEFAULT NULL COMMENT '转移价格',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `category` varchar(50) DEFAULT 'generic' COMMENT '分类：generic, country, new',
  `description` text COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=168 DEFAULT CHARSET=utf8mb4 COMMENT='域名TLD价格表';

--
-- 转存表中的数据 `domain_tlds`
--

INSERT INTO `domain_tlds` (`id`, `tld`, `registration_price`, `renewal_price`, `transfer_price`, `is_active`, `category`, `description`, `created_at`, `updated_at`) VALUES
(1, '.com', '12.99', '12.99', '12.99', 1, 'generic', '最受欢迎的通用顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(2, '.net', '14.99', '14.99', '14.99', 1, 'generic', '网络相关的通用顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(3, '.org', '13.99', '13.99', '13.99', 1, 'generic', '组织机构专用顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(4, '.info', '11.99', '11.99', '11.99', 1, 'generic', '信息类通用顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(5, '.biz', '15.99', '15.99', '15.99', 1, 'generic', '商业专用顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(6, '.us', '9.99', '9.99', '9.99', 1, 'country', '美国国家顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(7, '.cn', '8.99', '8.99', '8.99', 1, 'country', '中国国家顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(8, '.co', '29.99', '29.99', '29.99', 1, 'new', '新兴的通用顶级域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(9, '.io', '49.99', '49.99', '49.99', 1, 'new', '技术公司喜爱的域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(10, '.app', '19.99', '19.99', '19.99', 1, 'new', '应用程序专用域名', '2025-07-21 07:02:08', '2025-07-21 07:02:08'),
(11, 'com', '8.99', '8.99', '8.99', 1, 'generic', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(12, 'net', '10.99', '10.99', '10.99', 1, 'generic', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(13, 'org', '10.99', '10.99', '10.99', 1, 'generic', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(14, 'info', '2.99', '15.99', '15.99', 1, 'generic', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(15, 'biz', '15.99', '15.99', '15.99', 1, 'generic', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(16, 'us', '8.99', '8.99', '8.99', 1, 'country', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(17, 'co', '29.99', '29.99', '29.99', 1, 'new', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(18, 'io', '59.99', '59.99', '59.99', 1, 'new', NULL, '2025-07-21 09:48:05', '2025-07-21 10:39:59'),
(19, 'name', '9.99', '9.99', '9.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(20, 'mobi', '19.99', '19.99', '19.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(21, 'ca', '14.99', '14.99', '14.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(22, 'uk', '8.99', '8.99', '8.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(23, 'de', '8.99', '8.99', '8.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(24, 'fr', '9.99', '9.99', '9.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(25, 'it', '9.99', '9.99', '9.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(26, 'es', '9.99', '9.99', '9.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(27, 'nl', '9.99', '9.99', '9.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(28, 'be', '9.99', '9.99', '9.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(29, 'ch', '12.99', '12.99', '12.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(30, 'at', '14.99', '14.99', '14.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(31, 'au', '14.99', '14.99', '14.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(32, 'jp', '39.99', '39.99', '39.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(33, 'cn', '8.99', '8.99', '8.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(34, 'in', '9.99', '9.99', '9.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(35, 'br', '14.99', '14.99', '14.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(36, 'mx', '39.99', '39.99', '39.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(37, 'ru', '5.99', '5.99', '5.99', 1, 'country', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(38, 'me', '19.99', '19.99', '19.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(39, 'tv', '34.99', '34.99', '34.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(40, 'cc', '22.99', '22.99', '22.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(41, 'ws', '29.99', '29.99', '29.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(42, 'app', '19.99', '19.99', '19.99', 1, 'tech', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(43, 'dev', '14.99', '14.99', '14.99', 1, 'tech', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(44, 'tech', '49.99', '49.99', '49.99', 1, 'tech', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(45, 'online', '39.99', '39.99', '39.99', 1, 'tech', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(46, 'site', '29.99', '29.99', '29.99', 1, 'tech', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(47, 'website', '24.99', '24.99', '24.99', 1, 'tech', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(48, 'store', '59.99', '59.99', '59.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(49, 'shop', '39.99', '39.99', '39.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(50, 'blog', '29.99', '29.99', '29.99', 1, 'media', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(51, 'news', '24.99', '24.99', '24.99', 1, 'media', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(52, 'media', '34.99', '34.99', '34.99', 1, 'media', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(53, 'agency', '22.99', '22.99', '22.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(54, 'company', '9.99', '9.99', '9.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(55, 'business', '9.99', '9.99', '9.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(56, 'services', '34.99', '34.99', '34.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(57, 'solutions', '22.99', '22.99', '22.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(58, 'consulting', '34.99', '34.99', '34.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(59, 'management', '22.99', '22.99', '22.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(60, 'marketing', '34.99', '34.99', '34.99', 1, 'business', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(61, 'design', '49.99', '49.99', '49.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(62, 'studio', '24.99', '24.99', '24.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(63, 'photography', '22.99', '22.99', '22.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(64, 'gallery', '22.99', '22.99', '22.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(65, 'art', '14.99', '14.99', '14.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(66, 'music', '149.99', '149.99', '149.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(67, 'video', '24.99', '24.99', '24.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(68, 'film', '89.99', '89.99', '89.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(69, 'photo', '29.99', '29.99', '29.99', 1, 'creative', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(70, 'pics', '29.99', '29.99', '29.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(71, 'club', '19.99', '19.99', '19.99', 1, 'social', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(72, 'team', '29.99', '29.99', '29.99', 1, 'social', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(73, 'group', '22.99', '22.99', '22.99', 1, 'social', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(74, 'community', '34.99', '34.99', '34.99', 1, 'social', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(75, 'network', '22.99', '22.99', '22.99', 1, 'social', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(76, 'social', '34.99', '34.99', '34.99', 1, 'social', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(77, 'chat', '34.99', '34.99', '34.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(78, 'email', '24.99', '24.99', '24.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(79, 'support', '22.99', '22.99', '22.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(80, 'help', '29.99', '29.99', '29.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(81, 'center', '22.99', '22.99', '22.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(82, 'institute', '22.99', '22.99', '22.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(83, 'academy', '34.99', '34.99', '34.99', 1, 'education', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(84, 'education', '22.99', '22.99', '22.99', 1, 'education', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(85, 'university', '49.99', '49.99', '49.99', 1, 'education', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(86, 'college', '69.99', '69.99', '69.99', 1, 'education', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(87, 'school', '34.99', '34.99', '34.99', 1, 'education', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(88, 'training', '34.99', '34.99', '34.99', 1, 'education', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(89, 'courses', '39.99', '39.99', '39.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(90, 'health', '79.99', '79.99', '79.99', 1, 'health', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(91, 'fitness', '34.99', '34.99', '34.99', 1, 'health', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(92, 'care', '34.99', '34.99', '34.99', 1, 'health', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(93, 'clinic', '49.99', '49.99', '49.99', 1, 'health', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(94, 'dental', '49.99', '49.99', '49.99', 1, 'health', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(95, 'doctor', '99.99', '99.99', '99.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(96, 'lawyer', '49.99', '49.99', '49.99', 1, 'legal', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(97, 'attorney', '49.99', '49.99', '49.99', 1, 'legal', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(98, 'legal', '49.99', '49.99', '49.99', 1, 'legal', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(99, 'law', '99.99', '99.99', '99.99', 1, 'legal', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(100, 'finance', '49.99', '49.99', '49.99', 1, 'finance', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(101, 'financial', '49.99', '49.99', '49.99', 1, 'finance', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(102, 'money', '34.99', '34.99', '34.99', 1, 'finance', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(103, 'bank', '999.99', '999.99', '999.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(104, 'insurance', '499.99', '499.99', '499.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(105, 'loan', '29.99', '29.99', '29.99', 1, 'finance', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(106, 'credit', '99.99', '99.99', '99.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(107, 'tax', '49.99', '49.99', '49.99', 1, 'finance', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(108, 'accountant', '29.99', '29.99', '29.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(109, 'restaurant', '49.99', '49.99', '49.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(110, 'food', '39.99', '39.99', '39.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(111, 'kitchen', '49.99', '49.99', '49.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(112, 'cafe', '34.99', '34.99', '34.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(113, 'bar', '79.99', '79.99', '79.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(114, 'wine', '49.99', '49.99', '49.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(115, 'beer', '29.99', '29.99', '29.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(116, 'pizza', '49.99', '49.99', '49.99', 1, 'food', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(117, 'delivery', '49.99', '49.99', '49.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(118, 'catering', '34.99', '34.99', '34.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(119, 'hotel', '39.99', '39.99', '39.99', 1, 'travel', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(120, 'travel', '119.99', '119.99', '119.99', 1, 'travel', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(121, 'vacation', '29.99', '29.99', '29.99', 1, 'travel', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(122, 'holiday', '49.99', '49.99', '49.99', 1, 'travel', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(123, 'tours', '49.99', '49.99', '49.99', 1, 'travel', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(124, 'flights', '49.99', '49.99', '49.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(125, 'cruise', '49.99', '49.99', '49.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(126, 'taxi', '49.99', '49.99', '49.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(127, 'car', '299.99', '299.99', '299.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(128, 'auto', '299.99', '299.99', '299.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(129, 'cars', '199.99', '199.99', '199.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(130, 'bike', '34.99', '34.99', '34.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(131, 'motorcycles', '14.99', '14.99', '14.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(132, 'boats', '14.99', '14.99', '14.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(133, 'yachts', '14.99', '14.99', '14.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(134, 'games', '22.99', '22.99', '22.99', 1, 'gaming', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(135, 'game', '449.99', '449.99', '449.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(136, 'casino', '149.99', '149.99', '149.99', 1, 'gaming', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(137, 'poker', '59.99', '59.99', '59.99', 1, 'gaming', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(138, 'bet', '19.99', '19.99', '19.99', 1, 'gaming', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(139, 'sport', '449.99', '449.99', '449.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(140, 'sports', '34.99', '34.99', '34.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(141, 'football', '19.99', '19.99', '19.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(142, 'soccer', '19.99', '19.99', '19.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(143, 'golf', '49.99', '49.99', '49.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(144, 'tennis', '49.99', '49.99', '49.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(145, 'hockey', '49.99', '49.99', '49.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(146, 'baseball', '49.99', '49.99', '49.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(147, 'basketball', '49.99', '49.99', '49.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(148, 'racing', '29.99', '29.99', '29.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(149, 'run', '19.99', '19.99', '19.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(150, 'yoga', '29.99', '29.99', '29.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(151, 'dance', '24.99', '24.99', '24.99', 1, 'sports', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(152, 'fashion', '29.99', '29.99', '29.99', 1, 'fashion', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(153, 'style', '29.99', '29.99', '29.99', 1, 'fashion', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(154, 'beauty', '14.99', '14.99', '14.99', 1, 'fashion', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(155, 'hair', '14.99', '14.99', '14.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(156, 'makeup', '14.99', '14.99', '14.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(157, 'skin', '14.99', '14.99', '14.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(158, 'spa', '29.99', '29.99', '29.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(159, 'salon', '49.99', '49.99', '49.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(160, 'jewelry', '49.99', '49.99', '49.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(161, 'diamonds', '49.99', '49.99', '49.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(162, 'gold', '99.99', '99.99', '99.99', 1, 'generic', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(163, 'silver', '29.99', '29.99', '29.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(164, 'watch', '34.99', '34.99', '34.99', 1, 'new', NULL, '2025-07-21 10:04:00', '2025-07-21 10:04:00'),
(165, 'clothing', '29.99', '29.99', '29.99', 1, 'fashion', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(166, 'shoes', '49.99', '49.99', '49.99', 1, 'fashion', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59'),
(167, 'boutique', '29.99', '29.99', '29.99', 1, 'fashion', NULL, '2025-07-21 10:04:00', '2025-07-21 10:39:59');

-- --------------------------------------------------------

--
-- 表的结构 `domain_transfers`
--

CREATE TABLE IF NOT EXISTS `domain_transfers` (
  `id` int(11) NOT NULL,
  `domain_id` int(11) NOT NULL COMMENT '域名ID',
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名名称',
  `auth_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '转移码',
  `current_registrar` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前注册商',
  `new_registrar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标注册商',
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系邮箱',
  `status` enum('pending','approved','rejected','completed','cancelled','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '转移状态',
  `transfer_fee` decimal(10,2) DEFAULT NULL COMMENT '转移费用',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `initiated_by` int(11) DEFAULT NULL COMMENT '发起人用户ID',
  `approved_at` timestamp NULL DEFAULT NULL COMMENT '批准时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名转移表';

-- --------------------------------------------------------

--
-- 表的结构 `domain_transfer_logs`
--

CREATE TABLE IF NOT EXISTS `domain_transfer_logs` (
  `id` int(11) NOT NULL,
  `domain_id` int(11) NOT NULL,
  `from_user_id` int(11) DEFAULT NULL,
  `to_user_id` int(11) NOT NULL,
  `reason` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `domain_transfer_logs`
--

INSERT INTO `domain_transfer_logs` (`id`, `domain_id`, `from_user_id`, `to_user_id`, `reason`, `created_at`) VALUES
(1, 27, NULL, 15, '', '2025-07-20 19:16:13');

-- --------------------------------------------------------

--
-- 表的结构 `email_logs`
--

CREATE TABLE IF NOT EXISTS `email_logs` (
  `id` int(11) NOT NULL,
  `recipient` varchar(255) NOT NULL,
  `subject` varchar(500) NOT NULL,
  `content` text,
  `status` enum('sent','failed') NOT NULL DEFAULT 'sent',
  `error_message` text,
  `sent_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `email_queue`
--

CREATE TABLE IF NOT EXISTS `email_queue` (
  `id` int(11) NOT NULL,
  `to_email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收件人邮箱',
  `to_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收件人姓名',
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件主题',
  `body` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件内容',
  `template` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮件模板',
  `template_data` json DEFAULT NULL COMMENT '模板数据',
  `priority` int(11) DEFAULT '5' COMMENT '优先级',
  `attempts` int(11) DEFAULT '0' COMMENT '尝试次数',
  `max_attempts` int(11) DEFAULT '3' COMMENT '最大尝试次数',
  `status` enum('pending','processing','sent','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '状态',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `scheduled_at` timestamp NULL DEFAULT NULL COMMENT '计划发送时间',
  `sent_at` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件队列表';

-- --------------------------------------------------------

--
-- 表的结构 `email_templates`
--

CREATE TABLE IF NOT EXISTS `email_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `subject` varchar(500) NOT NULL,
  `content` text NOT NULL,
  `variables` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `logs`
--

CREATE TABLE IF NOT EXISTS `logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作',
  `resource_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型',
  `resource_id` int(11) DEFAULT NULL COMMENT '资源ID',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `request_data` json DEFAULT NULL COMMENT '请求数据',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB AUTO_INCREMENT=316 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

--
-- 转存表中的数据 `logs`
--

INSERT INTO `logs` (`id`, `user_id`, `action`, `resource_type`, `resource_id`, `ip_address`, `user_agent`, `request_data`, `response_data`, `created_at`) VALUES
(1, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'curl/8.14.1', '{"domain": "example.com"}', NULL, '2025-07-19 10:36:10'),
(2, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'curl/8.14.1', '{"keyword": "test"}', NULL, '2025-07-19 10:36:33'),
(3, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "path": "/create", "error": "订单不存在或状态不正确"}', NULL, '2025-07-19 10:49:38'),
(4, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "path": "/create", "error": "订单不存在或状态不正确"}', NULL, '2025-07-19 10:49:43'),
(5, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "path": "/create", "error": "订单不存在或状态不正确"}', NULL, '2025-07-19 10:49:57'),
(6, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'curl/8.14.1', '{"code": 400, "path": "/create", "error": "参数错误"}', NULL, '2025-07-19 10:57:06'),
(7, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "example.com"}', NULL, '2025-07-19 11:12:11'),
(8, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "example.com"}', NULL, '2025-07-19 11:14:42'),
(9, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "example.net"}', NULL, '2025-07-19 12:59:26'),
(10, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 14:04:21'),
(11, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 14:04:31'),
(12, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 14:06:51'),
(13, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 14:07:02'),
(14, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 14:07:17'),
(15, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 14:07:45'),
(16, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 14:09:48'),
(17, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 14:10:04'),
(18, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 14:17:23'),
(19, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 14:17:38'),
(20, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 14:24:20'),
(21, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 14:24:40'),
(22, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "qq.com"}', NULL, '2025-07-19 16:15:40'),
(23, NULL, 'domain_suggest', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"keyword": "qq"}', NULL, '2025-07-19 16:15:55'),
(24, NULL, 'email_sent', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"to": "<EMAIL>", "subject": "验证您的邮箱地址 - NameSilo域名销售", "message_length": 750}', NULL, '2025-07-19 17:13:21'),
(25, NULL, 'register_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"name": "张 克", "email": "<EMAIL>"}', NULL, '2025-07-19 17:13:21'),
(26, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', '{"code": 400, "error": "未知的API操作", "action": "test"}', NULL, '2025-07-19 17:28:43'),
(27, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-19 17:28:43'),
(28, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 18:11:58'),
(29, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 18:39:25'),
(30, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 18:39:26'),
(31, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 18:39:27'),
(32, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 18:39:28'),
(33, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 18:39:29'),
(34, NULL, 'admin_proxy_login', 'admin', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"admin_id": 1, "target_user": "张克"}', NULL, '2025-07-19 18:52:59'),
(35, NULL, 'admin_proxy_login', 'admin', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"admin_id": 1, "target_user": "张克"}', NULL, '2025-07-19 19:03:03'),
(36, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:19:47'),
(37, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:19:49'),
(38, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:19:50'),
(39, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:34:56'),
(40, NULL, 'email_sent', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"to": "<EMAIL>", "subject": "验证您的邮箱地址 - NameSilo域名销售", "message_length": 747}', NULL, '2025-07-19 19:35:57'),
(41, 15, 'register_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"name": " 克", "email": "<EMAIL>"}', NULL, '2025-07-19 19:35:57'),
(42, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:42:21'),
(43, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:43:43'),
(44, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:50:08'),
(45, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"email": "<EMAIL>"}', NULL, '2025-07-19 19:50:16'),
(46, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', '{"email": "<EMAIL>"}', NULL, '2025-07-19 20:11:01'),
(47, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Mobile Safari/537.36', NULL, NULL, '2025-07-19 20:15:30'),
(48, 15, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 04:43:22'),
(49, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 04:47:59'),
(50, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 05:00:51'),
(51, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 06:44:55'),
(52, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 06:53:31'),
(53, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:22:27'),
(54, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:22:32'),
(55, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:16'),
(56, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:17'),
(57, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:18'),
(58, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:18'),
(59, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:19'),
(60, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:19'),
(61, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:19'),
(62, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:19'),
(63, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:19'),
(64, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:19'),
(65, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:20'),
(66, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:26:21'),
(67, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:24'),
(68, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:27'),
(69, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:28'),
(70, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:30'),
(71, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:30'),
(72, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:31'),
(73, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:31'),
(74, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:32'),
(75, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:32'),
(76, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:32'),
(77, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:32'),
(78, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:26:33'),
(79, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:05'),
(80, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:06'),
(81, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:06'),
(82, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:07'),
(83, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:07'),
(84, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:07'),
(85, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:07'),
(86, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:07'),
(87, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:10'),
(88, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:10'),
(89, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:10'),
(90, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:11'),
(91, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:11'),
(92, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:11'),
(93, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:11'),
(94, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:11'),
(95, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:37'),
(96, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:27:44'),
(97, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:27:59'),
(98, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:00'),
(99, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:00'),
(100, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:00'),
(101, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:01'),
(102, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:01'),
(103, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:01'),
(104, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:02'),
(105, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:02'),
(106, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:02'),
(107, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:02'),
(108, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:02'),
(109, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:03'),
(110, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:03'),
(111, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:03'),
(112, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:03'),
(113, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:04'),
(114, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:04'),
(115, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:05'),
(116, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:05'),
(117, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:06'),
(118, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:07'),
(119, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:07'),
(120, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:07'),
(121, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:07'),
(122, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:08'),
(123, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:08'),
(124, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:08'),
(125, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:08'),
(126, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:08'),
(127, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:10'),
(128, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:10'),
(129, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:10'),
(130, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:10'),
(131, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:10'),
(132, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:10'),
(133, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:12'),
(134, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:12'),
(135, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:12'),
(136, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:12'),
(137, NULL, 'logout', 'auth', NULL, '127.0.0.1', '', NULL, NULL, '2025-07-20 07:28:12'),
(138, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:15'),
(139, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:17'),
(140, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:17'),
(141, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:18'),
(142, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:18'),
(143, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:18'),
(144, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:19'),
(145, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:19'),
(146, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:21'),
(147, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:21'),
(148, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:39'),
(149, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:28:56'),
(150, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:29:02'),
(151, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:29:03'),
(152, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:29:14'),
(153, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:29:25'),
(154, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:29:38'),
(155, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:30:22'),
(156, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:30:31'),
(157, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:30:47'),
(158, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:11'),
(159, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:22'),
(160, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:25'),
(161, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:32'),
(162, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:35'),
(163, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:38'),
(164, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:49'),
(165, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:56'),
(166, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:56'),
(167, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:56'),
(168, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:57'),
(169, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:57'),
(170, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:57'),
(171, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:57'),
(172, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:57'),
(173, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:58'),
(174, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:58'),
(175, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:58'),
(176, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:31:58'),
(177, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 07:32:01'),
(178, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:32:34'),
(179, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:32:37'),
(180, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:53:59'),
(181, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:56:27'),
(182, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:58:27'),
(183, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 07:59:12'),
(184, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 07:59:16'),
(185, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:05:52'),
(186, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:10:57'),
(187, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:11:02'),
(188, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:13:55'),
(189, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:16:58'),
(190, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:17:02'),
(191, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:17:04'),
(192, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:17:05'),
(193, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:17:12'),
(194, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:17:13'),
(195, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:22:12'),
(196, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:22:17'),
(197, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:22:59'),
(198, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:23:05'),
(199, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:28:00'),
(200, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:28:04'),
(201, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:31:55'),
(202, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:32:00'),
(203, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:33:31'),
(204, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:33:36'),
(205, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'API-Test-Tool/1.0', NULL, NULL, '2025-07-20 08:42:16'),
(206, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', 'API-Test-Tool/1.0', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 08:42:23'),
(207, 16, 'admin_proxy_login', 'admin', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"admin_id": 1, "target_user": "testuser"}', NULL, '2025-07-20 09:21:41'),
(208, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "error": "未知的API操作: test-connection", "action": "test-connection"}', NULL, '2025-07-20 10:01:59'),
(209, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "error": "未知的API操作: test-connection", "action": "test-connection"}', NULL, '2025-07-20 10:03:18'),
(210, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "error": "未知的API操作: test-connection", "action": "test-connection"}', NULL, '2025-07-20 10:03:26'),
(211, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "error": "未知的API操作: test-connection", "action": "test-connection"}', NULL, '2025-07-20 10:04:00'),
(212, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 400, "error": "未知的API操作: test-connection", "action": "test-connection"}', NULL, '2025-07-20 10:04:02'),
(213, NULL, 'domain_check', 'domain', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"domain": "test-domain-12345.com"}', NULL, '2025-07-20 10:06:54'),
(214, NULL, 'sync_prices', 'system', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"added": 0, "total": 0, "updated": 0}', NULL, '2025-07-20 10:09:53'),
(215, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": "test-connection"}', NULL, '2025-07-20 10:09:59'),
(216, NULL, 'payment_created', 'payment', NULL, '127.0.0.1', '', '{"amount": 0.01, "order_id": 3, "payment_method": "alipay"}', NULL, '2025-07-20 10:43:03'),
(217, NULL, 'payment_created', 'payment', NULL, '127.0.0.1', '', '{"amount": 0.01, "order_id": 4, "payment_method": "alipay"}', NULL, '2025-07-20 10:44:36'),
(218, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 10:46:41'),
(219, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 10:51:21'),
(220, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 10:51:37'),
(221, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 10:52:50'),
(222, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 10:52:53'),
(223, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 10:52:55'),
(224, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 10:52:56'),
(225, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 11:01:28'),
(226, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 11:08:29'),
(227, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 11:13:51'),
(228, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 11:14:07'),
(229, NULL, 'payment_api_error', 'api', NULL, '127.0.0.1', '', '{"code": 404, "path": "", "error": "API路径不存在"}', NULL, '2025-07-20 11:14:53'),
(230, NULL, 'payment_created', 'payment', NULL, '127.0.0.1', '', '{"amount": 0.01, "order_id": 5, "payment_method": "alipay"}', NULL, '2025-07-20 12:03:04'),
(231, NULL, 'payment_created', 'payment', NULL, '127.0.0.1', '', '{"amount": 0.01, "order_id": 6, "payment_method": "alipay"}', NULL, '2025-07-20 12:04:43'),
(232, NULL, 'payment_created', 'payment', NULL, '127.0.0.1', '', '{"amount": 0.01, "order_id": 7, "payment_method": "alipay"}', NULL, '2025-07-20 12:31:24'),
(233, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 13:47:42'),
(234, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-20 14:25:47'),
(235, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 14:25:57'),
(236, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 14:30:39'),
(237, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 14:30:41'),
(238, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 14:30:46'),
(239, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 14:30:51'),
(240, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-20 17:59:31'),
(241, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 18:53:36'),
(242, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-20 19:11:56'),
(243, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 19:31:58'),
(244, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 19:32:00'),
(245, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 19:42:57'),
(246, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-20 19:43:00'),
(247, NULL, 'sync_prices', 'system', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"added": 0, "total": 0, "updated": 0}', NULL, '2025-07-21 04:19:04'),
(248, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:19:45'),
(249, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:27:35'),
(250, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:27:53'),
(251, NULL, 'sync_prices', 'system', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"added": 4, "total": 20, "updated": 16}', NULL, '2025-07-21 04:46:26'),
(252, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:46:35'),
(253, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:46:43'),
(254, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:53:48'),
(255, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 04:54:47'),
(256, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 05:04:20'),
(257, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 06:36:22'),
(258, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 06:40:16'),
(259, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: NameSilo API密钥未配置", "action": "sync-domains"}', NULL, '2025-07-21 06:48:35'),
(260, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 06:49:50'),
(261, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 500, "error": "域名同步失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column ''expires_at'' in ''field list''", "action": "sync-domains"}', NULL, '2025-07-21 07:23:16'),
(262, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 11:55:49'),
(263, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 12:03:29'),
(264, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 12:03:43'),
(265, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 12:03:45');
INSERT INTO `logs` (`id`, `user_id`, `action`, `resource_type`, `resource_id`, `ip_address`, `user_agent`, `request_data`, `response_data`, `created_at`) VALUES
(266, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 12:03:45'),
(267, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 12:04:01'),
(268, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 13:03:10'),
(269, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'curl/8.14.1', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:09:24'),
(270, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:28'),
(271, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:32'),
(272, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:33'),
(273, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:34'),
(274, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:34'),
(275, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:34'),
(276, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:40'),
(277, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:40'),
(278, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:40'),
(279, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:41'),
(280, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:41'),
(281, NULL, 'login_failed', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"email": "<EMAIL>"}', NULL, '2025-07-21 17:14:41'),
(282, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 17:19:55'),
(283, 15, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 17:20:03'),
(284, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 17:23:07'),
(285, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 17:23:09'),
(286, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 17:23:30'),
(287, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 17:25:48'),
(288, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:26:33'),
(289, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:18'),
(290, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:20'),
(291, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:21'),
(292, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:21'),
(293, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:21'),
(294, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:22'),
(295, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:22'),
(296, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:29'),
(297, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:30'),
(298, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:30'),
(299, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:30:30'),
(300, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:31:59'),
(301, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:39:22'),
(302, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:39:24'),
(303, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:39:27'),
(304, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:39:30'),
(305, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:51:15'),
(306, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', '{"code": 405, "error": "Method not allowed", "action": ""}', NULL, '2025-07-21 17:51:16'),
(307, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-21 18:18:29'),
(308, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'curl/8.14.1', '{"code": 500, "error": "域名不存在", "action": "info"}', NULL, '2025-07-21 19:38:58'),
(309, NULL, 'api_error', 'api', NULL, '127.0.0.1', 'curl/8.14.1', '{"code": 500, "error": "域名不存在", "action": "info"}', NULL, '2025-07-21 19:39:12'),
(310, 1, 'domain_renewal', 'domain', 30, NULL, NULL, '{"years": 1, "amount": 12.99, "domain_id": "30", "new_expiry": "2026-07-22", "old_expiry": "2025-07-22", "domain_name": "recvsa.xyz"}', NULL, '2025-07-22 07:23:13'),
(311, 1, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-22 07:31:35'),
(312, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-22 07:31:35'),
(313, 15, 'login_success', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-22 07:31:42'),
(314, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-22 09:27:28'),
(315, NULL, 'logout', 'auth', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.530.400 QQBrowser/19.3.6526.400', NULL, NULL, '2025-07-22 09:27:29');

-- --------------------------------------------------------

--
-- 表的结构 `namesilo_config`
--

CREATE TABLE IF NOT EXISTS `namesilo_config` (
  `id` int(11) NOT NULL,
  `api_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API密钥',
  `sandbox_mode` tinyint(1) DEFAULT '1' COMMENT '沙盒模式',
  `auto_register` tinyint(1) DEFAULT '1' COMMENT '自动注册',
  `auto_renew` tinyint(1) DEFAULT '1' COMMENT '自动续费',
  `privacy_protection` tinyint(1) DEFAULT '1' COMMENT '隐私保护',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='NameSilo配置表';

--
-- 转存表中的数据 `namesilo_config`
--

INSERT INTO `namesilo_config` (`id`, `api_key`, `sandbox_mode`, `auto_register`, `auto_renew`, `privacy_protection`, `created_at`, `updated_at`) VALUES
(1, 'your_namesilo_api_key_here', 1, 1, 1, 1, '2025-07-21 15:57:59', '2025-07-21 15:57:59');

-- --------------------------------------------------------

--
-- 表的结构 `notifications`
--

CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知类型',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `data` json DEFAULT NULL COMMENT '附加数据',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

--
-- 转存表中的数据 `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `type`, `title`, `message`, `data`, `read_at`, `created_at`) VALUES
(1, 2, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(2, 4, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(3, 5, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(4, 6, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(5, 7, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(6, 8, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(7, 9, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(8, 10, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(9, 11, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(10, 12, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(11, 13, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(12, 15, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(13, 16, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06'),
(14, 1, 'info', '1', '1', NULL, NULL, '2025-07-21 14:09:06');

-- --------------------------------------------------------

--
-- 表的结构 `orders`
--

CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL,
  `order_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `status` enum('pending','processing','completed','cancelled','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '订单状态',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'CNY' COMMENT '货币',
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式',
  `payment_status` enum('pending','paid','failed','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '支付状态',
  `payment_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付ID',
  `payment_data` json DEFAULT NULL COMMENT '支付数据',
  `billing_address` json DEFAULT NULL COMMENT '账单地址',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

--
-- 转存表中的数据 `orders`
--

INSERT INTO `orders` (`id`, `order_number`, `user_id`, `status`, `total_amount`, `currency`, `payment_method`, `payment_status`, `payment_id`, `payment_data`, `billing_address`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'ORD20250720001', 1, 'completed', '99.99', 'CNY', NULL, 'paid', NULL, NULL, NULL, NULL, '2025-07-20 08:16:44', '2025-07-20 08:16:44'),
(2, 'ORD20250720002', 1, 'pending', '199.98', 'CNY', NULL, 'pending', NULL, NULL, NULL, NULL, '2025-07-20 08:16:44', '2025-07-20 08:16:44'),
(3, 'TEST202507201843036226', 1, 'pending', '0.01', 'CNY', 'alipay', 'pending', NULL, NULL, NULL, NULL, '2025-07-20 10:43:03', '2025-07-20 10:43:03'),
(4, 'TEST202507201844368514', 1, 'pending', '0.01', 'CNY', 'alipay', 'pending', NULL, NULL, NULL, NULL, '2025-07-20 10:44:36', '2025-07-20 10:44:36'),
(5, 'TEST202507202003045116', 1, 'pending', '0.01', 'CNY', 'alipay', 'pending', NULL, NULL, NULL, NULL, '2025-07-20 12:03:04', '2025-07-20 12:03:04'),
(6, 'TEST202507202004431747', 1, 'pending', '0.01', 'CNY', 'alipay', 'pending', NULL, NULL, NULL, NULL, '2025-07-20 12:04:43', '2025-07-20 12:04:43'),
(7, 'TEST202507202031244684', 1, 'pending', '0.01', 'CNY', 'alipay', 'pending', NULL, NULL, NULL, NULL, '2025-07-20 12:31:24', '2025-07-20 12:31:24');

-- --------------------------------------------------------

--
-- 表的结构 `order_items`
--

CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `domain_id` int(11) DEFAULT NULL COMMENT '域名ID',
  `domain_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名',
  `item_type` enum('registration','renewal','transfer','privacy') COLLATE utf8mb4_unicode_ci DEFAULT 'registration' COMMENT '项目类型',
  `years` int(11) DEFAULT '1' COMMENT '年数',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单项表';

--
-- 转存表中的数据 `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `domain_id`, `domain_name`, `item_type`, `years`, `price`, `created_at`) VALUES
(1, 1, NULL, 'example.com', 'registration', 1, '99.99', '2025-07-20 08:16:44'),
(2, 2, NULL, 'test.com', 'registration', 1, '99.99', '2025-07-20 08:16:44'),
(3, 2, NULL, 'demo.net', 'registration', 1, '99.99', '2025-07-20 08:16:44'),
(4, 3, NULL, 'test.com', 'registration', 1, '0.01', '2025-07-20 10:43:03'),
(5, 4, NULL, 'test.com', 'registration', 1, '0.01', '2025-07-20 10:44:36'),
(6, 5, NULL, 'test.com', 'registration', 1, '0.01', '2025-07-20 12:03:04'),
(7, 6, NULL, 'test.com', 'registration', 1, '0.01', '2025-07-20 12:04:43'),
(8, 7, NULL, 'test.com', 'registration', 1, '0.01', '2025-07-20 12:31:24');

-- --------------------------------------------------------

--
-- 表的结构 `payments`
--

CREATE TABLE IF NOT EXISTS `payments` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `payment_method` varchar(20) NOT NULL,
  `payment_number` varchar(50) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','success','failed','cancelled') DEFAULT 'pending',
  `trade_no` varchar(100) DEFAULT NULL,
  `notify_data` json DEFAULT NULL,
  `paid_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

--
-- 转存表中的数据 `payments`
--

INSERT INTO `payments` (`id`, `order_id`, `payment_method`, `payment_number`, `amount`, `status`, `trade_no`, `notify_data`, `paid_at`, `created_at`, `updated_at`) VALUES
(1, 3, 'alipay', 'PAY202507201843035127', '0.01', 'pending', NULL, NULL, NULL, '2025-07-20 10:43:03', '2025-07-20 10:43:03'),
(2, 4, 'alipay', 'PAY202507201844361208', '0.01', 'pending', NULL, NULL, NULL, '2025-07-20 10:44:36', '2025-07-20 10:44:36'),
(3, 5, 'alipay', 'PAY202507202003048320', '0.01', 'pending', NULL, NULL, NULL, '2025-07-20 12:03:04', '2025-07-20 12:03:04'),
(4, 6, 'alipay', 'PAY202507202004432083', '0.01', 'pending', NULL, NULL, NULL, '2025-07-20 12:04:43', '2025-07-20 12:04:43'),
(5, 7, 'alipay', 'PAY202507202031241472', '0.01', 'pending', NULL, NULL, NULL, '2025-07-20 12:31:24', '2025-07-20 12:31:24');

-- --------------------------------------------------------

--
-- 表的结构 `payment_config`
--

CREATE TABLE IF NOT EXISTS `payment_config` (
  `id` int(11) NOT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式',
  `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT '是否加密',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付配置表';

--
-- 转存表中的数据 `payment_config`
--

INSERT INTO `payment_config` (`id`, `payment_method`, `config_key`, `config_value`, `is_encrypted`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'alipay', 'app_id', '2021000000000000', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(2, 'alipay', 'private_key', 'your_private_key_here', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(3, 'alipay', 'public_key', 'alipay_public_key_here', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(4, 'alipay', 'sandbox_mode', '1', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(5, 'wechat', 'app_id', 'wx1234567890abcdef', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(6, 'wechat', 'mch_id', '1234567890', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(7, 'wechat', 'api_key', 'your_wechat_api_key_here', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(8, 'wechat', 'sandbox_mode', '1', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(9, 'paypal', 'client_id', 'your_paypal_client_id', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(10, 'paypal', 'client_secret', 'your_paypal_client_secret', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(11, 'paypal', 'sandbox_mode', '1', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(12, 'card', 'processor', 'stripe', 0, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(13, 'card', 'public_key', 'pk_test_1234567890', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49'),
(14, 'card', 'secret_key', 'sk_test_1234567890', 1, 1, '2025-07-21 16:00:49', '2025-07-21 16:00:49');

-- --------------------------------------------------------

--
-- 表的结构 `payment_configs`
--

CREATE TABLE IF NOT EXISTS `payment_configs` (
  `id` int(11) NOT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式: wechat, alipay, epay',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式名称',
  `enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用',
  `config` json DEFAULT NULL COMMENT '支付配置参数',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `payment_configs`
--

INSERT INTO `payment_configs` (`id`, `payment_method`, `name`, `enabled`, `config`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'wechat', '微信支付', 1, '{"app_id": "admin", "mch_id": "", "api_key": "admin123", "key_path": "", "cert_path": "", "notify_url": "/api/payment/notify/wechat"}', 1, '2025-07-19 10:58:48', '2025-07-19 10:59:26'),
(3, 'epay', '易支付', 0, '{"key": "", "pid": "", "api_url": "https://pay.example.com/", "notify_url": "/api/payment/notify/epay", "return_url": "/payment/return/epay"}', 3, '2025-07-19 10:58:48', '2025-07-19 10:58:48'),
(7, 'alipay', '支付宝', 1, '{"md5_key": "dr5kh1ozzf8k78o5wjkq06727yuk6hal", "sign_type": "MD5", "transport": "https", "notify_url": "/api/payment/notify/alipay", "partner_id": "2088532725053770", "return_url": "/payment/return/alipay", "environment": "production", "gateway_url": "https://mapi.alipay.com/gateway.do", "seller_email": "<EMAIL>", "input_charset": "utf-8"}', 0, '2025-07-20 12:26:25', '2025-07-20 12:31:15');

-- --------------------------------------------------------

--
-- 表的结构 `payment_records`
--

CREATE TABLE IF NOT EXISTS `payment_records` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_gateway` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'USD',
  `status` enum('pending','success','failed','cancelled','refunded') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `gateway_response` json DEFAULT NULL,
  `gateway_transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `refund_records`
--

CREATE TABLE IF NOT EXISTS `refund_records` (
  `id` int(11) NOT NULL,
  `payment_record_id` int(11) NOT NULL COMMENT '支付记录ID',
  `refund_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '退款ID',
  `amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '退款原因',
  `status` enum('pending','completed','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '退款状态',
  `gateway_response` text COLLATE utf8mb4_unicode_ci COMMENT '网关响应',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- --------------------------------------------------------

--
-- 表的结构 `sessions`
--

CREATE TABLE IF NOT EXISTS `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `payload` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话数据',
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';

-- --------------------------------------------------------

--
-- 表的结构 `settings`
--

CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设置键',
  `setting_value` text COLLATE utf8mb4_unicode_ci COMMENT '设置值',
  `setting_type` enum('string','integer','boolean','json') COLLATE utf8mb4_unicode_ci DEFAULT 'string' COMMENT '设置类型',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

--
-- 转存表中的数据 `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'EIEMLDR', 'string', '网站名称', '2025-07-19 10:43:29', '2025-07-19 11:05:56'),
(2, 'site_description', 'NameSilo专业域名注册服务，提供.com、.cn、.net等热门域名注册，价格优惠，服务专业', 'string', '网站描述', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(3, 'site_keywords', '域名注册,域名购买,NameSilo,域名服务,网站域名', 'string', '网站关键词', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(4, 'admin_email', '<EMAIL>', 'string', '管理员邮箱', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(5, 'currency', 'CNY', 'string', '货币代码', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(6, 'currency_symbol', '¥', 'string', '货币符号', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(7, 'domain_markup', '0.15', 'string', '域名价格加成比例', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(8, 'enable_registration', '1', 'boolean', '是否允许用户注册', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(9, 'enable_cart', '1', 'boolean', '是否启用购物车功能', '2025-07-19 10:43:29', '2025-07-19 11:05:57'),
(10, 'enable_email_verification', '1', 'boolean', '是否启用邮箱验证', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(11, 'enable_phone_verification', '0', 'boolean', '是否启用手机验证', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(12, 'max_cart_items', '50', 'integer', '购物车最大商品数量', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(13, 'session_lifetime', '120', 'integer', '会话生存时间（分钟）', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(14, 'password_min_length', '6', 'integer', '密码最小长度', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(15, 'upload_max_size', '10240', 'integer', '文件上传最大大小（KB）', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(16, 'backup_enabled', '1', 'boolean', '是否启用备份', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(17, 'backup_retention_days', '30', 'integer', '备份保留天数', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(18, 'rate_limit_api', '60', 'integer', 'API请求频率限制（每分钟）', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(19, 'rate_limit_login', '5', 'integer', '登录频率限制（每分钟）', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(20, 'maintenance_mode', '0', 'boolean', '维护模式', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(21, 'maintenance_message', '系统维护中，请稍后访问', 'string', '维护模式提示信息', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(22, 'analytics_enabled', '1', 'boolean', '是否启用统计分析', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(23, 'social_login_enabled', '0', 'boolean', '是否启用社交登录', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(24, 'notification_email', '1', 'boolean', '是否启用邮件通知', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(25, 'notification_sms', '0', 'boolean', '是否启用短信通知', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(26, 'auto_renew_enabled', '1', 'boolean', '是否启用自动续费', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(27, 'privacy_protection_default', '1', 'boolean', '默认启用隐私保护', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(28, 'transfer_lock_default', '1', 'boolean', '默认启用转移锁定', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(29, 'whois_privacy_default', '1', 'boolean', '默认启用WHOIS隐私', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(30, 'domain_search_limit', '100', 'integer', '域名搜索结果限制', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(31, 'order_timeout_minutes', '30', 'integer', '订单超时时间（分钟）', '2025-07-19 10:43:29', '2025-07-19 10:43:29'),
(32, 'payment_timeout_minutes', '15', 'integer', '支付超时时间（分钟）', '2025-07-19 10:43:29', '2025-07-19 10:43:29');

-- --------------------------------------------------------

--
-- 表的结构 `statistics`
--

CREATE TABLE IF NOT EXISTS `statistics` (
  `id` int(11) NOT NULL,
  `date` date NOT NULL COMMENT '统计日期',
  `metric_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指标名称',
  `metric_value` decimal(15,2) NOT NULL COMMENT '指标值',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统计数据表';

-- --------------------------------------------------------

--
-- 表的结构 `support_tickets`
--

CREATE TABLE IF NOT EXISTS `support_tickets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `category` varchar(50) NOT NULL,
  `priority` varchar(20) DEFAULT 'normal',
  `description` text NOT NULL,
  `status` varchar(20) DEFAULT 'open',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `admin_response` text COMMENT '管理员回复'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `system_logs`
--

CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int(11) NOT NULL,
  `level` enum('debug','info','notice','warning','error','critical','alert','emergency') COLLATE utf8mb4_unicode_ci DEFAULT 'info',
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `context` json DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `system_settings`
--

CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) NOT NULL,
  `key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `type` enum('string','integer','boolean','json','text') COLLATE utf8mb4_unicode_ci DEFAULT 'string',
  `description` text COLLATE utf8mb4_unicode_ci,
  `group` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'general',
  `is_public` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `system_settings`
--

INSERT INTO `system_settings` (`id`, `key`, `value`, `type`, `description`, `group`, `is_public`, `created_at`, `updated_at`) VALUES
(1, 'namesilo_api_key', '312af6e433536460d3377', 'string', NULL, 'domain', 0, '2025-07-22 07:27:21', '2025-07-22 07:29:54'),
(2, 'namesilo_sandbox', '0', 'string', NULL, 'domain', 0, '2025-07-22 07:27:21', '2025-07-22 07:27:21'),
(3, 'default_domain_provider', 'namesilo', 'string', NULL, 'domain', 0, '2025-07-22 07:27:21', '2025-07-22 07:27:21'),
(4, 'domain_auto_renew', '0', 'string', NULL, 'domain', 0, '2025-07-22 07:27:21', '2025-07-22 09:13:08'),
(5, 'domain_privacy_protection', '1', 'string', NULL, 'domain', 0, '2025-07-22 07:27:21', '2025-07-22 07:27:21');

-- --------------------------------------------------------

--
-- 表的结构 `tld_pricing`
--

CREATE TABLE IF NOT EXISTS `tld_pricing` (
  `id` int(11) NOT NULL,
  `tld` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `register_price` decimal(8,2) NOT NULL,
  `renew_price` decimal(8,2) NOT NULL,
  `transfer_price` decimal(8,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci DEFAULT 'USD',
  `is_active` tinyint(1) DEFAULT '1',
  `is_featured` tinyint(1) DEFAULT '0',
  `min_years` int(11) DEFAULT '1',
  `max_years` int(11) DEFAULT '10',
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- 转存表中的数据 `tld_pricing`
--

INSERT INTO `tld_pricing` (`id`, `tld`, `register_price`, `renew_price`, `transfer_price`, `currency`, `is_active`, `is_featured`, `min_years`, `max_years`, `description`, `created_at`, `updated_at`) VALUES
(1, '.com', '12.99', '14.99', '12.99', 'USD', 1, 1, 1, 10, '最受欢迎的顶级域名', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(2, '.net', '14.99', '16.99', '14.99', 'USD', 1, 0, 1, 10, '网络相关业务首选', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(3, '.org', '13.99', '15.99', '13.99', 'USD', 1, 0, 1, 10, '非营利组织专用', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(4, '.info', '8.99', '18.99', '8.99', 'USD', 1, 0, 1, 10, '信息类网站', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(5, '.biz', '9.99', '19.99', '9.99', 'USD', 1, 0, 1, 10, '商业网站专用', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(6, '.cn', '8.99', '8.99', '8.99', 'USD', 1, 1, 1, 10, '中国国家域名', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(7, '.com.cn', '8.99', '8.99', '8.99', 'USD', 1, 0, 1, 10, '中国商业域名', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(8, '.io', '39.99', '59.99', '39.99', 'USD', 1, 1, 1, 10, '科技公司热门选择', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(9, '.ai', '89.99', '89.99', '89.99', 'USD', 1, 1, 1, 10, '人工智能相关', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(10, '.tech', '19.99', '49.99', '19.99', 'USD', 1, 0, 1, 10, '技术类网站', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(11, '.online', '2.99', '39.99', '2.99', 'USD', 1, 1, 1, 10, '在线业务', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(12, '.store', '2.99', '59.99', '2.99', 'USD', 1, 0, 1, 10, '电商网站', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(13, '.app', '19.99', '19.99', '19.99', 'USD', 1, 0, 1, 10, '移动应用', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(14, '.dev', '14.99', '14.99', '14.99', 'USD', 1, 0, 1, 10, '开发者专用', '2025-07-21 14:48:34', '2025-07-21 14:48:34'),
(15, '.xyz', '1.99', '12.99', '1.99', 'USD', 1, 1, 1, 10, '新一代域名', '2025-07-21 14:48:34', '2025-07-21 14:48:34');

-- --------------------------------------------------------

--
-- 表的结构 `users`
--

CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `role` enum('user','admin') COLLATE utf8mb4_unicode_ci DEFAULT 'user' COMMENT '用户角色',
  `first_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名',
  `last_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话',
  `address` text COLLATE utf8mb4_unicode_ci COMMENT '地址',
  `city` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `state` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `zip_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮编',
  `country` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'CN' COMMENT '国家',
  `email_verified` tinyint(1) DEFAULT '0' COMMENT '邮箱是否验证',
  `email_verification_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱验证令牌',
  `password_reset_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码重置令牌',
  `password_reset_expires` datetime DEFAULT NULL COMMENT '密码重置过期时间',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '用户余额',
  `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '用户状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

--
-- 转存表中的数据 `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `role`, `first_name`, `last_name`, `phone`, `address`, `city`, `state`, `zip_code`, `country`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `last_login`, `balance`, `status`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$zNy20J5RrN6e.1dpGq7vtOTrMbjO5/PriE.IGVEHmGnezYjOpNXTq', 'admin', 'Admin', 'User', '', '', '', NULL, '', 'CN', 1, NULL, NULL, NULL, '2025-07-22 17:28:55', '0.00', 'active', '2025-07-19 10:18:46', '2025-07-22 09:28:55'),
(2, 'zhibi', '<EMAIL>', '$2y$10$yYvWi7Mu5TnbG77TZwbsSOUM8C1a6Gy1yFkqdwmV4/P6TkqyBMr42', 'user', '周', '余', NULL, NULL, NULL, NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:39:18', '2025-07-19 10:39:18'),
(4, 'john_doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'John', 'Doe', '13800138001', NULL, '北京', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(5, 'jane_smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Jane', 'Smith', '13800138002', NULL, '上海', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(6, 'mike_wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Mike', 'Wilson', '13800138003', NULL, '广州', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(7, 'sarah_johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Sarah', 'Johnson', '13800138004', NULL, '深圳', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(8, 'david_brown', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'David', 'Brown', '13800138005', NULL, '杭州', NULL, NULL, 'CN', 0, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 18:28:04'),
(9, 'lisa_davis', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Lisa', 'Davis', '13800138006', NULL, '南京', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(10, 'tom_miller', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Tom', 'Miller', '13800138007', NULL, '成都', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(11, 'amy_garcia', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Amy', 'Garcia', '13800138008', NULL, '武汉', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(12, 'chris_martinez', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Chris', 'Martinez', '13800138009', NULL, '西安', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(13, 'emma_rodriguez', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Emma', 'Rodriguez', '13800138010', NULL, '重庆', NULL, NULL, 'CN', 1, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-19 10:44:36', '2025-07-19 10:44:36'),
(15, '_克_03cfb8', '<EMAIL>', '$2y$10$9V2.9tz885gJT4DGG3UAsuKr6x67hSnvNOwRK5UR9P63hC.dModxG', 'user', '张', '克', '18482630471', '', '', NULL, '', 'UK', 0, '756548', NULL, '2025-07-22 02:53:30', '2025-07-22 15:31:42', '50.03', 'active', '2025-07-19 19:35:57', '2025-07-22 07:31:42'),
(16, 'testuser', '<EMAIL>', '', 'user', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'CN', 0, NULL, NULL, NULL, NULL, '0.00', 'active', '2025-07-20 08:00:33', '2025-07-20 15:09:41');

-- --------------------------------------------------------

--
-- 表的结构 `user_login_logs`
--

CREATE TABLE IF NOT EXISTS `user_login_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `location` varchar(255) DEFAULT NULL,
  `device` varchar(255) DEFAULT NULL,
  `status` varchar(20) DEFAULT 'success',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `user_security_settings`
--

CREATE TABLE IF NOT EXISTS `user_security_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `two_factor_enabled` tinyint(1) DEFAULT '0',
  `security_question` varchar(255) DEFAULT NULL,
  `security_answer` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 表的结构 `user_sessions`
--

CREATE TABLE IF NOT EXISTS `user_sessions` (
  `id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `api_keys`
--
ALTER TABLE `api_keys`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `key_hash` (`key_hash`);

--
-- Indexes for table `backups`
--
ALTER TABLE `backups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `billing_transactions`
--
ALTER TABLE `billing_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `domain_name` (`domain_name`);

--
-- Indexes for table `cart_items`
--
ALTER TABLE `cart_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `session_id` (`session_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `domain_name` (`domain_name`);

--
-- Indexes for table `coupons`
--
ALTER TABLE `coupons`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dns_records`
--
ALTER TABLE `dns_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_domain_id` (`domain_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `domains`
--
ALTER TABLE `domains`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `domain_name` (`domain_name`),
  ADD KEY `idx_domain_name` (`domain_name`),
  ADD KEY `idx_tld` (`tld`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_expiry_date` (`expiry_date`),
  ADD KEY `idx_featured` (`featured`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `domain_categories`
--
ALTER TABLE `domain_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_parent_id` (`parent_id`),
  ADD KEY `idx_slug` (`slug`);

--
-- Indexes for table `domain_logs`
--
ALTER TABLE `domain_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_domain_id` (`domain_id`),
  ADD KEY `idx_domain_name` (`domain_name`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `domain_prices`
--
ALTER TABLE `domain_prices`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tld` (`tld`),
  ADD KEY `idx_tld` (`tld`),
  ADD KEY `idx_enabled` (`enabled`);

--
-- Indexes for table `domain_providers`
--
ALTER TABLE `domain_providers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `domain_registration_logs`
--
ALTER TABLE `domain_registration_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_item_id` (`order_item_id`),
  ADD KEY `idx_success` (`success`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `domain_renewals`
--
ALTER TABLE `domain_renewals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_domain_id` (`domain_id`),
  ADD KEY `idx_domain_name` (`domain_name`),
  ADD KEY `idx_payment_status` (`payment_status`),
  ADD KEY `idx_renewed_by` (`renewed_by`),
  ADD KEY `idx_new_expiry_date` (`new_expiry_date`);

--
-- Indexes for table `domain_templates`
--
ALTER TABLE `domain_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `domain_tlds`
--
ALTER TABLE `domain_tlds`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tld` (`tld`),
  ADD KEY `category` (`category`),
  ADD KEY `is_active` (`is_active`);

--
-- Indexes for table `domain_transfers`
--
ALTER TABLE `domain_transfers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_domain_id` (`domain_id`),
  ADD KEY `idx_domain_name` (`domain_name`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_initiated_by` (`initiated_by`);

--
-- Indexes for table `domain_transfer_logs`
--
ALTER TABLE `domain_transfer_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_domain_id` (`domain_id`),
  ADD KEY `idx_from_user_id` (`from_user_id`),
  ADD KEY `idx_to_user_id` (`to_user_id`);

--
-- Indexes for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_recipient` (`recipient`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_sent_at` (`sent_at`);

--
-- Indexes for table `email_queue`
--
ALTER TABLE `email_queue`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_scheduled_at` (`scheduled_at`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `logs`
--
ALTER TABLE `logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_resource_type` (`resource_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `namesilo_config`
--
ALTER TABLE `namesilo_config`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_read_at` (`read_at`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `order_number` (`order_number`),
  ADD KEY `idx_order_number` (`order_number`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_payment_status` (`payment_status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `idx_domain_id` (`domain_id`),
  ADD KEY `idx_item_type` (`item_type`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `payment_number` (`payment_number`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `payment_config`
--
ALTER TABLE `payment_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_method_key` (`payment_method`,`config_key`),
  ADD KEY `idx_payment_method` (`payment_method`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `payment_configs`
--
ALTER TABLE `payment_configs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `payment_method` (`payment_method`);

--
-- Indexes for table `payment_records`
--
ALTER TABLE `payment_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `refund_records`
--
ALTER TABLE `refund_records`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `refund_id` (`refund_id`),
  ADD KEY `idx_payment_record_id` (`payment_record_id`),
  ADD KEY `idx_refund_id` (`refund_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_setting_key` (`setting_key`);

--
-- Indexes for table `statistics`
--
ALTER TABLE `statistics`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `date_metric` (`date`,`metric_name`,`category`);

--
-- Indexes for table `support_tickets`
--
ALTER TABLE `support_tickets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_level` (`level`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `key` (`key`),
  ADD KEY `idx_key` (`key`),
  ADD KEY `idx_group` (`group`);

--
-- Indexes for table `tld_pricing`
--
ALTER TABLE `tld_pricing`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tld` (`tld`),
  ADD KEY `idx_tld` (`tld`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_is_featured` (`is_featured`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `user_login_logs`
--
ALTER TABLE `user_login_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_security_settings`
--
ALTER TABLE `user_security_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `api_keys`
--
ALTER TABLE `api_keys`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `backups`
--
ALTER TABLE `backups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `billing_transactions`
--
ALTER TABLE `billing_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=36;
--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `cart_items`
--
ALTER TABLE `cart_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `coupons`
--
ALTER TABLE `coupons`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `coupon_usage`
--
ALTER TABLE `coupon_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `dns_records`
--
ALTER TABLE `dns_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=7;
--
-- AUTO_INCREMENT for table `domains`
--
ALTER TABLE `domains`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=37;
--
-- AUTO_INCREMENT for table `domain_categories`
--
ALTER TABLE `domain_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `domain_logs`
--
ALTER TABLE `domain_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `domain_prices`
--
ALTER TABLE `domain_prices`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `domain_providers`
--
ALTER TABLE `domain_providers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `domain_registration_logs`
--
ALTER TABLE `domain_registration_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `domain_renewals`
--
ALTER TABLE `domain_renewals`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `domain_templates`
--
ALTER TABLE `domain_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `domain_tlds`
--
ALTER TABLE `domain_tlds`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=168;
--
-- AUTO_INCREMENT for table `domain_transfers`
--
ALTER TABLE `domain_transfers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `domain_transfer_logs`
--
ALTER TABLE `domain_transfer_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `email_logs`
--
ALTER TABLE `email_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `email_queue`
--
ALTER TABLE `email_queue`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `logs`
--
ALTER TABLE `logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=316;
--
-- AUTO_INCREMENT for table `namesilo_config`
--
ALTER TABLE `namesilo_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=15;
--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=8;
--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=9;
--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=6;
--
-- AUTO_INCREMENT for table `payment_config`
--
ALTER TABLE `payment_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=15;
--
-- AUTO_INCREMENT for table `payment_configs`
--
ALTER TABLE `payment_configs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=8;
--
-- AUTO_INCREMENT for table `payment_records`
--
ALTER TABLE `payment_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `refund_records`
--
ALTER TABLE `refund_records`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=33;
--
-- AUTO_INCREMENT for table `statistics`
--
ALTER TABLE `statistics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `support_tickets`
--
ALTER TABLE `support_tickets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=21;
--
-- AUTO_INCREMENT for table `tld_pricing`
--
ALTER TABLE `tld_pricing`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=16;
--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT,AUTO_INCREMENT=17;
--
-- AUTO_INCREMENT for table `user_login_logs`
--
ALTER TABLE `user_login_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- AUTO_INCREMENT for table `user_security_settings`
--
ALTER TABLE `user_security_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
--
-- 限制导出的表
--

--
-- 限制表 `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- 限制表 `billing_transactions`
--
ALTER TABLE `billing_transactions`
  ADD CONSTRAINT `billing_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 限制表 `domains`
--
ALTER TABLE `domains`
  ADD CONSTRAINT `domains_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- 限制表 `domain_categories`
--
ALTER TABLE `domain_categories`
  ADD CONSTRAINT `domain_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `domain_categories` (`id`) ON DELETE SET NULL;

--
-- 限制表 `domain_registration_logs`
--
ALTER TABLE `domain_registration_logs`
  ADD CONSTRAINT `domain_registration_logs_ibfk_1` FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`) ON DELETE CASCADE;

--
-- 限制表 `logs`
--
ALTER TABLE `logs`
  ADD CONSTRAINT `logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- 限制表 `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`domain_id`) REFERENCES `domains` (`id`) ON DELETE SET NULL;

--
-- 限制表 `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`);

--
-- 限制表 `payment_records`
--
ALTER TABLE `payment_records`
  ADD CONSTRAINT `payment_records_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE;

--
-- 限制表 `refund_records`
--
ALTER TABLE `refund_records`
  ADD CONSTRAINT `refund_records_ibfk_1` FOREIGN KEY (`payment_record_id`) REFERENCES `payment_records` (`id`) ON DELETE CASCADE;

--
-- 限制表 `sessions`
--
ALTER TABLE `sessions`
  ADD CONSTRAINT `sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `support_tickets`
--
ALTER TABLE `support_tickets`
  ADD CONSTRAINT `support_tickets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 限制表 `system_logs`
--
ALTER TABLE `system_logs`
  ADD CONSTRAINT `system_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- 限制表 `user_login_logs`
--
ALTER TABLE `user_login_logs`
  ADD CONSTRAINT `user_login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 限制表 `user_security_settings`
--
ALTER TABLE `user_security_settings`
  ADD CONSTRAINT `user_security_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 限制表 `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
