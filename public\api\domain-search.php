<?php
/**
 * 域名搜索API
 * Domain Search API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

session_start();

// 包含域名相关函数
require_once 'domain-functions.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleSearchRequest();
            break;
            
        case 'POST':
            handleBulkSearchRequest($input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器错误: ' . $e->getMessage()
    ]);
}

/**
 * 处理单个域名搜索请求
 */
function handleSearchRequest() {
    $domain = $_GET['domain'] ?? '';
    $extensions = $_GET['extensions'] ?? '.com,.net,.org,.cn';
    
    if (empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '请提供要搜索的域名']);
        return;
    }
    
    // 清理域名输入
    $domain = strtolower(trim($domain));
    $domain = preg_replace('/[^a-z0-9\-]/', '', $domain);
    
    if (empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名格式不正确']);
        return;
    }
    
    // 解析扩展名
    $extensionList = explode(',', $extensions);
    $results = [];
    
    foreach ($extensionList as $ext) {
        $ext = trim($ext);
        if (!str_starts_with($ext, '.')) {
            $ext = '.' . $ext;
        }
        
        $fullDomain = $domain . $ext;
        
        try {
            // 获取价格信息
            $priceInfo = getDomainPrice($ext);

            // 检查域名可用性
            $availabilityResult = checkDomainAvailability($fullDomain);

            // 合并结果
            $result = array_merge($availabilityResult, [
                'tld' => $ext,
                'price' => $priceInfo['register'] ?? 19.99, // 只返回注册价格
                'price_info' => $priceInfo, // 完整价格信息
                'register_url' => "cart.php?action=add&domain=" . urlencode($fullDomain) . "&years=1",
                'whois_url' => "whois.php?domain=" . urlencode($fullDomain)
            ]);

            $results[] = $result;
        } catch (Exception $e) {
            $results[] = [
                'domain' => $fullDomain,
                'tld' => $ext,
                'available' => false,
                'status' => 'error',
                'reason' => '查询失败: ' . $e->getMessage(),
                'price' => null
            ];
        }
    }
    
    // 记录搜索日志
    logActivity('domain_search', null, [
        'domain' => $domain,
        'extensions' => $extensionList,
        'results_count' => count($results)
    ]);
    
    echo json_encode([
        'success' => true,
        'domain' => $domain,
        'results' => $results,
        'search_time' => date('Y-m-d H:i:s')
    ]);
}

/**
 * 处理批量域名搜索请求
 */
function handleBulkSearchRequest($input) {
    $domains = $input['domains'] ?? [];
    $extensions = $input['extensions'] ?? ['.com', '.net', '.org', '.cn'];
    
    if (empty($domains) || !is_array($domains)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '请提供要搜索的域名列表']);
        return;
    }
    
    if (count($domains) > 50) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '一次最多搜索50个域名']);
        return;
    }
    
    $allResults = [];
    
    foreach ($domains as $domain) {
        $domain = strtolower(trim($domain));
        $domain = preg_replace('/[^a-z0-9\-]/', '', $domain);
        
        if (empty($domain)) {
            continue;
        }
        
        $domainResults = [];
        
        foreach ($extensions as $ext) {
            $ext = trim($ext);
            if (!str_starts_with($ext, '.')) {
                $ext = '.' . $ext;
            }
            
            $fullDomain = $domain . $ext;
            
            try {
                $result = checkDomainAvailability($fullDomain);
                $domainResults[] = $result;
            } catch (Exception $e) {
                $domainResults[] = [
                    'domain' => $fullDomain,
                    'available' => false,
                    'reason' => '查询失败: ' . $e->getMessage()
                ];
            }
        }
        
        $allResults[$domain] = $domainResults;
    }
    
    // 记录批量搜索日志
    logActivity('bulk_domain_search', 'domain', null, [
        'domains_count' => count($domains),
        'extensions' => $extensions,
        'total_results' => array_sum(array_map('count', $allResults))
    ]);
    
    echo json_encode([
        'success' => true,
        'results' => $allResults,
        'search_time' => date('Y-m-d H:i:s')
    ]);
}

/**
 * 获取热门域名后缀
 */
function getPopularExtensions() {
    return [
        '.com' => ['name' => 'COM', 'description' => '最受欢迎的通用域名'],
        '.net' => ['name' => 'NET', 'description' => '网络服务域名'],
        '.org' => ['name' => 'ORG', 'description' => '组织机构域名'],
        '.cn' => ['name' => 'CN', 'description' => '中国国家域名'],
        '.com.cn' => ['name' => 'COM.CN', 'description' => '中国商业域名'],
        '.net.cn' => ['name' => 'NET.CN', 'description' => '中国网络域名'],
        '.org.cn' => ['name' => 'ORG.CN', 'description' => '中国组织域名'],
        '.gov.cn' => ['name' => 'GOV.CN', 'description' => '中国政府域名'],
        '.edu.cn' => ['name' => 'EDU.CN', 'description' => '中国教育域名'],
        '.info' => ['name' => 'INFO', 'description' => '信息服务域名'],
        '.biz' => ['name' => 'BIZ', 'description' => '商业域名'],
        '.name' => ['name' => 'NAME', 'description' => '个人域名'],
        '.mobi' => ['name' => 'MOBI', 'description' => '移动设备域名'],
        '.asia' => ['name' => 'ASIA', 'description' => '亚洲地区域名'],
        '.tel' => ['name' => 'TEL', 'description' => '电信服务域名'],
        '.travel' => ['name' => 'TRAVEL', 'description' => '旅游行业域名'],
        '.museum' => ['name' => 'MUSEUM', 'description' => '博物馆域名'],
        '.coop' => ['name' => 'COOP', 'description' => '合作社域名'],
        '.aero' => ['name' => 'AERO', 'description' => '航空业域名'],
        '.pro' => ['name' => 'PRO', 'description' => '专业人士域名']
    ];
}



// 如果是获取扩展名列表的请求
if (isset($_GET['action']) && $_GET['action'] === 'extensions') {
    echo json_encode([
        'success' => true,
        'extensions' => getPopularExtensions()
    ]);
    exit;
}

// 如果是获取域名建议的请求
if (isset($_GET['action']) && $_GET['action'] === 'suggestions') {
    $keyword = $_GET['keyword'] ?? '';
    $limit = intval($_GET['limit'] ?? 10);
    
    if (empty($keyword)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '请提供关键词']);
        exit;
    }
    
    $suggestions = getDomainSuggestions($keyword, $limit);
    
    echo json_encode([
        'success' => true,
        'keyword' => $keyword,
        'suggestions' => $suggestions
    ]);
    exit;
}
?>
