<?php
/**
 * 简单SMTP邮件发送类
 * Simple SMTP Mailer Class
 */

class SMTPMailer {
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $socket;
    private $debug = false;
    
    public function __construct($config) {
        $this->host = $config['smtp_host'];
        $this->port = $config['smtp_port'] ?? 587;
        $this->username = $config['smtp_username'];
        $this->password = $config['smtp_password'];
        $this->encryption = $config['smtp_encryption'] ?? 'tls';
    }
    
    public function setDebug($debug = true) {
        $this->debug = $debug;
    }
    
    private function log($message) {
        if ($this->debug) {
            error_log("SMTP: " . $message);
        }
    }
    
    private function connect() {
        $context = stream_context_create();
        
        if ($this->encryption === 'ssl') {
            $this->socket = stream_socket_client(
                "ssl://{$this->host}:{$this->port}",
                $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context
            );
        } else {
            $this->socket = stream_socket_client(
                "{$this->host}:{$this->port}",
                $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context
            );
        }
        
        if (!$this->socket) {
            throw new Exception("无法连接到SMTP服务器: $errstr ($errno)");
        }
        
        $this->log("已连接到 {$this->host}:{$this->port}");
        return $this->getResponse();
    }
    
    private function getResponse() {
        $response = '';
        $line = '';

        // 读取完整的SMTP响应（可能有多行）
        do {
            $line = fgets($this->socket, 512);
            if ($line === false) {
                throw new Exception("无法读取SMTP服务器响应");
            }
            $response .= $line;
            $this->log("收到: " . trim($line));
        } while (isset($line[3]) && $line[3] === '-'); // 继续读取多行响应

        return $response;
    }
    
    private function sendCommand($command) {
        $this->log("发送: " . trim($command));
        fwrite($this->socket, $command . "\r\n");
        return $this->getResponse();
    }
    
    private function startTLS() {
        if ($this->encryption === 'tls') {
            $response = $this->sendCommand("STARTTLS");

            // 检查STARTTLS响应，220表示准备开始TLS
            if (substr($response, 0, 3) !== '220') {
                throw new Exception("STARTTLS失败: 服务器响应 " . trim($response));
            }

            // 启用TLS加密
            $cryptoMethod = STREAM_CRYPTO_METHOD_TLS_CLIENT;
            if (defined('STREAM_CRYPTO_METHOD_TLSv1_2_CLIENT')) {
                $cryptoMethod = STREAM_CRYPTO_METHOD_TLSv1_2_CLIENT;
            }

            if (!stream_socket_enable_crypto($this->socket, true, $cryptoMethod)) {
                throw new Exception("TLS加密启用失败，请检查服务器TLS支持");
            }

            $this->log("TLS加密已启用");
        }
    }
    
    private function authenticate() {
        // 发送EHLO命令
        $hostname = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $response = $this->sendCommand("EHLO {$hostname}");
        if (substr($response, 0, 3) !== '250') {
            throw new Exception("EHLO失败: " . trim($response));
        }

        // 启动TLS（如果需要）
        $this->startTLS();

        // 重新发送EHLO（TLS后）
        if ($this->encryption === 'tls') {
            $response = $this->sendCommand("EHLO {$hostname}");
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("TLS后EHLO失败: " . trim($response));
            }
        }

        // 检查是否需要认证
        if (empty($this->username) || empty($this->password)) {
            $this->log("跳过SMTP认证（无用户名或密码）");
            return;
        }

        // 开始认证
        $response = $this->sendCommand("AUTH LOGIN");
        if (substr($response, 0, 3) !== '334') {
            // 尝试其他认证方法
            $response = $this->sendCommand("AUTH PLAIN");
            if (substr($response, 0, 3) !== '334') {
                throw new Exception("服务器不支持LOGIN或PLAIN认证: " . trim($response));
            }

            // PLAIN认证
            $authString = base64_encode("\0{$this->username}\0{$this->password}");
            $response = $this->sendCommand($authString);
            if (substr($response, 0, 3) !== '235') {
                throw new Exception("PLAIN认证失败: " . trim($response));
            }
        } else {
            // LOGIN认证
            // 发送用户名
            $response = $this->sendCommand(base64_encode($this->username));
            if (substr($response, 0, 3) !== '334') {
                throw new Exception("用户名认证失败: " . trim($response));
            }

            // 发送密码
            $response = $this->sendCommand(base64_encode($this->password));
            if (substr($response, 0, 3) !== '235') {
                throw new Exception("密码认证失败，请检查用户名和密码: " . trim($response));
            }
        }

        $this->log("SMTP认证成功");
    }
    
    public function sendMail($from, $to, $subject, $body, $fromName = '') {
        try {
            // 连接服务器
            $response = $this->connect();
            if (substr($response, 0, 3) !== '220') {
                throw new Exception("服务器连接失败: " . $response);
            }
            
            // 认证
            $this->authenticate();
            
            // 设置发件人
            $response = $this->sendCommand("MAIL FROM: <{$from}>");
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("设置发件人失败: " . $response);
            }
            
            // 设置收件人
            $response = $this->sendCommand("RCPT TO: <{$to}>");
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("设置收件人失败: " . $response);
            }
            
            // 开始数据传输
            $response = $this->sendCommand("DATA");
            if (substr($response, 0, 3) !== '354') {
                throw new Exception("开始数据传输失败: " . $response);
            }
            
            // 构建邮件头
            $headers = [];
            $headers[] = "From: " . ($fromName ? "{$fromName} <{$from}>" : $from);
            $headers[] = "To: {$to}";
            $headers[] = "Subject: =?UTF-8?B?" . base64_encode($subject) . "?=";
            $headers[] = "MIME-Version: 1.0";

            // 检测是否为HTML内容
            $isHtml = (strpos($body, '<') !== false && strpos($body, '>') !== false);
            if ($isHtml) {
                $headers[] = "Content-Type: text/html; charset=UTF-8";
            } else {
                $headers[] = "Content-Type: text/plain; charset=UTF-8";
            }

            $headers[] = "Content-Transfer-Encoding: 8bit";
            $headers[] = "Date: " . date('r');
            $headers[] = "Message-ID: <" . uniqid() . "@" . ($_SERVER['HTTP_HOST'] ?? 'localhost') . ">";
            
            // 发送邮件内容
            $mailData = implode("\r\n", $headers) . "\r\n\r\n" . $body . "\r\n.";
            fwrite($this->socket, $mailData . "\r\n");
            
            $response = $this->getResponse();
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("邮件发送失败: " . $response);
            }
            
            // 退出
            $this->sendCommand("QUIT");
            fclose($this->socket);
            
            $this->log("邮件发送成功");
            return true;
            
        } catch (Exception $e) {
            if ($this->socket) {
                fclose($this->socket);
            }
            throw $e;
        }
    }
    
    public function testConnection() {
        try {
            $this->log("开始SMTP连接测试");

            $response = $this->connect();
            if (substr($response, 0, 3) !== '220') {
                throw new Exception("服务器连接失败: " . trim($response));
            }

            $this->authenticate();
            $this->sendCommand("QUIT");
            fclose($this->socket);

            return ['success' => true, 'message' => 'SMTP连接和认证测试成功！'];
        } catch (Exception $e) {
            if ($this->socket) {
                fclose($this->socket);
            }
            return ['success' => false, 'message' => 'SMTP测试失败: ' . $e->getMessage()];
        }
    }

    /**
     * 简单的连接测试，不进行认证
     */
    public function testSimpleConnection() {
        try {
            $this->log("开始简单SMTP连接测试");

            $response = $this->connect();
            if (substr($response, 0, 3) !== '220') {
                throw new Exception("服务器连接失败: " . trim($response));
            }

            // 只测试EHLO，不进行认证
            $hostname = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $response = $this->sendCommand("EHLO {$hostname}");
            if (substr($response, 0, 3) !== '250') {
                throw new Exception("EHLO失败: " . trim($response));
            }

            $this->sendCommand("QUIT");
            fclose($this->socket);

            return ['success' => true, 'message' => 'SMTP服务器连接测试成功！'];
        } catch (Exception $e) {
            if ($this->socket) {
                fclose($this->socket);
            }
            return ['success' => false, 'message' => 'SMTP连接测试失败: ' . $e->getMessage()];
        }
    }
}

/**
 * 发送邮件的便捷函数
 * @param string $to 收件人邮箱
 * @param string $subject 邮件主题
 * @param string $body 邮件内容（HTML格式）
 * @param string $fromName 发件人姓名
 * @return bool 发送是否成功
 */
function sendEmail($to, $subject, $body, $fromName = 'NameSilo域名销售系统') {
    try {
        // 获取邮件配置
        require_once __DIR__ . '/../config/database.php';
        $db = getDatabase();

        // 从数据库获取SMTP配置
        $settings = [];
        try {
            // 尝试从 system_settings 表获取
            $stmt = $db->prepare("SELECT `key`, `value` FROM system_settings WHERE `key` LIKE 'smtp_%'");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (PDOException $e) {
            // 如果 system_settings 表不存在，尝试 settings 表
            try {
                $stmt = $db->prepare("SELECT setting_key as `key`, setting_value as `value` FROM settings WHERE setting_key LIKE 'smtp_%'");
                $stmt->execute();
                $result = $stmt->fetchAll();
                foreach ($result as $row) {
                    $settings[$row['key']] = $row['value'];
                }
            } catch (PDOException $e) {
                error_log("无法获取SMTP设置: " . $e->getMessage());
            }
        }

        // 检查必要的配置
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            error_log("SMTP配置不完整，请在管理后台的【设置】->【邮件设置】中配置SMTP信息");
            throw new Exception('邮件服务未配置，请联系管理员在后台配置SMTP邮件服务');
        }

        // 创建SMTP配置数组
        $smtpConfig = [
            'smtp_host' => $settings['smtp_host'],
            'smtp_port' => $settings['smtp_port'] ?? 587,
            'smtp_username' => $settings['smtp_username'],
            'smtp_password' => $settings['smtp_password'],
            'smtp_encryption' => $settings['smtp_encryption'] ?? 'tls'
        ];

        // 创建邮件发送器
        $mailer = new SMTPMailer($smtpConfig);

        // 发送邮件
        $result = $mailer->sendMail(
            $settings['smtp_username'], // 发件人邮箱
            $to, // 收件人邮箱
            $subject, // 主题
            $body, // 内容
            $fromName // 发件人姓名
        );

        return $result['success'] ?? false;

    } catch (Exception $e) {
        error_log("邮件发送失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 使用SMTP发送邮件（返回详细结果）
 * @param string $to 收件人邮箱
 * @param string $subject 邮件主题
 * @param string $body 邮件内容（HTML格式）
 * @param string $fromName 发件人姓名
 * @return array 发送结果
 */
function sendEmailWithSMTP($to, $subject, $body, $fromName = 'NameSilo域名销售系统') {
    try {
        // 获取邮件配置
        require_once __DIR__ . '/../config/database.php';
        $db = getDatabase();

        // 从数据库获取SMTP配置
        $settings = [];
        try {
            $stmt = $db->prepare("SELECT `key`, `value` FROM system_settings WHERE `key` LIKE 'smtp_%' OR `key` LIKE 'from_%'");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (PDOException $e) {
            return ['success' => false, 'message' => '无法获取SMTP设置: ' . $e->getMessage()];
        }

        // 检查必要的配置
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            return ['success' => false, 'message' => 'SMTP配置不完整，请在管理后台配置邮件设置'];
        }

        // 创建SMTP配置数组
        $smtpConfig = [
            'smtp_host' => $settings['smtp_host'],
            'smtp_port' => $settings['smtp_port'] ?? 587,
            'smtp_username' => $settings['smtp_username'],
            'smtp_password' => $settings['smtp_password'],
            'smtp_encryption' => $settings['smtp_encryption'] ?? 'tls'
        ];

        // 创建邮件发送器
        $mailer = new SMTPMailer($smtpConfig);

        // 发送邮件
        $result = $mailer->sendMail(
            $settings['from_email'] ?? $settings['smtp_username'], // 发件人邮箱
            $to, // 收件人邮箱
            $subject, // 主题
            $body, // 内容
            $settings['from_name'] ?? $fromName // 发件人姓名
        );

        return ['success' => true, 'message' => '邮件发送成功'];

    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * 测试SMTP连接
 * @param string $testEmail 测试邮箱地址
 * @return array 测试结果
 */
function testSMTPConnection($testEmail = '') {
    try {
        // 获取邮件配置
        require_once __DIR__ . '/../config/database.php';
        $db = getDatabase();

        // 从数据库获取SMTP配置
        $settings = [];
        try {
            $stmt = $db->prepare("SELECT `key`, `value` FROM system_settings WHERE `key` LIKE 'smtp_%' OR `key` LIKE 'from_%'");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (PDOException $e) {
            return ['success' => false, 'message' => '无法获取SMTP设置: ' . $e->getMessage()];
        }

        // 检查必要的配置
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            return ['success' => false, 'message' => 'SMTP配置不完整，请先配置邮件设置'];
        }

        // 创建SMTP配置数组
        $smtpConfig = [
            'smtp_host' => $settings['smtp_host'],
            'smtp_port' => $settings['smtp_port'] ?? 587,
            'smtp_username' => $settings['smtp_username'],
            'smtp_password' => $settings['smtp_password'],
            'smtp_encryption' => $settings['smtp_encryption'] ?? 'tls'
        ];

        // 创建邮件发送器
        $mailer = new SMTPMailer($smtpConfig);
        $mailer->setDebug(true);

        // 如果提供了测试邮箱，发送测试邮件
        if ($testEmail) {
            $subject = 'SMTP测试邮件 - ' . ($settings['from_name'] ?? 'NameSilo域名销售系统');
            $body = '
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #007bff;">SMTP测试邮件</h2>
                <p>这是一封SMTP配置测试邮件。</p>
                <p>如果您收到此邮件，说明SMTP配置正确。</p>
                <p>发送时间: ' . date('Y-m-d H:i:s') . '</p>
                <hr>
                <p style="color: #666; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
            </div>';

            $result = $mailer->sendMail(
                $settings['from_email'] ?? $settings['smtp_username'],
                $testEmail,
                $subject,
                $body,
                $settings['from_name'] ?? 'NameSilo域名销售系统'
            );

            return ['success' => true, 'message' => '测试邮件发送成功！请检查收件箱。'];
        } else {
            // 只测试连接
            return $mailer->testConnection();
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'SMTP测试失败: ' . $e->getMessage()];
    }
}
?>
