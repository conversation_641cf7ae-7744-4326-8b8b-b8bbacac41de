-- 创建购物车表
-- Create cart table

CREATE TABLE IF NOT EXISTS cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT '用户ID',
    session_id VARCHAR(255) COMMENT '会话ID',
    domain_name VARCHAR(255) NOT NULL COMMENT '域名',
    item_type ENUM('registration', 'renewal', 'transfer', 'privacy') DEFAULT 'registration' COMMENT '项目类型',
    years INT DEFAULT 1 COMMENT '年数',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';
