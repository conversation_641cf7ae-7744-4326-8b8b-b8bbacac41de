<?php
/**
 * 支持中心页面
 * Support Center
 */

session_start();
require_once '../config.php';

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$pageTitle = '支持中心';
$message = '';
$error = '';

// 处理工单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create_ticket') {
            $subject = trim($_POST['subject'] ?? '');
            $category = $_POST['category'] ?? '';
            $priority = $_POST['priority'] ?? 'normal';
            $description = trim($_POST['description'] ?? '');
            
            if (empty($subject) || empty($category) || empty($description)) {
                throw new Exception('请填写所有必填字段');
            }
            
            // 保存工单到数据库
            $db = getDatabase();

            // 创建工单表（如果不存在）
            $db->exec("
                CREATE TABLE IF NOT EXISTS support_tickets (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    subject VARCHAR(255) NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    priority VARCHAR(20) DEFAULT 'normal',
                    description TEXT NOT NULL,
                    admin_response TEXT,
                    status VARCHAR(20) DEFAULT 'open',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");

            // 检查并添加admin_response字段（如果不存在）
            try {
                $db->exec("ALTER TABLE support_tickets ADD COLUMN admin_response TEXT");
            } catch (Exception $e) {
                // 字段已存在，忽略错误
            }

            $stmt = $db->prepare("INSERT INTO support_tickets (user_id, subject, category, priority, description) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$userId, $subject, $category, $priority, $description]);

            $message = '工单提交成功！我们会在24小时内回复您。';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取用户工单列表
try {
    $db = getDatabase();
    $stmt = $db->prepare("SELECT * FROM support_tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 20");
    $stmt->execute([$userId]);
    $tickets = $stmt->fetchAll();
} catch (Exception $e) {
    $tickets = [];
}

// 常见问题
$faqs = [
    [
        'question' => '如何注册域名？',
        'answer' => '在首页搜索框中输入您想要的域名，选择可用的域名后点击"立即注册"，按照提示完成支付即可。'
    ],
    [
        'question' => '域名注册后多久生效？',
        'answer' => '域名注册成功后通常在几分钟内生效，最长不超过24小时。'
    ],
    [
        'question' => '如何修改域名DNS？',
        'answer' => '登录用户中心，进入域名管理页面，点击对应域名的"DNS管理"按钮即可修改。'
    ],
    [
        'question' => '忘记密码怎么办？',
        'answer' => '在登录页面点击"忘记密码"，输入您的邮箱地址，我们会发送重置密码的链接到您的邮箱。'
    ],
    [
        'question' => '如何申请退款？',
        'answer' => '请联系客服或提交工单说明退款原因，我们会在3-5个工作日内处理您的退款申请。'
    ]
];

// 开始输出缓冲
ob_start();
?>

<!-- 页面标题 -->
<div class="page-header">
    <h1 class="page-title">支持中心</h1>
    <p class="page-subtitle">我们随时为您提供帮助和支持</p>
</div>

<!-- 消息提示 -->
<?php if ($message): ?>
<div class="alert alert-success alert-dismissible fade show">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 快速联系方式 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-primary bg-opacity-10 text-primary mx-auto">
                <i class="fas fa-headset"></i>
            </div>
            <h5 class="h6 mb-2">在线客服</h5>
            <p class="text-muted small mb-3">工作时间：9:00-18:00</p>
            <button class="btn btn-sm btn-primary" onclick="startLiveChat()">
                立即咨询
            </button>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-success bg-opacity-10 text-success mx-auto">
                <i class="fas fa-envelope"></i>
            </div>
            <h5 class="h6 mb-2">邮件支持</h5>
            <p class="text-muted small mb-3"><EMAIL></p>
            <button class="btn btn-sm btn-success" onclick="sendEmail()">
                发送邮件
            </button>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-info bg-opacity-10 text-info mx-auto">
                <i class="fas fa-phone"></i>
            </div>
            <h5 class="h6 mb-2">电话支持</h5>
            <p class="text-muted small mb-3">************</p>
            <button class="btn btn-sm btn-info" onclick="callSupport()">
                拨打电话
            </button>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card text-center">
            <div class="stats-icon bg-warning bg-opacity-10 text-warning mx-auto">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <h5 class="h6 mb-2">提交工单</h5>
            <p class="text-muted small mb-3">24小时内回复</p>
            <button class="btn btn-sm btn-warning" onclick="showTicketModal()">
                创建工单
            </button>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- 我的工单 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt me-2"></i>
                    我的工单
                </h5>
                <button class="btn btn-sm btn-primary" onclick="showTicketModal()">
                    <i class="fas fa-plus me-1"></i>
                    新建工单
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($tickets)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">暂无工单</h6>
                        <p class="text-muted mb-3">您还没有提交任何工单</p>
                        <button class="btn btn-primary" onclick="showTicketModal()">
                            <i class="fas fa-plus me-2"></i>
                            创建第一个工单
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>工单号</th>
                                    <th>主题</th>
                                    <th>类别</th>
                                    <th>状态</th>
                                    <th>优先级</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tickets as $ticket): ?>
                                <tr>
                                    <td>
                                        <span class="font-monospace">#<?= $ticket['id'] ?></span>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($ticket['subject']) ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        $categoryText = match($ticket['category']) {
                                            'technical' => '技术支持',
                                            'billing' => '账单问题',
                                            'general' => '一般咨询',
                                            default => '其他'
                                        };
                                        ?>
                                        <span class="badge bg-secondary"><?= $categoryText ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusConfig = [
                                            'open' => ['class' => 'warning', 'text' => '处理中'],
                                            'resolved' => ['class' => 'success', 'text' => '已解决'],
                                            'closed' => ['class' => 'secondary', 'text' => '已关闭']
                                        ];
                                        $config = $statusConfig[$ticket['status']] ?? ['class' => 'secondary', 'text' => '未知'];
                                        ?>
                                        <span class="badge bg-<?= $config['class'] ?>"><?= $config['text'] ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $priorityConfig = [
                                            'low' => ['class' => 'success', 'text' => '低'],
                                            'normal' => ['class' => 'info', 'text' => '普通'],
                                            'high' => ['class' => 'warning', 'text' => '高'],
                                            'urgent' => ['class' => 'danger', 'text' => '紧急']
                                        ];
                                        $pConfig = $priorityConfig[$ticket['priority']] ?? ['class' => 'secondary', 'text' => '普通'];
                                        ?>
                                        <span class="badge bg-<?= $pConfig['class'] ?>"><?= $pConfig['text'] ?></span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('Y-m-d H:i', strtotime($ticket['updated_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewTicket(<?= $ticket['id'] ?>)">
                                            查看
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 常见问题 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    常见问题
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <?php foreach ($faqs as $index => $faq): ?>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq<?= $index ?>">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse<?= $index ?>">
                                <?= htmlspecialchars($faq['question']) ?>
                            </button>
                        </h2>
                        <div id="collapse<?= $index ?>" class="accordion-collapse collapse" 
                             data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <?= htmlspecialchars($faq['answer']) ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="text-center mt-3">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        查看更多FAQ
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 帮助资源 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-book me-2"></i>
                    帮助资源
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-alt me-2"></i>
                        用户手册
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-video me-2"></i>
                        视频教程
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-code me-2"></i>
                        API文档
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-comments me-2"></i>
                        社区论坛
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建工单模态框 -->
<div class="modal fade" id="ticketModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-ticket-alt me-2"></i>
                    创建工单
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_ticket">
                    
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label class="form-label">主题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="subject" 
                                   placeholder="请简要描述您的问题" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">优先级</label>
                            <select class="form-select" name="priority">
                                <option value="low">低</option>
                                <option value="normal" selected>普通</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">问题类别 <span class="text-danger">*</span></label>
                            <select class="form-select" name="category" required>
                                <option value="">请选择问题类别</option>
                                <option value="technical">技术支持</option>
                                <option value="billing">账单问题</option>
                                <option value="domain">域名相关</option>
                                <option value="account">账户问题</option>
                                <option value="general">一般咨询</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">问题描述 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="description" rows="6" 
                                      placeholder="请详细描述您遇到的问题，包括具体的错误信息、操作步骤等" required></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label">附件</label>
                            <input type="file" class="form-control" multiple 
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt">
                            <small class="text-muted">支持图片、PDF、Word文档等格式，最大5MB</small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>
                        提交工单
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 工单详情模态框 -->
<div class="modal fade" id="ticketDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">工单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ticketDetailBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>
</div>

<script>
// 显示创建工单模态框
function showTicketModal() {
    new bootstrap.Modal(document.getElementById('ticketModal')).show();
}

// 查看工单详情
function viewTicket(ticketId) {
    document.getElementById('ticketDetailBody').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载工单详情...</p>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('ticketDetailModal')).show();
    
    // 模拟加载工单详情
    setTimeout(() => {
        document.getElementById('ticketDetailBody').innerHTML = `
            <div class="row g-3 mb-4">
                <div class="col-md-6">
                    <strong>工单号:</strong> #${ticketId}
                </div>
                <div class="col-md-6">
                    <strong>状态:</strong> <span class="badge bg-warning">处理中</span>
                </div>
                <div class="col-md-6">
                    <strong>类别:</strong> 技术支持
                </div>
                <div class="col-md-6">
                    <strong>优先级:</strong> <span class="badge bg-info">普通</span>
                </div>
                <div class="col-12">
                    <strong>主题:</strong> 域名解析问题
                </div>
                <div class="col-12">
                    <strong>创建时间:</strong> 2024-01-15 10:30:00
                </div>
            </div>
            
            <div class="border rounded p-3 mb-3">
                <h6>问题描述:</h6>
                <p>我的域名namesilo-namesilo-sample.com无法正常解析，已经设置了A记录指向服务器IP，但是网站仍然无法访问。请帮忙检查一下。</p>
            </div>
            
            <div class="border rounded p-3">
                <h6>客服回复:</h6>
                <p class="text-muted">我们正在处理您的问题，请稍等...</p>
            </div>
            
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle me-2"></i>
                工单详情功能正在完善中，更多功能即将提供...
            </div>
        `;
    }, 1000);
}

// 开始在线客服
function startLiveChat() {
    alert('在线客服功能正在开发中，请先提交工单或发送邮件');
}

// 发送邮件
function sendEmail() {
    window.location.href = 'mailto:<EMAIL>';
}

// 拨打电话
function callSupport() {
    alert('客服电话：************\n工作时间：周一至周五 9:00-18:00');
}
</script>

<?php
$pageContent = ob_get_clean();
include 'layout.php';
?>
