<?php
/**
 * 支持中心管理内容页面
 * Support Center Management Content Page
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// 添加错误处理
set_error_handler(function($severity, $message, $file, $line) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>PHP错误:</h4>";
    echo "<p><strong>错误信息:</strong> $message</p>";
    echo "<p><strong>文件:</strong> $file</p>";
    echo "<p><strong>行号:</strong> $line</p>";
    echo "</div>";
});

try {
    // 获取数据库连接
    $db = getDatabase();

    if (!$db) {
        throw new Exception("数据库连接失败");
    }

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>异常错误:</h4>";
    echo "<p><strong>错误信息:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>文件:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>堆栈跟踪:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
    exit;
}

// 处理操作
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($_POST['action']) {
            case 'update_ticket':
                $ticketId = $_POST['ticket_id'];
                $status = $_POST['status'];
                $adminResponse = $_POST['admin_response'] ?? '';
                
                $stmt = $db->prepare("UPDATE support_tickets SET status = ?, admin_response = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$status, $adminResponse, $ticketId]);
                
                // 如果有回复内容，发送通知给用户
                if (!empty($adminResponse)) {
                    $stmt = $db->prepare("SELECT user_id, subject FROM support_tickets WHERE id = ?");
                    $stmt->execute([$ticketId]);
                    $ticket = $stmt->fetch();
                    
                    if ($ticket) {
                        $stmt = $db->prepare("INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'info')");
                        $stmt->execute([
                            $ticket['user_id'],
                            "工单回复：{$ticket['subject']}",
                            "您的工单已收到回复：{$adminResponse}"
                        ]);
                    }
                }
                
                $message = '工单更新成功！';
                break;
                
            case 'close_ticket':
                $ticketId = $_POST['ticket_id'];
                $stmt = $db->prepare("UPDATE support_tickets SET status = 'closed', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$ticketId]);
                
                $message = '工单已关闭！';
                break;
        }
    } catch (Exception $e) {
        $error = '操作失败：' . $e->getMessage();
    }
}

// 获取工单统计
try {
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_tickets,
            COUNT(CASE WHEN status = 'open' THEN 1 END) as open_tickets,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tickets,
            COUNT(CASE WHEN status = 'closed' THEN 1 END) as closed_tickets,
            COUNT(CASE WHEN priority = 'high' AND status != 'closed' THEN 1 END) as high_priority_tickets,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_tickets
        FROM support_tickets
    ");
    $ticketStats = $stmt->fetch();
} catch (Exception $e) {
    $ticketStats = [
        'total_tickets' => 0,
        'open_tickets' => 0,
        'in_progress_tickets' => 0,
        'closed_tickets' => 0,
        'high_priority_tickets' => 0,
        'today_tickets' => 0
    ];
}

// 获取工单列表
$currentPage = (int)($_GET['p'] ?? 1); // 使用 'p' 参数避免与路由冲突
$limit = 20;
$offset = ($currentPage - 1) * $limit;

$whereClause = '';
$params = [];

// 搜索过滤
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $whereClause = " WHERE (st.subject LIKE ? OR st.description LIKE ? OR u.username LIKE ?)";
    $params = [$search, $search, $search];
}

if (!empty($_GET['status'])) {
    $statusFilter = $_GET['status'];
    $whereClause .= $whereClause ? " AND st.status = ?" : " WHERE st.status = ?";
    $params[] = $statusFilter;
}

if (!empty($_GET['priority'])) {
    $priorityFilter = $_GET['priority'];
    $whereClause .= $whereClause ? " AND st.priority = ?" : " WHERE st.priority = ?";
    $params[] = $priorityFilter;
}

if (!empty($_GET['category'])) {
    $categoryFilter = $_GET['category'];
    $whereClause .= $whereClause ? " AND st.category = ?" : " WHERE st.category = ?";
    $params[] = $categoryFilter;
}

try {
    $stmt = $db->prepare("
        SELECT 
            st.*,
            u.username,
            u.email
        FROM support_tickets st
        LEFT JOIN users u ON st.user_id = u.id
        {$whereClause}
        ORDER BY 
            CASE WHEN st.priority = 'high' THEN 1 
                 WHEN st.priority = 'normal' THEN 2 
                 ELSE 3 END,
            st.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $params[] = $limit;
    $params[] = $offset;
    $stmt->execute($params);
    $tickets = $stmt->fetchAll();
    
    // 获取总数
    $countParams = array_slice($params, 0, -2);
    $stmt = $db->prepare("
        SELECT COUNT(*) 
        FROM support_tickets st
        LEFT JOIN users u ON st.user_id = u.id
        {$whereClause}
    ");
    $stmt->execute($countParams);
    $totalTickets = $stmt->fetchColumn();
    $totalPages = ceil($totalTickets / $limit);
    
} catch (Exception $e) {
    $tickets = [];
    $totalPages = 1;
}
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">支持中心管理</h1>
            <p class="text-muted">管理用户工单和技术支持</p>
        </div>
    </div>

    <!-- 消息提示 -->
    <?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- 工单统计 -->
    <div class="row g-4 mb-4">
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-ticket-alt fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($ticketStats['total_tickets']) ?></h5>
                    <p class="card-text text-muted">总工单数</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-folder-open fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($ticketStats['open_tickets']) ?></h5>
                    <p class="card-text text-muted">待处理</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-cog fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($ticketStats['in_progress_tickets']) ?></h5>
                    <p class="card-text text-muted">处理中</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($ticketStats['closed_tickets']) ?></h5>
                    <p class="card-text text-muted">已关闭</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($ticketStats['high_priority_tickets']) ?></h5>
                    <p class="card-text text-muted">高优先级</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-secondary mb-2">
                        <i class="fas fa-calendar-day fa-2x"></i>
                    </div>
                    <h5 class="card-title"><?= number_format($ticketStats['today_tickets']) ?></h5>
                    <p class="card-text text-muted">今日新增</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">搜索</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="标题、内容或用户名">
                </div>
                <div class="col-md-2">
                    <label class="form-label">状态</label>
                    <select class="form-select" name="status">
                        <option value="">全部状态</option>
                        <option value="open" <?= ($_GET['status'] ?? '') === 'open' ? 'selected' : '' ?>>待处理</option>
                        <option value="in_progress" <?= ($_GET['status'] ?? '') === 'in_progress' ? 'selected' : '' ?>>处理中</option>
                        <option value="closed" <?= ($_GET['status'] ?? '') === 'closed' ? 'selected' : '' ?>>已关闭</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">优先级</label>
                    <select class="form-select" name="priority">
                        <option value="">全部优先级</option>
                        <option value="low" <?= ($_GET['priority'] ?? '') === 'low' ? 'selected' : '' ?>>低</option>
                        <option value="normal" <?= ($_GET['priority'] ?? '') === 'normal' ? 'selected' : '' ?>>普通</option>
                        <option value="high" <?= ($_GET['priority'] ?? '') === 'high' ? 'selected' : '' ?>>高</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">分类</label>
                    <select class="form-select" name="category">
                        <option value="">全部分类</option>
                        <option value="technical" <?= ($_GET['category'] ?? '') === 'technical' ? 'selected' : '' ?>>技术问题</option>
                        <option value="billing" <?= ($_GET['category'] ?? '') === 'billing' ? 'selected' : '' ?>>账单问题</option>
                        <option value="domain" <?= ($_GET['category'] ?? '') === 'domain' ? 'selected' : '' ?>>域名问题</option>
                        <option value="other" <?= ($_GET['category'] ?? '') === 'other' ? 'selected' : '' ?>>其他</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <a href="?" class="btn btn-outline-secondary">重置</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 工单列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">工单列表</h5>
        </div>
        <div class="card-body">
            <?php if (empty($tickets)): ?>
            <div class="text-center py-4">
                <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无工单记录</p>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户</th>
                            <th>标题</th>
                            <th>分类</th>
                            <th>优先级</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tickets as $ticket): ?>
                        <tr class="<?= $ticket['priority'] === 'high' ? 'table-danger' : '' ?>">
                            <td><?= $ticket['id'] ?></td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($ticket['username']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars($ticket['email']) ?></small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong><?= htmlspecialchars($ticket['subject']) ?></strong>
                                    <br>
                                    <small class="text-muted"><?= htmlspecialchars(mb_substr($ticket['description'], 0, 50)) ?>...</small>
                                </div>
                            </td>
                            <td>
                                <?php
                                $categoryText = match($ticket['category']) {
                                    'technical' => '技术问题',
                                    'billing' => '账单问题',
                                    'domain' => '域名问题',
                                    'other' => '其他',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-secondary"><?= $categoryText ?></span>
                            </td>
                            <td>
                                <?php
                                $priorityClass = match($ticket['priority']) {
                                    'high' => 'danger',
                                    'normal' => 'primary',
                                    'low' => 'secondary',
                                    default => 'secondary'
                                };
                                $priorityText = match($ticket['priority']) {
                                    'high' => '高',
                                    'normal' => '普通',
                                    'low' => '低',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-<?= $priorityClass ?>"><?= $priorityText ?></span>
                            </td>
                            <td>
                                <?php
                                $statusClass = match($ticket['status']) {
                                    'open' => 'warning',
                                    'in_progress' => 'info',
                                    'closed' => 'success',
                                    default => 'secondary'
                                };
                                $statusText = match($ticket['status']) {
                                    'open' => '待处理',
                                    'in_progress' => '处理中',
                                    'closed' => '已关闭',
                                    default => '未知'
                                };
                                ?>
                                <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                            </td>
                            <td><?= date('Y-m-d H:i:s', strtotime($ticket['created_at'])) ?></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary"
                                        data-bs-toggle="modal"
                                        data-bs-target="#viewTicketModal"
                                        data-ticket-id="<?= $ticket['id'] ?>"
                                        data-ticket-title="<?= htmlspecialchars($ticket['title']) ?>"
                                        data-ticket-content="<?= htmlspecialchars($ticket['content']) ?>"
                                        data-ticket-status="<?= $ticket['status'] ?>"
                                        data-user-name="<?= htmlspecialchars($ticket['username'] ?? '未知用户') ?>"
                                        title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if ($ticket['status'] !== 'closed'): ?>
                                <button class="btn btn-sm btn-outline-success"
                                        data-bs-toggle="modal"
                                        data-bs-target="#updateTicketModal"
                                        data-ticket-id="<?= $ticket['id'] ?>"
                                        data-ticket-title="<?= htmlspecialchars($ticket['title']) ?>"
                                        data-current-status="<?= $ticket['status'] ?>"
                                        title="处理">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <form method="POST" style="display: inline;" onsubmit="return confirm('确定要关闭这个工单吗？')">
                                    <input type="hidden" name="action" value="close_ticket">
                                    <input type="hidden" name="ticket_id" value="<?= $ticket['id'] ?>">
                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="关闭">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="工单分页">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                        <a class="page-link" href="?page=support&p=<?= $i ?><?= !empty($_GET['search']) ? '&search=' . urlencode($_GET['search']) : '' ?><?= !empty($_GET['status']) ? '&status=' . urlencode($_GET['status']) : '' ?><?= !empty($_GET['priority']) ? '&priority=' . urlencode($_GET['priority']) : '' ?><?= !empty($_GET['category']) ? '&category=' . urlencode($_GET['category']) : '' ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 查看工单详情模态框 -->
<div class="modal fade" id="viewTicketModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">工单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="ticketDetails">
                <!-- 工单详情将通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 处理工单模态框 -->
<div class="modal fade" id="updateTicketModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">处理工单</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_ticket">
                    <input type="hidden" name="ticket_id" id="update_ticket_id">
                    
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status" required>
                            <option value="open">待处理</option>
                            <option value="in_progress">处理中</option>
                            <option value="closed">已关闭</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">回复内容</label>
                        <textarea class="form-control" name="admin_response" rows="5" placeholder="请输入回复内容..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">更新工单</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 查看工单模态框
    const viewTicketModal = document.getElementById('viewTicketModal');
    if (viewTicketModal) {
        viewTicketModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const ticketId = button.getAttribute('data-ticket-id');
            const ticketTitle = button.getAttribute('data-ticket-title');
            const ticketContent = button.getAttribute('data-ticket-content');
            const ticketStatus = button.getAttribute('data-ticket-status');
            const userName = button.getAttribute('data-user-name');

            const statusMap = {
                'open': '待处理',
                'in_progress': '处理中',
                'resolved': '已解决',
                'closed': '已关闭'
            };

            document.getElementById('ticketDetails').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>工单标题：</strong> ${ticketTitle}
                    </div>
                    <div class="col-md-6">
                        <strong>提交用户：</strong> ${userName}
                    </div>
                    <div class="col-12 mt-3">
                        <strong>状态：</strong> <span class="badge bg-primary">${statusMap[ticketStatus] || ticketStatus}</span>
                    </div>
                    <div class="col-12 mt-3">
                        <strong>工单内容：</strong>
                        <div class="border p-3 mt-2 bg-light">
                            ${ticketContent.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                </div>
            `;
        });
    }

    // 处理工单模态框
    const updateTicketModal = document.getElementById('updateTicketModal');
    if (updateTicketModal) {
        updateTicketModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const ticketId = button.getAttribute('data-ticket-id');
            const ticketTitle = button.getAttribute('data-ticket-title');
            const currentStatus = button.getAttribute('data-current-status');

            document.getElementById('update_ticket_id').value = ticketId;
            document.querySelector('#updateTicketModal .modal-title').textContent = `处理工单: ${ticketTitle}`;

            // 设置当前状态
            const statusSelect = document.querySelector('#updateTicketModal select[name="status"]');
            if (statusSelect) {
                statusSelect.value = currentStatus;
            }
        });
    }
});
</script>
