<?php
/**
 * TLD数据导入页面内容
 */

// 检查权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}
?>

<style>
.result-box {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-top: 1rem;
    max-height: 400px;
    overflow-y: auto;
}
.success { color: #198754; }
.error { color: #dc3545; }
.info { color: #0dcaf0; }
.warning { color: #ffc107; }
.progress-container {
    margin: 20px 0;
}
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 10px 0;
}
</style>

<div>
        <h1>导入TLD数据到域名价格管理</h1>
        <p class="text-muted">将价格API中的119个TLD数据导入到域名价格管理系统</p>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>导入操作</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-3 w-100" onclick="checkCurrentData()">
                            <i class="fas fa-search me-2"></i>
                            检查当前数据
                        </button>
                        <small class="text-muted mb-2 d-block">查看API数据或者合作伙伴</small>

                        <button class="btn btn-success mb-3 w-100" onclick="importTldData()">
                            <i class="fas fa-download me-2"></i>
                            导入TLD数据
                        </button>
                        <small class="text-muted mb-2 d-block">将API数据或者合作伙伴数据导入数据库</small>

                        <button class="btn btn-warning mb-3 w-100" onclick="updatePrices()">
                            <i class="fas fa-sync me-2"></i>
                            更新价格信息
                        </button>
                        <small class="text-muted mb-2 d-block">查询通过API更新域名价格</small>

                        <button class="btn btn-info mb-3 w-100" onclick="verifyData()">
                            <i class="fas fa-check me-2"></i>
                            验证导入结果
                        </button>
                        <small class="text-muted mb-2 d-block">验证本地域名数据价格与API数据的数据是否一致</small>

                        <button class="btn btn-secondary mb-3 w-100" onclick="compareData()">
                            <i class="fas fa-balance-scale me-2"></i>
                            对比数据差异
                        </button>
                        <small class="text-muted mb-2 d-block">对比本地域名数据价格与API数据的数据是否一致</small>

                        <hr>

                        <button class="btn btn-warning mb-3 w-100" onclick="fullSync()">
                            <i class="fas fa-sync-alt me-2"></i>
                            完全同步
                        </button>
                        <small class="text-muted mb-2 d-block">完全同步本地和远程数据</small>

                        <button class="btn btn-danger mb-3 w-100" onclick="clearAllData()">
                            <i class="fas fa-trash me-2"></i>
                            清空所有数据
                        </button>
                        <small class="text-muted mb-2 d-block">清空本地所有域名价格数据</small>

                        <hr>

                        <button class="btn btn-info mb-3 w-100" onclick="fetchRemotePrices()">
                            <i class="fas fa-cloud-download-alt me-2"></i>
                            获取远程域名商价格
                        </button>
                        <small class="text-muted mb-2 d-block">从多个域名商获取最新价格数据</small>

                        <button class="btn btn-outline-info mb-3 w-100" onclick="showRemoteProviders()">
                            <i class="fas fa-list me-2"></i>
                            查看支持的域名商
                        </button>
                        <small class="text-muted mb-2 d-block">查看所有支持的域名商和价格源</small>
                    </div>
                </div>
                
                <div class="stats-card">
                    <h6>预期导入数据</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 id="expectedTlds">119</h3>
                            <small>TLD数量</small>
                        </div>
                        <div class="col-6">
                            <h3 id="expectedCategories">17</h3>
                            <small>分类数量</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h5>操作结果</h5>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearResult()">清空</button>
                    </div>
                    <div class="card-body">
                        <div class="progress-container" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="result-box" id="result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }

        function showProgress(percent) {
            const progressContainer = document.querySelector('.progress-container');
            const progressBar = document.querySelector('.progress-bar');
            
            if (percent > 0) {
                progressContainer.style.display = 'block';
                progressBar.style.width = percent + '%';
                progressBar.textContent = Math.round(percent) + '%';
            } else {
                progressContainer.style.display = 'none';
            }
        }

        async function checkCurrentData() {
            showResult('正在查看API数据或者合作伙伴...', 'info');

            try {
                // 获取API数据
                const apiResponse = await fetch('ajax/domain-price-api.php?action=get-all-prices');
                const apiResult = await apiResponse.json();

                if (apiResult.success) {
                    showResult('✅ API数据获取成功', 'success');
                    showResult(`📊 API中有 ${apiResult.total} 个TLD`, 'info');

                    // 显示API数据示例
                    if (apiResult.prices) {
                        showResult('📋 API数据示例 (前10个):', 'info');
                        const tlds = Object.keys(apiResult.prices).slice(0, 10);
                        tlds.forEach(tld => {
                            const price = apiResult.prices[tld];
                            showResult(`  .${tld}: $${price.registration} (${price.category || 'generic'})`, 'info');
                        });
                    }

                    // 检查合作伙伴数据
                    showResult('🤝 检查合作伙伴数据...', 'info');
                    const partnerResponse = await fetch('ajax/remote-price-fetcher.php?action=get-providers');
                    const partnerResult = await partnerResponse.json();

                    if (partnerResult.success) {
                        const availablePartners = partnerResult.providers.filter(p => p.available);
                        showResult(`✅ 可用合作伙伴: ${availablePartners.length} 个`, 'success');
                        availablePartners.forEach(partner => {
                            showResult(`  📈 ${partner.name} - 支持 ${partner.supported_tlds} 个TLD`, 'info');
                        });
                    }
                } else {
                    showResult('❌ 获取API数据失败: ' + apiResult.error, 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        async function importTldData() {
            showResult('开始将API数据或者合作伙伴数据导入数据库...', 'info');
            showProgress(10);
            
            try {
                // 第一步：获取价格数据
                showResult('📥 正在获取价格API数据...', 'info');
                const priceResponse = await fetch('ajax/domain-price-api.php?action=get-all-prices');
                const priceResult = await priceResponse.json();
                
                if (!priceResult.success) {
                    throw new Error('获取价格数据失败: ' + priceResult.error);
                }
                
                showProgress(30);
                showResult(`✅ 获取到 ${priceResult.total} 个TLD价格数据`, 'success');
                
                // 第二步：同步到数据库
                showResult('💾 正在同步到数据库...', 'info');
                const syncResponse = await fetch('ajax/domain-price-api.php?action=sync-to-database', {
                    method: 'POST'
                });
                const syncResult = await syncResponse.json();
                
                if (!syncResult.success) {
                    throw new Error('数据库同步失败: ' + syncResult.error);
                }
                
                showProgress(80);
                showResult('✅ 数据库同步成功!', 'success');
                showResult(`📈 新增: ${syncResult.added} 个TLD`, 'info');
                showResult(`📊 更新: ${syncResult.updated} 个TLD`, 'info');
                showResult(`📋 总计: ${syncResult.total} 个TLD`, 'info');
                
                showProgress(100);
                
                // 第三步：验证结果
                setTimeout(() => {
                    showProgress(0);
                    showResult('🎉 TLD数据导入完成！', 'success');
                    showResult('💡 建议点击"验证导入结果"确认数据完整性', 'info');
                }, 1000);
                
            } catch (error) {
                showProgress(0);
                showResult('❌ 导入失败: ' + error.message, 'error');
            }
        }

        async function updatePrices() {
            showResult('正在查询通过API更新域名价格...', 'info');

            try {
                // 第一步：获取最新API价格
                showResult('📡 正在获取最新API价格...', 'info');
                const apiResponse = await fetch('ajax/domain-price-api.php?action=get-all-prices');
                const apiResult = await apiResponse.json();

                if (!apiResult.success) {
                    throw new Error('获取API价格失败: ' + apiResult.error);
                }

                // 第二步：获取当前数据库价格
                showResult('📊 正在获取当前数据库价格...', 'info');
                const dbResponse = await fetch('ajax/test-database.php?action=view-prices');
                const dbResult = await dbResponse.json();

                if (!dbResult.success) {
                    throw new Error('获取数据库价格失败: ' + dbResult.error);
                }

                // 第三步：比较并更新价格
                showResult('🔄 正在比较和更新价格...', 'info');
                const updateResponse = await fetch('ajax/compare-tld-data.php?action=update-prices', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        api_prices: apiResult.prices,
                        db_prices: dbResult.prices
                    })
                });
                const updateResult = await updateResponse.json();

                if (updateResult.success) {
                    showResult('✅ 价格更新成功!', 'success');
                    showResult(`� 更新了 ${updateResult.updated_count || 0} 个TLD价格`, 'info');
                    showResult(`💰 平均价格变化: ${updateResult.avg_change > 0 ? '+' : ''}$${updateResult.avg_change || 0}`, 'info');

                    if (updateResult.updated_tlds && updateResult.updated_tlds.length > 0) {
                        showResult('📋 更新的TLD (前5个):', 'info');
                        updateResult.updated_tlds.slice(0, 5).forEach(tld => {
                            showResult(`  .${tld.tld}: $${tld.old_price} → $${tld.new_price}`, 'info');
                        });
                    }
                } else {
                    showResult('❌ 更新失败: ' + updateResult.error, 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        async function verifyData() {
            showResult('正在验证本地域名数据价格与API数据的数据是否一致...', 'info');
            
            try {
                // 检查数据库数据
                const dbResponse = await fetch('ajax/test-database.php?action=view-prices');
                const dbResult = await dbResponse.json();
                
                // 检查API数据
                const apiResponse = await fetch('ajax/domain-price-api.php?action=get-all-prices');
                const apiResult = await apiResponse.json();
                
                if (dbResult.success && apiResult.success) {
                    const dbCount = dbResult.total;
                    const apiCount = apiResult.total;
                    
                    showResult('✅ 数据验证完成', 'success');
                    showResult(`📊 数据库TLD数量: ${dbCount}`, 'info');
                    showResult(`📊 API TLD数量: ${apiCount}`, 'info');
                    
                    if (dbCount === apiCount) {
                        showResult('🎉 数据完整性验证通过！', 'success');
                    } else {
                        showResult('⚠️ 数据不一致，建议重新导入', 'warning');
                    }
                    
                    // 检查分类分布
                    const categories = {};
                    dbResult.prices.forEach(price => {
                        const category = price.category || 'unknown';
                        categories[category] = (categories[category] || 0) + 1;
                    });
                    
                    showResult('📋 分类验证:', 'info');
                    Object.entries(categories).forEach(([category, count]) => {
                        showResult(`  - ${category}: ${count} 个TLD`, 'info');
                    });
                    
                } else {
                    showResult('❌ 验证失败', 'error');
                }
            } catch (error) {
                showResult('❌ 验证失败: ' + error.message, 'error');
            }
        }

        async function compareData() {
            showResult('正在对比本地域名数据价格与API数据的数据是否一致...', 'info');

            try {
                const response = await fetch('ajax/compare-tld-data.php?action=compare-data');
                const result = await response.json();

                if (result.success) {
                    showResult('✅ 数据对比完成', 'success');
                    showResult(`📊 数据库TLD: ${result.summary.db_total} 个`, 'info');
                    showResult(`📊 API TLD: ${result.summary.api_total} 个`, 'info');
                    showResult(`🤝 共同TLD: ${result.summary.common} 个`, 'info');
                    showResult(`➕ 仅数据库有: ${result.summary.only_in_db} 个`, 'warning');
                    showResult(`➖ 仅API有: ${result.summary.only_in_api} 个`, 'warning');

                    if (result.summary.only_in_db > 0) {
                        showResult('📋 仅数据库中的TLD:', 'info');
                        result.details.only_in_db.slice(0, 10).forEach(tld => {
                            showResult(`  - .${tld.tld} ($${tld.registration_price}, ${tld.category})`, 'info');
                        });
                        if (result.details.only_in_db.length > 10) {
                            showResult(`  ... 还有 ${result.details.only_in_db.length - 10} 个`, 'info');
                        }
                    }

                    if (result.summary.only_in_api > 0) {
                        showResult('📋 仅API中的TLD:', 'info');
                        result.details.only_in_api.slice(0, 10).forEach(tld => {
                            showResult(`  - .${tld.tld} ($${tld.registration_price}, ${tld.category})`, 'info');
                        });
                        if (result.details.only_in_api.length > 10) {
                            showResult(`  ... 还有 ${result.details.only_in_api.length - 10} 个`, 'info');
                        }
                    }
                } else {
                    showResult('❌ 对比失败: ' + result.error, 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        async function fullSync() {
            if (!confirm('确定要执行完全同步吗？这将清空现有数据并重新导入API数据！')) {
                return;
            }

            showResult('开始完全同步...', 'warning');
            showProgress(10);

            try {
                const response = await fetch('ajax/compare-tld-data.php?action=full-sync', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showProgress(100);
                    showResult('✅ 完全同步成功!', 'success');
                    showResult(`🗑️ 清空: ${result.cleared} 个TLD`, 'info');
                    showResult(`📈 新增: ${result.added} 个TLD`, 'info');
                    showResult(`📋 总计: ${result.total} 个TLD`, 'info');

                    setTimeout(() => {
                        showProgress(0);
                        showResult('🎉 现在数据库和API完全一致！', 'success');
                    }, 1000);
                } else {
                    showProgress(0);
                    showResult('❌ 完全同步失败: ' + result.error, 'error');
                }
            } catch (error) {
                showProgress(0);
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        async function clearAllData() {
            if (!confirm('确定要清空所有TLD数据吗？此操作不可恢复！')) {
                return;
            }

            showResult('正在清空所有数据...', 'warning');

            try {
                const response = await fetch('ajax/test-database.php?action=clear-prices', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showResult('✅ 数据清空成功', 'success');
                } else {
                    showResult('❌ 清空失败: ' + result.error, 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        async function fetchRemotePrices() {
            showResult('正在获取远程域名商价格数据...', 'info');

            try {
                const response = await fetch('ajax/remote-price-fetcher.php?action=fetch-prices', {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.success) {
                    showResult('✅ 远程价格获取成功!', 'success');
                    showResult(`📊 获取了 ${result.total_providers} 个域名商的价格`, 'info');
                    showResult(`📈 更新了 ${result.updated_tlds} 个TLD价格`, 'info');

                    // 显示价格对比
                    if (result.price_comparisons && result.price_comparisons.length > 0) {
                        showResult('💰 价格对比 (前10个TLD):', 'info');
                        result.price_comparisons.slice(0, 10).forEach(comparison => {
                            showResult(`  .${comparison.tld}: 当前 $${comparison.current_price} → 最低 $${comparison.best_price} (${comparison.provider})`, 'info');
                        });
                    }
                } else {
                    showResult('❌ 远程价格获取失败: ' + result.error, 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        async function showRemoteProviders() {
            showResult('正在获取支持的域名商列表...', 'info');

            try {
                const response = await fetch('ajax/remote-price-fetcher.php?action=get-providers');
                const result = await response.json();

                if (result.success) {
                    showResult('📋 支持的域名商:', 'success');
                    result.providers.forEach(provider => {
                        const status = provider.available ? '✅' : '❌';
                        showResult(`  ${status} ${provider.name} - ${provider.description}`, 'info');
                        if (provider.supported_tlds) {
                            showResult(`    支持 ${provider.supported_tlds} 个TLD`, 'info');
                        }
                    });
                } else {
                    showResult('❌ 获取域名商列表失败: ' + result.error, 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            showResult('TLD数据导入工具已准备就绪', 'info');
            showResult('这个工具将把价格API中的119个TLD数据导入到域名价格管理系统', 'info');
            showResult('建议先点击"检查当前数据"了解现状', 'info');
        });
    </script>
