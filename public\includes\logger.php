<?php
/**
 * 统一日志系统
 */

class Logger {
    private static $logDir;
    
    public static function init($logDir = null) {
        self::$logDir = $logDir ?: dirname(dirname(dirname(__DIR__))) . '/logs';
        if (!is_dir(self::$logDir)) {
            mkdir(self::$logDir, 0755, true);
        }
    }
    
    public static function log($level, $message, $context = []) {
        self::init();
        
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        
        $logMessage = "[$timestamp] [$level] $message";
        if ($contextStr) {
            $logMessage .= " Context: $contextStr";
        }
        
        $logFile = self::$logDir . '/system-' . date('Y-m-d') . '.log';
        file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    public static function error($message, $context = []) {
        self::log('ERROR', $message, $context);
    }
    
    public static function info($message, $context = []) {
        self::log('INFO', $message, $context);
    }
}
?>