<?php
/**
 * 支付配置API接口
 * Payment Configuration API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));

// 引入统一的数据库连接
require_once dirname(__DIR__) . '/includes/database.php';
    }
    return true;
}

// 数据库连接
function getDatabase() {
    static $pdo = null;
    if ($pdo === null) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $database = $_ENV['DB_DATABASE'] ?? 'www_bt_cn';
        $username = $_ENV['DB_USERNAME'] ?? 'www_bt_cn';
        $password = $_ENV['DB_PASSWORD'] ?? 'YAfxfrB8nr6F84LP';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    return $pdo;
}

// 记录活动日志
function logActivity($action, $type, $userId = null, $details = []) {
    try {
        $db = getDatabase();
        $stmt = $db->prepare("INSERT INTO activity_logs (user_id, action, type, details, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            $userId,
            $action,
            $type,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
        error_log('Activity log failed: ' . $e->getMessage());
    }
}

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // 获取支付配置
        $paymentMethod = $_GET['method'] ?? '';
        
        if (!$paymentMethod) {
            throw new Exception('支付方式参数不能为空', 400);
        }
        
        $db = getDatabase();
        $stmt = $db->prepare("SELECT enabled, config FROM payment_configs WHERE payment_method = ?");
        $stmt->execute([$paymentMethod]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'enabled' => (bool)$result['enabled'],
                'config' => json_decode($result['config'], true)
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'enabled' => false,
                'config' => []
            ]);
        }
        
    } elseif ($method === 'POST') {
        // 保存支付配置
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('请求数据格式错误', 400);
        }
        
        $paymentMethod = $input['method'] ?? '';
        $enabled = isset($input['enabled']) ? (bool)$input['enabled'] : false;
        $config = $input['config'] ?? [];
        
        if (!$paymentMethod) {
            throw new Exception('支付方式不能为空', 400);
        }
        
        // 验证配置参数
        if ($paymentMethod === 'alipay') {
            $signType = $config['sign_type'] ?? 'MD5';
            
            if ($signType === 'MD5') {
                if (empty($config['partner_id']) || empty($config['md5_key'])) {
                    throw new Exception('合作伙伴身份和MD5密钥不能为空', 400);
                }
            } else {
                if (empty($config['app_id']) || empty($config['rsa_private_key']) || empty($config['rsa_public_key'])) {
                    throw new Exception('应用ID、私钥和公钥不能为空', 400);
                }
            }
        }
        
        $db = getDatabase();
        
        // 检查支付方式是否存在
        $stmt = $db->prepare("SELECT id FROM payment_configs WHERE payment_method = ?");
        $stmt->execute([$paymentMethod]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            // 更新配置
            $stmt = $db->prepare("UPDATE payment_configs SET enabled = ?, config = ?, updated_at = NOW() WHERE payment_method = ?");
            $stmt->execute([$enabled ? 1 : 0, json_encode($config), $paymentMethod]);
        } else {
            // 插入新配置
            $stmt = $db->prepare("INSERT INTO payment_configs (payment_method, enabled, config, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
            $stmt->execute([$paymentMethod, $enabled ? 1 : 0, json_encode($config)]);
        }
        
        // 记录日志
        logActivity('payment_config_update', 'payment', null, [
            'method' => $paymentMethod,
            'enabled' => $enabled,
            'config_keys' => array_keys($config)
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => '配置保存成功'
        ]);
        
    } else {
        throw new Exception('不支持的请求方法', 405);
    }
    
} catch (Exception $e) {
    $code = $e->getCode() ?: 500;
    http_response_code($code);
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => $code
    ]);
    
    // 记录错误日志
    logActivity('payment_config_error', 'api', null, [
        'error' => $e->getMessage(),
        'code' => $code,
        'method' => $_SERVER['REQUEST_METHOD']
    ]);
}
?>
