<?php
/**
 * 购物车API
 * Shopping Cart API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once '../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDatabase();
    
    switch ($method) {
        case 'GET':
            handleGetRequest($db);
            break;
            
        case 'POST':
            handlePostRequest($db, $input);
            break;
            
        case 'PUT':
            handlePutRequest($db, $input);
            break;
            
        case 'DELETE':
            handleDeleteRequest($db, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误：' . $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取购物车内容
 */
function handleGetRequest($db) {
    $sessionId = session_id();
    $userId = $_SESSION['user_id'] ?? null;
    
    // 获取购物车商品
    $cartItems = getCartItems($db, $sessionId, $userId);
    
    // 计算总价
    $total = calculateCartTotal($cartItems);
    
    echo json_encode([
        'success' => true,
        'data' => [
            'items' => $cartItems,
            'total' => $total,
            'count' => count($cartItems)
        ]
    ]);
}

/**
 * 处理POST请求 - 添加商品到购物车
 */
function handlePostRequest($db, $input) {
    $sessionId = session_id();
    $userId = $_SESSION['user_id'] ?? null;
    
    $domain = trim($input['domain'] ?? '');
    $period = intval($input['period'] ?? 1);
    $type = $input['type'] ?? 'register'; // register, renew, transfer
    
    if (empty($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名不能为空']);
        return;
    }
    
    if ($period < 1 || $period > 10) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '注册年限必须在1-10年之间']);
        return;
    }
    
    // 验证域名格式
    if (!isValidDomain($domain)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '域名格式不正确']);
        return;
    }
    
    // 检查域名是否已在购物车中
    $stmt = $db->prepare("SELECT id FROM cart_items WHERE (session_id = ? OR user_id = ?) AND domain_name = ? AND type = ?");
    $stmt->execute([$sessionId, $userId, $domain, $type]);
    
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => '该域名已在购物车中']);
        return;
    }
    
    // 获取域名价格
    $price = getDomainPrice($domain, $type);
    $totalPrice = $price * $period;
    
    // 添加到购物车
    $stmt = $db->prepare("
        INSERT INTO cart_items (session_id, user_id, domain_name, type, period, unit_price, total_price, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$sessionId, $userId, $domain, $type, $period, $price, $totalPrice]);
    
    echo json_encode([
        'success' => true,
        'message' => '已添加到购物车',
        'data' => [
            'id' => $db->lastInsertId(),
            'domain' => $domain,
            'type' => $type,
            'period' => $period,
            'price' => $totalPrice
        ]
    ]);
}

/**
 * 处理PUT请求 - 更新购物车商品
 */
function handlePutRequest($db, $input) {
    $sessionId = session_id();
    $userId = $_SESSION['user_id'] ?? null;
    
    $itemId = intval($input['id'] ?? 0);
    $period = intval($input['period'] ?? 1);
    
    if ($itemId <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '商品ID无效']);
        return;
    }
    
    if ($period < 1 || $period > 10) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '注册年限必须在1-10年之间']);
        return;
    }
    
    // 获取购物车商品
    $stmt = $db->prepare("SELECT * FROM cart_items WHERE id = ? AND (session_id = ? OR user_id = ?)");
    $stmt->execute([$itemId, $sessionId, $userId]);
    $item = $stmt->fetch();
    
    if (!$item) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '购物车商品不存在']);
        return;
    }
    
    // 计算新价格
    $totalPrice = $item['unit_price'] * $period;
    
    // 更新购物车商品
    $stmt = $db->prepare("UPDATE cart_items SET period = ?, total_price = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$period, $totalPrice, $itemId]);
    
    echo json_encode([
        'success' => true,
        'message' => '购物车已更新',
        'data' => [
            'id' => $itemId,
            'period' => $period,
            'price' => $totalPrice
        ]
    ]);
}

/**
 * 处理DELETE请求 - 删除购物车商品
 */
function handleDeleteRequest($db, $input) {
    $sessionId = session_id();
    $userId = $_SESSION['user_id'] ?? null;
    
    $itemId = intval($input['id'] ?? 0);
    
    if ($itemId <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '商品ID无效']);
        return;
    }
    
    // 删除购物车商品
    $stmt = $db->prepare("DELETE FROM cart_items WHERE id = ? AND (session_id = ? OR user_id = ?)");
    $stmt->execute([$itemId, $sessionId, $userId]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => '商品已从购物车中删除'
        ]);
    } else {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '购物车商品不存在']);
    }
}

/**
 * 获取购物车商品
 */
function getCartItems($db, $sessionId, $userId) {
    $sql = "SELECT * FROM cart_items WHERE session_id = ?";
    $params = [$sessionId];
    
    if ($userId) {
        $sql .= " OR user_id = ?";
        $params[] = $userId;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll();
}

/**
 * 计算购物车总价
 */
function calculateCartTotal($cartItems) {
    $total = 0;
    foreach ($cartItems as $item) {
        $total += $item['total_price'];
    }
    return $total;
}

/**
 * 验证域名格式
 */
function isValidDomain($domain) {
    return preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/', $domain);
}

/**
 * 获取域名价格
 */
function getDomainPrice($domain, $type = 'register') {
    $extension = strtolower(substr($domain, strrpos($domain, '.')));
    
    $prices = [
        '.com' => ['register' => 89, 'renew' => 89, 'transfer' => 89],
        '.net' => ['register' => 99, 'renew' => 99, 'transfer' => 99],
        '.org' => ['register' => 109, 'renew' => 109, 'transfer' => 109],
        '.cn' => ['register' => 29, 'renew' => 29, 'transfer' => 29],
        '.com.cn' => ['register' => 39, 'renew' => 39, 'transfer' => 39],
        '.net.cn' => ['register' => 39, 'renew' => 39, 'transfer' => 39],
        '.org.cn' => ['register' => 39, 'renew' => 39, 'transfer' => 39],
        '.info' => ['register' => 119, 'renew' => 119, 'transfer' => 119],
        '.biz' => ['register' => 129, 'renew' => 129, 'transfer' => 129],
        '.me' => ['register' => 199, 'renew' => 199, 'transfer' => 199]
    ];
    
    $defaultPrice = ['register' => 149, 'renew' => 149, 'transfer' => 149];
    $domainPrices = $prices[$extension] ?? $defaultPrice;
    
    return $domainPrices[$type] ?? $domainPrices['register'];
}
?>
