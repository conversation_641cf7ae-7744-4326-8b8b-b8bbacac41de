<?php
/**
 * 邮件配置测试页面
 */
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">邮件配置测试</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="#">邮件管理</a></li>
                    <li class="breadcrumb-item active">邮件配置测试</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>邮件配置测试页面
                    </h5>
                </div>
                <div class="card-body">
                    <p>这是一个测试页面，用于验证邮件管理功能是否正常工作。</p>
                    <p>当前时间: <?= date('Y-m-d H:i:s') ?></p>
                    
                    <?php
                    try {
                        $db = getDatabase();
                        echo '<div class="alert alert-success">数据库连接成功</div>';
                        
                        // 测试查询
                        $stmt = $db->query("SELECT COUNT(*) as count FROM system_settings");
                        $result = $stmt->fetch();
                        echo '<p>系统设置表记录数: ' . $result['count'] . '</p>';
                        
                    } catch (Exception $e) {
                        echo '<div class="alert alert-danger">数据库连接失败: ' . $e->getMessage() . '</div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>
