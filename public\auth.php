<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 定义根路径常量
define('ROOT_PATH', dirname(__DIR__));

require_once 'config.php';

session_start();

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = getDatabase();
    if (!$db) {
        throw new Exception('数据库连接失败');
    }

    switch ($method) {
        case 'GET':
            handleGetRequest();
            break;
        case 'POST':
            handlePostRequest($db, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取当前用户状态
 */
function handleGetRequest() {
    $action = $_GET['action'] ?? 'status';
    
    switch ($action) {
        case 'status':
            // 返回当前登录状态
            if (isset($_SESSION['user_id'])) {
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'logged_in' => true,
                        'user_id' => $_SESSION['user_id'],
                        'username' => $_SESSION['username'] ?? '',
                        'email' => $_SESSION['email'] ?? ''
                    ]
                ]);
            } else {
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'logged_in' => false
                    ]
                ]);
            }
            break;
            
        case 'logout':
            // 退出登录
            session_destroy();
            echo json_encode([
                'success' => true,
                'message' => '退出登录成功'
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 处理POST请求 - 登录和注册
 */
function handlePostRequest($db, $input) {
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'login':
            handleLogin($db, $input);
            break;
        case 'register':
            handleRegister($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
}

/**
 * 处理用户登录
 */
function handleLogin($db, $input) {
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '用户名和密码不能为空']);
        return;
    }
    
    try {
        // 查找用户
        $sql = "SELECT id, username, email, password FROM users WHERE username = ? OR email = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => '用户不存在']);
            return;
        }
        
        // 验证密码
        if (!empty($user['password']) && password_verify($password, $user['password'])) {
            // 登录成功
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            
            echo json_encode([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email']
                ]
            ]);
        } else {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => '密码错误']);
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '登录失败: ' . $e->getMessage()]);
    }
}

/**
 * 处理用户注册
 */
function handleRegister($db, $input) {
    $username = trim($input['username'] ?? '');
    $email = trim($input['email'] ?? '');
    $password = $input['password'] ?? '';

    if (empty($username) || empty($email) || empty($password)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '所有字段都是必填的']);
        return;
    }

    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '邮箱格式不正确']);
        return;
    }

    // 验证用户名格式（只允许字母、数字、下划线）
    if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '用户名只能包含字母、数字、下划线，长度3-20位']);
        return;
    }

    // 验证密码强度
    if (strlen($password) < 6) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '密码长度至少6位']);
        return;
    }
    
    try {
        // 检查用户名和邮箱是否已存在
        $sql = "SELECT id FROM users WHERE username = ? OR email = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetch()) {
            http_response_code(409);
            echo json_encode(['success' => false, 'message' => '用户名或邮箱已存在']);
            return;
        }
        
        // 创建新用户
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password, balance) VALUES (?, ?, ?, 0.00)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$username, $email, $hashedPassword]);
        
        $userId = $db->lastInsertId();
        
        // 自动登录
        $_SESSION['user_id'] = $userId;
        $_SESSION['username'] = $username;
        $_SESSION['email'] = $email;
        
        echo json_encode([
            'success' => true,
            'message' => '注册成功',
            'data' => [
                'user_id' => $userId,
                'username' => $username,
                'email' => $email
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => '注册失败: ' . $e->getMessage()]);
    }
}
