<?php
// 初始化变量
$orders = [];
$totalCount = 0;
$totalPages = 0;
$stats = [
    'total' => 0,
    'pending' => 0,
    'completed' => 0,
    'cancelled' => 0,
    'refunded' => 0
];
$message = '';
$error = '';
$usingDatabase = false;

try {
    $db = getDatabase();

    // 检查orders表是否存在
    $tablesCheck = $db->query("SHOW TABLES LIKE 'orders'")->fetchAll();

    if (empty($tablesCheck)) {
        throw new Exception("订单表不存在");
    }

    $usingDatabase = true;

    // 处理操作
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'update_status':
                $orderId = $_POST['order_id'] ?? '';
                $newStatus = $_POST['new_status'] ?? '';

                if ($orderId && $newStatus) {
                    $stmt = $db->prepare("UPDATE orders SET status = ? WHERE id = ?");
                    $stmt->execute([$newStatus, $orderId]);
                    $message = '订单状态已更新';
                }
                break;

            case 'update_payment_status':
                $orderId = $_POST['order_id'] ?? '';
                $newPaymentStatus = $_POST['new_payment_status'] ?? '';

                if ($orderId && $newPaymentStatus) {
                    $stmt = $db->prepare("UPDATE orders SET payment_status = ? WHERE id = ?");
                    $stmt->execute([$newPaymentStatus, $orderId]);
                    $message = '支付状态已更新';
                }
                break;
        }
    }
} catch (Exception $e) {
    $error = '数据库连接失败: ' . $e->getMessage();
}

// 获取订单列表（仅在数据库可用时）
if ($usingDatabase) {
    try {
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';
        $payment_status = $_GET['payment_status'] ?? '';
        $date_from = $_GET['date_from'] ?? '';
        $date_to = $_GET['date_to'] ?? '';
        $page_num = max(1, intval($_GET['page_num'] ?? 1));
        $limit = 20;
        $offset = ($page_num - 1) * $limit;

        $where = [];
        $params = [];

        if ($search) {
            $where[] = "(o.order_number LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }

        if ($status) {
            $where[] = "o.status = ?";
            $params[] = $status;
        }

        if ($payment_status) {
            $where[] = "o.payment_status = ?";
            $params[] = $payment_status;
        }

        if ($date_from) {
            $where[] = "DATE(o.created_at) >= ?";
            $params[] = $date_from;
        }

        if ($date_to) {
            $where[] = "DATE(o.created_at) <= ?";
            $params[] = $date_to;
        }

        $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

        // 获取总数
        $countSql = "SELECT COUNT(*) FROM orders o
                     LEFT JOIN users u ON o.user_id = u.id
                     $whereClause";
        $totalOrders = $db->prepare($countSql);
        $totalOrders->execute($params);
        $totalCount = $totalOrders->fetchColumn();
        $totalPages = ceil($totalCount / $limit);

        // 获取订单列表
        $sql = "SELECT o.*, u.username, u.email
                FROM orders o
                LEFT JOIN users u ON o.user_id = u.id
                $whereClause
                ORDER BY o.created_at DESC
                LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll();

        // 获取统计数据
        $stats = $db->query("SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_orders
            FROM orders")->fetch();

    } catch (Exception $e) {
        $error = '获取订单数据失败: ' . $e->getMessage();
    }
}
?>

<!-- 消息提示 -->
<?php if ($message): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!$usingDatabase): ?>
<div class="alert alert-warning" role="alert">
    <i class="fas fa-database me-2"></i>
    <strong>数据库未配置</strong> - 订单表不存在或数据库连接失败。
    <a href="check-database.php" class="alert-link">点击这里检查数据库配置</a>
</div>
<?php endif; ?>

<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">订单管理</h1>
        <p class="text-muted">管理所有域名订单</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
            <i class="fas fa-print me-2"></i>
            打印报表
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportOrders()">
            <i class="fas fa-download me-2"></i>
            导出数据
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-primary bg-opacity-10 text-primary rounded-3 p-3 me-3">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总订单数</h6>
                        <h4 class="mb-0"><?= number_format($stats['total'] ?? 0) ?></h4>
                        <small class="text-success">今日: <?= $stats['today_orders'] ?? 0 ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-warning bg-opacity-10 text-warning rounded-3 p-3 me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">待处理</h6>
                        <h4 class="mb-0"><?= number_format($stats['pending'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-success bg-opacity-10 text-success rounded-3 p-3 me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">已完成</h6>
                        <h4 class="mb-0"><?= number_format($stats['completed'] ?? 0) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stat-icon bg-info bg-opacity-10 text-info rounded-3 p-3 me-3">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">总收入</h6>
                        <h4 class="mb-0">$<?= number_format($stats['total_revenue'] ?? 0, 2) ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="orders">
            <div class="col-md-3">
                <label class="form-label">搜索</label>
                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="订单号、用户、域名...">
            </div>
            <div class="col-md-2">
                <label class="form-label">订单状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>待处理</option>
                    <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>处理中</option>
                    <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>已完成</option>
                    <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>已取消</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">支付状态</label>
                <select class="form-select" name="payment_status">
                    <option value="">全部状态</option>
                    <option value="pending" <?= $payment_status === 'pending' ? 'selected' : '' ?>>待支付</option>
                    <option value="paid" <?= $payment_status === 'paid' ? 'selected' : '' ?>>已支付</option>
                    <option value="failed" <?= $payment_status === 'failed' ? 'selected' : '' ?>>支付失败</option>
                    <option value="refunded" <?= $payment_status === 'refunded' ? 'selected' : '' ?>>已退款</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">开始日期</label>
                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">结束日期</label>
                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 订单列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            订单列表 (共 <?= number_format($totalCount) ?> 个订单)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>域名</th>
                        <th>金额</th>
                        <th>订单状态</th>
                        <th>支付状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($orders)): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无订单数据</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($orders as $order): ?>
                    <tr>
                        <td>
                            <span class="fw-medium">#<?= htmlspecialchars($order['order_number'] ?? $order['id']) ?></span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-medium"><?= htmlspecialchars($order['username'] ?? 'N/A') ?></div>
                                    <div class="text-muted small"><?= htmlspecialchars($order['email'] ?? 'N/A') ?></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-globe text-primary me-2"></i>
                                <span class="fw-medium"><?= htmlspecialchars($order['domain_name'] ?? 'N/A') ?></span>
                            </div>
                        </td>
                        <td>
                            <span class="fw-medium text-success">$<?= number_format($order['total_amount'] ?? 0, 2) ?></span>
                        </td>
                        <td>
                            <?php
                            $statusClass = [
                                'pending' => 'warning',
                                'processing' => 'info',
                                'completed' => 'success',
                                'cancelled' => 'danger'
                            ][$order['status']] ?? 'secondary';
                            $statusText = [
                                'pending' => '待处理',
                                'processing' => '处理中',
                                'completed' => '已完成',
                                'cancelled' => '已取消'
                            ][$order['status']] ?? $order['status'];
                            ?>
                            <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                        </td>
                        <td>
                            <?php
                            $paymentStatusClass = [
                                'pending' => 'warning',
                                'paid' => 'success',
                                'failed' => 'danger',
                                'refunded' => 'info'
                            ][$order['payment_status']] ?? 'secondary';
                            $paymentStatusText = [
                                'pending' => '待支付',
                                'paid' => '已支付',
                                'failed' => '支付失败',
                                'refunded' => '已退款'
                            ][$order['payment_status']] ?? $order['payment_status'];
                            ?>
                            <span class="badge bg-<?= $paymentStatusClass ?>"><?= $paymentStatusText ?></span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= date('Y-m-d H:i', strtotime($order['created_at'])) ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><h6 class="dropdown-header">订单状态</h6></li>
                                    <?php foreach (['pending' => '待处理', 'processing' => '处理中', 'completed' => '已完成', 'cancelled' => '已取消'] as $statusValue => $statusLabel): ?>
                                    <?php if ($statusValue !== $order['status']): ?>
                                    <li>
                                        <form method="POST" class="dropdown-item-form">
                                            <input type="hidden" name="action" value="update_status">
                                            <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                            <input type="hidden" name="new_status" value="<?= $statusValue ?>">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-arrow-right me-2"></i>
                                                标记为<?= $statusLabel ?>
                                            </button>
                                        </form>
                                    </li>
                                    <?php endif; ?>
                                    <?php endforeach; ?>
                                    
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">支付状态</h6></li>
                                    <?php foreach (['pending' => '待支付', 'paid' => '已支付', 'failed' => '支付失败', 'refunded' => '已退款'] as $paymentValue => $paymentLabel): ?>
                                    <?php if ($paymentValue !== $order['payment_status']): ?>
                                    <li>
                                        <form method="POST" class="dropdown-item-form">
                                            <input type="hidden" name="action" value="update_payment_status">
                                            <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                                            <input type="hidden" name="new_payment_status" value="<?= $paymentValue ?>">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-credit-card me-2"></i>
                                                标记为<?= $paymentLabel ?>
                                            </button>
                                        </form>
                                    </li>
                                    <?php endif; ?>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="card-footer">
        <nav aria-label="订单列表分页">
            <ul class="pagination justify-content-center mb-0">
                <?php if ($page_num > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=orders&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&payment_status=<?= urlencode($payment_status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                    <a class="page-link" href="?page=orders&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&payment_status=<?= urlencode($payment_status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page_num < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=orders&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&payment_status=<?= urlencode($payment_status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<style>
.dropdown-item-form {
    margin: 0;
}
.dropdown-item-form .dropdown-item {
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    padding: 0.375rem 1rem;
}
.dropdown-item-form .dropdown-item:hover {
    background-color: var(--bs-dropdown-link-hover-bg);
}
</style>

<script>
function exportOrders() {
    // 这里可以实现导出功能
    alert('导出功能开发中...');
}
</script>
