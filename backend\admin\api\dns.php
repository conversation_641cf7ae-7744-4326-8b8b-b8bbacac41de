<?php
/**
 * 管理后台DNS管理API
 * Admin DNS Management API
 */

session_start();
require_once '../../config.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$domain = $_GET['domain'] ?? $_POST['domain'] ?? '';

if (empty($domain)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => '域名参数缺失']);
    exit;
}

try {
    $db = getDatabase();
    
    // 验证域名是否存在
    $stmt = $db->prepare("SELECT id, user_id FROM domains WHERE domain_name = ?");
    $stmt->execute([$domain]);
    $domainInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$domainInfo) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => '域名不存在']);
        exit;
    }
    
    switch ($method) {
        case 'GET':
            // 获取DNS记录
            getDNSRecords($domain, $db);
            break;
            
        case 'POST':
            // 添加DNS记录
            addDNSRecord($domain, $db);
            break;
            
        case 'PUT':
            // 更新DNS记录
            updateDNSRecord($domain, $db);
            break;
            
        case 'DELETE':
            // 删除DNS记录
            deleteDNSRecord($domain, $db);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    error_log("DNS API错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => '服务器内部错误']);
}

/**
 * 获取DNS记录
 */
function getDNSRecords($domain, $db) {
    try {
        // 模拟DNS记录数据（实际应该从NameSilo API获取）
        $records = [
            [
                'id' => 1,
                'type' => 'A',
                'host' => '@',
                'value' => '*************',
                'ttl' => 3600,
                'priority' => null
            ],
            [
                'id' => 2,
                'type' => 'CNAME',
                'host' => 'www',
                'value' => $domain,
                'ttl' => 3600,
                'priority' => null
            ],
            [
                'id' => 3,
                'type' => 'MX',
                'host' => '@',
                'value' => 'mail.' . $domain,
                'ttl' => 3600,
                'priority' => 10
            ]
        ];
        
        echo json_encode([
            'success' => true,
            'records' => $records,
            'domain' => $domain
        ]);
        
    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * 添加DNS记录
 */
function addDNSRecord($domain, $db) {
    $type = $_POST['type'] ?? '';
    $host = $_POST['host'] ?? '';
    $value = $_POST['value'] ?? '';
    $ttl = intval($_POST['ttl'] ?? 3600);
    $priority = $_POST['priority'] ?? null;
    
    if (empty($type) || empty($host) || empty($value)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '必填字段不能为空']);
        return;
    }
    
    // 验证记录类型
    $validTypes = ['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'SRV'];
    if (!in_array($type, $validTypes)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '无效的记录类型']);
        return;
    }
    
    // 这里应该调用NameSilo API添加DNS记录
    // 目前返回模拟成功响应
    
    echo json_encode([
        'success' => true,
        'message' => 'DNS记录添加成功',
        'record' => [
            'id' => rand(100, 999),
            'type' => $type,
            'host' => $host,
            'value' => $value,
            'ttl' => $ttl,
            'priority' => $priority
        ]
    ]);
}

/**
 * 更新DNS记录
 */
function updateDNSRecord($domain, $db) {
    parse_str(file_get_contents('php://input'), $data);
    
    $recordId = $data['record_id'] ?? '';
    $type = $data['type'] ?? '';
    $host = $data['host'] ?? '';
    $value = $data['value'] ?? '';
    $ttl = intval($data['ttl'] ?? 3600);
    $priority = $data['priority'] ?? null;
    
    if (empty($recordId) || empty($type) || empty($host) || empty($value)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '必填字段不能为空']);
        return;
    }
    
    // 这里应该调用NameSilo API更新DNS记录
    // 目前返回模拟成功响应
    
    echo json_encode([
        'success' => true,
        'message' => 'DNS记录更新成功'
    ]);
}

/**
 * 删除DNS记录
 */
function deleteDNSRecord($domain, $db) {
    parse_str(file_get_contents('php://input'), $data);
    
    $recordId = $data['record_id'] ?? '';
    
    if (empty($recordId)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '记录ID不能为空']);
        return;
    }
    
    // 这里应该调用NameSilo API删除DNS记录
    // 目前返回模拟成功响应
    
    echo json_encode([
        'success' => true,
        'message' => 'DNS记录删除成功'
    ]);
}
?>
