/**
 * NameSilo域名销售系统 - 用户中心样式
 * NameSilo Domain Sales System - User Center CSS
 */

/* 用户中心特定样式 */
.balance-display {
    background: linear-gradient(135deg, #059669, #06b6d4);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    margin: 0 1rem 1rem;
}

.balance-amount {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
}

.balance-label {
    font-size: 12px;
    opacity: 0.9;
    margin: 0;
}

/* 统计卡片增强 */
.stats-card {
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #06b6d4);
    opacity: 0;
    transition: all 0.2s ease-out;
}

.stats-card:hover::before {
    opacity: 1;
}

/* 表格优化 */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: #f3f4f6;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6b7280;
}

/* 徽章样式增强 */
.badge {
    font-weight: 500;
    letter-spacing: 0.25px;
}

.badge.bg-success {
    background: #059669 !important;
}

.badge.bg-warning {
    background: #d97706 !important;
}

.badge.bg-danger {
    background: #dc2626 !important;
}

.badge.bg-info {
    background: #2563eb !important;
}

.badge.bg-primary {
    background: #4f46e5 !important;
}

/* 按钮增强 */
.btn {
    font-weight: 500;
    letter-spacing: 0.25px;
}

.btn-sm {
    font-size: 12px;
    padding: 6px 12px;
}

/* 输入框样式 */
.form-control, .form-select {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease-out;
}

.form-control:focus, .form-select:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-label {
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.modal-header {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    font-size: 14px;
}

.alert-success {
    background: rgba(5, 150, 105, 0.1);
    color: #059669;
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    color: #d97706;
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    color: #dc2626;
}

.alert-info {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
}

/* 进度条样式 */
.progress {
    height: 6px;
    border-radius: 3px;
    background: #f3f4f6;
}

.progress-bar {
    border-radius: 3px;
}

/* 分页样式 */
.pagination {
    gap: 0.25rem;
}

.page-link {
    border: 1px solid #e5e7eb;
    color: #6b7280;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease-out;
}

.page-link:hover {
    background: #f3f4f6;
    border-color: #4f46e5;
    color: #4f46e5;
}

.page-item.active .page-link {
    background: #4f46e5;
    border-color: #4f46e5;
    color: white;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    padding: 0.25rem;
}

.dropdown-item {
    border-radius: 6px;
    font-size: 14px;
    padding: 8px 12px;
    transition: all 0.2s ease-out;
}

.dropdown-item:hover {
    background: #f3f4f6;
    color: #4f46e5;
}

/* 列表组样式 */
.list-group-item {
    border: 1px solid #e5e7eb;
    font-size: 14px;
    transition: all 0.2s ease-out;
}

.list-group-item:hover {
    background: #f3f4f6;
    color: #4f46e5;
}

/* 手风琴样式 */
.accordion-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 0.25rem;
}

.accordion-button {
    font-weight: 500;
    font-size: 14px;
    padding: 1rem;
}

.accordion-button:not(.collapsed) {
    background: #f3f4f6;
    color: #4f46e5;
}

.accordion-body {
    font-size: 14px;
    color: #6b7280;
}

/* 工具提示样式 */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background: #111827;
    border-radius: 6px;
}

/* 加载动画 */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 2px;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

/* 响应式优化 */
@media (max-width: 576px) {
    .stats-card {
        text-align: center;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 12px;
    }
    
    .btn {
        font-size: 12px;
        padding: 6px 12px;
    }
    
    .balance-display {
        margin: 0 0.5rem 0.5rem;
        padding: 0.75rem;
    }
    
    .balance-amount {
        font-size: 16px;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .top-navbar,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-area {
        padding: 0 !important;
    }
}
