<?php
$db = getDatabase();

// 检查并添加余额字段
try {
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'balance'");
    if ($stmt->rowCount() == 0) {
        $db->exec("ALTER TABLE users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '用户余额' AFTER last_login");
    }
} catch (PDOException $e) {
    // 忽略错误，可能字段已存在
}

// 处理返回管理员身份
if (isset($_GET['return_admin']) && isset($_SESSION['original_admin_id'])) {
    // 恢复管理员会话
    $_SESSION['admin_id'] = $_SESSION['original_admin_id'];
    $_SESSION['user_role'] = $_SESSION['original_admin_role'] ?? 'admin';

    // 清除用户会话信息
    unset($_SESSION['user_id']);
    unset($_SESSION['user_email']);
    unset($_SESSION['user_name']);
    unset($_SESSION['original_admin_id']);
    unset($_SESSION['original_admin_role']);
    unset($_SESSION['login_as_user']);

    $message = '已返回管理员身份';
}

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $userId = $_POST['user_id'] ?? '';

    switch ($action) {
        case 'toggle_status':
            if ($userId) {
                $stmt = $db->prepare("SELECT status FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                $newStatus = $user['status'] === 'active' ? 'inactive' : 'active';
                $db->prepare("UPDATE users SET status = ? WHERE id = ?")->execute([$newStatus, $userId]);
                $message = '用户状态已更新';
            }
            break;
            
        case 'delete_user':
            if ($userId) {
                $db->prepare("DELETE FROM users WHERE id = ? AND role != 'admin'")->execute([$userId]);
                $message = '用户已删除';
            }
            break;

        case 'update_balance':
            if ($userId) {
                $balance = floatval($_POST['balance'] ?? 0);
                $db->prepare("UPDATE users SET balance = ? WHERE id = ?")->execute([$balance, $userId]);
                $message = '余额已更新';
            }
            break;

        case 'login_as_user':
            if ($userId && $userId != $_SESSION['admin_id']) {
                // 记录管理员代理登录
                $stmt = $db->prepare("SELECT username, first_name, last_name, email FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                if ($user) {
                    // 保存原始管理员信息
                    $_SESSION['original_admin_id'] = $_SESSION['admin_id'];
                    $_SESSION['original_admin_role'] = $_SESSION['user_role'] ?? 'admin';

                    // 清除管理员会话，设置用户会话信息
                    unset($_SESSION['admin_id']);
                    unset($_SESSION['user_role']);

                    $_SESSION['user_id'] = $userId;
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = trim($user['first_name'] . ' ' . $user['last_name']) ?: $user['username'];
                    $_SESSION['login_as_user'] = true;

                    $message = '已切换到用户: ' . $user['username'] . '，正在跳转到用户中心...';

                    // 设置重定向标志
                    $_SESSION['redirect_to_dashboard'] = true;
                }
            }
            break;

        case 'add_user':
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $firstName = trim($_POST['first_name'] ?? '');
            $lastName = trim($_POST['last_name'] ?? '');
            $role = $_POST['role'] ?? 'user';

            if ($username && $email && $password) {
                try {
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("INSERT INTO users (username, email, password_hash, role, first_name, last_name, email_verified, status) VALUES (?, ?, ?, ?, ?, ?, 1, 'active')");
                    $stmt->execute([$username, $email, $passwordHash, $role, $firstName, $lastName]);
                    $message = '用户添加成功';
                } catch (PDOException $e) {
                    $error = '添加失败：' . $e->getMessage();
                }
            } else {
                $error = '请填写必填字段';
            }
            break;

        case 'edit_user':
            if ($userId) {
                $username = trim($_POST['username'] ?? '');
                $email = trim($_POST['email'] ?? '');
                $firstName = trim($_POST['first_name'] ?? '');
                $lastName = trim($_POST['last_name'] ?? '');
                $role = $_POST['role'] ?? 'user';
                $status = $_POST['status'] ?? 'active';
                $newPassword = $_POST['new_password'] ?? '';

                if ($username && $email) {
                    try {
                        if ($newPassword) {
                            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                            $stmt = $db->prepare("UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, role = ?, status = ?, password_hash = ? WHERE id = ?");
                            $stmt->execute([$username, $email, $firstName, $lastName, $role, $status, $passwordHash, $userId]);
                        } else {
                            $stmt = $db->prepare("UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, role = ?, status = ? WHERE id = ?");
                            $stmt->execute([$username, $email, $firstName, $lastName, $role, $status, $userId]);
                        }
                        $message = '用户信息已更新';
                    } catch (PDOException $e) {
                        $error = '更新失败：' . $e->getMessage();
                    }
                } else {
                    $error = '请填写必填字段';
                }
            }
            break;
    }
}

// 获取用户列表
$search = $_GET['search'] ?? '';
$role = $_GET['role'] ?? '';
$status = $_GET['status'] ?? '';
$page_num = max(1, intval($_GET['page_num'] ?? 1));
$limit = 20;
$offset = ($page_num - 1) * $limit;

$where = [];
$params = [];

if ($search) {
    $where[] = "(username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($role) {
    $where[] = "role = ?";
    $params[] = $role;
}

if ($status) {
    $where[] = "status = ?";
    $params[] = $status;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// 获取总数
$countSql = "SELECT COUNT(*) FROM users $whereClause";
$totalUsers = $db->prepare($countSql);
$totalUsers->execute($params);
$totalCount = $totalUsers->fetchColumn();
$totalPages = ceil($totalCount / $limit);

// 获取用户列表
$sql = "SELECT *, COALESCE(balance, 0.00) as balance FROM users $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $db->prepare($sql);
$stmt->execute($params);
$users = $stmt->fetchAll();
?>

<!-- 消息提示 -->
<?php if (isset($message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    <?= htmlspecialchars($message) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i>
    <?= htmlspecialchars($error) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 页面标题和操作 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">用户管理</h1>
        <p class="text-muted">管理系统用户账户</p>
    </div>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
        <i class="fas fa-plus me-2"></i>
        添加用户
    </button>
</div>

<!-- 筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="users">
            <div class="col-md-4">
                <label class="form-label">搜索</label>
                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="用户名、邮箱、姓名...">
            </div>
            <div class="col-md-3">
                <label class="form-label">角色</label>
                <select class="form-select" name="role">
                    <option value="">全部角色</option>
                    <option value="admin" <?= $role === 'admin' ? 'selected' : '' ?>>管理员</option>
                    <option value="user" <?= $role === 'user' ? 'selected' : '' ?>>普通用户</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>活跃</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>禁用</option>
                    <option value="suspended" <?= $status === 'suspended' ? 'selected' : '' ?>>暂停</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>
            用户列表 (共 <?= number_format($totalCount) ?> 个用户)
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="60">ID</th>
                        <th width="200">用户信息</th>
                        <th width="80">角色</th>
                        <th width="80">状态</th>
                        <th width="120">注册时间</th>
                        <th width="120">最后登录</th>
                        <th width="120">余额</th>
                        <th width="80">编辑</th>
                        <th width="100">登录</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <i class="fas fa-user-slash fa-2x text-muted mb-2"></i>
                            <br>
                            <span class="text-muted">暂无用户数据</span>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <span class="fw-medium">#<?= $user['id'] ?></span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div>
                                    <div class="fw-medium"><?= htmlspecialchars($user['username']) ?></div>
                                    <div class="text-muted small"><?= htmlspecialchars($user['email']) ?></div>
                                    <?php if ($user['first_name'] || $user['last_name']): ?>
                                    <div class="text-muted small">
                                        <?= htmlspecialchars(trim($user['first_name'] . ' ' . $user['last_name'])) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php
                            $roleClass = $user['role'] === 'admin' ? 'danger' : 'primary';
                            $roleText = $user['role'] === 'admin' ? '管理员' : '普通用户';
                            ?>
                            <span class="badge bg-<?= $roleClass ?>"><?= $roleText ?></span>
                        </td>
                        <td>
                            <?php
                            $statusClass = [
                                'active' => 'success',
                                'inactive' => 'secondary',
                                'suspended' => 'warning'
                            ][$user['status']] ?? 'secondary';
                            $statusText = [
                                'active' => '活跃',
                                'inactive' => '禁用',
                                'suspended' => '暂停'
                            ][$user['status']] ?? $user['status'];
                            ?>
                            <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= date('Y-m-d H:i', strtotime($user['created_at'])) ?>
                            </span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : '从未登录' ?>
                            </span>
                        </td>
                        <td class="balance-cell">
                            <div class="d-flex align-items-center justify-content-between">
                                <span class="fw-medium text-success">$<?= number_format($user['balance'], 2) ?></span>
                                <button type="button" class="btn btn-xs btn-outline-primary ms-1"
                                        data-bs-toggle="modal"
                                        data-bs-target="#balanceModal"
                                        data-user-id="<?= $user['id'] ?>"
                                        data-username="<?= htmlspecialchars($user['username']) ?>"
                                        data-balance="<?= $user['balance'] ?>"
                                        title="修改余额">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                        </td>
                        <td class="text-center">
                            <button type="button" class="btn btn-sm btn-outline-info"
                                    data-bs-toggle="modal"
                                    data-bs-target="#editUserModal"
                                    data-user-id="<?= $user['id'] ?>"
                                    data-username="<?= htmlspecialchars($user['username']) ?>"
                                    data-email="<?= htmlspecialchars($user['email']) ?>"
                                    data-first-name="<?= htmlspecialchars($user['first_name']) ?>"
                                    data-last-name="<?= htmlspecialchars($user['last_name']) ?>"
                                    data-role="<?= $user['role'] ?>"
                                    data-status="<?= $user['status'] ?>"
                                    title="编辑用户信息">
                                <i class="fas fa-user-edit me-1"></i>编辑
                            </button>
                        </td>
                        <td class="text-center">
                            <?php
                            $currentAdminId = $_SESSION['admin_id'] ?? $_SESSION['user_id'] ?? 0;
                            if ($user['role'] !== 'admin' && $user['id'] != $currentAdminId):
                            ?>
                            <a href="proxy-login.php?user_id=<?= $user['id'] ?>"
                               class="btn btn-sm btn-outline-warning"
                               title="以该用户身份登录"
                               onclick="return confirm('确定要以用户 <?= htmlspecialchars($user['username']) ?> 的身份登录吗？')">
                                <i class="fas fa-sign-in-alt me-1"></i>登录
                            </a>
                            <?php elseif ($user['role'] === 'admin'): ?>
                            <span class="text-muted small">
                                <i class="fas fa-shield-alt me-1"></i>管理员
                            </span>
                            <?php else: ?>
                            <span class="text-muted small">
                                <i class="fas fa-user-check me-1"></i>当前用户
                            </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <?php if ($user['role'] !== 'admin'): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                    <button type="submit" class="btn btn-outline-<?= $user['status'] === 'active' ? 'warning' : 'success' ?>" 
                                            title="<?= $user['status'] === 'active' ? '禁用用户' : '启用用户' ?>">
                                        <i class="fas fa-<?= $user['status'] === 'active' ? 'ban' : 'check' ?>"></i>
                                    </button>
                                </form>
                                <form method="POST" class="d-inline" onsubmit="return confirm('确定要删除这个用户吗？')">
                                    <input type="hidden" name="action" value="delete_user">
                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                    <button type="submit" class="btn btn-outline-danger" title="删除用户">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                <?php else: ?>
                                <span class="text-muted small">管理员账户</span>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="card-footer">
        <nav aria-label="用户列表分页">
            <ul class="pagination justify-content-center mb-0">
                <?php if ($page_num > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=users&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($role) ?>&status=<?= urlencode($status) ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                    <a class="page-link" href="?page=users&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($role) ?>&status=<?= urlencode($status) ?>">
                        <?= $i ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page_num < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=users&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&role=<?= urlencode($role) ?>&status=<?= urlencode($status) ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    添加新用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_user">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">用户名 *</label>
                            <input type="text" class="form-control" name="username" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">邮箱 *</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">名字</label>
                            <input type="text" class="form-control" name="first_name">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">姓氏</label>
                            <input type="text" class="form-control" name="last_name">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">密码 *</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">角色</label>
                            <select class="form-select" name="role">
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        添加用户
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 余额修改模态框 -->
<div class="modal fade" id="balanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-wallet me-2"></i>
                    修改用户余额
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_balance">
                    <input type="hidden" name="user_id" id="balanceUserId">

                    <div class="mb-3">
                        <label class="form-label">用户</label>
                        <input type="text" class="form-control" id="balanceUsername" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">当前余额</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="text" class="form-control" id="currentBalance" readonly>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">新余额 *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" name="balance" id="newBalance"
                                   step="0.01" min="0" required>
                        </div>
                        <div class="form-text">请输入新的余额金额</div>

                        <!-- 快捷操作按钮 -->
                        <div class="mt-2">
                            <small class="text-muted">快捷操作：</small>
                            <div class="btn-group btn-group-sm mt-1" role="group">
                                <button type="button" class="btn btn-outline-secondary" onclick="addToBalance(10)">+$10</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="addToBalance(50)">+$50</button>
                                <button type="button" class="btn btn-outline-secondary" onclick="addToBalance(100)">+$100</button>
                                <button type="button" class="btn btn-outline-warning" onclick="setBalance(0)">清零</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        更新余额
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 用户编辑模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>
                    编辑用户信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_user">
                    <input type="hidden" name="user_id" id="editUserId">

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">用户名 *</label>
                            <input type="text" class="form-control" name="username" id="editUsername" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">邮箱 *</label>
                            <input type="email" class="form-control" name="email" id="editEmail" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">名字</label>
                            <input type="text" class="form-control" name="first_name" id="editFirstName">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">姓氏</label>
                            <input type="text" class="form-control" name="last_name" id="editLastName">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">角色</label>
                            <select class="form-select" name="role" id="editRole">
                                <option value="user">普通用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">状态</label>
                            <select class="form-select" name="status" id="editStatus">
                                <option value="active">活跃</option>
                                <option value="inactive">禁用</option>
                                <option value="suspended">暂停</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">新密码</label>
                            <input type="password" class="form-control" name="new_password" id="editPassword">
                            <div class="form-text">留空则不修改密码</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* 优化表格样式 */
.table th {
    font-weight: 600;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6;
    vertical-align: middle;
    text-align: center;
}

.table td {
    vertical-align: middle;
    padding: 12px 8px;
}

.table td:first-child,
.table td:nth-child(3),
.table td:nth-child(4),
.table td:nth-child(7),
.table td:nth-child(8),
.table td:nth-child(9),
.table td:nth-child(10) {
    text-align: center;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 10px;
    line-height: 1.2;
}

/* 余额列样式 */
.balance-cell {
    min-width: 120px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }

    .btn-sm {
        padding: 2px 6px;
        font-size: 11px;
    }
}
</style>

<script>
// 余额修改模态框
document.addEventListener('DOMContentLoaded', function() {
    const balanceModal = document.getElementById('balanceModal');
    if (balanceModal) {
        balanceModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const username = button.getAttribute('data-username');
            const balance = button.getAttribute('data-balance');

            document.getElementById('balanceUserId').value = userId;
            document.getElementById('balanceUsername').value = username;
            document.getElementById('currentBalance').value = parseFloat(balance).toFixed(2);
            document.getElementById('newBalance').value = parseFloat(balance).toFixed(2);
        });
    }

    // 用户编辑模态框
    const editUserModal = document.getElementById('editUserModal');
    if (editUserModal) {
        editUserModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const username = button.getAttribute('data-username');
            const email = button.getAttribute('data-email');
            const firstName = button.getAttribute('data-first-name');
            const lastName = button.getAttribute('data-last-name');
            const role = button.getAttribute('data-role');
            const status = button.getAttribute('data-status');

            document.getElementById('editUserId').value = userId;
            document.getElementById('editUsername').value = username;
            document.getElementById('editEmail').value = email;
            document.getElementById('editFirstName').value = firstName;
            document.getElementById('editLastName').value = lastName;
            document.getElementById('editRole').value = role;
            document.getElementById('editStatus').value = status;
            document.getElementById('editPassword').value = '';
        });
    }

    // 余额快捷操作函数
    window.addToBalance = function(amount) {
        const currentBalance = parseFloat(document.getElementById('currentBalance').value) || 0;
        const newBalance = currentBalance + amount;
        document.getElementById('newBalance').value = newBalance.toFixed(2);
    };

    window.setBalance = function(amount) {
        document.getElementById('newBalance').value = amount.toFixed(2);
    };

    // 处理用户中心重定向
    <?php if (isset($_SESSION['redirect_to_dashboard']) && $_SESSION['redirect_to_dashboard']): ?>
    setTimeout(function() {
        window.open('/dashboard.php', '_blank');
    }, 2000);
    <?php
    unset($_SESSION['redirect_to_dashboard']);
    endif;
    ?>
});
</script>
