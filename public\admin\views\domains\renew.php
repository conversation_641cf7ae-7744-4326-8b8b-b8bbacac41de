<?php
// 域名续费页面
if (!defined('ADMIN_PATH')) {
    die('Direct access not permitted');
}

// 获取域名ID
$domainId = $_GET['id'] ?? 0;

if (!$domainId) {
    header('Location: ?page=domains');
    exit;
}

// 获取数据库连接
$db = getDatabase();

// 获取域名信息
try {
    $stmt = $db->prepare("
        SELECT d.*, u.username, u.email as user_email 
        FROM domains d 
        LEFT JOIN users u ON d.user_id = u.id 
        WHERE d.id = ?
    ");
    $stmt->execute([$domainId]);
    $domain = $stmt->fetch();
    
    if (!$domain) {
        header('Location: ?page=domains');
        exit;
    }
} catch (Exception $e) {
    $error = "获取域名信息失败：" . $e->getMessage();
}

// 获取续费价格
$renewalPrice = $domain['renewal_price'] ?? 12.99;

// 处理续费提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'renew') {
    $years = (int)($_POST['years'] ?? 1);
    $autoRenew = isset($_POST['auto_renew']) ? 1 : 0;
    
    if ($years < 1 || $years > 10) {
        $error = "续费年限必须在1-10年之间";
    } else {
        try {
            // 计算新的到期日期
            $currentExpiry = $domain['expiry_date'] ?? date('Y-m-d');
            $newExpiry = date('Y-m-d', strtotime($currentExpiry . " +{$years} years"));
            
            // 更新域名信息
            $stmt = $db->prepare("
                UPDATE domains 
                SET expiry_date = ?, auto_renew = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$newExpiry, $autoRenew, $domainId]);
            
            // 记录续费日志
            $totalAmount = $renewalPrice * $years;
            $logData = [
                'domain_id' => $domainId,
                'domain_name' => $domain['domain_name'],
                'years' => $years,
                'amount' => $totalAmount,
                'old_expiry' => $currentExpiry,
                'new_expiry' => $newExpiry
            ];
            
            $stmt = $db->prepare("
                INSERT INTO logs (user_id, action, resource_type, resource_id, request_data, created_at) 
                VALUES (?, 'domain_renewal', 'domain', ?, ?, NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'] ?? null,
                $domainId,
                json_encode($logData)
            ]);
            
            $success = "域名续费成功！新的到期日期：{$newExpiry}";
            
            // 重新获取更新后的数据
            $stmt = $db->prepare("
                SELECT d.*, u.username, u.email as user_email 
                FROM domains d 
                LEFT JOIN users u ON d.user_id = u.id 
                WHERE d.id = ?
            ");
            $stmt->execute([$domainId]);
            $domain = $stmt->fetch();
            
        } catch (Exception $e) {
            $error = "续费失败：" . $e->getMessage();
        }
    }
}

ob_start();
?>

<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">域名续费</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="?page=dashboard">首页</a></li>
                    <li class="breadcrumb-item"><a href="?page=domains">域名管理</a></li>
                    <li class="breadcrumb-item active">续费 - <?= htmlspecialchars($domain['domain_name']) ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="?page=domains&action=manage&id=<?= $domainId ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                返回管理
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?= htmlspecialchars($success) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- 续费表单 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sync me-2"></i>
                        域名续费
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 域名信息 -->
                    <div class="alert alert-info">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">
                                    <i class="fas fa-globe me-2"></i>
                                    <?= htmlspecialchars($domain['domain_name']) ?>
                                </h6>
                                <small class="text-muted">
                                    当前到期时间：<?= $domain['expiry_date'] ? date('Y-m-d', strtotime($domain['expiry_date'])) : '无到期日期' ?>
                                </small>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <?php
                                $statusClass = [
                                    'available' => 'success',
                                    'registered' => 'primary',
                                    'expired' => 'danger',
                                    'pending' => 'warning'
                                ][$domain['status']] ?? 'secondary';
                                $statusText = [
                                    'available' => '可售',
                                    'registered' => '已注册',
                                    'expired' => '已过期',
                                    'pending' => '待处理'
                                ][$domain['status']] ?? $domain['status'];
                                ?>
                                <span class="badge bg-<?= $statusClass ?> fs-6"><?= $statusText ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- 续费表单 -->
                    <form method="POST" id="renewForm">
                        <input type="hidden" name="action" value="renew">
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">续费年限</label>
                                <select class="form-select" name="years" id="years" onchange="calculateTotal()">
                                    <option value="1">1年</option>
                                    <option value="2">2年</option>
                                    <option value="3">3年</option>
                                    <option value="5">5年</option>
                                    <option value="10">10年</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">续费价格</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control" value="<?= number_format($renewalPrice, 2) ?>" readonly>
                                    <span class="input-group-text">/ 年</span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="auto_renew" id="autoRenew" <?= $domain['auto_renew'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="autoRenew">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    启用自动续费
                                    <small class="text-muted d-block">域名到期前自动续费，避免域名过期</small>
                                </label>
                            </div>
                        </div>

                        <!-- 费用计算 -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title">费用明细</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>续费年限：</span>
                                            <span id="selectedYears">1年</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>单价：</span>
                                            <span>$<?= number_format($renewalPrice, 2) ?></span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>新到期日期：</span>
                                            <span id="newExpiryDate"><?= date('Y-m-d', strtotime(($domain['expiry_date'] ?? date('Y-m-d')) . ' +1 year')) ?></span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>总计：</span>
                                            <span class="text-primary" id="totalAmount">$<?= number_format($renewalPrice, 2) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-credit-card me-2"></i>
                                确认续费
                            </button>
                            <a href="?page=domains&action=manage&id=<?= $domainId ?>" class="btn btn-outline-secondary">
                                取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="col-lg-4">
            <!-- 续费说明 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        续费说明
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            续费后域名到期时间将延长
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            支持1-10年续费期限
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            可选择启用自动续费
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            续费价格按年计算
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 域名信息 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>
                        域名信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">域名</small>
                        <div><?= htmlspecialchars($domain['domain_name']) ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">TLD</small>
                        <div><?= htmlspecialchars($domain['tld']) ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">归属人</small>
                        <div><?= $domain['username'] ? htmlspecialchars($domain['username']) : '未分配' ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">注册时间</small>
                        <div><?= $domain['registration_date'] ? date('Y-m-d', strtotime($domain['registration_date'])) : '未注册' ?></div>
                    </div>
                    <div>
                        <small class="text-muted">当前到期时间</small>
                        <div><?= $domain['expiry_date'] ? date('Y-m-d', strtotime($domain['expiry_date'])) : '无到期日期' ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateTotal() {
    const years = parseInt(document.getElementById('years').value);
    const renewalPrice = <?= $renewalPrice ?>;
    const currentExpiry = '<?= $domain['expiry_date'] ?? date('Y-m-d') ?>';
    
    // 计算总价
    const total = years * renewalPrice;
    document.getElementById('totalAmount').textContent = '$' + total.toFixed(2);
    document.getElementById('selectedYears').textContent = years + '年';
    
    // 计算新到期日期
    const expiryDate = new Date(currentExpiry);
    expiryDate.setFullYear(expiryDate.getFullYear() + years);
    const newExpiry = expiryDate.toISOString().split('T')[0];
    document.getElementById('newExpiryDate').textContent = newExpiry;
}

// 页面加载时计算一次
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});
</script>


