<?php
$title = '系统日志';
$page = 'logs';

$db = getDatabase();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'clear_logs':
            $days = (int)($_POST['days'] ?? 30);
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            $stmt = $db->prepare("DELETE FROM logs WHERE created_at < ?");
            $stmt->execute([$cutoffDate]);
            $deletedCount = $stmt->rowCount();
            $message = "已清理 {$deletedCount} 条日志记录";
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$action_filter = $_GET['action'] ?? '';
$user_filter = $_GET['user'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page_num = max(1, (int)($_GET['page_num'] ?? 1));
$per_page = 50;
$offset = ($page_num - 1) * $per_page;

// 构建查询条件
$where = ['1=1'];
$params = [];

if ($search) {
    $where[] = "(l.action LIKE ? OR l.resource_type LIKE ? OR l.ip_address LIKE ?)";
    $searchTerm = "%{$search}%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

if ($action_filter) {
    $where[] = "l.action = ?";
    $params[] = $action_filter;
}

if ($user_filter) {
    $where[] = "u.username = ?";
    $params[] = $user_filter;
}

if ($date_from) {
    $where[] = "DATE(l.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where[] = "DATE(l.created_at) <= ?";
    $params[] = $date_to;
}

$whereClause = implode(' AND ', $where);

// 获取总数
$totalQuery = "SELECT COUNT(*) as total FROM logs l LEFT JOIN users u ON l.user_id = u.id WHERE {$whereClause}";
$stmt = $db->prepare($totalQuery);
$stmt->execute($params);
$total = $stmt->fetch()['total'];

// 获取日志列表
$logsQuery = "SELECT l.*, u.username FROM logs l LEFT JOIN users u ON l.user_id = u.id WHERE {$whereClause} ORDER BY l.created_at DESC LIMIT {$offset}, {$per_page}";
$stmt = $db->prepare($logsQuery);
$stmt->execute($params);
$logs = $stmt->fetchAll();

$totalPages = ceil($total / $per_page);

// 获取统计数据
$stats = $db->query("SELECT 
    COUNT(*) as total_logs,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT action) as unique_actions,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_logs
    FROM logs")->fetch();

// 获取热门操作
$popularActions = $db->query("SELECT action, COUNT(*) as count FROM logs GROUP BY action ORDER BY count DESC LIMIT 10")->fetchAll();

// 获取活跃用户
$activeUsers = $db->query("SELECT u.username, COUNT(*) as log_count FROM logs l LEFT JOIN users u ON l.user_id = u.id WHERE u.username IS NOT NULL GROUP BY l.user_id ORDER BY log_count DESC LIMIT 10")->fetchAll();

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">系统日志</h1>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
            <i class="fas fa-trash"></i>
            清理日志
        </button>
        <button class="btn btn-outline-primary" onclick="exportLogs()">
            <i class="fas fa-download"></i>
            导出日志
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-list"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['total_logs'] ?? 0) ?></div>
            <p class="stats-label">总日志数</p>
            <div class="stats-trend up">
                今日: <?= $stats['today_logs'] ?? 0 ?>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['unique_users'] ?? 0) ?></div>
            <p class="stats-label">活跃用户</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="fas fa-cogs"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['unique_actions'] ?? 0) ?></div>
            <p class="stats-label">操作类型</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-number"><?= round(($stats['total_logs'] ?? 0) / max(1, (time() - strtotime('-30 days')) / 86400), 1) ?></div>
            <p class="stats-label">日均日志</p>
        </div>
    </div>
</div>

<?php if (isset($message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="logs">
            <div class="col-md-3">
                <input type="text" class="form-control" name="search" placeholder="搜索操作、IP..." value="<?= htmlspecialchars($search) ?>">
            </div>
            <div class="col-md-2">
                <select class="form-select" name="action">
                    <option value="">所有操作</option>
                    <?php foreach ($popularActions as $action): ?>
                        <option value="<?= htmlspecialchars($action['action']) ?>" <?= $action_filter === $action['action'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($action['action']) ?> (<?= $action['count'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="user">
                    <option value="">所有用户</option>
                    <?php foreach ($activeUsers as $user): ?>
                        <option value="<?= htmlspecialchars($user['username']) ?>" <?= $user_filter === $user['username'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($user['username']) ?> (<?= $user['log_count'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 日志列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-list"></i>
            系统日志 (共 <?= number_format($total) ?> 条记录)
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($logs)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>用户</th>
                            <th>操作</th>
                            <th>资源</th>
                            <th>IP地址</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td>
                                    <small><?= date('m-d H:i:s', strtotime($log['created_at'])) ?></small>
                                </td>
                                <td>
                                    <?php if ($log['username']): ?>
                                        <span class="badge bg-primary"><?= htmlspecialchars($log['username']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">访客</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <code><?= htmlspecialchars($log['action']) ?></code>
                                </td>
                                <td>
                                    <?php if ($log['resource_type']): ?>
                                        <?= htmlspecialchars($log['resource_type']) ?>
                                        <?php if ($log['resource_id']): ?>
                                            #<?= $log['resource_id'] ?>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= htmlspecialchars($log['ip_address']) ?></small>
                                </td>
                                <td>
                                    <?php if ($log['request_data'] || $log['response_data']): ?>
                                        <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#logModal<?= $log['id'] ?>">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <!-- 日志详情模态框 -->
                                        <div class="modal fade" id="logModal<?= $log['id'] ?>" tabindex="-1">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">日志详情 #<?= $log['id'] ?></h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <h6>基本信息</h6>
                                                                <table class="table table-sm">
                                                                    <tr><td>时间</td><td><?= $log['created_at'] ?></td></tr>
                                                                    <tr><td>用户</td><td><?= htmlspecialchars($log['username'] ?? '访客') ?></td></tr>
                                                                    <tr><td>操作</td><td><?= htmlspecialchars($log['action']) ?></td></tr>
                                                                    <tr><td>IP地址</td><td><?= htmlspecialchars($log['ip_address']) ?></td></tr>
                                                                </table>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <h6>用户代理</h6>
                                                                <small class="text-muted"><?= htmlspecialchars($log['user_agent']) ?></small>
                                                            </div>
                                                        </div>
                                                        
                                                        <?php if ($log['request_data']): ?>
                                                            <div class="mt-3">
                                                                <h6>请求数据</h6>
                                                                <pre class="bg-light p-2 rounded"><code><?= htmlspecialchars(json_encode(json_decode($log['request_data']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></code></pre>
                                                            </div>
                                                        <?php endif; ?>
                                                        
                                                        <?php if ($log['response_data']): ?>
                                                            <div class="mt-3">
                                                                <h6>响应数据</h6>
                                                                <pre class="bg-light p-2 rounded"><code><?= htmlspecialchars(json_encode(json_decode($log['response_data']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></code></pre>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer">
                    <nav>
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page_num > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=logs&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&action=<?= urlencode($action_filter) ?>&user=<?= urlencode($user_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        上一页
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=logs&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&action=<?= urlencode($action_filter) ?>&user=<?= urlencode($user_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page_num < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=logs&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&action=<?= urlencode($action_filter) ?>&user=<?= urlencode($user_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        下一页
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-list"></i>
                <p>没有找到日志记录</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 清理日志模态框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">清理日志</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="clear_logs">
                    <div class="mb-3">
                        <label class="form-label">清理多少天前的日志</label>
                        <select class="form-select" name="days">
                            <option value="7">7天前</option>
                            <option value="30" selected>30天前</option>
                            <option value="90">90天前</option>
                            <option value="365">1年前</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        此操作将永久删除选定时间之前的所有日志记录，无法恢复！
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">确认清理</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function exportLogs() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.open('?' + params.toString(), '_blank');
}
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
