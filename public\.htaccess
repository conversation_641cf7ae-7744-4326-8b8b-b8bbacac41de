# NameSilo域名销售系统 - Apache配置
RewriteEngine On

# 安全设置
ServerSignature Off
Options -Indexes -ExecCGI

# 防止访问敏感文件
<Files ~ "^\.(env|git|htaccess|htpasswd)$">
    Order allow,deny
    Deny from all
</Files>

# 防止访问配置文件
<Files ~ "\.(ini|conf|config)$">
    Order allow,deny
    Deny from all
</Files>

# 防止访问日志文件
<Files ~ "\.log$">
    Order allow,deny
    Deny from all
</Files>

# 设置默认首页
DirectoryIndex index.php index.html

# URL重写规则
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# 强制HTTPS (生产环境启用)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# 压缩设置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>