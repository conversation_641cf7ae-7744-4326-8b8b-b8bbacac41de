<?php
$title = '用户管理';
$page = 'users';

$db = getDatabase();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $userId = $_POST['user_id'] ?? '';
    
    switch ($action) {
        case 'toggle_status':
            if ($userId) {
                $stmt = $db->prepare("SELECT status FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();
                $newStatus = $user['status'] === 'active' ? 'inactive' : 'active';
                $db->prepare("UPDATE users SET status = ? WHERE id = ?")->execute([$newStatus, $userId]);
                $message = '用户状态已更新';
            }
            break;
            
        case 'delete_user':
            if ($userId) {
                $db->prepare("DELETE FROM users WHERE id = ? AND role != 'admin'")->execute([$userId]);
                $message = '用户已删除';
            }
            break;

        case 'add_user':
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $firstName = trim($_POST['first_name'] ?? '');
            $lastName = trim($_POST['last_name'] ?? '');
            $role = $_POST['role'] ?? 'user';

            if ($username && $email && $password) {
                try {
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("INSERT INTO users (username, email, password_hash, role, first_name, last_name, email_verified, status) VALUES (?, ?, ?, ?, ?, ?, 1, 'active')");
                    $stmt->execute([$username, $email, $passwordHash, $role, $firstName, $lastName]);
                    $message = '用户添加成功';
                } catch (PDOException $e) {
                    $error = '添加失败：' . $e->getMessage();
                }
            } else {
                $error = '请填写必填字段';
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$role = $_GET['role'] ?? '';
$page_num = max(1, (int)($_GET['page_num'] ?? 1));
$per_page = 20;
$offset = ($page_num - 1) * $per_page;

// 构建查询条件
$where = ['1=1'];
$params = [];

if ($search) {
    $where[] = "(username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
    $searchTerm = "%{$search}%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($status) {
    $where[] = "status = ?";
    $params[] = $status;
}

if ($role) {
    $where[] = "role = ?";
    $params[] = $role;
}

$whereClause = implode(' AND ', $where);

// 获取总数
$totalQuery = "SELECT COUNT(*) as total FROM users WHERE {$whereClause}";
$total = $db->prepare($totalQuery);
$total->execute($params);
$total = $total->fetch()['total'];

// 获取用户列表
$usersQuery = "SELECT * FROM users WHERE {$whereClause} ORDER BY created_at DESC LIMIT {$offset}, {$per_page}";
$users = $db->prepare($usersQuery);
$users->execute($params);
$users = $users->fetchAll();

$totalPages = ceil($total / $per_page);

ob_start();
?>

<!-- 页面头部 -->
<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-users text-primary"></i>
                用户管理
            </h1>
            <p class="page-subtitle">管理系统用户账户和权限</p>
        </div>
        <div class="page-actions">
            <button class="btn btn-outline-primary" onclick="exportUsers()">
                <i class="fas fa-download"></i>
                导出用户
            </button>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus"></i>
                添加用户
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4" data-aos="fade-up" data-aos-delay="100">
    <?php
    $stats = $db->query("SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today
        FROM users")->fetch();
    ?>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #6366f1, #8b5cf6);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['total']) ?></div>
            <p class="stats-label">总用户数</p>
            <div class="stats-trend up">
                <i class="fas fa-arrow-up"></i>
                今日新增: <?= $stats['today'] ?>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['active']) ?></div>
            <p class="stats-label">活跃用户</p>
            <div class="stats-trend up">
                <i class="fas fa-percentage"></i>
                占比: <?= $stats['total'] > 0 ? round($stats['active'] / $stats['total'] * 100, 1) : 0 ?>%
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['admins']) ?></div>
            <p class="stats-label">管理员</p>
            <div class="stats-trend">
                <i class="fas fa-shield-alt"></i>
                系统管理员
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['total'] - $stats['active']) ?></div>
            <p class="stats-label">非活跃用户</p>
            <div class="stats-trend">
                <i class="fas fa-user-times"></i>
                需要关注
            </div>
        </div>
    </div>
</div>

<!-- 消息提示 -->
<?php if (isset($message)): ?>
    <div class="alert alert-success alert-dismissible fade show" data-aos="fade-up">
        <i class="fas fa-check-circle"></i>
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" data-aos="fade-up">
        <i class="fas fa-exclamation-circle"></i>
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- 搜索和筛选 -->
<div class="card mb-4" data-aos="fade-up" data-aos-delay="200">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-filter"></i>
            搜索和筛选
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3" id="searchForm">
            <input type="hidden" name="page" value="users">
            <div class="col-lg-4 col-md-6">
                <label class="form-label">搜索关键词</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search"
                           placeholder="搜索用户名、邮箱、姓名..."
                           value="<?= htmlspecialchars($search) ?>">
                </div>
            </div>
            <div class="col-lg-2 col-md-3">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="">所有状态</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>
                        <i class="fas fa-check-circle"></i> 活跃
                    </option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>
                        <i class="fas fa-pause-circle"></i> 非活跃
                    </option>
                    <option value="suspended" <?= $status === 'suspended' ? 'selected' : '' ?>>
                        <i class="fas fa-ban"></i> 已暂停
                    </option>
                </select>
            </div>
            <div class="col-lg-2 col-md-3">
                <label class="form-label">角色</label>
                <select class="form-select" name="role">
                    <option value="">所有角色</option>
                    <option value="user" <?= $role === 'user' ? 'selected' : '' ?>>
                        <i class="fas fa-user"></i> 普通用户
                    </option>
                    <option value="admin" <?= $role === 'admin' ? 'selected' : '' ?>>
                        <i class="fas fa-user-shield"></i> 管理员
                    </option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6">
                <label class="form-label">排序方式</label>
                <select class="form-select" name="sort">
                    <option value="created_at_desc">注册时间 ↓</option>
                    <option value="created_at_asc">注册时间 ↑</option>
                    <option value="username_asc">用户名 A-Z</option>
                    <option value="username_desc">用户名 Z-A</option>
                    <option value="last_login_desc">最后登录 ↓</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary flex-fill">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <a href="index.php?page=users" class="btn btn-outline-secondary">
                        <i class="fas fa-redo"></i>
                    </a>
                </div>
            </div>
        </form>

        <!-- 快速筛选标签 -->
        <div class="mt-3">
            <div class="d-flex flex-wrap gap-2">
                <span class="text-muted small">快速筛选：</span>
                <a href="?page=users&status=active" class="badge badge-success text-decoration-none">
                    活跃用户 (<?= $stats['active'] ?>)
                </a>
                <a href="?page=users&role=admin" class="badge badge-danger text-decoration-none">
                    管理员 (<?= $stats['admins'] ?>)
                </a>
                <a href="?page=users&status=inactive" class="badge badge-warning text-decoration-none">
                    非活跃用户 (<?= $stats['total'] - $stats['active'] ?>)
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="card" data-aos="fade-up" data-aos-delay="300">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title">
                <i class="fas fa-users"></i>
                用户列表
            </h5>
            <div class="d-flex align-items-center gap-3">
                <span class="badge badge-info">
                    共 <?= number_format($total) ?> 个用户
                </span>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectAll()">
                        <i class="fas fa-check-square"></i>
                        全选
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="batchAction()">
                        <i class="fas fa-cogs"></i>
                        批量操作
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($users)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="usersTable">
                    <thead>
                        <tr>
                            <th width="50">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAllCheckbox">
                                </div>
                            </th>
                            <th width="80">头像</th>
                            <th>用户信息</th>
                            <th>联系方式</th>
                            <th>角色权限</th>
                            <th>账户状态</th>
                            <th>活动信息</th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr data-user-id="<?= $user['id'] ?>">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input user-checkbox" type="checkbox" value="<?= $user['id'] ?>">
                                    </div>
                                </td>
                                <td>
                                    <div class="user-avatar-wrapper">
                                        <div class="user-avatar-large bg-<?= $user['status'] === 'active' ? 'primary' : 'secondary' ?>">
                                            <?php if ($user['first_name']): ?>
                                                <?= strtoupper(substr($user['first_name'], 0, 1)) ?>
                                            <?php else: ?>
                                                <?= strtoupper(substr($user['username'], 0, 1)) ?>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($user['status'] === 'active'): ?>
                                            <div class="status-indicator online"></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="user-info-cell">
                                        <div class="user-name">
                                            <strong><?= htmlspecialchars($user['username']) ?></strong>
                                            <span class="user-id">#<?= $user['id'] ?></span>
                                        </div>
                                        <?php if ($user['first_name'] || $user['last_name']): ?>
                                            <div class="user-fullname">
                                                <?= htmlspecialchars(trim($user['first_name'] . ' ' . $user['last_name'])) ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="user-meta">
                                            <span class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                注册于 <?= date('Y-m-d', strtotime($user['created_at'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <div class="email-info">
                                            <i class="fas fa-envelope"></i>
                                            <?= htmlspecialchars($user['email']) ?>
                                            <?php if ($user['email_verified']): ?>
                                                <i class="fas fa-check-circle text-success ms-1" title="邮箱已验证"></i>
                                            <?php else: ?>
                                                <i class="fas fa-exclamation-circle text-warning ms-1" title="邮箱未验证"></i>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($user['phone']): ?>
                                            <div class="phone-info">
                                                <i class="fas fa-phone"></i>
                                                <?= htmlspecialchars($user['phone']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="role-info">
                                        <span class="badge badge-<?= $user['role'] === 'admin' ? 'danger' : 'info' ?>">
                                            <i class="fas fa-<?= $user['role'] === 'admin' ? 'user-shield' : 'user' ?>"></i>
                                            <?= $user['role'] === 'admin' ? '管理员' : '普通用户' ?>
                                        </span>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <div class="permission-info">
                                                <small class="text-muted">
                                                    <i class="fas fa-key"></i>
                                                    完全权限
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="status-info">
                                        <span class="badge badge-<?= $user['status'] === 'active' ? 'success' : ($user['status'] === 'suspended' ? 'danger' : 'warning') ?>">
                                            <i class="fas fa-<?= $user['status'] === 'active' ? 'check-circle' : ($user['status'] === 'suspended' ? 'ban' : 'pause-circle') ?>"></i>
                                            <?= ['active' => '活跃', 'inactive' => '非活跃', 'suspended' => '已暂停'][$user['status']] ?? $user['status'] ?>
                                        </span>
                                        <div class="status-detail">
                                            <small class="text-muted">
                                                <?php if ($user['status'] === 'active'): ?>
                                                    <i class="fas fa-circle text-success"></i>
                                                    正常使用中
                                                <?php elseif ($user['status'] === 'suspended'): ?>
                                                    <i class="fas fa-exclamation-triangle text-danger"></i>
                                                    账户已暂停
                                                <?php else: ?>
                                                    <i class="fas fa-pause text-warning"></i>
                                                    暂未激活
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="activity-info">
                                        <div class="last-login">
                                            <i class="fas fa-clock"></i>
                                            <?php if ($user['last_login']): ?>
                                                <span title="<?= date('Y-m-d H:i:s', strtotime($user['last_login'])) ?>">
                                                    <?= date('m-d H:i', strtotime($user['last_login'])) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">从未登录</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="login-count">
                                            <small class="text-muted">
                                                <i class="fas fa-sign-in-alt"></i>
                                                登录 <?= $user['login_count'] ?? 0 ?> 次
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="viewUser(<?= $user['id'] ?>)" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($user['role'] !== 'admin' || $_SESSION['admin_id'] == $user['id']): ?>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                                        onclick="editUser(<?= $user['id'] ?>)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($user['role'] !== 'admin'): ?>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-<?= $user['status'] === 'active' ? 'warning' : 'success' ?>"
                                                                onclick="return confirm('确定要<?= $user['status'] === 'active' ? '禁用' : '启用' ?>此用户吗？')"
                                                                title="<?= $user['status'] === 'active' ? '禁用' : '启用' ?>用户">
                                                            <i class="fas fa-<?= $user['status'] === 'active' ? 'ban' : 'check' ?>"></i>
                                                        </button>
                                                    </form>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="delete_user">
                                                        <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                onclick="return confirm('确定要删除此用户吗？此操作不可恢复！')"
                                                                title="删除用户">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">受保护</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer">
                    <nav>
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page_num > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=users&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&role=<?= urlencode($role) ?>">
                                        上一页
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=users&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&role=<?= urlencode($role) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page_num < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=users&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&role=<?= urlencode($role) ?>">
                                        下一页
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h4>没有找到用户</h4>
                <p>尝试调整搜索条件或添加新用户</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus"></i>
                    添加第一个用户
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus text-primary"></i>
                    添加新用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="addUserForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_user">

                    <!-- 基本信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-user"></i>
                                基本信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" name="username" required
                                           placeholder="请输入用户名" minlength="3" maxlength="50">
                                </div>
                                <div class="form-text">用户名长度为3-50个字符</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" name="email" required
                                           placeholder="请输入邮箱地址">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 个人信息 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-id-card"></i>
                                个人信息
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">姓氏</label>
                                <input type="text" class="form-control" name="first_name"
                                       placeholder="请输入姓氏">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">名字</label>
                                <input type="text" class="form-control" name="last_name"
                                       placeholder="请输入名字">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">手机号码</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    <input type="tel" class="form-control" name="phone"
                                           placeholder="请输入手机号码">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">角色权限</label>
                                <select class="form-select" name="role">
                                    <option value="user">
                                        <i class="fas fa-user"></i> 普通用户
                                    </option>
                                    <option value="admin">
                                        <i class="fas fa-user-shield"></i> 管理员
                                    </option>
                                </select>
                                <div class="form-text">管理员拥有完全权限</div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户设置 -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-cog"></i>
                                账户设置
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">登录密码 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" name="password" required
                                           placeholder="请输入密码" minlength="6">
                                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">密码长度至少6个字符</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">确认密码 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" name="password_confirm" required
                                           placeholder="请再次输入密码">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="email_verified" value="1" checked>
                                <label class="form-check-label">
                                    邮箱已验证（跳过邮箱验证流程）
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        添加用户
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user text-primary"></i>
                    用户详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 用户详情内容将通过AJAX加载 -->
                <div class="text-center py-4">
                    <div class="loading"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cogs text-primary"></i>
                    批量操作
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>已选择 <span id="selectedCount">0</span> 个用户</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="batchToggleStatus('active')">
                        <i class="fas fa-check"></i>
                        批量启用
                    </button>
                    <button type="button" class="btn btn-warning" onclick="batchToggleStatus('inactive')">
                        <i class="fas fa-pause"></i>
                        批量禁用
                    </button>
                    <button type="button" class="btn btn-danger" onclick="batchDelete()">
                        <i class="fas fa-trash"></i>
                        批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 表单验证
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    const password = this.querySelector('input[name="password"]').value;
    const passwordConfirm = this.querySelector('input[name="password_confirm"]').value;

    if (password !== passwordConfirm) {
        e.preventDefault();
        alert('两次输入的密码不一致！');
        return false;
    }
});

// 密码显示/隐藏切换
function togglePassword(button) {
    const input = button.parentElement.querySelector('input');
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// 全选功能
function selectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    selectAllCheckbox.checked = !selectAllCheckbox.checked;
    userCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

// 监听复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('user-checkbox')) {
        updateSelectedCount();

        // 更新全选状态
        const userCheckboxes = document.querySelectorAll('.user-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        if (checkedCheckboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCheckboxes.length === userCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
});

// 批量操作
function batchAction() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('请先选择要操作的用户');
        return;
    }

    updateSelectedCount();
    const modal = new bootstrap.Modal(document.getElementById('batchActionModal'));
    modal.show();
}

// 查看用户详情
function viewUser(userId) {
    const modal = new bootstrap.Modal(document.getElementById('userDetailModal'));
    modal.show();

    // 这里可以通过AJAX加载用户详情
    // fetch(`api/users.php?action=detail&id=${userId}`)
    //     .then(response => response.json())
    //     .then(data => {
    //         document.getElementById('userDetailContent').innerHTML = data.html;
    //     });
}

// 编辑用户
function editUser(userId) {
    // 这里可以实现编辑用户功能
    console.log('编辑用户:', userId);
}

// 导出用户
function exportUsers() {
    // 这里可以实现导出功能
    console.log('导出用户数据');
}

// 批量状态切换
function batchToggleStatus(status) {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (confirm(`确定要将选中的 ${userIds.length} 个用户设置为${status === 'active' ? '启用' : '禁用'}状态吗？`)) {
        // 这里可以发送AJAX请求进行批量操作
        console.log('批量设置状态:', status, userIds);
    }
}

// 批量删除
function batchDelete() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (confirm(`确定要删除选中的 ${userIds.length} 个用户吗？此操作不可恢复！`)) {
        // 这里可以发送AJAX请求进行批量删除
        console.log('批量删除用户:', userIds);
    }
}
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
