<?php
/**
 * 通知API
 * Notifications API
 */

// 关闭错误显示，避免影响JSON输出
error_reporting(0);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 定义项目根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
define('ADMIN_PATH', dirname(__DIR__));

// 简化的数据库连接函数
function getDatabase() {
    try {
        // 加载环境变量
        $envFile = ROOT_PATH . '/.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) continue;
                if (strpos($line, '=') !== false) {
                    [$name, $value] = explode('=', $line, 2);
                    $_ENV[trim($name)] = trim($value, '"\'');
                }
            }
        }
        
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $dbname = $_ENV['DB_DATABASE'] ?? $_ENV['DB_NAME'] ?? 'namesilo';
        $username = $_ENV['DB_USERNAME'] ?? $_ENV['DB_USER'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? $_ENV['DB_PASS'] ?? '';
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        return $pdo;
    } catch (Exception $e) {
        throw new Exception('数据库连接失败: ' . $e->getMessage());
    }
}

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'check':
            // 检查通知
            $notifications = [];
            
            try {
                $db = getDatabase();
                
                // 检查是否有新订单
                $stmt = $db->query("SHOW TABLES LIKE 'orders'");
                if ($stmt->fetch()) {
                    $newOrders = $db->query("SELECT COUNT(*) FROM orders WHERE status = 'pending' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")->fetchColumn();
                    if ($newOrders > 0) {
                        $notifications[] = [
                            'id' => 'new_orders',
                            'type' => 'info',
                            'title' => '新订单',
                            'message' => "有 {$newOrders} 个新的待处理订单",
                            'time' => date('Y-m-d H:i:s'),
                            'url' => 'index.php?page=orders&status=pending'
                        ];
                    }
                }
                
                // 检查是否有新用户
                $stmt = $db->query("SHOW TABLES LIKE 'users'");
                if ($stmt->fetch()) {
                    $newUsers = $db->query("SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)")->fetchColumn();
                    if ($newUsers > 0) {
                        $notifications[] = [
                            'id' => 'new_users',
                            'type' => 'success',
                            'title' => '新用户',
                            'message' => "有 {$newUsers} 个新注册用户",
                            'time' => date('Y-m-d H:i:s'),
                            'url' => 'index.php?page=users'
                        ];
                    }
                }
                
            } catch (Exception $e) {
                // 数据库不可用时返回系统通知
                $notifications[] = [
                    'id' => 'system_status',
                    'type' => 'warning',
                    'title' => '系统状态',
                    'message' => '数据库连接异常，请检查配置',
                    'time' => date('Y-m-d H:i:s'),
                    'url' => 'init-database.php'
                ];
            }
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications)
            ]);
            break;
            
        case 'mark_read':
            // 标记通知为已读
            $notificationId = $_POST['id'] ?? '';
            
            // 这里可以实现标记已读的逻辑
            // 目前简单返回成功
            echo json_encode([
                'success' => true,
                'message' => '通知已标记为已读'
            ]);
            break;
            
        case 'get_all':
            // 获取所有通知
            $notifications = [
                [
                    'id' => 'welcome',
                    'type' => 'info',
                    'title' => '欢迎使用',
                    'message' => '欢迎使用 NameSilo 域名销售系统',
                    'time' => date('Y-m-d H:i:s'),
                    'read' => false
                ]
            ];
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => '无效的操作'
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '系统错误: ' . $e->getMessage()
    ]);
}
?>
