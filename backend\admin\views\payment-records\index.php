<?php
$title = '支付记录';
$page = 'payment-records';

$db = getDatabase();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_status':
            $paymentId = (int)($_POST['payment_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            
            if ($paymentId && in_array($status, ['pending', 'processing', 'success', 'failed', 'cancelled'])) {
                $stmt = $db->prepare("UPDATE payments SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$status, $paymentId]);
                
                // 如果标记为成功，同时更新订单状态
                if ($status === 'success') {
                    $stmt = $db->prepare("UPDATE orders o JOIN payments p ON o.id = p.order_id SET o.status = 'completed' WHERE p.id = ?");
                    $stmt->execute([$paymentId]);
                }
                
                $message = '支付状态已更新';
            }
            break;
            
        case 'manual_verify':
            $paymentId = (int)($_POST['payment_id'] ?? 0);
            if ($paymentId) {
                // 这里可以实现手动验证支付状态的逻辑
                $message = '手动验证请求已发送';
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$method_filter = $_GET['method'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page_num = max(1, (int)($_GET['page_num'] ?? 1));
$per_page = 20;
$offset = ($page_num - 1) * $per_page;

// 构建查询条件
$where = ['1=1'];
$params = [];

if ($search) {
    $where[] = "(p.payment_number LIKE ? OR p.trade_no LIKE ? OR o.order_number LIKE ?)";
    $searchTerm = "%{$search}%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

if ($status_filter) {
    $where[] = "p.status = ?";
    $params[] = $status_filter;
}

if ($method_filter) {
    $where[] = "p.payment_method = ?";
    $params[] = $method_filter;
}

if ($date_from) {
    $where[] = "DATE(p.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where[] = "DATE(p.created_at) <= ?";
    $params[] = $date_to;
}

$whereClause = implode(' AND ', $where);

// 获取总数
$totalQuery = "SELECT COUNT(*) as total FROM payments p LEFT JOIN orders o ON p.order_id = o.id WHERE {$whereClause}";
$stmt = $db->prepare($totalQuery);
$stmt->execute($params);
$total = $stmt->fetch()['total'];

// 获取支付记录列表
$paymentsQuery = "SELECT p.*, o.order_number, u.username FROM payments p 
    LEFT JOIN orders o ON p.order_id = o.id 
    LEFT JOIN users u ON o.user_id = u.id 
    WHERE {$whereClause} 
    ORDER BY p.created_at DESC 
    LIMIT {$offset}, {$per_page}";
$stmt = $db->prepare($paymentsQuery);
$stmt->execute($params);
$payments = $stmt->fetchAll();

$totalPages = ceil($total / $per_page);

// 获取统计数据
$stats = $db->query("SELECT 
    COUNT(*) as total_payments,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as success_payments,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
    SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_payments
    FROM payments")->fetch();

ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">支付记录</h1>
    <div class="d-flex gap-2">
        <a href="?page=payments" class="btn btn-outline-secondary">
            <i class="fas fa-cog"></i>
            支付配置
        </a>
        <button class="btn btn-outline-success" onclick="exportPayments()">
            <i class="fas fa-download"></i>
            导出记录
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['total_payments'] ?? 0) ?></div>
            <p class="stats-label">总支付数</p>
            <div class="stats-trend up">
                今日: <?= $stats['today_payments'] ?? 0 ?>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['success_payments'] ?? 0) ?></div>
            <p class="stats-label">成功支付</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number"><?= number_format($stats['pending_payments'] ?? 0) ?></div>
            <p class="stats-label">待支付</p>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="fas fa-yen-sign"></i>
            </div>
            <div class="stats-number">¥<?= number_format($stats['total_amount'] ?? 0, 2) ?></div>
            <p class="stats-label">总收入</p>
        </div>
    </div>
</div>

<?php if (isset($message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="page" value="payment-records">
            <div class="col-md-3">
                <input type="text" class="form-control" name="search" placeholder="搜索支付单号、订单号..." value="<?= htmlspecialchars($search) ?>">
            </div>
            <div class="col-md-2">
                <select class="form-select" name="status">
                    <option value="">所有状态</option>
                    <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>待支付</option>
                    <option value="processing" <?= $status_filter === 'processing' ? 'selected' : '' ?>>处理中</option>
                    <option value="success" <?= $status_filter === 'success' ? 'selected' : '' ?>>成功</option>
                    <option value="failed" <?= $status_filter === 'failed' ? 'selected' : '' ?>>失败</option>
                    <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>取消</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="method">
                    <option value="">所有方式</option>
                    <option value="wechat" <?= $method_filter === 'wechat' ? 'selected' : '' ?>>微信支付</option>
                    <option value="alipay" <?= $method_filter === 'alipay' ? 'selected' : '' ?>>支付宝</option>
                    <option value="epay" <?= $method_filter === 'epay' ? 'selected' : '' ?>>易支付</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 支付记录列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title">
            <i class="fas fa-list"></i>
            支付记录 (共 <?= number_format($total) ?> 条记录)
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($payments)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>支付单号</th>
                            <th>订单信息</th>
                            <th>支付方式</th>
                            <th>金额</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($payment['payment_number']) ?></strong>
                                    <?php if ($payment['trade_no']): ?>
                                        <br><small class="text-muted">第三方: <?= htmlspecialchars($payment['trade_no']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($payment['order_number']) ?></strong>
                                    <?php if ($payment['username']): ?>
                                        <br><small class="text-muted">用户: <?= htmlspecialchars($payment['username']) ?></small>
                                    <?php else: ?>
                                        <br><small class="text-muted">访客用户</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $methodIcons = [
                                        'wechat' => 'fab fa-weixin text-success',
                                        'alipay' => 'fab fa-alipay text-primary',
                                        'epay' => 'fas fa-credit-card text-warning'
                                    ];
                                    $methodNames = [
                                        'wechat' => '微信支付',
                                        'alipay' => '支付宝',
                                        'epay' => '易支付'
                                    ];
                                    $icon = $methodIcons[$payment['payment_method']] ?? 'fas fa-credit-card';
                                    $name = $methodNames[$payment['payment_method']] ?? $payment['payment_method'];
                                    ?>
                                    <i class="<?= $icon ?>"></i>
                                    <?= $name ?>
                                </td>
                                <td>
                                    <strong>¥<?= number_format($payment['amount'], 2) ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'success' => 'success',
                                        'failed' => 'danger',
                                        'cancelled' => 'secondary'
                                    ];
                                    $statusLabels = [
                                        'pending' => '待支付',
                                        'processing' => '处理中',
                                        'success' => '成功',
                                        'failed' => '失败',
                                        'cancelled' => '取消'
                                    ];
                                    $color = $statusColors[$payment['status']] ?? 'secondary';
                                    $label = $statusLabels[$payment['status']] ?? $payment['status'];
                                    ?>
                                    <span class="badge bg-<?= $color ?>"><?= $label ?></span>
                                    <?php if ($payment['paid_at']): ?>
                                        <br><small class="text-muted">完成: <?= date('m-d H:i', strtotime($payment['paid_at'])) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= date('Y-m-d H:i', strtotime($payment['created_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                data-bs-toggle="modal" data-bs-target="#paymentModal<?= $payment['id'] ?>">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <?php if ($payment['status'] === 'pending'): ?>
                                            <button type="button" class="btn btn-outline-success btn-sm" 
                                                    onclick="updatePaymentStatus(<?= $payment['id'] ?>, 'success')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-warning btn-sm dropdown-toggle" 
                                                    data-bs-toggle="dropdown">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="updatePaymentStatus(<?= $payment['id'] ?>, 'success')">标记成功</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="updatePaymentStatus(<?= $payment['id'] ?>, 'failed')">标记失败</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" onclick="manualVerify(<?= $payment['id'] ?>)">手动验证</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <!-- 支付详情模态框 -->
                                    <div class="modal fade" id="paymentModal<?= $payment['id'] ?>" tabindex="-1">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">支付详情 - <?= htmlspecialchars($payment['payment_number']) ?></h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6>支付信息</h6>
                                                            <table class="table table-sm">
                                                                <tr><td>支付单号</td><td><?= htmlspecialchars($payment['payment_number']) ?></td></tr>
                                                                <tr><td>第三方交易号</td><td><?= htmlspecialchars($payment['trade_no'] ?? '-') ?></td></tr>
                                                                <tr><td>支付方式</td><td><?= $name ?></td></tr>
                                                                <tr><td>金额</td><td>¥<?= number_format($payment['amount'], 2) ?></td></tr>
                                                                <tr><td>状态</td><td><span class="badge bg-<?= $color ?>"><?= $label ?></span></td></tr>
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6>时间信息</h6>
                                                            <table class="table table-sm">
                                                                <tr><td>创建时间</td><td><?= $payment['created_at'] ?></td></tr>
                                                                <tr><td>更新时间</td><td><?= $payment['updated_at'] ?></td></tr>
                                                                <tr><td>支付时间</td><td><?= $payment['paid_at'] ?? '-' ?></td></tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    <?php if ($payment['notify_data']): ?>
                                                        <div class="mt-3">
                                                            <h6>通知数据</h6>
                                                            <pre class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;"><code><?= htmlspecialchars(json_encode(json_decode($payment['notify_data']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></code></pre>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                                    <?php if ($payment['status'] === 'pending'): ?>
                                                        <button type="button" class="btn btn-success" onclick="updatePaymentStatus(<?= $payment['id'] ?>, 'success')">
                                                            <i class="fas fa-check"></i>
                                                            标记成功
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer">
                    <nav>
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page_num > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=payment-records&page_num=<?= $page_num - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&method=<?= urlencode($method_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        上一页
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page_num - 2); $i <= min($totalPages, $page_num + 2); $i++): ?>
                                <li class="page-item <?= $i === $page_num ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=payment-records&page_num=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&method=<?= urlencode($method_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page_num < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=payment-records&page_num=<?= $page_num + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&method=<?= urlencode($method_filter) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                        下一页
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-credit-card"></i>
                <p>没有找到支付记录</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// 更新支付状态
function updatePaymentStatus(paymentId, status) {
    if (confirm('确定要更新支付状态吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="payment_id" value="${paymentId}">
            <input type="hidden" name="status" value="${status}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// 手动验证
function manualVerify(paymentId) {
    if (confirm('确定要手动验证支付状态吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="manual_verify">
            <input type="hidden" name="payment_id" value="${paymentId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// 导出支付记录
function exportPayments() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.open('?' + params.toString(), '_blank');
}
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
