<?php
/**
 * 支付配置文件
 * Payment Configuration
 */

return [
    // 默认支付网关
    'default' => $_ENV['PAYMENT_DEFAULT'] ?? 'alipay',
    
    // 支付网关配置
    'gateways' => [
        // 支付宝配置
        'alipay' => [
            'enabled' => filter_var($_ENV['ALIPAY_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'app_id' => $_ENV['ALIPAY_APP_ID'] ?? '',
            'private_key' => $_ENV['ALIPAY_PRIVATE_KEY'] ?? '',
            'public_key' => $_ENV['ALIPAY_PUBLIC_KEY'] ?? '',
            'sign_type' => $_ENV['ALIPAY_SIGN_TYPE'] ?? 'RSA2',
            'charset' => 'utf-8',
            'gateway_url' => $_ENV['ALIPAY_GATEWAY_URL'] ?? 'https://openapi.alipay.com/gateway.do',
            'notify_url' => $_ENV['APP_URL'] . '/api/v1/payments/alipay/notify',
            'return_url' => $_ENV['APP_URL'] . '/payment/success',
            'sandbox' => filter_var($_ENV['ALIPAY_SANDBOX'] ?? false, FILTER_VALIDATE_BOOLEAN),
        ],
        
        // 微信支付配置
        'wechat' => [
            'enabled' => filter_var($_ENV['WECHAT_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'app_id' => $_ENV['WECHAT_APP_ID'] ?? '',
            'mch_id' => $_ENV['WECHAT_MCH_ID'] ?? '',
            'key' => $_ENV['WECHAT_KEY'] ?? '',
            'cert_path' => $_ENV['WECHAT_CERT_PATH'] ?? '',
            'key_path' => $_ENV['WECHAT_KEY_PATH'] ?? '',
            'notify_url' => $_ENV['APP_URL'] . '/api/v1/payments/wechat/notify',
            'sandbox' => filter_var($_ENV['WECHAT_SANDBOX'] ?? false, FILTER_VALIDATE_BOOLEAN),
        ],
        
        // PayPal配置
        'paypal' => [
            'enabled' => filter_var($_ENV['PAYPAL_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'client_id' => $_ENV['PAYPAL_CLIENT_ID'] ?? '',
            'client_secret' => $_ENV['PAYPAL_CLIENT_SECRET'] ?? '',
            'sandbox' => filter_var($_ENV['PAYPAL_SANDBOX'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'webhook_id' => $_ENV['PAYPAL_WEBHOOK_ID'] ?? '',
            'notify_url' => $_ENV['APP_URL'] . '/api/v1/payments/paypal/notify',
            'return_url' => $_ENV['APP_URL'] . '/payment/success',
            'cancel_url' => $_ENV['APP_URL'] . '/payment/cancel',
        ],
        
        // Stripe配置
        'stripe' => [
            'enabled' => filter_var($_ENV['STRIPE_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'public_key' => $_ENV['STRIPE_PUBLIC_KEY'] ?? '',
            'secret_key' => $_ENV['STRIPE_SECRET_KEY'] ?? '',
            'webhook_secret' => $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '',
            'success_url' => $_ENV['APP_URL'] . '/payment/success',
            'cancel_url' => $_ENV['APP_URL'] . '/payment/cancel',
        ],
        
        // 银联配置
        'unionpay' => [
            'enabled' => filter_var($_ENV['UNIONPAY_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'mer_id' => $_ENV['UNIONPAY_MER_ID'] ?? '',
            'cert_path' => $_ENV['UNIONPAY_CERT_PATH'] ?? '',
            'cert_pwd' => $_ENV['UNIONPAY_CERT_PWD'] ?? '',
            'verify_cert_path' => $_ENV['UNIONPAY_VERIFY_CERT_PATH'] ?? '',
            'front_url' => $_ENV['APP_URL'] . '/payment/success',
            'back_url' => $_ENV['APP_URL'] . '/api/v1/payments/unionpay/notify',
            'sandbox' => filter_var($_ENV['UNIONPAY_SANDBOX'] ?? false, FILTER_VALIDATE_BOOLEAN),
        ],
    ],
    
    // 支付配置
    'settings' => [
        // 支持的货币
        'currencies' => [
            'CNY' => ['name' => '人民币', 'symbol' => '¥', 'decimals' => 2],
            'USD' => ['name' => '美元', 'symbol' => '$', 'decimals' => 2],
            'EUR' => ['name' => '欧元', 'symbol' => '€', 'decimals' => 2],
            'GBP' => ['name' => '英镑', 'symbol' => '£', 'decimals' => 2],
            'JPY' => ['name' => '日元', 'symbol' => '¥', 'decimals' => 0],
        ],
        
        // 默认货币
        'default_currency' => $_ENV['PAYMENT_CURRENCY'] ?? 'CNY',
        
        // 支付超时时间（分钟）
        'timeout' => (int)($_ENV['PAYMENT_TIMEOUT'] ?? 30),
        
        // 最小支付金额
        'min_amount' => (float)($_ENV['PAYMENT_MIN_AMOUNT'] ?? 0.01),
        
        // 最大支付金额
        'max_amount' => (float)($_ENV['PAYMENT_MAX_AMOUNT'] ?? 50000.00),
        
        // 手续费配置
        'fees' => [
            'alipay' => (float)($_ENV['ALIPAY_FEE_RATE'] ?? 0.006), // 0.6%
            'wechat' => (float)($_ENV['WECHAT_FEE_RATE'] ?? 0.006), // 0.6%
            'paypal' => (float)($_ENV['PAYPAL_FEE_RATE'] ?? 0.034), // 3.4%
            'stripe' => (float)($_ENV['STRIPE_FEE_RATE'] ?? 0.029), // 2.9%
            'unionpay' => (float)($_ENV['UNIONPAY_FEE_RATE'] ?? 0.005), // 0.5%
        ],
        
        // 自动退款配置
        'auto_refund' => [
            'enabled' => filter_var($_ENV['AUTO_REFUND_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'timeout_hours' => (int)($_ENV['AUTO_REFUND_TIMEOUT'] ?? 24),
            'max_attempts' => (int)($_ENV['AUTO_REFUND_MAX_ATTEMPTS'] ?? 3),
        ],
        
        // 支付重试配置
        'retry' => [
            'enabled' => filter_var($_ENV['PAYMENT_RETRY_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'max_attempts' => (int)($_ENV['PAYMENT_RETRY_MAX_ATTEMPTS'] ?? 3),
            'delay_seconds' => (int)($_ENV['PAYMENT_RETRY_DELAY'] ?? 5),
        ],
        
        // 支付日志配置
        'logging' => [
            'enabled' => filter_var($_ENV['PAYMENT_LOG_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'level' => $_ENV['PAYMENT_LOG_LEVEL'] ?? 'info',
            'sensitive_fields' => ['password', 'key', 'secret', 'token'],
        ],
        
        // 支付安全配置
        'security' => [
            'verify_ssl' => filter_var($_ENV['PAYMENT_VERIFY_SSL'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'encrypt_sensitive_data' => filter_var($_ENV['PAYMENT_ENCRYPT_DATA'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'ip_whitelist' => array_filter(explode(',', $_ENV['PAYMENT_IP_WHITELIST'] ?? '')),
            'rate_limit' => [
                'enabled' => filter_var($_ENV['PAYMENT_RATE_LIMIT'] ?? true, FILTER_VALIDATE_BOOLEAN),
                'max_requests' => (int)($_ENV['PAYMENT_RATE_LIMIT_MAX'] ?? 100),
                'window_minutes' => (int)($_ENV['PAYMENT_RATE_LIMIT_WINDOW'] ?? 60),
            ],
        ],
    ],
    
    // 支付状态映射
    'status_mapping' => [
        'pending' => '待支付',
        'processing' => '处理中',
        'success' => '支付成功',
        'failed' => '支付失败',
        'cancelled' => '已取消',
        'refunded' => '已退款',
        'partial_refund' => '部分退款',
        'expired' => '已过期',
    ],
    
    // 支付方式显示配置
    'display' => [
        'alipay' => [
            'name' => '支付宝',
            'icon' => 'fab fa-alipay',
            'color' => '#1677ff',
            'description' => '使用支付宝安全支付',
        ],
        'wechat' => [
            'name' => '微信支付',
            'icon' => 'fab fa-weixin',
            'color' => '#07c160',
            'description' => '使用微信安全支付',
        ],
        'paypal' => [
            'name' => 'PayPal',
            'icon' => 'fab fa-paypal',
            'color' => '#0070ba',
            'description' => '使用PayPal国际支付',
        ],
        'stripe' => [
            'name' => 'Stripe',
            'icon' => 'fab fa-stripe',
            'color' => '#635bff',
            'description' => '使用信用卡支付',
        ],
        'unionpay' => [
            'name' => '银联支付',
            'icon' => 'fas fa-credit-card',
            'color' => '#e21836',
            'description' => '使用银联卡支付',
        ],
    ],
    
    // 测试配置
    'testing' => [
        'enabled' => filter_var($_ENV['PAYMENT_TESTING'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'mock_success_rate' => (float)($_ENV['PAYMENT_MOCK_SUCCESS_RATE'] ?? 0.9),
        'mock_delay_seconds' => (int)($_ENV['PAYMENT_MOCK_DELAY'] ?? 2),
        'test_amounts' => [
            '0.01' => 'success',
            '0.02' => 'failed',
            '0.03' => 'cancelled',
            '0.04' => 'timeout',
        ],
    ],
];
?>
