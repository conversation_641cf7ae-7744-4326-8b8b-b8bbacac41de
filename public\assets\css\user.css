/**
 * NameSilo域名销售系统 - 用户中心样式
 * NameSilo Domain Sales System - User Center CSS
 */

/* 扩展管理后台样式 */

/* 用户中心特定样式 */
.balance-display {
    background: linear-gradient(135deg, var(--success-color), var(--accent-color));
    color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    text-align: center;
    margin: 0 var(--spacing-md) var(--spacing-md);
}

.balance-amount {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
}

.balance-label {
    font-size: 12px;
    opacity: 0.9;
    margin: 0;
}

/* 统计卡片增强 */
.stats-card {
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: var(--transition);
}

.stats-card:hover::before {
    opacity: 1;
}

/* 表格优化 */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background: var(--border-light);
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-muted);
}

/* 徽章样式增强 */
.badge {
    font-weight: 500;
    letter-spacing: 0.25px;
}

.badge.bg-success {
    background: var(--success-color) !important;
}

.badge.bg-warning {
    background: var(--warning-color) !important;
}

.badge.bg-danger {
    background: var(--danger-color) !important;
}

.badge.bg-info {
    background: var(--info-color) !important;
}

.badge.bg-primary {
    background: var(--primary-color) !important;
}

/* 按钮增强 */
.btn {
    font-weight: 500;
    letter-spacing: 0.25px;
}

.btn-sm {
    font-size: 12px;
    padding: 6px 12px;
}

/* 输入框样式 */
.form-control, .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
}

.alert-success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.alert-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.alert-info {
    background: rgba(37, 99, 235, 0.1);
    color: var(--info-color);
}

/* 进度条样式 */
.progress {
    height: 6px;
    border-radius: 3px;
    background: var(--border-light);
}

.progress-bar {
    border-radius: 3px;
}

/* 分页样式 */
.pagination {
    gap: var(--spacing-xs);
}

.page-link {
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    transition: var(--transition);
}

.page-link:hover {
    background: var(--sidebar-hover);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xs);
}

.dropdown-item {
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    padding: 8px 12px;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--sidebar-hover);
    color: var(--primary-color);
}

/* 列表组样式 */
.list-group-item {
    border: 1px solid var(--border-color);
    font-size: 14px;
    transition: var(--transition);
}

.list-group-item:hover {
    background: var(--sidebar-hover);
    color: var(--primary-color);
}

/* 手风琴样式 */
.accordion-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-xs);
}

.accordion-button {
    font-weight: 500;
    font-size: 14px;
    padding: var(--spacing-md);
}

.accordion-button:not(.collapsed) {
    background: var(--sidebar-hover);
    color: var(--primary-color);
}

.accordion-body {
    font-size: 14px;
    color: var(--text-muted);
}

/* 工具提示样式 */
.tooltip {
    font-size: 12px;
}

.tooltip-inner {
    background: var(--text-color);
    border-radius: var(--border-radius-sm);
}

/* 加载动画 */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 2px;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

/* 响应式优化 */
@media (max-width: 576px) {
    .stats-card {
        text-align: center;
    }
    
    .card-header {
        padding: var(--spacing-md);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .table-responsive {
        font-size: 12px;
    }
    
    .btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .top-navbar,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-area {
        padding: 0 !important;
    }
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
    /* 深色模式样式将在后续版本中添加 */
}
