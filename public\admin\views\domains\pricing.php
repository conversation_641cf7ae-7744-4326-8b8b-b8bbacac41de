<?php
$title = '域名价格设置';
$page = 'domains';

// 模拟域名价格数据
$domainPricing = [
    [
        'extension' => '.com',
        'registration_price' => 120.00,
        'renewal_price' => 120.00,
        'transfer_price' => 120.00,
        'status' => 'active',
        'popular' => true
    ],
    [
        'extension' => '.net',
        'registration_price' => 14.99,
        'renewal_price' => 16.99,
        'transfer_price' => 14.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.org',
        'registration_price' => 13.99,
        'renewal_price' => 15.99,
        'transfer_price' => 13.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.info',
        'registration_price' => 11.99,
        'renewal_price' => 13.99,
        'transfer_price' => 11.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.biz',
        'registration_price' => 15.99,
        'renewal_price' => 17.99,
        'transfer_price' => 15.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.app',
        'registration_price' => 19.99,
        'renewal_price' => 21.99,
        'transfer_price' => 19.99,
        'status' => 'active',
        'popular' => true
    ],
    [
        'extension' => '.dev',
        'registration_price' => 16.99,
        'renewal_price' => 18.99,
        'transfer_price' => 16.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.io',
        'registration_price' => 49.99,
        'renewal_price' => 52.99,
        'transfer_price' => 49.99,
        'status' => 'active',
        'popular' => true
    ],
    [
        'extension' => '.co',
        'registration_price' => 32.99,
        'renewal_price' => 35.99,
        'transfer_price' => 32.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.me',
        'registration_price' => 18.99,
        'renewal_price' => 20.99,
        'transfer_price' => 18.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.cc',
        'registration_price' => 22.99,
        'renewal_price' => 24.99,
        'transfer_price' => 22.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.site',
        'registration_price' => 24.99,
        'renewal_price' => 26.99,
        'transfer_price' => 24.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.online',
        'registration_price' => 29.99,
        'renewal_price' => 31.99,
        'transfer_price' => 29.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.store',
        'registration_price' => 34.99,
        'renewal_price' => 36.99,
        'transfer_price' => 34.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.tech',
        'registration_price' => 39.99,
        'renewal_price' => 41.99,
        'transfer_price' => 39.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.top',
        'registration_price' => 7.99,
        'renewal_price' => 9.99,
        'transfer_price' => 7.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.club',
        'registration_price' => 13.99,
        'renewal_price' => 15.99,
        'transfer_price' => 13.99,
        'status' => 'active',
        'popular' => false
    ],
    [
        'extension' => '.cn',
        'registration_price' => 60.00,
        'renewal_price' => 65.00,
        'transfer_price' => 60.00,
        'status' => 'active',
        'popular' => false
    ]
];

ob_start();
?>

<div class="main-content">
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4" data-aos="fade-down">
            <div>
                <h1 class="h3 mb-1">
                    <i class="fas fa-tags text-primary"></i>
                    域名价格设置
                </h1>
                <p class="text-muted mb-0">管理各种域名后缀的注册、续费和转移价格</p>
            </div>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPricingModal">
                    <i class="fas fa-plus"></i>
                    添加价格规则
                </button>
                <button class="btn btn-outline-success" onclick="exportPricing()">
                    <i class="fas fa-download"></i>
                    导出价格表
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4" data-aos="fade-up">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #4f46e5, #6366f1);">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="stats-number"><?= count($domainPricing) ?></div>
                    <p class="stats-label">域名后缀总数</p>
                    <div class="stats-trend up">
                        <i class="fas fa-check-circle"></i>
                        全部激活
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #059669, #10b981);">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stats-number"><?= count(array_filter($domainPricing, function($item) { return $item['popular']; })) ?></div>
                    <p class="stats-label">热门后缀</p>
                    <div class="stats-trend up">
                        <i class="fas fa-fire"></i>
                        推荐销售
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #d97706, #f59e0b);">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stats-number">¥<?= number_format(array_sum(array_column($domainPricing, 'registration_price')) / count($domainPricing), 2) ?></div>
                    <p class="stats-label">平均注册价格</p>
                    <div class="stats-trend">
                        <i class="fas fa-chart-line"></i>
                        市场均价
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="stats-card">
                    <div class="stats-icon" style="background: linear-gradient(135deg, #2563eb, #3b82f6);">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stats-number">15%</div>
                    <p class="stats-label">平均利润率</p>
                    <div class="stats-trend up">
                        <i class="fas fa-trending-up"></i>
                        持续增长
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="card mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="搜索域名后缀..." id="searchInput">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="active">激活</option>
                            <option value="inactive">停用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="popularFilter">
                            <option value="">全部类型</option>
                            <option value="popular">热门后缀</option>
                            <option value="normal">普通后缀</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格表格 -->
        <div class="card" data-aos="fade-up" data-aos-delay="200">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i>
                        域名价格列表
                    </h5>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="bulkEdit()">
                            <i class="fas fa-edit"></i>
                            批量编辑
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="syncPrices()">
                            <i class="fas fa-sync"></i>
                            同步价格
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="pricingTable">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                    </div>
                                </th>
                                <th>域名后缀</th>
                                <th>注册价格</th>
                                <th>续费价格</th>
                                <th>转移价格</th>
                                <th>状态</th>
                                <th>标签</th>
                                <th width="120">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($domainPricing as $index => $pricing): ?>
                            <tr data-extension="<?= $pricing['extension'] ?>" data-status="<?= $pricing['status'] ?>" data-popular="<?= $pricing['popular'] ? 'popular' : 'normal' ?>">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input row-checkbox" type="checkbox" value="<?= $pricing['extension'] ?>">
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="domain-extension">
                                            <strong><?= $pricing['extension'] ?></strong>
                                        </div>
                                        <?php if ($pricing['popular']): ?>
                                        <span class="badge bg-warning ms-2">
                                            <i class="fas fa-star"></i>
                                            热门
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="price-display">¥<?= number_format($pricing['registration_price'], 2) ?></span>
                                </td>
                                <td>
                                    <span class="price-display">¥<?= number_format($pricing['renewal_price'], 2) ?></span>
                                </td>
                                <td>
                                    <span class="price-display">¥<?= number_format($pricing['transfer_price'], 2) ?></span>
                                </td>
                                <td>
                                    <?php if ($pricing['status'] === 'active'): ?>
                                    <span class="badge bg-success">激活</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">停用</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <?php if ($pricing['popular']): ?>
                                        <span class="badge bg-warning">热门</span>
                                        <?php endif; ?>
                                        <?php if ($pricing['registration_price'] < 20): ?>
                                        <span class="badge bg-success">低价</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-outline-primary btn-sm" onclick="editPricing('<?= $pricing['extension'] ?>')" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deletePricing('<?= $pricing['extension'] ?>')" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加价格规则模态框 -->
<div class="modal fade" id="addPricingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i>
                    添加价格规则
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPricingForm">
                    <div class="mb-3">
                        <label class="form-label">域名后缀</label>
                        <input type="text" class="form-control" name="extension" placeholder="例如: .example" required>
                        <div class="form-text">请包含点号，例如 .com</div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">注册价格</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" name="registration_price" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">续费价格</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" name="renewal_price" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">转移价格</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" name="transfer_price" step="0.01" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="popular" id="popularCheck">
                            <label class="form-check-label" for="popularCheck">
                                标记为热门后缀
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="active" id="activeCheck" checked>
                            <label class="form-check-label" for="activeCheck">
                                立即激活
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePricing()">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
// 搜索和筛选功能
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const popularFilter = document.getElementById('popularFilter').value;
    const rows = document.querySelectorAll('#pricingTable tbody tr');
    
    rows.forEach(row => {
        const extension = row.dataset.extension.toLowerCase();
        const status = row.dataset.status;
        const popular = row.dataset.popular;
        
        const matchesSearch = extension.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesPopular = !popularFilter || popular === popularFilter;
        
        if (matchesSearch && matchesStatus && matchesPopular) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 重置筛选
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('popularFilter').value = '';
    filterTable();
}

// 全选功能
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.row-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 编辑价格
function editPricing(extension) {
    alert('编辑 ' + extension + ' 的价格设置');
}

// 删除价格
function deletePricing(extension) {
    if (confirm('确定要删除 ' + extension + ' 的价格设置吗？')) {
        alert('已删除 ' + extension + ' 的价格设置');
    }
}

// 批量编辑
function bulkEdit() {
    const selected = document.querySelectorAll('.row-checkbox:checked');
    if (selected.length === 0) {
        alert('请先选择要编辑的项目');
        return;
    }
    alert('批量编辑 ' + selected.length + ' 个项目');
}

// 同步价格
function syncPrices() {
    if (confirm('确定要从上游供应商同步最新价格吗？')) {
        alert('价格同步功能开发中...');
    }
}

// 导出价格表
function exportPricing() {
    alert('导出价格表功能开发中...');
}

// 保存价格设置
function savePricing() {
    const form = document.getElementById('addPricingForm');
    const formData = new FormData(form);
    
    // 这里应该发送到后端保存
    alert('价格规则保存成功！');
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('addPricingModal'));
    modal.hide();
    
    // 重置表单
    form.reset();
}

// 绑定搜索和筛选事件
document.getElementById('searchInput').addEventListener('input', filterTable);
document.getElementById('statusFilter').addEventListener('change', filterTable);
document.getElementById('popularFilter').addEventListener('change', filterTable);
</script>

<?php
$content = ob_get_clean();
include ADMIN_PATH . '/views/layouts/admin.php';
?>
