<?php
/**
 * 统一错误处理机制
 * Unified Error Handling System
 */

// 防止直接访问
if (!defined('ROOT_PATH') && !defined('ADMIN_PATH') && !defined('CONFIG_LOADED')) {
    die('Direct access not allowed');
}

/**
 * 自定义错误处理函数
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $errorTypes = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];
    
    $errorType = $errorTypes[$errno] ?? 'Unknown Error';
    $message = "[$errorType] $errstr in $errfile on line $errline";
    
    // 记录错误日志
    error_log($message);
    
    // 在开发环境显示错误
    if (defined('DEBUG') && DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px; border-left: 4px solid #c62828;'>";
        echo "<strong>$errorType:</strong> $errstr<br>";
        echo "<strong>File:</strong> $errfile<br>";
        echo "<strong>Line:</strong> $errline";
        echo "</div>";
    }
    
    return true;
}

/**
 * 自定义异常处理函数
 */
function customExceptionHandler($exception) {
    $message = "Uncaught Exception: " . $exception->getMessage() . 
               " in " . $exception->getFile() . 
               " on line " . $exception->getLine();
    
    // 记录异常日志
    error_log($message);
    
    // 在开发环境显示异常
    if (defined('DEBUG') && DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px; border-left: 4px solid #c62828;'>";
        echo "<strong>Uncaught Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<strong>File:</strong> " . $exception->getFile() . "<br>";
        echo "<strong>Line:</strong> " . $exception->getLine() . "<br>";
        echo "<strong>Stack Trace:</strong><br><pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        // 生产环境显示友好错误页面
        http_response_code(500);
        include_once __DIR__ . '/error-pages/500.html';
    }
}

/**
 * JSON API错误响应
 */
function jsonError($message, $code = 500, $data = null) {
    http_response_code($code);
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'success' => false,
        'message' => $message,
        'code' => $code
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * JSON API成功响应
 */
function jsonSuccess($message = 'Success', $data = null) {
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'success' => true,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 记录系统日志
 */
function logSystem($level, $message, $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : '';
    
    $logMessage = "[$timestamp] [$level] $message";
    if ($contextStr) {
        $logMessage .= " Context: $contextStr";
    }
    
    // 写入日志文件
    $logDir = defined('ROOT_PATH') ? ROOT_PATH . '/logs' : dirname(dirname(__DIR__)) . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/system-' . date('Y-m-d') . '.log';
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
    
    // 同时写入系统错误日志
    error_log($logMessage);
}

/**
 * 数据库错误处理
 */
function handleDatabaseError($e, $operation = 'Database operation') {
    $message = "$operation failed: " . $e->getMessage();
    logSystem('error', $message, [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    if (defined('DEBUG') && DEBUG) {
        throw $e;
    } else {
        throw new Exception('数据库操作失败，请稍后重试');
    }
}

/**
 * 设置错误处理器
 */
function setupErrorHandlers() {
    // 设置错误报告级别
    if (defined('DEBUG') && DEBUG) {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
    } else {
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
        ini_set('display_errors', 0);
    }
    
    // 设置自定义错误和异常处理器
    set_error_handler('customErrorHandler');
    set_exception_handler('customExceptionHandler');
}

// 自动设置错误处理器
setupErrorHandlers();
?>
