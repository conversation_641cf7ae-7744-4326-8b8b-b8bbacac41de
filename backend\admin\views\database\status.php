<?php
$title = '数据库状态';
$page = 'database-status';

// 获取数据库状态信息
function getDatabaseStatus() {
    try {
        $db = getDatabase();
        $status = [
            'connected' => true,
            'tables' => [],
            'stats' => [],
            'size' => 0
        ];
        
        // 获取所有表
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $status['tables'] = $tables;
        
        // 获取每个表的统计信息
        foreach ($tables as $table) {
            try {
                $count = $db->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                $size = $db->query("SELECT ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size' 
                                   FROM information_schema.TABLES 
                                   WHERE table_schema = DATABASE() AND table_name = '$table'")->fetchColumn();
                
                $status['stats'][$table] = [
                    'count' => $count,
                    'size' => $size ?: 0
                ];
                $status['size'] += $size ?: 0;
            } catch (Exception $e) {
                $status['stats'][$table] = [
                    'count' => 'Error',
                    'size' => 0
                ];
            }
        }
        
        return $status;
    } catch (Exception $e) {
        return [
            'connected' => false,
            'error' => $e->getMessage()
        ];
    }
}

$dbStatus = getDatabaseStatus();

ob_start();
?>

<div class="page-header" data-aos="fade-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-database text-primary"></i>
                数据库状态
            </h1>
            <p class="page-subtitle">查看数据库连接状态和表信息</p>
        </div>
        <div>
            <a href="database-manager.php" class="btn btn-primary">
                <i class="fas fa-tools"></i>
                数据库管理
            </a>
        </div>
    </div>
</div>

<?php if (!$dbStatus['connected']): ?>
<div class="alert alert-danger" data-aos="fade-up">
    <div class="d-flex align-items-center">
        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
        <div>
            <h5 class="alert-heading">数据库连接失败</h5>
            <p class="mb-0"><?= htmlspecialchars($dbStatus['error']) ?></p>
        </div>
    </div>
</div>
<?php else: ?>

<!-- 数据库概览 -->
<div class="row mb-4" data-aos="fade-up">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #28a745, #20c997);">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number">正常</div>
            <p class="stats-label">连接状态</p>
            <div class="stats-trend up">
                <i class="fas fa-signal"></i>
                已连接
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #007bff, #6610f2);">
                <i class="fas fa-table"></i>
            </div>
            <div class="stats-number"><?= count($dbStatus['tables']) ?></div>
            <p class="stats-label">数据表数量</p>
            <div class="stats-trend">
                <i class="fas fa-database"></i>
                系统表
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                <i class="fas fa-hdd"></i>
            </div>
            <div class="stats-number"><?= number_format($dbStatus['size'], 2) ?> MB</div>
            <p class="stats-label">数据库大小</p>
            <div class="stats-trend">
                <i class="fas fa-chart-line"></i>
                存储空间
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(135deg, #17a2b8, #6f42c1);">
                <i class="fas fa-server"></i>
            </div>
            <div class="stats-number"><?= $_ENV['DB_HOST'] ?? 'localhost' ?></div>
            <p class="stats-label">数据库主机</p>
            <div class="stats-trend">
                <i class="fas fa-network-wired"></i>
                <?= $_ENV['DB_DATABASE'] ?? 'namesilo_sales' ?>
            </div>
        </div>
    </div>
</div>

<!-- 数据表详情 -->
<div class="row">
    <div class="col-12">
        <div class="card" data-aos="fade-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    数据表详情
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-table"></i> 表名</th>
                                <th><i class="fas fa-list-ol"></i> 记录数</th>
                                <th><i class="fas fa-hdd"></i> 大小 (MB)</th>
                                <th><i class="fas fa-info-circle"></i> 说明</th>
                                <th><i class="fas fa-cogs"></i> 操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $tableDescriptions = [
                                'users' => '用户账户信息',
                                'domains' => '域名库存信息',
                                'domain_categories' => '域名分类管理',
                                'orders' => '订单交易记录',
                                'payment_records' => '支付流水记录',
                                'system_logs' => '系统操作日志',
                                'system_settings' => '系统配置参数',
                                'user_sessions' => '用户会话管理'
                            ];
                            
                            foreach ($dbStatus['stats'] as $table => $info): 
                                $description = $tableDescriptions[$table] ?? '系统数据表';
                                $recordCount = $info['count'];
                                $tableSize = $info['size'];
                                
                                // 根据记录数设置状态颜色
                                $statusClass = 'text-success';
                                if ($recordCount === 'Error') {
                                    $statusClass = 'text-danger';
                                } elseif ($recordCount == 0) {
                                    $statusClass = 'text-warning';
                                }
                            ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($table) ?></strong>
                                </td>
                                <td>
                                    <span class="<?= $statusClass ?>">
                                        <?= $recordCount === 'Error' ? '错误' : number_format($recordCount) ?>
                                    </span>
                                </td>
                                <td><?= number_format($tableSize, 2) ?></td>
                                <td class="text-muted"><?= $description ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="viewTableData('<?= $table ?>')" 
                                                title="查看数据">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="showTableStructure('<?= $table ?>')" 
                                                title="表结构">
                                            <i class="fas fa-sitemap"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card" data-aos="fade-up" data-aos-delay="100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    数据库信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>主机地址:</strong></td>
                        <td><?= $_ENV['DB_HOST'] ?? 'localhost' ?></td>
                    </tr>
                    <tr>
                        <td><strong>端口:</strong></td>
                        <td><?= $_ENV['DB_PORT'] ?? '3306' ?></td>
                    </tr>
                    <tr>
                        <td><strong>数据库名:</strong></td>
                        <td><?= $_ENV['DB_DATABASE'] ?? 'namesilo_sales' ?></td>
                    </tr>
                    <tr>
                        <td><strong>用户名:</strong></td>
                        <td><?= $_ENV['DB_USERNAME'] ?? 'root' ?></td>
                    </tr>
                    <tr>
                        <td><strong>字符集:</strong></td>
                        <td>utf8mb4</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card" data-aos="fade-up" data-aos-delay="200">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i>
                    存储分析
                </h5>
            </div>
            <div class="card-body">
                <canvas id="storageChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<?php endif; ?>

<script>
// 查看表数据
function viewTableData(tableName) {
    // 这里可以实现查看表数据的功能
    alert('查看 ' + tableName + ' 表数据功能待实现');
}

// 显示表结构
function showTableStructure(tableName) {
    // 这里可以实现显示表结构的功能
    alert('查看 ' + tableName + ' 表结构功能待实现');
}

// 存储分析图表
<?php if ($dbStatus['connected']): ?>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('storageChart');
    if (ctx) {
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: [
                    <?php foreach ($dbStatus['stats'] as $table => $info): ?>
                    '<?= $table ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    data: [
                        <?php foreach ($dbStatus['stats'] as $table => $info): ?>
                        <?= $info['size'] ?: 0 ?>,
                        <?php endforeach; ?>
                    ],
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
});
<?php endif; ?>
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/admin.php';
?>
